<?php /* Smarty version 2.6.33, created on 2025-05-21 15:57:10
         compiled from /var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/dashlet.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/dashlet.html', 2, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/dashlet.html', 6, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/dashlet.html', 31, false),array('function', 'math', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/dashlet.html', 4, false),array('function', 'json', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/dashlet.html', 21, false),)), $this); ?>
<div id="calendar_container_<?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
" class="calendar_container" style="overflow: auto; width: 100%; height: 100%; background-color: #ffffff;">
<?php ob_start(); ?><?php if (is_array ( $this->_tpl_vars['days'] )): ?><?php echo count($this->_tpl_vars['days']); ?>
<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('count_days', ob_get_contents());ob_end_clean(); ?>
<?php ob_start(); ?><?php if (is_array ( $this->_tpl_vars['users'] )): ?><?php echo count($this->_tpl_vars['users']); ?>
<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('count_users', ob_get_contents());ob_end_clean(); ?>
<?php echo smarty_function_math(array('assign' => 'cal_height','equation' => 'r*(u+1)*d','r' => $this->_tpl_vars['dashlet_data']['cal_row_height'],'d' => $this->_tpl_vars['count_days'],'u' => $this->_tpl_vars['count_users']), $this);?>

<?php echo smarty_function_math(array('assign' => 'vscroll','equation' => '1*(ch>mh)','mh' => $this->_tpl_vars['dashlet_data']['cal_max_height'],'ch' => $this->_tpl_vars['cal_height']), $this);?>

<?php echo smarty_function_math(array('assign' => 'cal_width','equation' => '(e-s+1+(s>0)+(e<23))*w1+363+14*vscroll','s' => ((is_array($_tmp=@$this->_tpl_vars['settings']['week_start_hour'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)),'e' => ((is_array($_tmp=@$this->_tpl_vars['settings']['week_end_hour'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)),'w1' => $this->_tpl_vars['dashlet_data']['w1'],'vscroll' => $this->_tpl_vars['vscroll']), $this);?>

<?php echo smarty_function_math(array('assign' => 'width','equation' => '320+cw+4*vscroll','cw' => $this->_tpl_vars['cal_width'],'vscroll' => $this->_tpl_vars['vscroll']), $this);?>

<?php if ($this->_tpl_vars['width'] < 1000): ?><?php $this->assign('width', 1000); ?><?php endif; ?>
  <?php $this->assign('msg_width', 500); ?>
  <?php echo smarty_function_math(array('assign' => 'hm','equation' => '(a-b)/2*c','a' => $this->_tpl_vars['width'],'b' => $this->_tpl_vars['msg_width'],'c' => $this->_tpl_vars['dashlet']->get('full_width')), $this);?>

  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_messages.html", 'smarty_include_vars' => array('width' => $this->_tpl_vars['msg_width'],'margin_left' => $this->_tpl_vars['hm'],'margin_right' => $this->_tpl_vars['hm'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  <div style="width: <?php echo $this->_tpl_vars['width']; ?>
px;">
    <div class="floatl" style="padding: 5px 20px 5px 0; width: 300px;">
      <form name="planned_time_filters" id="planned_time_filters_<?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
" method="post" action="">
                <?php $_from = $this->_tpl_vars['module_scripts']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['script']):
?>
          <script type="text/javascript" defer="defer" src="<?php echo $this->_tpl_vars['script']['src']; ?>
?<?php echo $this->_tpl_vars['system_options']['build']; ?>
"></script>
        <?php endforeach; endif; unset($_from); ?>
        <script type="text/javascript" defer="defer">
                    initDashlet('plannedTime', '<?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
', '<?php echo $this->_tpl_vars['dashlet']->get('controller'); ?>
', <?php echo smarty_function_json(array('encode' => ((is_array($_tmp=@$this->_tpl_vars['dashlet_data'])) ? $this->_run_mod_handler('default', true, $_tmp, '') : smarty_modifier_default($_tmp, ''))), $this);?>
);
        </script>
        <script defer="defer" src="<?php echo @PH_MODULES_URL; ?>
tasks/javascript/tasks.js?<?php echo $this->_tpl_vars['system_options']['build']; ?>
"></script>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_filters.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </form>
      <div id="pta_legend_container">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_legend.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </div>
      <?php if ($this->_tpl_vars['dashlet_data']['default_task_type']): ?>
      <br />
      <button type="button" name="addTaskGo" id="pta_addTaskGo" class="button" onclick="plannedTime.hideOverlib(); ajaxSaveTask('ajax_add', '<?php echo $this->_tpl_vars['dashlet_data']['default_task_type']; ?>
', 'dashlet_messages_<?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
', plannedTime.reloadTasks.bind(plannedTime));"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['plugin_add_task'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
      <?php endif; ?>
    </div>
    <div class="floatl">
      <div class="calendar_inner_container" id="calendar_<?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
" style="overflow: auto; width: <?php echo $this->_tpl_vars['cal_width']; ?>
px;">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_calendar.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </div>
    </div>
    <div class="clear"></div>
  </div>
</div>