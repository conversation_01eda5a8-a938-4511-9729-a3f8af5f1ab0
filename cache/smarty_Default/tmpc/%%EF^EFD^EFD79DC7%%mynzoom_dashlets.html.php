<?php /* Smarty version 2.6.33, created on 2025-06-20 16:46:28
         compiled from /var/www/Nzoom-Hella/_libs/modules/users/templates/mynzoom_dashlets.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/users/templates/mynzoom_dashlets.html', 6, false),array('function', 'popup', '/var/www/Nzoom-Hella/_libs/modules/users/templates/mynzoom_dashlets.html', 6, false),array('function', 'help', '/var/www/Nzoom-Hella/_libs/modules/users/templates/mynzoom_dashlets.html', 28, false),)), $this); ?>
    <input type="hidden" name="layout" id="layout" value="<?php echo $this->_tpl_vars['layout']; ?>
" />
    <table cellspacing="0" cellpadding="0" border="0" class="t_table">
      <tr>
        <td colspan="5" class="t_caption3">
          <div class="t_caption2_title">
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/info.png" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" <?php echo smarty_function_popup(array('text' => $this->_tpl_vars['layouts'][$this->_tpl_vars['layout']]['description'],'caption' => $this->_config[0]['vars']['system_info']), $this);?>
 />
            <?php echo ((is_array($_tmp=$this->_tpl_vars['layouts'][$this->_tpl_vars['layout']]['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </div>
        </td>
      </tr>
      <tr>
        <td colspan="5">
          <script type="text/javascript">dashlets_info = new Object(); dashlets_info = <?php echo $this->_tpl_vars['dashlets_info']; ?>
;</script>
          <table id="dashlets_container" cellspacing="0" cellpadding="0" border="0" class="t_table" style="width: 800px;">
            <tr>
              <td class="t_caption t_border t_border_left" style="width:50px">
                <div class="t_caption_title"><?php echo $this->_config[0]['vars']['num']; ?>
</div>
              </td>
               <td class="t_caption t_border" style="width:80px">
                <div class="t_caption_title"><?php echo $this->_config[0]['vars']['users_mynzoom_settings_position']; ?>
</div>
              </td>
              <td class="t_caption t_border" style="width:200px">
                <div class="t_caption_title"><?php echo $this->_config[0]['vars']['users_mynzoom_settings_dashlets']; ?>
</div>
              </td>
              <td class="t_caption t_border">
                <div class="t_caption_title floatl"><?php echo $this->_config[0]['vars']['users_mynzoom_settings_description']; ?>
</div>
                <div class="t_buttons">
                  <div id="dashlets_container_plusButton" onclick="addField('dashlets_container');" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['add_row'],'popup_only' => 1), $this);?>
><div class="t_plus"></div></div>
                  <div id="dashlets_container_minusButton"<?php if (empty ( $this->_tpl_vars['settings']['dashlets'] ) || count ( $this->_tpl_vars['settings']['dashlets'] ) <= 1): ?> class="disabled"<?php endif; ?> onclick="removeField('dashlets_container');" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['remove_row'],'popup_only' => 1), $this);?>
><div class="t_minus"></div></div>
                </div>
              </td>
            </tr>
            <?php $_from = $this->_tpl_vars['settings']['dashlets']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['value'] => $this->_tpl_vars['position']):
        $this->_foreach['i']['iteration']++;
?> 
            <tr id="dashlets_container_<?php echo $this->_foreach['i']['iteration']; ?>
">
              <td class="t_border t_v_border t_border_left" style="text-align: center;">
                <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
/small/delete.png" height="12" width="12" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="hide_row"<?php if (empty ( $this->_tpl_vars['settings']['dashlets'] ) || count ( $this->_tpl_vars['settings']['dashlets'] ) <= 1): ?> style="visibility: hidden;"<?php endif; ?> onclick="confirmAction('delete_row', function() { hideField('dashlets_container','<?php echo $this->_foreach['i']['iteration']; ?>
'); }, this);" />
                <a href="javascript: void(0);" onclick="javascript: disableField('dashlets_container','<?php echo $this->_foreach['i']['iteration']; ?>
');"><?php echo $this->_foreach['i']['iteration']; ?>
</a>
              </td>
              <td class="t_border t_v_border" style="padding: 5px">
                <select name="positions[<?php echo $this->_foreach['i']['iteration']-1; ?>
]" id="positions_<?php echo $this->_foreach['i']['iteration']; ?>
" class="selbox short" onfocus="highlight(this)" onblur="unhighlight(this)">
                  <optgroup label="<?php echo $this->_config[0]['vars']['left']; ?>
">
                    <?php unset($this->_sections['pos']);
$this->_sections['pos']['start'] = (int)1;
$this->_sections['pos']['loop'] = is_array($_loop=11) ? count($_loop) : max(0, (int)$_loop); unset($_loop);
$this->_sections['pos']['name'] = 'pos';
$this->_sections['pos']['show'] = true;
$this->_sections['pos']['max'] = $this->_sections['pos']['loop'];
$this->_sections['pos']['step'] = 1;
if ($this->_sections['pos']['start'] < 0)
    $this->_sections['pos']['start'] = max($this->_sections['pos']['step'] > 0 ? 0 : -1, $this->_sections['pos']['loop'] + $this->_sections['pos']['start']);
else
    $this->_sections['pos']['start'] = min($this->_sections['pos']['start'], $this->_sections['pos']['step'] > 0 ? $this->_sections['pos']['loop'] : $this->_sections['pos']['loop']-1);
if ($this->_sections['pos']['show']) {
    $this->_sections['pos']['total'] = min(ceil(($this->_sections['pos']['step'] > 0 ? $this->_sections['pos']['loop'] - $this->_sections['pos']['start'] : $this->_sections['pos']['start']+1)/abs($this->_sections['pos']['step'])), $this->_sections['pos']['max']);
    if ($this->_sections['pos']['total'] == 0)
        $this->_sections['pos']['show'] = false;
} else
    $this->_sections['pos']['total'] = 0;
if ($this->_sections['pos']['show']):

            for ($this->_sections['pos']['index'] = $this->_sections['pos']['start'], $this->_sections['pos']['iteration'] = 1;
                 $this->_sections['pos']['iteration'] <= $this->_sections['pos']['total'];
                 $this->_sections['pos']['index'] += $this->_sections['pos']['step'], $this->_sections['pos']['iteration']++):
$this->_sections['pos']['rownum'] = $this->_sections['pos']['iteration'];
$this->_sections['pos']['index_prev'] = $this->_sections['pos']['index'] - $this->_sections['pos']['step'];
$this->_sections['pos']['index_next'] = $this->_sections['pos']['index'] + $this->_sections['pos']['step'];
$this->_sections['pos']['first']      = ($this->_sections['pos']['iteration'] == 1);
$this->_sections['pos']['last']       = ($this->_sections['pos']['iteration'] == $this->_sections['pos']['total']);
?>
                      <?php ob_start(); ?><?php echo $this->_sections['pos']['index']; ?>
l<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('position_side', ob_get_contents());ob_end_clean(); ?>
                      <option value="<?php echo $this->_tpl_vars['position_side']; ?>
" <?php if ($this->_tpl_vars['position'] == $this->_tpl_vars['position_side']): ?>selected="selected"<?php endif; ?>>
                        <?php echo $this->_sections['pos']['index']; ?>
 - <?php echo $this->_config[0]['vars']['left']; ?>

                      </option>
                    <?php endfor; endif; ?>
                  </optgroup>
                  <optgroup label="<?php echo $this->_config[0]['vars']['right']; ?>
">
                    <?php unset($this->_sections['pos']);
$this->_sections['pos']['start'] = (int)1;
$this->_sections['pos']['loop'] = is_array($_loop=11) ? count($_loop) : max(0, (int)$_loop); unset($_loop);
$this->_sections['pos']['name'] = 'pos';
$this->_sections['pos']['show'] = true;
$this->_sections['pos']['max'] = $this->_sections['pos']['loop'];
$this->_sections['pos']['step'] = 1;
if ($this->_sections['pos']['start'] < 0)
    $this->_sections['pos']['start'] = max($this->_sections['pos']['step'] > 0 ? 0 : -1, $this->_sections['pos']['loop'] + $this->_sections['pos']['start']);
else
    $this->_sections['pos']['start'] = min($this->_sections['pos']['start'], $this->_sections['pos']['step'] > 0 ? $this->_sections['pos']['loop'] : $this->_sections['pos']['loop']-1);
if ($this->_sections['pos']['show']) {
    $this->_sections['pos']['total'] = min(ceil(($this->_sections['pos']['step'] > 0 ? $this->_sections['pos']['loop'] - $this->_sections['pos']['start'] : $this->_sections['pos']['start']+1)/abs($this->_sections['pos']['step'])), $this->_sections['pos']['max']);
    if ($this->_sections['pos']['total'] == 0)
        $this->_sections['pos']['show'] = false;
} else
    $this->_sections['pos']['total'] = 0;
if ($this->_sections['pos']['show']):

            for ($this->_sections['pos']['index'] = $this->_sections['pos']['start'], $this->_sections['pos']['iteration'] = 1;
                 $this->_sections['pos']['iteration'] <= $this->_sections['pos']['total'];
                 $this->_sections['pos']['index'] += $this->_sections['pos']['step'], $this->_sections['pos']['iteration']++):
$this->_sections['pos']['rownum'] = $this->_sections['pos']['iteration'];
$this->_sections['pos']['index_prev'] = $this->_sections['pos']['index'] - $this->_sections['pos']['step'];
$this->_sections['pos']['index_next'] = $this->_sections['pos']['index'] + $this->_sections['pos']['step'];
$this->_sections['pos']['first']      = ($this->_sections['pos']['iteration'] == 1);
$this->_sections['pos']['last']       = ($this->_sections['pos']['iteration'] == $this->_sections['pos']['total']);
?>
                      <?php ob_start(); ?><?php echo $this->_sections['pos']['index']; ?>
r<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('position_side', ob_get_contents());ob_end_clean(); ?>
                      <option value="<?php echo $this->_tpl_vars['position_side']; ?>
" <?php if ($this->_tpl_vars['position'] == $this->_tpl_vars['position_side']): ?>selected="selected"<?php endif; ?>>
                        <?php echo $this->_sections['pos']['index']; ?>
 - <?php echo $this->_config[0]['vars']['right']; ?>

                      </option>
                    <?php endfor; endif; ?>
                  </optgroup>
                </select>
              </td>
              <td class="t_border t_v_border" style="padding: 5px">
                <select name="dashlets[<?php echo $this->_foreach['i']['iteration']-1; ?>
]" id="dashlets_<?php echo $this->_foreach['i']['iteration']; ?>
" class="selbox<?php if (! $this->_tpl_vars['value']): ?> undefined<?php endif; ?>" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this); dashletsInfo(this.value, 'description_'+this.id);">
                  <option value="" class="undefined"<?php if (! $this->_tpl_vars['value']): ?> selected="selected"<?php endif; ?>>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                <?php $_from = $this->_tpl_vars['dashlets']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['label'] => $this->_tpl_vars['optgroup']):
?>
                  <optgroup label="<?php echo $this->_tpl_vars['label']; ?>
">
                  <?php $_from = $this->_tpl_vars['optgroup']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['dashlet']):
?>
                    <?php ob_start(); ?><?php echo $this->_tpl_vars['value']; ?>
_deleted<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('value_deleted', ob_get_contents());ob_end_clean(); ?>
                    <?php ob_start(); ?><?php echo $this->_tpl_vars['value']; ?>
_inactive<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('value_inactive', ob_get_contents());ob_end_clean(); ?>
                    <?php ob_start(); ?>
                      <?php if ($this->_tpl_vars['dashlet']['option_value'] == $this->_tpl_vars['value'] || $this->_tpl_vars['dashlet']['option_value'] == $this->_tpl_vars['value_deleted'] || $this->_tpl_vars['dashlet']['option_value'] == $this->_tpl_vars['value_inactive']): ?>
                        selected="selected"
                      <?php endif; ?>
                    <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('additional', ob_get_contents());ob_end_clean(); ?>
                    <?php if (! preg_match ( '#_(deleted|inactive)#' , $this->_tpl_vars['dashlet']['option_value'] ) || preg_match ( '#selected#' , $this->_tpl_vars['additional'] )): ?>
                    <option value="<?php echo $this->_tpl_vars['dashlet']['option_value']; ?>
" <?php echo $this->_tpl_vars['additional']; ?>
><?php echo ((is_array($_tmp=$this->_tpl_vars['dashlet']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                    <?php endif; ?>
                  <?php endforeach; endif; unset($_from); ?>
                  </optgroup>
                <?php endforeach; endif; unset($_from); ?>
                </select>
              </td>
              <td class="t_border t_v_border">
                <div id="description_dashlets_<?php echo $this->_foreach['i']['iteration']; ?>
" style="border: 0px none; padding:5px">
                  <script type="text/javascript">dashletsInfo($('dashlets_<?php echo $this->_foreach['i']['iteration']; ?>
').value, 'description_dashlets_<?php echo $this->_foreach['i']['iteration']; ?>
');</script>
                </div>
              </td>
            </tr>
            <?php endforeach; else: ?>
            <tr id="dashlets_container_1">
              <td class="t_border t_v_border" style="text-align: center;">
                <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
/small/delete.png" height="12" width="12" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="hide_row" style="visibility: hidden;" onclick="confirmAction('delete_row', function() { hideField('dashlets_container','1'); }, this);" />
                <a href="javascript: void(0);" onclick="javascript: disableField('dashlets_container','1');">1</a>
              </td>
              <td class="t_border t_v_border" style="padding: 5px">
                <select name="positions[]" id="positions_1" class="selbox short" onfocus="highlight(this)" onblur="unhighlight(this)">
                  <optgroup label="<?php echo $this->_config[0]['vars']['left']; ?>
">
                    <?php unset($this->_sections['pos']);
$this->_sections['pos']['start'] = (int)1;
$this->_sections['pos']['loop'] = is_array($_loop=11) ? count($_loop) : max(0, (int)$_loop); unset($_loop);
$this->_sections['pos']['name'] = 'pos';
$this->_sections['pos']['show'] = true;
$this->_sections['pos']['max'] = $this->_sections['pos']['loop'];
$this->_sections['pos']['step'] = 1;
if ($this->_sections['pos']['start'] < 0)
    $this->_sections['pos']['start'] = max($this->_sections['pos']['step'] > 0 ? 0 : -1, $this->_sections['pos']['loop'] + $this->_sections['pos']['start']);
else
    $this->_sections['pos']['start'] = min($this->_sections['pos']['start'], $this->_sections['pos']['step'] > 0 ? $this->_sections['pos']['loop'] : $this->_sections['pos']['loop']-1);
if ($this->_sections['pos']['show']) {
    $this->_sections['pos']['total'] = min(ceil(($this->_sections['pos']['step'] > 0 ? $this->_sections['pos']['loop'] - $this->_sections['pos']['start'] : $this->_sections['pos']['start']+1)/abs($this->_sections['pos']['step'])), $this->_sections['pos']['max']);
    if ($this->_sections['pos']['total'] == 0)
        $this->_sections['pos']['show'] = false;
} else
    $this->_sections['pos']['total'] = 0;
if ($this->_sections['pos']['show']):

            for ($this->_sections['pos']['index'] = $this->_sections['pos']['start'], $this->_sections['pos']['iteration'] = 1;
                 $this->_sections['pos']['iteration'] <= $this->_sections['pos']['total'];
                 $this->_sections['pos']['index'] += $this->_sections['pos']['step'], $this->_sections['pos']['iteration']++):
$this->_sections['pos']['rownum'] = $this->_sections['pos']['iteration'];
$this->_sections['pos']['index_prev'] = $this->_sections['pos']['index'] - $this->_sections['pos']['step'];
$this->_sections['pos']['index_next'] = $this->_sections['pos']['index'] + $this->_sections['pos']['step'];
$this->_sections['pos']['first']      = ($this->_sections['pos']['iteration'] == 1);
$this->_sections['pos']['last']       = ($this->_sections['pos']['iteration'] == $this->_sections['pos']['total']);
?>
                      <?php ob_start(); ?><?php echo $this->_sections['pos']['index']; ?>
l<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('position_side', ob_get_contents());ob_end_clean(); ?>
                      <option value="<?php echo $this->_tpl_vars['position_side']; ?>
">
                        <?php echo $this->_sections['pos']['index']; ?>
 - <?php echo $this->_config[0]['vars']['left']; ?>

                      </option>
                    <?php endfor; endif; ?>
                  </optgroup>
                  <optgroup label="<?php echo $this->_config[0]['vars']['right']; ?>
">
                    <?php unset($this->_sections['pos']);
$this->_sections['pos']['start'] = (int)1;
$this->_sections['pos']['loop'] = is_array($_loop=11) ? count($_loop) : max(0, (int)$_loop); unset($_loop);
$this->_sections['pos']['name'] = 'pos';
$this->_sections['pos']['show'] = true;
$this->_sections['pos']['max'] = $this->_sections['pos']['loop'];
$this->_sections['pos']['step'] = 1;
if ($this->_sections['pos']['start'] < 0)
    $this->_sections['pos']['start'] = max($this->_sections['pos']['step'] > 0 ? 0 : -1, $this->_sections['pos']['loop'] + $this->_sections['pos']['start']);
else
    $this->_sections['pos']['start'] = min($this->_sections['pos']['start'], $this->_sections['pos']['step'] > 0 ? $this->_sections['pos']['loop'] : $this->_sections['pos']['loop']-1);
if ($this->_sections['pos']['show']) {
    $this->_sections['pos']['total'] = min(ceil(($this->_sections['pos']['step'] > 0 ? $this->_sections['pos']['loop'] - $this->_sections['pos']['start'] : $this->_sections['pos']['start']+1)/abs($this->_sections['pos']['step'])), $this->_sections['pos']['max']);
    if ($this->_sections['pos']['total'] == 0)
        $this->_sections['pos']['show'] = false;
} else
    $this->_sections['pos']['total'] = 0;
if ($this->_sections['pos']['show']):

            for ($this->_sections['pos']['index'] = $this->_sections['pos']['start'], $this->_sections['pos']['iteration'] = 1;
                 $this->_sections['pos']['iteration'] <= $this->_sections['pos']['total'];
                 $this->_sections['pos']['index'] += $this->_sections['pos']['step'], $this->_sections['pos']['iteration']++):
$this->_sections['pos']['rownum'] = $this->_sections['pos']['iteration'];
$this->_sections['pos']['index_prev'] = $this->_sections['pos']['index'] - $this->_sections['pos']['step'];
$this->_sections['pos']['index_next'] = $this->_sections['pos']['index'] + $this->_sections['pos']['step'];
$this->_sections['pos']['first']      = ($this->_sections['pos']['iteration'] == 1);
$this->_sections['pos']['last']       = ($this->_sections['pos']['iteration'] == $this->_sections['pos']['total']);
?>
                      <?php ob_start(); ?><?php echo $this->_sections['pos']['index']; ?>
r<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('position_side', ob_get_contents());ob_end_clean(); ?>
                      <option value="<?php echo $this->_tpl_vars['position_side']; ?>
">
                        <?php echo $this->_sections['pos']['index']; ?>
 - <?php echo $this->_config[0]['vars']['right']; ?>

                      </option>
                    <?php endfor; endif; ?>
                  </optgroup>
                </select>
              </td>
              <td class="t_border t_v_border" style="padding: 5px">
                <select name="dashlets[0]" id="dashlets_1" class="selbox undefined" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this); dashletsInfo(this.value, 'description_'+this.id);">
                  <option value="" class="undefined" selected="selected">[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                  <?php $_from = $this->_tpl_vars['dashlets']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['label'] => $this->_tpl_vars['optgroup']):
?>
                  <optgroup label="<?php echo $this->_tpl_vars['label']; ?>
">
                    <?php $_from = $this->_tpl_vars['optgroup']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['dashlet']):
?>
                    <option value="<?php echo $this->_tpl_vars['dashlet']['option_value']; ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['dashlet']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                    <?php endforeach; endif; unset($_from); ?>
                  </optgroup>
                <?php endforeach; endif; unset($_from); ?>
                </select>
              </td>
              <td class="t_border t_v_border">
                <div id="description_dashlets_1" style="border: 0px none; padding:5px">
                </div>
              </td>
            </tr>
            <?php endif; unset($_from); ?>
          </table>
        </td>
      </tr>
    </table>