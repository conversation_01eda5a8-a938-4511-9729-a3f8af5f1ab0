<?php /* Smarty version 2.6.33, created on 2025-06-20 13:59:19
         compiled from view_config.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'regex_replace', 'view_config.html', 25, false),array('modifier', 'url2href', 'view_config.html', 29, false),array('modifier', 'nl2br', 'view_config.html', 37, false),array('modifier', 'date_format', 'view_config.html', 39, false),array('modifier', 'default', 'view_config.html', 39, false),array('modifier', 'escape', 'view_config.html', 56, false),array('modifier', 'encrypt', 'view_config.html', 72, false),array('function', 'getimagesize', 'view_config.html', 71, false),array('function', 'help', 'view_config.html', 108, false),array('function', 'json', 'view_config.html', 166, false),)), $this); ?>
<?php if ($this->_tpl_vars['var']['source']): ?>
  <tr>
    <td colspan="3">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_configurator_list.html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['var']['frankenstein'],'exclude_edit_del' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  </tr>
<?php else: ?>
  <tr>
    <td colspan="3" style="border-style: none;">
      <div class="nz-view-wrapper">
      <table class="t_grouping_table<?php if ($this->_tpl_vars['var']['borderless']): ?> t_borderless<?php endif; ?>"<?php if ($this->_tpl_vars['var']['t_width']): ?> width="<?php echo $this->_tpl_vars['var']['t_width']; ?>
"<?php endif; ?>>
        <?php if ($this->_tpl_vars['var']['rows'] == 1): ?>
          <tr>
            <?php $_from = $this->_tpl_vars['var']['labels']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['idx'] => $this->_tpl_vars['col_name']):
?>
              <th<?php if ($this->_tpl_vars['var']['hidden'][$this->_tpl_vars['idx']]): ?> style="display: none;"<?php endif; ?>><?php echo $this->_tpl_vars['col_name']; ?>
</th>
            <?php endforeach; endif; unset($_from); ?>
          </tr>
          <tr>
            <?php $_from = $this->_tpl_vars['var']['names']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['name']):
?>
              <td style="<?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['text_align'] && ! ( $this->_tpl_vars['var'][$this->_tpl_vars['name']]['text_align'] == 'right' && $this->_tpl_vars['var']['width'][$this->_tpl_vars['name']] )): ?>text-align: <?php echo $this->_tpl_vars['var'][$this->_tpl_vars['name']]['text_align']; ?>
;<?php endif; ?><?php if ($this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']]): ?> display: none;<?php endif; ?>"<?php if ($this->_tpl_vars['var']['width'][$this->_tpl_vars['name']]): ?> width="<?php echo $this->_tpl_vars['var']['width'][$this->_tpl_vars['name']]; ?>
"<?php endif; ?>>
                <?php echo ''; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['text_align'] == 'right' && $this->_tpl_vars['var']['width'][$this->_tpl_vars['name']]): ?><?php echo '<div style="float: left; text-align: '; ?><?php echo $this->_tpl_vars['var'][$this->_tpl_vars['name']]['text_align']; ?><?php echo '; width: '; ?><?php echo $this->_tpl_vars['var']['width'][$this->_tpl_vars['name']]; ?><?php echo ''; ?><?php if (preg_match ( '#^[\d\.]+$#' , $this->_tpl_vars['var']['width'][$this->_tpl_vars['name']] )): ?><?php echo 'px'; ?><?php endif; ?><?php echo ';">'; ?><?php endif; ?><?php echo ''; ?><?php if (! $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']]): ?><?php echo ''; ?><?php if ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'text'): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['use_asterisk'] && preg_match ( '#^asteriskcall_(fax|phone|gsm).*$#' , $this->_tpl_vars['name'] )): ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['name'])) ? $this->_run_mod_handler('regex_replace', true, $_tmp, '/^asteriskcall_(fax|phone|gsm).*$/', '$1') : smarty_modifier_regex_replace($_tmp, '/^asteriskcall_(fax|phone|gsm).*$/', '$1')); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('asterisk_contact', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['asterisk_contact']): ?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_asterisk_contact.html contact_type=".($this->_tpl_vars['asterisk_contact'])." number=".($this->_tpl_vars['var']).".values[".($this->_tpl_vars['name'])."]", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['var']['values'][$this->_tpl_vars['name']])) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'autocompleter'): ?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_view_autocompleter.html", 'smarty_include_vars' => array('value' => $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']],'value_id' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['value_id'],'view_mode_url' => $this->_tpl_vars['var']['autocomplete'][$this->_tpl_vars['name']]['view_mode_url'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'textarea'): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['values'][$this->_tpl_vars['name']])) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'date'): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['values'][$this->_tpl_vars['name']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'datetime'): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['values'][$this->_tpl_vars['name']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'time'): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['values'][$this->_tpl_vars['name']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['time_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['time_short'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'dropdown'): ?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_view_dropdown_radio.html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['var'][$this->_tpl_vars['name']],'value' => $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'radio'): ?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_view_dropdown_radio.html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['var'][$this->_tpl_vars['name']],'value' => $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'checkbox_group'): ?><?php echo ''; ?><?php if (is_array ( $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']] ) && count ( $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']] ) > 16): ?><?php echo '<div class="scroll_box" style="width: 100%!important;">'; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['options']): ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['mcb'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['mcb']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['option']):
        $this->_foreach['mcb']['iteration']++;
?><?php echo ''; ?><?php if (@ in_array ( $this->_tpl_vars['option']['option_value'] , $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']] )): ?><?php echo ''; ?><?php if (trim ( $this->_tpl_vars['option']['label'] )): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?><?php echo ''; ?><?php if (! empty ( $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options_align'] ) && $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options_align'] == 'horizontal'): ?><?php echo '&nbsp;'; ?><?php else: ?><?php echo '<br />'; ?><?php endif; ?><?php echo ''; ?><?php else: ?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'small/check_yes.png" border="0" alt="" />'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['optgroups']): ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['var'][$this->_tpl_vars['name']]['optgroups']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['optgroup_name'] => $this->_tpl_vars['optgroup']):
?><?php echo ''; ?><?php $_from = $this->_tpl_vars['optgroup']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['mcb'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['mcb']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['option']):
        $this->_foreach['mcb']['iteration']++;
?><?php echo ''; ?><?php if (@ in_array ( $this->_tpl_vars['option']['option_value'] , $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']] )): ?><?php echo ''; ?><?php if (trim ( $this->_tpl_vars['option']['label'] )): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?><?php echo ''; ?><?php if (! empty ( $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options_align'] ) && $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options_align'] == 'horizontal'): ?><?php echo '&nbsp;'; ?><?php else: ?><?php echo '<br />'; ?><?php endif; ?><?php echo ''; ?><?php else: ?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'small/check_yes.png" border="0" alt="" />'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if (is_array ( $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']] ) && count ( $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']] ) > 16): ?><?php echo '</div>'; ?><?php endif; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'file_upload'): ?><?php echo ''; ?><?php $this->assign('file_info', $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']]); ?><?php echo ''; ?><?php if (! empty ( $this->_tpl_vars['file_info'] ) && is_object ( $this->_tpl_vars['file_info'] ) && ! $this->_tpl_vars['file_info']->get('deleted_by')): ?><?php echo ''; ?><?php if (! $this->_tpl_vars['file_info']->get('not_exist')): ?><?php echo ''; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['view_mode'] == 'thumbnail' && $this->_tpl_vars['file_info']->isImage()): ?><?php echo ''; ?><?php echo smarty_function_getimagesize(array('assign' => 'image_dimensions','image_path' => $this->_tpl_vars['file_info']->get('path')), $this);?><?php echo '<img src="'; ?><?php echo $_SERVER['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '=files&amp;files=viewfile&amp;viewfile='; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file_info']->get('id'))) ? $this->_run_mod_handler('encrypt', true, $_tmp, '_viewfile_') : smarty_modifier_encrypt($_tmp, '_viewfile_')))) ? $this->_run_mod_handler('escape', true, $_tmp, 'url') : smarty_modifier_escape($_tmp, 'url')); ?><?php echo ''; ?><?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?><?php echo '&amp;archive=1'; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_width']): ?><?php echo '&amp;maxwidth='; ?><?php echo $this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_width']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_height']): ?><?php echo '&amp;maxheight='; ?><?php echo $this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_height']; ?><?php echo ''; ?><?php endif; ?><?php echo '" onclick="showFullLBImage(\''; ?><?php echo $_SERVER['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['module']; ?><?php echo ''; ?><?php echo $this->_tpl_vars['controller_string']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['action_param']; ?><?php echo '=viewfile&amp;viewfile='; ?><?php echo $this->_tpl_vars['var']['model_id']; ?><?php echo '&amp;file='; ?><?php echo $this->_tpl_vars['file_info']->get('id'); ?><?php echo ''; ?><?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?><?php echo '&amp;archive=1'; ?><?php endif; ?><?php echo '\', \''; ?><?php echo $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']]; ?><?php echo '\', \''; ?><?php echo $this->_tpl_vars['image_dimensions']['width']; ?><?php echo '\', \''; ?><?php echo $this->_tpl_vars['image_dimensions']['height']; ?><?php echo '\')" alt="" />'; ?><?php else: ?><?php echo '<a href="'; ?><?php echo $_SERVER['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['module']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['action_param']; ?><?php echo '=viewfile&amp;viewfile='; ?><?php echo $this->_tpl_vars['var']['model_id']; ?><?php echo '&amp;file='; ?><?php echo $this->_tpl_vars['file_info']->get('id'); ?><?php echo ''; ?><?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?><?php echo '&amp;archive=1'; ?><?php endif; ?><?php echo '" target="_blank"><img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo ''; ?><?php echo $this->_tpl_vars['file_info']->getIconName(); ?><?php echo '.png" width="16" height="16" border="0" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" /></a><a href="'; ?><?php echo $_SERVER['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['module']; ?><?php echo ''; ?><?php echo $this->_tpl_vars['controller_string']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['action_param']; ?><?php echo '=getfile&amp;getfile='; ?><?php echo $this->_tpl_vars['var']['model_id']; ?><?php echo '&amp;file='; ?><?php echo $this->_tpl_vars['file_info']->get('id'); ?><?php echo '"><img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'download.png" width="16" height="16" border="0" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" /></a>'; ?><?php endif; ?><?php echo ''; ?><?php else: ?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo ''; ?><?php echo $this->_tpl_vars['file_info']->getIconName(); ?><?php echo '.png" width="16" height="16" border="0" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" class="pointer dimmed" /><img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'download.png" width="16" height="16" border="0" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" class="pointer dimmed" />'; ?><?php endif; ?><?php echo ''; ?><?php else: ?><?php echo '&nbsp;'; ?><?php endif; ?><?php echo ''; ?><?php else: ?><?php echo '&nbsp;'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['text_align'] == 'right' && $this->_tpl_vars['var']['width'][$this->_tpl_vars['name']]): ?><?php echo '</div>'; ?><?php endif; ?><?php echo ''; ?><?php if (( $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']] || $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']] === '0' ) && $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']]): ?><?php echo '&nbsp;'; ?><?php echo $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']]; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>

              </td>
            <?php endforeach; endif; unset($_from); ?>
          </tr>

        <?php else: ?>
          <?php if ($this->_tpl_vars['var']['columns'] && count ( $this->_tpl_vars['var']['columns'] ) > 0): ?>
          <tr>
            <th colspan="2"></th>
            <?php $_from = $this->_tpl_vars['var']['columns']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['col_name'] => $this->_tpl_vars['column']):
?>
              <th><?php echo $this->_tpl_vars['column']; ?>
</th>
            <?php endforeach; endif; unset($_from); ?>
          </tr>
          <?php endif; ?>
          <?php $_from = $this->_tpl_vars['var']['names']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['configurator'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['configurator']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['name']):
        $this->_foreach['configurator']['iteration']++;
?>
            <?php if (! $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']] && ! preg_match ( '/__[a-z]+$/' , $this->_tpl_vars['name'] )): ?>
              <?php ob_start(); ?><?php if ($this->_tpl_vars['var']['help'][$this->_tpl_vars['key']]): ?><?php echo $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']]; ?>
<?php else: ?><?php echo $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']]; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?>
              <tr>
                <td style="border-right: 0px!important;"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'text_content' => $this->_tpl_vars['info']), $this);?>
</td>
                <td style="border-left: 0px!important;<?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['text_align'] && ! ( $this->_tpl_vars['var'][$this->_tpl_vars['name']]['text_align'] == 'right' && $this->_tpl_vars['var']['width'][$this->_tpl_vars['name']] )): ?>text-align: <?php echo $this->_tpl_vars['var'][$this->_tpl_vars['name']]['text_align']; ?>
;<?php endif; ?>"<?php if ($this->_tpl_vars['var']['width'][$this->_tpl_vars['name']]): ?> width="<?php echo $this->_tpl_vars['var']['width'][$this->_tpl_vars['name']]; ?>
"<?php endif; ?>>
                  <?php echo ''; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['text_align'] == 'right' && $this->_tpl_vars['var']['width'][$this->_tpl_vars['name']]): ?><?php echo '<div style="float: left; text-align: '; ?><?php echo $this->_tpl_vars['var'][$this->_tpl_vars['name']]['text_align']; ?><?php echo '; width: '; ?><?php echo $this->_tpl_vars['var']['width'][$this->_tpl_vars['name']]; ?><?php echo ''; ?><?php if (preg_match ( '#^[\d\.]+$#' , $this->_tpl_vars['var']['width'][$this->_tpl_vars['name']] )): ?><?php echo 'px'; ?><?php endif; ?><?php echo ';">'; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'text'): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['use_asterisk'] && preg_match ( '#^asteriskcall_(fax|phone|gsm).*$#' , $this->_tpl_vars['name'] )): ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['name'])) ? $this->_run_mod_handler('regex_replace', true, $_tmp, '/^asteriskcall_(fax|phone|gsm).*$/', '$1') : smarty_modifier_regex_replace($_tmp, '/^asteriskcall_(fax|phone|gsm).*$/', '$1')); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('asterisk_contact', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['asterisk_contact']): ?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_asterisk_contact.html contact_type=".($this->_tpl_vars['asterisk_contact'])." number=".($this->_tpl_vars['var']).".values[".($this->_tpl_vars['name'])."]", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['var']['values'][$this->_tpl_vars['name']])) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'autocompleter'): ?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_view_autocompleter.html", 'smarty_include_vars' => array('value' => $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']],'value_id' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['value_id'],'view_mode_url' => $this->_tpl_vars['var']['autocomplete'][$this->_tpl_vars['name']]['view_mode_url'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'textarea'): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['values'][$this->_tpl_vars['name']])) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'date'): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['values'][$this->_tpl_vars['name']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'datetime'): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['values'][$this->_tpl_vars['name']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'time'): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['values'][$this->_tpl_vars['name']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['time_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['time_short'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'dropdown'): ?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_view_dropdown_radio.html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['var'][$this->_tpl_vars['name']],'value' => $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'radio'): ?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_view_dropdown_radio.html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['var'][$this->_tpl_vars['name']],'value' => $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'checkbox_group'): ?><?php echo ''; ?><?php if (is_array ( $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']] ) && count ( $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']] ) > 16): ?><?php echo '<div class="scroll_box" style="width:100%!important;">'; ?><?php endif; ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['mcb'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['mcb']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['option']):
        $this->_foreach['mcb']['iteration']++;
?><?php echo ''; ?><?php if (@ in_array ( $this->_tpl_vars['option']['option_value'] , $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']] )): ?><?php echo ''; ?><?php if (trim ( $this->_tpl_vars['option']['label'] )): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?><?php echo ''; ?><?php if (! empty ( $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options_align'] ) && $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options_align'] == 'horizontal'): ?><?php echo '&nbsp;'; ?><?php else: ?><?php echo '<br />'; ?><?php endif; ?><?php echo ''; ?><?php else: ?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'small/check_yes.png" border="0" alt="" />'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php if (is_array ( $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']] ) && count ( $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']] ) > 16): ?><?php echo '</div>'; ?><?php endif; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'file_upload'): ?><?php echo ''; ?><?php $this->assign('file_info', $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']]); ?><?php echo ''; ?><?php if (! empty ( $this->_tpl_vars['file_info'] ) && is_object ( $this->_tpl_vars['file_info'] ) && ! $this->_tpl_vars['file_info']->get('deleted_by')): ?><?php echo ''; ?><?php if (! $this->_tpl_vars['file_info']->get('not_exist')): ?><?php echo ''; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['view_mode'] == 'thumbnail' && $this->_tpl_vars['file_info']->isImage()): ?><?php echo ''; ?><?php echo smarty_function_getimagesize(array('assign' => 'image_dimensions','image_path' => $this->_tpl_vars['file_info']->get('path')), $this);?><?php echo '<img src="'; ?><?php echo $_SERVER['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '=files&amp;files=viewfile&amp;viewfile='; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file_info']->get('id'))) ? $this->_run_mod_handler('encrypt', true, $_tmp, '_viewfile_') : smarty_modifier_encrypt($_tmp, '_viewfile_')))) ? $this->_run_mod_handler('escape', true, $_tmp, 'url') : smarty_modifier_escape($_tmp, 'url')); ?><?php echo ''; ?><?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?><?php echo '&amp;archive=1'; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_width']): ?><?php echo '&amp;maxwidth='; ?><?php echo $this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_width']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_height']): ?><?php echo '&amp;maxheight='; ?><?php echo $this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_height']; ?><?php echo ''; ?><?php endif; ?><?php echo '" onclick="showFullLBImage(\''; ?><?php echo $_SERVER['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['module']; ?><?php echo ''; ?><?php echo $this->_tpl_vars['controller_string']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['action_param']; ?><?php echo '=viewfile&amp;viewfile='; ?><?php echo $this->_tpl_vars['var']['model_id']; ?><?php echo '&amp;file='; ?><?php echo $this->_tpl_vars['file_info']->get('id'); ?><?php echo ''; ?><?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?><?php echo '&amp;archive=1'; ?><?php endif; ?><?php echo '\', \''; ?><?php echo $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']]; ?><?php echo '\', \''; ?><?php echo $this->_tpl_vars['image_dimensions']['width']; ?><?php echo '\', \''; ?><?php echo $this->_tpl_vars['image_dimensions']['height']; ?><?php echo '\')" />'; ?><?php else: ?><?php echo '<a href="'; ?><?php echo $_SERVER['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['module']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['action_param']; ?><?php echo '=viewfile&amp;viewfile='; ?><?php echo $this->_tpl_vars['var']['model_id']; ?><?php echo '&amp;file='; ?><?php echo $this->_tpl_vars['file_info']->get('id'); ?><?php echo ''; ?><?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?><?php echo '&amp;archive=1'; ?><?php endif; ?><?php echo '" target="_blank"><img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo ''; ?><?php echo $this->_tpl_vars['file_info']->getIconName(); ?><?php echo '.png" width="16" height="16" border="0" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" /></a><a href="'; ?><?php echo $_SERVER['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['module']; ?><?php echo ''; ?><?php echo $this->_tpl_vars['controller_string']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['action_param']; ?><?php echo '=getfile&amp;getfile='; ?><?php echo $this->_tpl_vars['var']['model_id']; ?><?php echo '&amp;file='; ?><?php echo $this->_tpl_vars['file_info']->get('id'); ?><?php echo '"><img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'download.png" width="16" height="16" border="0" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" /></a>'; ?><?php endif; ?><?php echo ''; ?><?php else: ?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo ''; ?><?php echo $this->_tpl_vars['file_info']->getIconName(); ?><?php echo '.png" width="16" height="16" border="0" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" class="pointer dimmed" /><img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'download.png" width="16" height="16" border="0" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" class="pointer dimmed" />'; ?><?php endif; ?><?php echo ''; ?><?php else: ?><?php echo '&nbsp;'; ?><?php endif; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'map'): ?><?php echo '<script type="text/javascript">params_map_'; ?><?php echo $this->_tpl_vars['name']; ?><?php echo ' = '; ?><?php echo smarty_function_json(array('encode' => ((is_array($_tmp=@$this->_tpl_vars['var'][$this->_tpl_vars['name']]['map_params'])) ? $this->_run_mod_handler('default', true, $_tmp, false) : smarty_modifier_default($_tmp, false))), $this);?><?php echo ';</script>'; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['map_params']['type'] == 'inline'): ?><?php echo '&nbsp;'; ?><?php else: ?><?php echo '<input type="image" src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'map.png" width="16" height="16" alt="" class="image_map" border="0" onclick="showMap(this, params_map_'; ?><?php echo $this->_tpl_vars['name']; ?><?php echo ', \''; ?><?php echo $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']]; ?><?php echo '\', \'\'); return false;" />'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['text_align'] == 'right' && $this->_tpl_vars['var']['width'][$this->_tpl_vars['name']]): ?><?php echo '</div>'; ?><?php endif; ?><?php echo ''; ?><?php if (( $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']] || $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']] === '0' ) && $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']]): ?><?php echo '&nbsp;'; ?><?php echo $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']]; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>

                </td>
                <?php $_from = $this->_tpl_vars['var']['columns']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['col'] => $this->_tpl_vars['c']):
?>
                  <?php ob_start(); ?><?php echo $this->_tpl_vars['name']; ?>
__<?php echo $this->_tpl_vars['col']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('name_pattern', ob_get_contents());ob_end_clean(); ?>
                  <td style="<?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name_pattern']]['text_align'] && ! ( $this->_tpl_vars['var'][$this->_tpl_vars['name_pattern']]['text_align'] == 'right' && $this->_tpl_vars['var']['width'][$this->_tpl_vars['name_pattern']] )): ?>text-align: <?php echo $this->_tpl_vars['var'][$this->_tpl_vars['name_pattern']]['text_align']; ?>
;<?php endif; ?>"<?php if ($this->_tpl_vars['var']['width'][$this->_tpl_vars['name_pattern']]): ?> width="<?php echo $this->_tpl_vars['var']['width'][$this->_tpl_vars['name_pattern']]; ?>
"<?php endif; ?>>
                    <?php echo ''; ?><?php $_from = $this->_tpl_vars['var']['names']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['param_key'] => $this->_tpl_vars['param_name']):
?><?php echo ''; ?><?php if (! $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['param_key']] && preg_match ( '/(__[a-z]+)$/' , $this->_tpl_vars['param_name'] , $this->_tpl_vars['mtch'][0] ) && $this->_tpl_vars['name_pattern'] == $this->_tpl_vars['param_name']): ?><?php echo ''; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name_pattern']]['text_align'] == 'right' && $this->_tpl_vars['var']['width'][$this->_tpl_vars['name_pattern']]): ?><?php echo '<div style="float: left; text-align: '; ?><?php echo $this->_tpl_vars['var'][$this->_tpl_vars['name_pattern']]['text_align']; ?><?php echo '; width: '; ?><?php echo $this->_tpl_vars['var']['width'][$this->_tpl_vars['name_pattern']]; ?><?php echo ''; ?><?php if (preg_match ( '#^[\d\.]+$#' , $this->_tpl_vars['var']['width'][$this->_tpl_vars['name_pattern']] )): ?><?php echo 'px'; ?><?php endif; ?><?php echo '">'; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['var']['types'][$this->_tpl_vars['param_key']] == 'text'): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['use_asterisk'] && preg_match ( '#^asteriskcall_(fax|phone|gsm).*$#' , $this->_tpl_vars['param_name'] )): ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['param_name'])) ? $this->_run_mod_handler('regex_replace', true, $_tmp, '/^asteriskcall_(fax|phone|gsm).*$/', '$1') : smarty_modifier_regex_replace($_tmp, '/^asteriskcall_(fax|phone|gsm).*$/', '$1')); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('asterisk_contact', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['asterisk_contact']): ?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_asterisk_contact.html contact_type=".($this->_tpl_vars['asterisk_contact'])." number=".($this->_tpl_vars['var']).".values[".($this->_tpl_vars['param_name'])."]", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['var']['values'][$this->_tpl_vars['param_name']])) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['param_key']] == 'autocompleter'): ?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_view_autocompleter.html", 'smarty_include_vars' => array('value' => $this->_tpl_vars['var']['values'][$this->_tpl_vars['param_name']],'value_id' => $this->_tpl_vars['var'][$this->_tpl_vars['param_name']]['value_id'],'view_mode_url' => $this->_tpl_vars['var']['autocomplete'][$this->_tpl_vars['param_name']]['view_mode_url'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['param_key']] == 'textarea'): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['values'][$this->_tpl_vars['param_name']])) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['param_key']] == 'date'): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['values'][$this->_tpl_vars['param_name']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['param_key']] == 'datetime'): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['values'][$this->_tpl_vars['param_name']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['param_key']] == 'time'): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['values'][$this->_tpl_vars['param_name']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['time_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['time_short'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['param_key']] == 'dropdown'): ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['var'][$this->_tpl_vars['name_pattern']]['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['option']):
?><?php echo ''; ?><?php if ($this->_tpl_vars['option']['option_value'] == $this->_tpl_vars['var']['values'][$this->_tpl_vars['param_name']]): ?><?php echo ' '; ?><?php echo $this->_tpl_vars['option']['label']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['param_key']] == 'radio'): ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['var'][$this->_tpl_vars['name_pattern']]['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['option']):
?><?php echo ''; ?><?php if (( is_array ( $this->_tpl_vars['var']['values'][$this->_tpl_vars['param_name']] ) && @ in_array ( $this->_tpl_vars['option']['option_value'] , $this->_tpl_vars['var']['values'][$this->_tpl_vars['param_name']] ) ) || ( ! is_array ( $this->_tpl_vars['var']['values'][$this->_tpl_vars['param_name']] ) && $this->_tpl_vars['option']['option_value'] == $this->_tpl_vars['var']['values'][$this->_tpl_vars['param_name']] )): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['param_key']] == 'checkbox_group'): ?><?php echo ''; ?><?php if (is_array ( $this->_tpl_vars['var']['values'][$this->_tpl_vars['param_name']] ) && count ( $this->_tpl_vars['var']['values'][$this->_tpl_vars['param_name']] ) > 16): ?><?php echo '<div class="scroll_box" style="width:100%!important;">'; ?><?php endif; ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['var'][$this->_tpl_vars['name_pattern']]['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['cb'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['cb']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['option']):
        $this->_foreach['cb']['iteration']++;
?><?php echo ''; ?><?php if (@ in_array ( $this->_tpl_vars['option']['option_value'] , $this->_tpl_vars['var']['values'][$this->_tpl_vars['param_name']] )): ?><?php echo ''; ?><?php if (trim ( $this->_tpl_vars['option']['label'] )): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?><?php echo ''; ?><?php if (! empty ( $this->_tpl_vars['var'][$this->_tpl_vars['param_name']]['options_align'] ) && $this->_tpl_vars['var'][$this->_tpl_vars['param_name']]['options_align'] == 'horizontal'): ?><?php echo '&nbsp;'; ?><?php else: ?><?php echo '<br />'; ?><?php endif; ?><?php echo ''; ?><?php else: ?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'small/check_yes.png" border="0" alt="" />'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php if (is_array ( $this->_tpl_vars['var']['values'][$this->_tpl_vars['param_name']] ) && count ( $this->_tpl_vars['var']['values'][$this->_tpl_vars['param_name']] ) > 16): ?><?php echo '</div>'; ?><?php endif; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['param_key']] == 'file_upload'): ?><?php echo ''; ?><?php $this->assign('file_info', $this->_tpl_vars['var']['values'][$this->_tpl_vars['param_name']]); ?><?php echo ''; ?><?php if (! empty ( $this->_tpl_vars['file_info'] ) && is_object ( $this->_tpl_vars['file_info'] ) && ! $this->_tpl_vars['file_info']->get('deleted_by')): ?><?php echo ''; ?><?php if (! $this->_tpl_vars['file_info']->get('not_exist')): ?><?php echo ''; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name_pattern']]['view_mode'] == 'thumbnail' && $this->_tpl_vars['file_info']->isImage()): ?><?php echo ''; ?><?php echo smarty_function_getimagesize(array('assign' => 'image_dimensions','image_path' => $this->_tpl_vars['file_info']->get('path')), $this);?><?php echo '<img src="'; ?><?php echo $_SERVER['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '=files&amp;files=viewfile&amp;viewfile='; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file_info']->get('id'))) ? $this->_run_mod_handler('encrypt', true, $_tmp, '_viewfile_') : smarty_modifier_encrypt($_tmp, '_viewfile_')))) ? $this->_run_mod_handler('escape', true, $_tmp, 'url') : smarty_modifier_escape($_tmp, 'url')); ?><?php echo ''; ?><?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?><?php echo '&amp;archive=1'; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name_pattern']]['thumb_width']): ?><?php echo '&amp;maxwidth='; ?><?php echo $this->_tpl_vars['var'][$this->_tpl_vars['name_pattern']]['thumb_width']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name_pattern']]['thumb_height']): ?><?php echo '&amp;maxheight='; ?><?php echo $this->_tpl_vars['var'][$this->_tpl_vars['name_pattern']]['thumb_height']; ?><?php echo ''; ?><?php endif; ?><?php echo '" onclick="showFullLBImage(\''; ?><?php echo $_SERVER['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['module']; ?><?php echo ''; ?><?php echo $this->_tpl_vars['controller_string']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['action_param']; ?><?php echo '=viewfile&amp;viewfile='; ?><?php echo $this->_tpl_vars['var']['model_id']; ?><?php echo '&amp;file='; ?><?php echo $this->_tpl_vars['file_info']->get('id'); ?><?php echo ''; ?><?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?><?php echo '&amp;archive=1'; ?><?php endif; ?><?php echo '\', \''; ?><?php echo $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']]; ?><?php echo '\', \''; ?><?php echo $this->_tpl_vars['image_dimensions']['width']; ?><?php echo '\', \''; ?><?php echo $this->_tpl_vars['image_dimensions']['height']; ?><?php echo '\')" />'; ?><?php else: ?><?php echo '<a href="'; ?><?php echo $_SERVER['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['module']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['action_param']; ?><?php echo '=viewfile&amp;viewfile='; ?><?php echo $this->_tpl_vars['var']['model_id']; ?><?php echo '&amp;file='; ?><?php echo $this->_tpl_vars['file_info']->get('id'); ?><?php echo ''; ?><?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?><?php echo '&amp;archive=1'; ?><?php endif; ?><?php echo '" target="_blank"><img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo ''; ?><?php echo $this->_tpl_vars['file_info']->getIconName(); ?><?php echo '.png" width="16" height="16" border="0" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" /></a><a href="'; ?><?php echo $_SERVER['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['module']; ?><?php echo ''; ?><?php echo $this->_tpl_vars['controller_string']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['action_param']; ?><?php echo '=getfile&amp;getfile='; ?><?php echo $this->_tpl_vars['var']['model_id']; ?><?php echo '&amp;file='; ?><?php echo $this->_tpl_vars['file_info']->get('id'); ?><?php echo '"><img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'download.png" width="16" height="16" border="0" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" /></a>'; ?><?php endif; ?><?php echo ''; ?><?php else: ?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo ''; ?><?php echo $this->_tpl_vars['file_info']->getIconName(); ?><?php echo '.png" width="16" height="16" border="0" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" class="pointer dimmed" /><img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'download.png" width="16" height="16" border="0" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" class="pointer dimmed" />'; ?><?php endif; ?><?php echo ''; ?><?php else: ?><?php echo '&nbsp;'; ?><?php endif; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['param_key']] == 'map'): ?><?php echo '<script type="text/javascript">params_map_'; ?><?php echo $this->_tpl_vars['name_pattern']; ?><?php echo ' = '; ?><?php echo smarty_function_json(array('encode' => ((is_array($_tmp=@$this->_tpl_vars['var'][$this->_tpl_vars['name_pattern']]['map_params'])) ? $this->_run_mod_handler('default', true, $_tmp, false) : smarty_modifier_default($_tmp, false))), $this);?><?php echo ';</script>'; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name_pattern']]['map_params']['type'] == 'inline'): ?><?php echo '&nbsp;'; ?><?php else: ?><?php echo '<input type="image" src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'map.png" width="16" height="16" alt="" class="image_map" border="0" onclick="showMap(this, params_map_'; ?><?php echo $this->_tpl_vars['name_pattern']; ?><?php echo ', \''; ?><?php echo $this->_tpl_vars['var']['labels'][$this->_tpl_vars['param_key']]; ?><?php echo '\', \'\'); return false;" />'; ?><?php endif; ?><?php echo ''; ?><?php else: ?><?php echo '&nbsp;'; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name_pattern']]['text_align'] == 'right' && $this->_tpl_vars['var']['width'][$this->_tpl_vars['name_pattern']]): ?><?php echo '</div>'; ?><?php endif; ?><?php echo ''; ?><?php if (( $this->_tpl_vars['var']['values'][$this->_tpl_vars['param_name']] || $this->_tpl_vars['var']['values'][$this->_tpl_vars['param_name']] === '0' ) && $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['param_key']]): ?><?php echo '&nbsp;'; ?><?php echo $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['param_key']]; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endforeach; else: ?><?php echo '&nbsp;'; ?><?php endif; unset($_from); ?><?php echo ''; ?>

                  </td>
                <?php endforeach; endif; unset($_from); ?>
              </tr>
            <?php endif; ?>
          <?php endforeach; endif; unset($_from); ?>
        <?php endif; ?>
      </table>
      </div>
    </td>
  </tr>
<?php endif; ?>