<?php /* Smarty version 2.6.33, created on 2025-05-21 13:03:02
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/input_dropdown.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/input_dropdown.html', 75, false),array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/input_dropdown.html', 89, false),array('modifier', 'strip_tags', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/input_dropdown.html', 110, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/input_dropdown.html', 110, false),array('modifier', 'mb_lower', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/input_dropdown.html', 120, false),array('function', 'help', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/input_dropdown.html', 77, false),)), $this); ?>
<?php if (! empty ( $this->_tpl_vars['index'] )): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['eq_indexes']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['index']; ?><?php echo ''; ?><?php elseif (! empty ( $this->_tpl_vars['empty_indexes'] )): ?><?php echo ''; ?><?php elseif (! empty ( $this->_tpl_vars['name_index'] )): ?><?php echo ''; ?><?php echo $this->_tpl_vars['name_index']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['index']-1; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('index_array', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>
<?php endif; ?>
<?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['standalone']): ?><?php echo ''; ?><?php if (preg_match ( '#^(\d+%|)$#' , $this->_tpl_vars['width'] )): ?><?php echo '100%'; ?><?php elseif (is_numeric ( $this->_tpl_vars['width'] )): ?><?php echo ''; ?><?php echo $this->_tpl_vars['width']; ?><?php echo 'px'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('width', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

<?php ob_start(); ?><?php if (! empty ( $this->_tpl_vars['height'] ) && ! preg_match ( '#%$#' , $this->_tpl_vars['height'] )): ?><?php echo $this->_tpl_vars['height']; ?>
px<?php elseif (! empty ( $this->_tpl_vars['height'] )): ?><?php echo $this->_tpl_vars['height']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('height', ob_get_contents());ob_end_clean(); ?>
<?php if (empty ( $this->_tpl_vars['standalone'] )): ?>
<tr<?php if (! empty ( $this->_tpl_vars['hidden'] )): ?> style="display: none"<?php endif; ?> class="nz-form-input">
    <td class="labelbox">
        <a name="error_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
"></a>
        <label for="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['readonly']): ?>_readonly<?php endif; ?>"<?php if ($this->_tpl_vars['messages']->getErrors($this->_tpl_vars['name'])): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['label'],'text_content' => $this->_tpl_vars['help']), $this);?>
</label>
  </td>

    <td<?php if ($this->_tpl_vars['required']): ?> class="required"><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?> class="unrequired">&nbsp;<?php endif; ?></td>

    <td nowrap="nowrap">
<?php endif; ?>

<?php $this->assign('hide_first_option', '1'); ?>
<?php if (( ! empty ( $this->_tpl_vars['options'] ) || ! empty ( $this->_tpl_vars['optgroups'] ) && count($this->_tpl_vars['optgroups']) ) && ! ( ! empty ( $this->_tpl_vars['skip_please_select'] ) || ! empty ( $this->_tpl_vars['options']['skip_please_select'] ) || ! empty ( $this->_tpl_vars['optgroups']['skip_please_select'] ) ) && ( empty ( $this->_tpl_vars['required'] ) || ( ! empty ( $this->_tpl_vars['really_required'] ) && $this->_tpl_vars['required'] && ! $this->_tpl_vars['hidden'] ) )): ?>
  <?php $this->assign('hide_first_option', '0'); ?>
  <?php if (! empty ( $this->_tpl_vars['really_required'] ) && $this->_tpl_vars['required']): ?>
    <?php if (! empty ( $this->_tpl_vars['options'] ) && ( count($this->_tpl_vars['options']) == 1 )): ?>
      <?php $this->assign('hide_first_option', '1'); ?>
    <?php elseif (! empty ( $this->_tpl_vars['optgroups'] ) && ( count($this->_tpl_vars['optgroups']) == 1 )): ?>
      <?php $_from = $this->_tpl_vars['optgroups']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['opt_check']):
?>
        <?php if (count($this->_tpl_vars['opt_check']) == 1): ?>
          <?php $this->assign('hide_first_option', '1'); ?>
        <?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
    <?php endif; ?>
  <?php endif; ?>
<?php endif; ?>
    <span class="nz-form-input-wrapper">
        <select 
      name="<?php echo $this->_tpl_vars['name']; ?>
<?php if ($this->_tpl_vars['readonly']): ?>_readonly<?php endif; ?><?php if (! empty ( $this->_tpl_vars['index'] )): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>"
      id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['readonly']): ?>_readonly<?php endif; ?><?php if (! empty ( $this->_tpl_vars['index'] )): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"
      class="selbox<?php if ($this->_tpl_vars['readonly']): ?> readonly<?php endif; ?><?php if (empty ( $this->_tpl_vars['options'] ) && empty ( $this->_tpl_vars['optgroups'] )): ?> missing_records<?php elseif (empty ( $this->_tpl_vars['hide_first_option'] ) && ( empty ( $this->_tpl_vars['undefined_strict_check'] ) && ! $this->_tpl_vars['value'] || $this->_tpl_vars['value'] === '' )): ?> undefined<?php endif; ?><?php if (! empty ( $this->_tpl_vars['custom_class'] )): ?> <?php echo $this->_tpl_vars['custom_class']; ?>
<?php endif; ?>"
      style="<?php if ($this->_tpl_vars['hidden']): ?>display: none;<?php endif; ?><?php if ($this->_tpl_vars['width']): ?>width: <?php echo $this->_tpl_vars['width']; ?>
;<?php endif; ?><?php if ($this->_tpl_vars['text_align']): ?> text-align: <?php echo $this->_tpl_vars['text_align']; ?>
;<?php endif; ?><?php if ($this->_tpl_vars['height']): ?>height: <?php echo $this->_tpl_vars['height']; ?>
;<?php endif; ?><?php if ($this->_tpl_vars['custom_style']): ?><?php echo $this->_tpl_vars['custom_style']; ?>
<?php endif; ?>"
      title="<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['label'])) ? $this->_run_mod_handler('strip_tags', true, $_tmp, false) : smarty_modifier_strip_tags($_tmp, false)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
      onclick="<?php if (! empty ( $this->_tpl_vars['js_methods']['onclick'] )): ?><?php echo $this->_tpl_vars['js_methods']['onclick']; ?>
<?php endif; ?>"
      <?php if ($this->_tpl_vars['disabled'] || $this->_tpl_vars['readonly']): ?> disabled="disabled"<?php endif; ?>
      onchange="toggleUndefined(this); <?php if ($this->_tpl_vars['sequences']): ?>if (this.value) {<?php echo $this->_tpl_vars['sequences']; ?>
}<?php elseif (! empty ( $this->_tpl_vars['on_change'] )): ?>change_options(<?php echo $this->_tpl_vars['on_change']; ?>
)<?php elseif (! empty ( $this->_tpl_vars['onchange'] )): ?><?php echo $this->_tpl_vars['onchange']; ?>
<?php elseif (! empty ( $this->_tpl_vars['js_methods']['onchange'] )): ?><?php echo $this->_tpl_vars['js_methods']['onchange']; ?>
<?php endif; ?>"
      <?php if (! empty ( $this->_tpl_vars['js_methods']['onkeypress'] )): ?>onkeypress="<?php echo $this->_tpl_vars['js_methods']['onkeypress']; ?>
"<?php endif; ?>>
            <?php if (empty ( $this->_tpl_vars['options'] ) && empty ( $this->_tpl_vars['optgroups'] )): ?>
      <option value="" class="missing_records"<?php if ($this->_tpl_vars['value'] === ""): ?> selected="selected"<?php endif; ?>><?php if ($this->_tpl_vars['no_select_records_label']): ?>[<?php echo ((is_array($_tmp=$this->_tpl_vars['no_select_records_label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]<?php else: ?>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_select_records'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]<?php endif; ?></option>
    <?php elseif (empty ( $this->_tpl_vars['hide_first_option'] )): ?>
      <option value="" class="undefined"<?php if ($this->_tpl_vars['value'] === ""): ?> selected="selected"<?php endif; ?>><?php if (((is_array($_tmp=@$this->_tpl_vars['first_option_label'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>[<?php echo ((is_array($_tmp=$this->_tpl_vars['first_option_label'])) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp)); ?>
]<?php else: ?>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]<?php endif; ?></option>
    <?php endif; ?>
    <?php if (! empty ( $this->_tpl_vars['optgroups'] )): ?>
      <?php $_from = $this->_tpl_vars['optgroups']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['optlabel'] => $this->_tpl_vars['optgroup']):
?>
        <?php if ($this->_tpl_vars['optlabel'] !== 'skip_please_select'): ?>
          <optgroup label="<?php if (! empty ( $this->_tpl_vars['optgroup_label_source'] ) == 'config'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['optlabel']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_tpl_vars['optlabel'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>">
            <?php $_from = $this->_tpl_vars['optgroup']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['option']):
?>
              <?php $this->assign('label', $this->_tpl_vars['option']['label']); ?>
              <?php if (( ! empty ( $this->_tpl_vars['show_inactive_options'] ) || ! isset ( $this->_tpl_vars['option']['active_option'] ) || $this->_tpl_vars['option']['active_option'] == 1 || $this->_tpl_vars['option']['option_value'] === $this->_tpl_vars['value'] )): ?>
                <option value="<?php echo ((is_array($_tmp=$this->_tpl_vars['option']['option_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="<?php if (((is_array($_tmp=@$this->_tpl_vars['option']['class_name'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?><?php echo $this->_tpl_vars['option']['class_name']; ?>
 <?php endif; ?><?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 && ! $this->_tpl_vars['disable_inactive_style'] )): ?>inactive_option<?php endif; ?>"<?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 && ! $this->_tpl_vars['disable_inactive_style'] )): ?> title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"<?php endif; ?><?php if ($this->_tpl_vars['option']['option_value'] === $this->_tpl_vars['value']): ?> selected="selected"<?php endif; ?><?php if (( ! empty ( $this->_tpl_vars['show_inactive_options'] ) && isset ( $this->_tpl_vars['option']['active_option'] ) && ! $this->_tpl_vars['option']['active_option'] && $this->_tpl_vars['option']['option_value'] !== $this->_tpl_vars['value'] ) && ! ( $this->_tpl_vars['action'] == 'getoptions' && in_array ( $_GET['getoptions'] , array ( 'search' , 'filter' ) ) )): ?> disabled="disabled"<?php endif; ?>><?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 && ! $this->_tpl_vars['disable_inactive_style'] )): ?>*&nbsp;<?php endif; ?><?php if (! empty ( $this->_tpl_vars['option_label_source'] ) == 'config'): ?><?php echo ((is_array($_tmp=@$this->_config[0]['vars'][$this->_tpl_vars['label']])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php else: ?><?php if (! empty ( $this->_tpl_vars['do_not_escape_labels'] )): ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['label'])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php else: ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['label'])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php endif; ?><?php endif; ?></option>
              <?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
          </optgroup>
        <?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
    <?php else: ?>
      <?php $_from = $this->_tpl_vars['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['idx'] => $this->_tpl_vars['option']):
?>
      <?php $this->assign('label', $this->_tpl_vars['option']['label']); ?>
        <?php if ($this->_tpl_vars['idx'] !== 'skip_please_select'): ?>
        <?php if (( ! empty ( $this->_tpl_vars['show_inactive_options'] ) || ! isset ( $this->_tpl_vars['option']['active_option'] ) || $this->_tpl_vars['option']['active_option'] == 1 || $this->_tpl_vars['option']['option_value'] === $this->_tpl_vars['value'] )): ?>
          <option value="<?php echo ((is_array($_tmp=$this->_tpl_vars['option']['option_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="<?php if (((is_array($_tmp=@$this->_tpl_vars['option']['class_name'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?><?php echo $this->_tpl_vars['option']['class_name']; ?>
 <?php endif; ?><?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 && ! $this->_tpl_vars['disable_inactive_style'] )): ?>inactive_option<?php endif; ?>"<?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 && ! $this->_tpl_vars['disable_inactive_style'] )): ?> title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"<?php endif; ?><?php if ($this->_tpl_vars['option']['option_value'] === $this->_tpl_vars['value']): ?> selected="selected"<?php endif; ?><?php if (( ! empty ( $this->_tpl_vars['show_inactive_options'] ) && isset ( $this->_tpl_vars['option']['active_option'] ) && ! $this->_tpl_vars['option']['active_option'] && $this->_tpl_vars['option']['option_value'] !== $this->_tpl_vars['value'] ) && ! ( $this->_tpl_vars['action'] == 'getoptions' && in_array ( $_GET['getoptions'] , array ( 'search' , 'filter' ) ) )): ?> disabled="disabled"<?php endif; ?>><?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 && ! $this->_tpl_vars['disable_inactive_style'] )): ?>*&nbsp;<?php endif; ?><?php if (! empty ( $this->_tpl_vars['option_label_source'] ) == 'config'): ?><?php echo ((is_array($_tmp=@$this->_config[0]['vars'][$this->_tpl_vars['label']])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php else: ?><?php if (! empty ( $this->_tpl_vars['do_not_escape_labels'] )): ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['label'])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php else: ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['label'])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php endif; ?><?php endif; ?></option>
        <?php endif; ?>
        <?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
    <?php endif; ?>
    </select>
    <?php if ($this->_tpl_vars['readonly']): ?>
      <input type="hidden" name="<?php echo $this->_tpl_vars['name']; ?>
<?php if (! empty ( $this->_tpl_vars['index'] )): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>" id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>" value="<?php echo $this->_tpl_vars['value']; ?>
" readonly="readonly" class="selbox_hidden readonly<?php if (! empty ( $this->_tpl_vars['custom_class'] )): ?> <?php echo $this->_tpl_vars['custom_class']; ?>
<?php endif; ?>" />
    <?php endif; ?>

        <?php if (empty ( $this->_tpl_vars['back_label'] ) && ! empty ( $this->_tpl_vars['var']['back_label'] )): ?>
      <?php $this->assign('back_label', $this->_tpl_vars['var']['back_label']); ?>
    <?php endif; ?>
    <?php if (empty ( $this->_tpl_vars['back_label_style'] ) && ! empty ( $this->_tpl_vars['var']['back_label_style'] )): ?>
      <?php $this->assign('back_label_style', $this->_tpl_vars['var']['back_label_style']); ?>
    <?php endif; ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_back_label.html", 'smarty_include_vars' => array('custom_id' => ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, '') : smarty_modifier_default($_tmp, '')),'name' => ((is_array($_tmp=@$this->_tpl_vars['name'])) ? $this->_run_mod_handler('default', true, $_tmp, '') : smarty_modifier_default($_tmp, '')),'back_label' => ((is_array($_tmp=@$this->_tpl_vars['back_label'])) ? $this->_run_mod_handler('default', true, $_tmp, '') : smarty_modifier_default($_tmp, '')),'back_label_style' => ((is_array($_tmp=@$this->_tpl_vars['back_label_style'])) ? $this->_run_mod_handler('default', true, $_tmp, '') : smarty_modifier_default($_tmp, '')))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </span>
<?php if (! $this->_tpl_vars['standalone']): ?>
  </td>
</tr>
<?php endif; ?>