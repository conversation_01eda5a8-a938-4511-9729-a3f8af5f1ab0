<?php /* Smarty version 2.6.33, created on 2025-06-20 16:46:57
         compiled from input_file_upload.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', 'input_file_upload.html', 37, false),array('modifier', 'escape', 'input_file_upload.html', 96, false),array('modifier', 'encrypt', 'input_file_upload.html', 109, false),array('modifier', 'strip_tags', 'input_file_upload.html', 181, false),array('function', 'help', 'input_file_upload.html', 80, false),array('function', 'getimagesize', 'input_file_upload.html', 120, false),)), $this); ?>
<?php if (((is_array($_tmp=@$this->_tpl_vars['index'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if (((is_array($_tmp=@$this->_tpl_vars['eq_indexes'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?><?php echo ''; ?><?php echo $this->_tpl_vars['index']; ?><?php echo ''; ?><?php elseif (((is_array($_tmp=@$this->_tpl_vars['empty_indexes'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?><?php echo ''; ?><?php elseif (((is_array($_tmp=@$this->_tpl_vars['name_index'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?><?php echo ''; ?><?php echo $this->_tpl_vars['name_index']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['index']-1; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('index_array', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>
<?php endif; ?>
<?php ob_start(); ?><?php echo ''; ?><?php if (((is_array($_tmp=@$this->_tpl_vars['standalone'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?><?php echo ''; ?><?php if (preg_match ( '#^(\d+%|)$#' , ((is_array($_tmp=@$this->_tpl_vars['width'])) ? $this->_run_mod_handler('default', true, $_tmp, '') : smarty_modifier_default($_tmp, '')) )): ?><?php echo '100%'; ?><?php elseif (is_numeric ( ((is_array($_tmp=@$this->_tpl_vars['width'])) ? $this->_run_mod_handler('default', true, $_tmp, '') : smarty_modifier_default($_tmp, '')) )): ?><?php echo ''; ?><?php echo $this->_tpl_vars['width']; ?><?php echo 'px'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('width', ob_get_contents());ob_end_clean(); ?>
<?php ob_start(); ?><?php echo ''; ?><?php if (((is_array($_tmp=@$this->_tpl_vars['height'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?><?php echo ''; ?><?php if (! preg_match ( '#%$#' , ((is_array($_tmp=@$this->_tpl_vars['height'])) ? $this->_run_mod_handler('default', true, $_tmp, '') : smarty_modifier_default($_tmp, '')) )): ?><?php echo ''; ?><?php echo $this->_tpl_vars['height']; ?><?php echo 'px'; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['height']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('height', ob_get_contents());ob_end_clean(); ?>
<?php if (! ((is_array($_tmp=@$this->_tpl_vars['view_mode'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?><?php $this->assign('view_mode', ((is_array($_tmp=@$this->_tpl_vars['var']['view_mode'])) ? $this->_run_mod_handler('default', true, $_tmp, '') : smarty_modifier_default($_tmp, ''))); ?><?php endif; ?>
<?php if (! ((is_array($_tmp=@$this->_tpl_vars['thumb_width'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?><?php $this->assign('thumb_width', ((is_array($_tmp=@$this->_tpl_vars['var']['thumb_width'])) ? $this->_run_mod_handler('default', true, $_tmp, '') : smarty_modifier_default($_tmp, ''))); ?><?php endif; ?>
<?php if (! ((is_array($_tmp=@$this->_tpl_vars['thumb_height'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?><?php $this->assign('thumb_height', ((is_array($_tmp=@$this->_tpl_vars['var']['thumb_height'])) ? $this->_run_mod_handler('default', true, $_tmp, '') : smarty_modifier_default($_tmp, ''))); ?><?php endif; ?>

<?php if (! ((is_array($_tmp=@$this->_tpl_vars['standalone'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>
<tr<?php if (((is_array($_tmp=@$this->_tpl_vars['hidden'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) || ( ((is_array($_tmp=@$this->_tpl_vars['width'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) === '0' )): ?> style="display: none"<?php endif; ?> class="nz-form-input">
    <td class="labelbox">
        <a name="error_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
"></a>
        <label for="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
" style="white-space: nowrap;"<?php if ($this->_tpl_vars['messages']->getErrors($this->_tpl_vars['name'])): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['label'],'text_content' => $this->_tpl_vars['help']), $this);?>
</label>
  </td>

    <td style="width: 10px;"<?php if (((is_array($_tmp=@$this->_tpl_vars['required'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?> class="required"><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?> class="unrequired">&nbsp;<?php endif; ?></td>

    <td nowrap="nowrap" valign="top">
<?php endif; ?>
<span class="nz-form-input-wrapper">
  <?php if (((is_array($_tmp=@$this->_tpl_vars['standalone'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?><div class="stretch_file_upload_field" style="width: <?php echo $this->_tpl_vars['width']; ?>
; float: left"></div><?php endif; ?>
  <?php if (! empty ( $this->_tpl_vars['value'] ) && is_object ( $this->_tpl_vars['value'] ) && ! $this->_tpl_vars['value']->get('deleted_by')): ?>
    <?php $this->assign('file_info', $this->_tpl_vars['value']); ?>
    <?php $this->assign('iconName', $this->_tpl_vars['file_info']->getIconName()); ?>
    <?php if ($this->_tpl_vars['file_info']->get('not_exist')): ?>
      <span class="material-icons nz-file-not-exist nz-tooltip nz-tooltip-dir-top"
            data-tooltip-content="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_file_not_exist'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
            title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_file_not_exist'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">warning</span>
      <span class="nz-input-controls nz--disabled"
        onclick="Nz.alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
','<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_view_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');"
        ><?php if ($this->_tpl_vars['iconName'] === 'attachments'): ?>
        <i class="material-icons nz-input-icon nz--disabled"><?php echo $this->_tpl_vars['theme']->getIconForAction('attachments'); ?>
</i>
        <?php else: ?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['iconName']; ?>
.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="nz-input-icon" />
      <?php endif; ?></span>
      <span class="nz-input-controls"
        ><i class="material-icons nz-input-icon nz--disabled" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
            onclick="Nz.alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
','<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_view_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');"><?php echo $this->_tpl_vars['theme']->getIconForAction('download'); ?>
</i></span>
      <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module'],'delete_file') && ( $this->_tpl_vars['currentUser']->get('id') == $this->_tpl_vars['file_info']->get('added_by') ) && ( $this->_tpl_vars['file_info']->get('model_id') == $this->_tpl_vars['var']['model_id'] )): ?>
        <span class="nz-input-controls"><a onclick="return confirmAction('delete_file', function(el) { deleteFileAsAdditionalVar(el, '<?php echo ((is_array($_tmp=$this->_tpl_vars['file_info']->get('id'))) ? $this->_run_mod_handler('encrypt', true, $_tmp, '_delete_file_') : smarty_modifier_encrypt($_tmp, '_delete_file_')); ?>
'); }, this);"
              class="nz-icon-button nz-input-icon"
              title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
          ><?php echo $this->_tpl_vars['theme']->getIconForAction('delete'); ?>
</a></span>
      <?php else: ?>
        <span class="nz-input-controls"><i class="material-icons nz-input-icon nz--disabled"
           title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
           onclick="Nz.alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
','<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_delete_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');"><?php echo $this->_tpl_vars['theme']->getIconForAction('delete'); ?>
</i></span>
      <?php endif; ?>
    <?php else: ?>
        <?php if ($this->_tpl_vars['view_mode'] == 'thumbnail' && $this->_tpl_vars['file_info']->isImage()): ?>
          <?php echo smarty_function_getimagesize(array('assign' => 'image_dimensions','image_path' => $this->_tpl_vars['file_info']->get('path')), $this);?>

          <div style="position: relative;" class="nz-thumbnail">
            <img src="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=files&amp;files=viewfile&amp;viewfile=<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file_info']->get('id'))) ? $this->_run_mod_handler('encrypt', true, $_tmp, '_viewfile_') : smarty_modifier_encrypt($_tmp, '_viewfile_')))) ? $this->_run_mod_handler('escape', true, $_tmp, 'url') : smarty_modifier_escape($_tmp, 'url')); ?>
<?php if ($this->_tpl_vars['thumb_width']): ?>&amp;maxwidth=<?php echo $this->_tpl_vars['thumb_width']; ?>
<?php endif; ?><?php if ($this->_tpl_vars['thumb_height']): ?>&amp;maxheight=<?php echo $this->_tpl_vars['thumb_height']; ?>
<?php endif; ?>"
                 onclick="showFullLBImage('<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=viewfile&amp;viewfile=<?php echo $this->_tpl_vars['var']['model_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['file_info']->get('id'); ?>
', '<?php echo $this->_tpl_vars['var']['label']; ?>
', '<?php echo $this->_tpl_vars['image_dimensions']['width']; ?>
', '<?php echo $this->_tpl_vars['image_dimensions']['height']; ?>
')"
                 <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module'],'delete_file') && ( $this->_tpl_vars['currentUser']->get('id') == $this->_tpl_vars['file_info']->get('added_by') ) && ! $this->_tpl_vars['file_info']->get('not_exist') && ( $this->_tpl_vars['file_info']->get('model_id') == $this->_tpl_vars['var']['model_id'] )): ?>
                   onmouseover="img_show_delete_icon('img_del_div_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if (((is_array($_tmp=@$this->_tpl_vars['index'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>')"
                   onmouseout="img_delete_icon_closetime('');"
                   onload="img_position_delete_icon('img_del_div_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if (((is_array($_tmp=@$this->_tpl_vars['index'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>');"
                 <?php endif; ?>
                 alt=""
              />
            <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module'],'delete_file') && ( $this->_tpl_vars['currentUser']->get('id') == $this->_tpl_vars['file_info']->get('added_by') ) && ! $this->_tpl_vars['file_info']->get('not_exist') && ( $this->_tpl_vars['file_info']->get('model_id') == $this->_tpl_vars['var']['model_id'] )): ?>
              <div style="position: absolute; top: 5px; right: 5px; visibility: hidden; display: none;"
                   id="img_del_div_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if (((is_array($_tmp=@$this->_tpl_vars['index'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"
                   onmouseover="img_delete_icon_cancelclosetime();"
                   onmouseout="img_delete_icon_closetime('img_del_div_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if (((is_array($_tmp=@$this->_tpl_vars['index'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>');">
                <span class="nz-input-controls"
                ><a href="javascript:void(0);" onclick="return confirmAction('delete_file', function(el) { deleteFileAsAdditionalVar(el, '<?php echo ((is_array($_tmp=$this->_tpl_vars['file_info']->get('id'))) ? $this->_run_mod_handler('encrypt', true, $_tmp, '_delete_file_') : smarty_modifier_encrypt($_tmp, '_delete_file_')); ?>
'); }, this);"
                    class="nz-icon-button nz-input-icon"
                    title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo $this->_tpl_vars['theme']->getIconForAction('delete'); ?>
</a></span>
              </div>
            <?php endif; ?>
          </div>
        <?php else: ?>
          <span class="nz-input-controls"><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=viewfile&amp;viewfile=<?php echo $this->_tpl_vars['var']['model_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['file_info']->get('id'); ?>
" target="_blank"
              title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
              class="nz-input-icon <?php echo ''; ?><?php if ($this->_tpl_vars['iconName'] === 'attachments'): ?><?php echo 'nz-icon-button">'; ?><?php echo $this->_tpl_vars['theme']->getIconForAction('attachments'); ?><?php echo '</a>'; ?><?php else: ?><?php echo '"><img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo ''; ?><?php echo $this->_tpl_vars['iconName']; ?><?php echo '.png" width="16" height="16" border="0" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" /></a>'; ?><?php endif; ?><?php echo ''; ?>

          </span>
          <span class="nz-input-controls"
          ><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=getfile&amp;getfile=<?php echo $this->_tpl_vars['var']['model_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['file_info']->get('id'); ?>
"
              download
              title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
              class="nz-icon-button nz-input-icon"
            ><?php echo $this->_tpl_vars['theme']->getIconForAction('download'); ?>
</a></span>
            <span class="nz-input-controls">
          <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module'],'delete_file') && ( $this->_tpl_vars['currentUser']->get('id') == $this->_tpl_vars['file_info']->get('added_by') ) && ! $this->_tpl_vars['file_info']->get('not_exist') && ( $this->_tpl_vars['file_info']->get('model_id') == $this->_tpl_vars['var']['model_id'] )): ?>
            <a href="javascript:void(0);" onclick="return confirmAction('delete_file', function(el) { deleteFileAsAdditionalVar(el, '<?php echo ((is_array($_tmp=$this->_tpl_vars['file_info']->get('id'))) ? $this->_run_mod_handler('encrypt', true, $_tmp, '_delete_file_') : smarty_modifier_encrypt($_tmp, '_delete_file_')); ?>
'); }, this);"
                title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                class="nz-icon-button nz-input-icon"
              ><?php echo $this->_tpl_vars['theme']->getIconForAction('delete'); ?>
</a>
          <?php else: ?>
            <i class="material-icons nz-input-icon nz--disabled"
               title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
               onclick="Nz.alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_delete_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');"><?php echo $this->_tpl_vars['theme']->getIconForAction('delete'); ?>
</i>
          <?php endif; ?>
            </span>
        <?php endif; ?>
      <?php endif; ?>
      <input type="hidden" name="dbid_<?php echo $this->_tpl_vars['name']; ?>
<?php if (((is_array($_tmp=@$this->_tpl_vars['index'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>" id="dbid_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['file_info']->get('id'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
    <?php endif; ?>

    
    <span class="nz-file-upload-indicator nz-input-controls"
          id="file_upld_trigger_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"
          style="<?php if (((is_array($_tmp=@$this->_tpl_vars['readonly'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) || ((is_array($_tmp=@$this->_tpl_vars['disabled'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) || ( ! empty ( $this->_tpl_vars['value'] ) && is_object ( $this->_tpl_vars['value'] ) && ! $this->_tpl_vars['value']->get('deleted_by') )): ?> display: none;<?php endif; ?>"
          title="<?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['label'])) ? $this->_run_mod_handler('strip_tags', true, $_tmp, false) : smarty_modifier_strip_tags($_tmp, false)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
"
          onclick="activateUploadFileBrowser('<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>');">
      <i class="material-icons nz-input-icon nz-file-upload__upload"><?php echo $this->_tpl_vars['theme']->getIconForAction('upload'); ?>
</i>
      <i class="material-icons nz-input-icon nz-file-upload__change"><?php echo $this->_tpl_vars['theme']->getIconForAction('file_present'); ?>
</i>
    </span>
    <input
      type="file"
      name="<?php echo $this->_tpl_vars['name']; ?>
<?php if ($this->_tpl_vars['index']): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>"
      id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"
      class="txtbox file_upload<?php if (((is_array($_tmp=@$this->_tpl_vars['readonly'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?> readonly<?php endif; ?><?php if (((is_array($_tmp=@$this->_tpl_vars['class_name'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?> <?php echo $this->_tpl_vars['class_name']; ?>
<?php endif; ?>"
      style="position: absolute; left: -100000px;<?php if ($this->_tpl_vars['width']): ?> width: <?php echo $this->_tpl_vars['width']; ?>
;<?php endif; ?><?php if ($this->_tpl_vars['height']): ?> height: <?php echo $this->_tpl_vars['height']; ?>
;<?php endif; ?>"
      title="<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['label'])) ? $this->_run_mod_handler('strip_tags', true, $_tmp, false) : smarty_modifier_strip_tags($_tmp, false)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
      onchange="changeTriggerIcon(this);<?php if (((is_array($_tmp=@$this->_tpl_vars['onchange'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?><?php echo $this->_tpl_vars['onchange']; ?>
<?php endif; ?>"
      <?php if (((is_array($_tmp=@$this->_tpl_vars['onclick'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?> onclick="<?php echo $this->_tpl_vars['onclick']; ?>
"<?php endif; ?>
      <?php if (((is_array($_tmp=@$this->_tpl_vars['accept'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?> accept="<?php echo $this->_tpl_vars['accept']; ?>
"<?php endif; ?>
      <?php if (((is_array($_tmp=@$this->_tpl_vars['readonly'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?> readonly="readonly"<?php endif; ?>
      <?php if (((is_array($_tmp=@$this->_tpl_vars['disabled'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) || ( ! empty ( $this->_tpl_vars['value'] ) && is_object ( $this->_tpl_vars['value'] ) && ! $this->_tpl_vars['value']->get('deleted_by') && $this->_tpl_vars['source'] != 'gt2' )): ?> disabled="disabled"<?php endif; ?> />
    <input type="hidden" name="deleteid_<?php echo $this->_tpl_vars['name']; ?>
<?php if (((is_array($_tmp=@$this->_tpl_vars['index'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>" id="deleteid_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>" value="<?php if ($this->_tpl_vars['deleteid'] > 0): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['deleteid'])) ? $this->_run_mod_handler('encrypt', true, $_tmp, '_delete_file_') : smarty_modifier_encrypt($_tmp, '_delete_file_')); ?>
<?php endif; ?>" />

        <?php if (! ((is_array($_tmp=@$this->_tpl_vars['back_label'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) && ! empty ( $this->_tpl_vars['var']['back_label'] )): ?>
      <?php $this->assign('back_label', $this->_tpl_vars['var']['back_label']); ?>
    <?php endif; ?>
    <?php if (! ((is_array($_tmp=@$this->_tpl_vars['back_label_style'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) && ! empty ( $this->_tpl_vars['var']['back_label_style'] )): ?>
      <?php $this->assign('back_label_style', $this->_tpl_vars['var']['back_label_style']); ?>
    <?php endif; ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_back_label.html", 'smarty_include_vars' => array('custom_id' => ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)),'name' => $this->_tpl_vars['name'],'back_label' => ((is_array($_tmp=@$this->_tpl_vars['back_label'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)),'back_label_style' => ((is_array($_tmp=@$this->_tpl_vars['back_label_style'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
</span>
<?php if (! ((is_array($_tmp=@$this->_tpl_vars['standalone'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>
  </td>
</tr>
<?php endif; ?>