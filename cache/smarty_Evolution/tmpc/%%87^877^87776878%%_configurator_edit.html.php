<?php /* Smarty version 2.6.33, created on 2025-06-20 16:46:57
         compiled from _configurator_edit.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'help', '_configurator_edit.html', 261, false),array('function', 'csv2array', '_configurator_edit.html', 752, false),array('modifier', 'escape', '_configurator_edit.html', 758, false),)), $this); ?>
<table id="var_config_<?php echo $this->_tpl_vars['var']['config']; ?>
" class="t_grouping_table<?php if ($this->_tpl_vars['var']['borderless']): ?> t_borderless<?php endif; ?><?php if ($this->_tpl_vars['var']['custom_class']): ?> <?php echo $this->_tpl_vars['var']['custom_class']; ?>
<?php endif; ?>"<?php if ($this->_tpl_vars['var']['t_width']): ?> width="<?php echo $this->_tpl_vars['var']['t_width']; ?>
"<?php endif; ?>>
  <?php if ($this->_tpl_vars['var']['rows'] == 1): ?>
    <tr>
    <?php $_from = $this->_tpl_vars['var']['labels']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['cheader'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['cheader']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['idx'] => $this->_tpl_vars['col_name']):
        $this->_foreach['cheader']['iteration']++;
?>
      <th colspan="2"<?php if ($this->_tpl_vars['var']['hidden'][$this->_tpl_vars['idx']]): ?> style="display: none;"<?php endif; ?>><?php echo $this->_tpl_vars['col_name']; ?>
</th>
    <?php endforeach; endif; unset($_from); ?>
    </tr>
    <tr>
      <?php $_from = $this->_tpl_vars['var']['names']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['name']):
?>
        <td style="border-right: none!important;<?php if ($this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']]): ?> display: none;<?php endif; ?>" class="<?php if (! $this->_tpl_vars['var']['required'][$this->_tpl_vars['key']]): ?>un<?php endif; ?>required"><?php if ($this->_tpl_vars['var']['required'][$this->_tpl_vars['key']]): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php endif; ?></td>
        <td style="border-left: none!important;<?php if ($this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']]): ?> display: none;<?php endif; ?>"<?php if ($this->_tpl_vars['var']['width'][$this->_tpl_vars['name']]): ?> width="<?php echo $this->_tpl_vars['var']['width'][$this->_tpl_vars['name']]; ?>
"<?php endif; ?> nowrap="nowrap">
          <?php echo ''; ?><?php if ($this->_tpl_vars['configData']['params']): ?><?php echo ''; ?><?php $this->assign('var_value', $this->_tpl_vars['configData']['params'][$this->_tpl_vars['name']]); ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php $this->assign('var_value', $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']]); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['calculate'] > 0): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['calculate_buttons']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['calculate_buttons']; ?><?php echo ', '; ?><?php endif; ?><?php echo ''; ?><?php echo $this->_tpl_vars['name']; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('calculate_buttons', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>

         <?php if (! empty ( $this->_tpl_vars['var']['width'][$this->_tpl_vars['name']] )): ?>
           <?php $this->assign('var_width', $this->_tpl_vars['var']['width'][$this->_tpl_vars['name']]-10); ?>
         <?php else: ?>
           <?php $this->assign('var_width', ''); ?>
         <?php endif; ?>
          <?php if ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'text'): ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_text.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'value' => $this->_tpl_vars['var_value'],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label_style'],'calculate' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['calculate'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['key']],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['key']],'width' => $this->_tpl_vars['var_width'],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']],'origin' => 'config','height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['key']],'restrict' => $this->_tpl_vars['var']['js_filters'][$this->_tpl_vars['key']],'show_placeholder' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['show_placeholder'],'text_align' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['text_align'],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'textarea'): ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_textarea.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'value' => $this->_tpl_vars['var_value'],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label_style'],'calculate' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['calculate'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['key']],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['key']],'width' => $this->_tpl_vars['var_width'],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']],'origin' => 'config','height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['key']],'restrict' => $this->_tpl_vars['var']['js_filters'][$this->_tpl_vars['key']],'show_placeholder' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['show_placeholder'],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'date'): ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_date.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'value' => $this->_tpl_vars['var_value'],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label_style'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['key']],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['key']],'width' => $this->_tpl_vars['var_width'],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']],'origin' => 'config','height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['key']],'restrict' => $this->_tpl_vars['var']['js_filters'][$this->_tpl_vars['key']],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'datetime'): ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_datetime.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'value' => $this->_tpl_vars['var_value'],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label_style'],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['key']],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['key']],'width' => $this->_tpl_vars['var_width'],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']],'origin' => 'config','height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['key']],'restrict' => $this->_tpl_vars['var']['js_filters'][$this->_tpl_vars['key']],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'time'): ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_time.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'value' => $this->_tpl_vars['var_value'],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label_style'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['key']],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['key']],'width' => $this->_tpl_vars['var_width'],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']],'origin' => 'config','height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['key']],'restrict' => $this->_tpl_vars['var']['js_filters'][$this->_tpl_vars['key']],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'dropdown'): ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_dropdown.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'value' => $this->_tpl_vars['var_value'],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label_style'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['key']],'options' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options'],'optgroups' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['optgroups'],'on_change' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['on_change'],'sequences' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['sequences'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['key']],'width' => $this->_tpl_vars['var_width'],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']],'really_required' => $this->_tpl_vars['var']['required'][$this->_tpl_vars['key']],'required' => $this->_tpl_vars['var']['required'][$this->_tpl_vars['key']],'origin' => 'config','js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['key']],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']],'text_align' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['text_align'],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'map'): ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_map.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'value' => $this->_tpl_vars['var_value'],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label_style'],'map_params' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['map_params'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['key']],'width' => $this->_tpl_vars['var_width'],'origin' => 'config','height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'radio'): ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_radio.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'value' => $this->_tpl_vars['var_value'],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label_style'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['key']],'options' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options'],'optgroups' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['optgroups'],'on_change' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['on_change'],'sequences' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['sequences'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['key']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['key']],'options_align' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options_align'],'origin' => 'config','height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'checkbox_group'): ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_checkbox_group.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'value' => $this->_tpl_vars['var_value'],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label_style'],'options' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['key']],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['key']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']],'origin' => 'config','options_align' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options_align'],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'autocompleter'): ?>
            <?php $this->assign('id_var_name', $this->_tpl_vars['var'][$this->_tpl_vars['name']]['autocomplete']['id_var']); ?>
            <?php ob_start(); ?><?php if ($this->_tpl_vars['configData']['params']): ?><?php echo $this->_tpl_vars['configData']['params'][$this->_tpl_vars['id_var_name']]; ?>
<?php else: ?><?php echo $this->_tpl_vars['var'][$this->_tpl_vars['name']]['value_id']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_value_id', ob_get_contents());ob_end_clean(); ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_autocompleter.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'value' => $this->_tpl_vars['var_value'],'value_id' => $this->_tpl_vars['var_value_id'],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label_style'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['key']],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['key']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']],'autocomplete' => $this->_tpl_vars['var']['autocomplete'][$this->_tpl_vars['name']],'width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['key']],'restrict' => $this->_tpl_vars['var']['js_filters'][$this->_tpl_vars['key']],'show_placeholder' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['show_placeholder'],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'file_upload'): ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_file_upload.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'value' => $this->_tpl_vars['var_value'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['key']],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label_style'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['key']],'width' => $this->_tpl_vars['var_width'],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']],'origin' => 'config','height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']],'view_mode' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['view_mode'],'thumb_width' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_width'],'thumb_height' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_height'],'deleteid' => $this->_tpl_vars['var']['deleteids'][$this->_tpl_vars['name']],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php else: ?>
          <td>&nbsp;</td>
          <?php endif; ?>
        </td>
      <?php endforeach; endif; unset($_from); ?>
    </tr>

  <?php else: ?>

    <?php if ($this->_tpl_vars['var']['columns'] && count ( $this->_tpl_vars['var']['columns'] ) > 0): ?>
    <tr>
    <th colspan="3"></th>
    <?php $_from = $this->_tpl_vars['var']['columns']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['cheader'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['cheader']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['col_name'] => $this->_tpl_vars['column']):
        $this->_foreach['cheader']['iteration']++;
?>
      <th colspan="2"><?php echo $this->_tpl_vars['column']; ?>
</th>
    <?php endforeach; endif; unset($_from); ?>
    </tr>
    <?php endif; ?>
    <?php $_from = $this->_tpl_vars['var']['names']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['configurator'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['configurator']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['name']):
        $this->_foreach['configurator']['iteration']++;
?>
      <?php if (! preg_match ( '/__[a-z]+$/' , $this->_tpl_vars['name'] )): ?>
        <?php ob_start(); ?><?php if ($this->_tpl_vars['var']['help'][$this->_tpl_vars['key']]): ?><?php echo $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']]; ?>
<?php else: ?><?php echo $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']]; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?>
        <tr<?php if ($this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']]): ?> style="display: none"<?php endif; ?>>
          <td style="border-right: 0px!important;"><a name="error_<?php echo $this->_tpl_vars['var']['names'][$this->_tpl_vars['key']]; ?>
"></a><label for="<?php echo $this->_tpl_vars['var']['names'][$this->_tpl_vars['key']]; ?>
"<?php if ($this->_tpl_vars['messages']->getErrors($this->_tpl_vars['var']['names'][$this->_tpl_vars['key']])): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'text_content' => $this->_tpl_vars['info']), $this);?>
</label></td>
          <td style="border-right: 0px!important;border-left: 0px!important;" class="<?php if (! $this->_tpl_vars['var']['required'][$this->_tpl_vars['key']]): ?>un<?php endif; ?>required"><?php if ($this->_tpl_vars['var']['required'][$this->_tpl_vars['key']]): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php endif; ?></td>
          <td style="border-left: 0px!important;"<?php if ($this->_tpl_vars['var']['width'][$this->_tpl_vars['name']]): ?> width="<?php echo $this->_tpl_vars['var']['width'][$this->_tpl_vars['name']]; ?>
"<?php endif; ?>>
            <?php echo ''; ?><?php if ($this->_tpl_vars['configData']['params']): ?><?php echo ''; ?><?php $this->assign('var_value', $this->_tpl_vars['configData']['params'][$this->_tpl_vars['name']]); ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php $this->assign('var_value', $this->_tpl_vars['var']['values'][$this->_tpl_vars['name']]); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['calculate'] > 0): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['calculate_buttons']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['calculate_buttons']; ?><?php echo ', '; ?><?php endif; ?><?php echo ''; ?><?php echo $this->_tpl_vars['var'][$this->_tpl_vars['name']]['id']; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('calculate_buttons', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>

            <?php if (! empty ( $this->_tpl_vars['var']['width'][$this->_tpl_vars['name']] )): ?>
              <?php $this->assign('var_width', $this->_tpl_vars['var']['width'][$this->_tpl_vars['name']]-10); ?>
            <?php else: ?>
              <?php $this->assign('var_width', ''); ?>
            <?php endif; ?>
            <?php if ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'text'): ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_text.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'value' => $this->_tpl_vars['var_value'],'calculate' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['calculate'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['key']],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label_style'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['key']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']],'origin' => 'config','width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['key']],'restrict' => $this->_tpl_vars['var']['js_filters'][$this->_tpl_vars['key']],'show_placeholder' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['show_placeholder'],'text_align' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['text_align'],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'textarea'): ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_textarea.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'value' => $this->_tpl_vars['var_value'],'calculate' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['calculate'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['key']],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label_style'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['key']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']],'origin' => 'config','width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['key']],'restrict' => $this->_tpl_vars['var']['js_filters'][$this->_tpl_vars['key']],'show_placeholder' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['show_placeholder'],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'date'): ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_date.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'value' => $this->_tpl_vars['var_value'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['key']],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label_style'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['key']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']],'origin' => 'config','width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['key']],'restrict' => $this->_tpl_vars['var']['js_filters'][$this->_tpl_vars['key']],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'datetime'): ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_datetime.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'value' => $this->_tpl_vars['var_value'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['key']],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label_style'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['key']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']],'origin' => 'config','width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['key']],'restrict' => $this->_tpl_vars['var']['js_filters'][$this->_tpl_vars['key']],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'time'): ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_time.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'value' => $this->_tpl_vars['var_value'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['key']],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label_style'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['key']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']],'origin' => 'config','width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['key']],'restrict' => $this->_tpl_vars['var']['js_filters'][$this->_tpl_vars['key']],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'dropdown'): ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_dropdown.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'value' => $this->_tpl_vars['var_value'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['key']],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label_style'],'options' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options'],'optgroups' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['optgroups'],'on_change' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['on_change'],'sequences' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['sequences'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['key']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']],'really_required' => $this->_tpl_vars['var']['required'][$this->_tpl_vars['key']],'required' => $this->_tpl_vars['var']['required'][$this->_tpl_vars['key']],'origin' => 'config','width' => $this->_tpl_vars['var_width'],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['key']],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']],'text_align' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['text_align'],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'radio'): ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_radio.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'value' => $this->_tpl_vars['var_value'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['key']],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label_style'],'options' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options'],'optgroups' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['optgroups'],'on_change' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['on_change'],'sequences' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['sequences'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['key']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']],'origin' => 'config','js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['key']],'options_align' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options_align'],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'checkbox_group'): ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_checkbox_group.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'value' => $this->_tpl_vars['var_value'],'options' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['key']],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label_style'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['key']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']],'origin' => 'config','options_align' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options_align'],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'autocompleter'): ?>
              <?php $this->assign('id_var_name', $this->_tpl_vars['var'][$this->_tpl_vars['name']]['autocomplete']['id_var']); ?>
              <?php ob_start(); ?><?php if ($this->_tpl_vars['configData']['params']): ?><?php echo $this->_tpl_vars['configData']['params'][$this->_tpl_vars['id_var_name']]; ?>
<?php else: ?><?php echo $this->_tpl_vars['var'][$this->_tpl_vars['name']]['value_id']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_value_id', ob_get_contents());ob_end_clean(); ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_autocompleter.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'value' => $this->_tpl_vars['var_value'],'value_id' => $this->_tpl_vars['var_value_id'],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label_style'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['key']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']],'autocomplete' => $this->_tpl_vars['var']['autocomplete'][$this->_tpl_vars['name']],'width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['key']],'restrict' => $this->_tpl_vars['var']['js_filters'][$this->_tpl_vars['key']],'show_placeholder' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['show_placeholder'],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'file_upload'): ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_file_upload.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'value' => $this->_tpl_vars['var_value'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['key']],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label_style'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['key']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']],'origin' => 'config','width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']],'view_mode' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['view_mode'],'thumb_width' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_width'],'thumb_height' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_height'],'deleteid' => $this->_tpl_vars['var']['deleteids'][$this->_tpl_vars['name']],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'map'): ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_map.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'value' => $this->_tpl_vars['var_value'],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'map_params' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['map_params'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['key']],'width' => $this->_tpl_vars['var_width'],'origin' => 'config','height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php endif; ?>
          </td>
          <?php $_from = $this->_tpl_vars['var']['columns']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['col'] => $this->_tpl_vars['c']):
?>
            <?php ob_start(); ?><?php echo $this->_tpl_vars['name']; ?>
__<?php echo $this->_tpl_vars['col']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('name_pattern', ob_get_contents());ob_end_clean(); ?>
            <?php $this->assign('skey', false); ?>
            <?php $this->assign('sname', false); ?>
            <?php $_from = $this->_tpl_vars['var']['names']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['param_key'] => $this->_tpl_vars['param_name']):
?>
              <?php if (preg_match ( '/(__[a-z]+)$/' , $this->_tpl_vars['param_name'] , $this->_tpl_vars['mtch'][0] ) && $this->_tpl_vars['name_pattern'] == $this->_tpl_vars['param_name']): ?>
                <?php echo ''; ?><?php if ($this->_tpl_vars['configData']['params']): ?><?php echo ''; ?><?php $this->assign('var_value', $this->_tpl_vars['configData']['params'][$this->_tpl_vars['param_name']]); ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php $this->assign('var_value', $this->_tpl_vars['var']['values'][$this->_tpl_vars['param_name']]); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['param_name']]['calculate'] > 0): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['calculate_buttons']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['calculate_buttons']; ?><?php echo ', '; ?><?php endif; ?><?php echo ''; ?><?php echo $this->_tpl_vars['var'][$this->_tpl_vars['param_name']]['id']; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('calculate_buttons', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>

                <?php if (! empty ( $this->_tpl_vars['var']['width'][$this->_tpl_vars['name_pattern']] )): ?>
                  <?php $this->assign('var_width', $this->_tpl_vars['var']['width'][$this->_tpl_vars['name_pattern']]-10); ?>
                <?php else: ?>
                  <?php $this->assign('var_width', ''); ?>
                <?php endif; ?>
                <?php $this->assign('skey', $this->_tpl_vars['param_key']); ?>
                <?php $this->assign('sname', $this->_tpl_vars['param_name']); ?>
              <?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
            <?php if ($this->_tpl_vars['skey'] && $this->_tpl_vars['sname']): ?>
              <td style="border-right: none!important;" class="<?php if (! $this->_tpl_vars['var']['required'][$this->_tpl_vars['skey']]): ?>un<?php endif; ?>required"><?php if ($this->_tpl_vars['var']['required'][$this->_tpl_vars['skey']]): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php endif; ?></td>
              <td style="border-left: none!important;"<?php if ($this->_tpl_vars['var']['width'][$this->_tpl_vars['name_pattern']]): ?> width="<?php echo $this->_tpl_vars['var']['width'][$this->_tpl_vars['name_pattern']]; ?>
"<?php endif; ?> nowrap="nowrap">
                <a name="error_<?php echo $this->_tpl_vars['var']['names'][$this->_tpl_vars['skey']]; ?>
"></a>
                <?php if ($this->_tpl_vars['var']['types'][$this->_tpl_vars['skey']] == 'text'): ?>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_text.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['sname'],'value' => $this->_tpl_vars['var_value'],'calculate' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['calculate'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['skey']],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['skey']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['skey']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['skey']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['back_label_style'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['skey']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['skey']],'origin' => 'config','width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['skey']],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['skey']],'restrict' => $this->_tpl_vars['var']['js_filters'][$this->_tpl_vars['skey']],'show_placeholder' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['show_placeholder'],'text_align' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['text_align'],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['skey']] == 'textarea'): ?>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_textarea.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['sname'],'value' => $this->_tpl_vars['var_value'],'calculate' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['calculate'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['skey']],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['skey']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['skey']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['skey']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['back_label_style'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['skey']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['skey']],'origin' => 'config','width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['skey']],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['skey']],'restrict' => $this->_tpl_vars['var']['js_filters'][$this->_tpl_vars['skey']],'show_placeholder' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['show_placeholder'],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['skey']] == 'date'): ?>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_date.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['sname'],'value' => $this->_tpl_vars['var_value'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['skey']],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['skey']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['skey']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['skey']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['back_label_style'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['skey']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['skey']],'origin' => 'config','width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['skey']],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['skey']],'restrict' => $this->_tpl_vars['var']['js_filters'][$this->_tpl_vars['skey']],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['skey']] == 'datetime'): ?>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_datetime.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['sname'],'value' => $this->_tpl_vars['var_value'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['skey']],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['skey']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['skey']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['skey']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['back_label_style'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['skey']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['skey']],'origin' => 'config','width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['skey']],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['skey']],'restrict' => $this->_tpl_vars['var']['js_filters'][$this->_tpl_vars['skey']],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['skey']] == 'time'): ?>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_time.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['sname'],'value' => $this->_tpl_vars['var_value'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['skey']],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['skey']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['skey']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['skey']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['back_label_style'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['skey']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['skey']],'origin' => 'config','width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['skey']],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['skey']],'restrict' => $this->_tpl_vars['var']['js_filters'][$this->_tpl_vars['skey']],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['skey']] == 'dropdown'): ?>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_dropdown.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['sname'],'value' => $this->_tpl_vars['var_value'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['skey']],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['skey']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['skey']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['skey']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['back_label_style'],'options' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['options'],'optgroups' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['optgroups'],'on_change' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['on_change'],'sequences' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['sequences'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['skey']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['skey']],'really_required' => $this->_tpl_vars['var']['required'][$this->_tpl_vars['skey']],'required' => $this->_tpl_vars['var']['required'][$this->_tpl_vars['skey']],'origin' => 'config','width' => $this->_tpl_vars['var_width'],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['skey']],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['skey']],'text_align' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['text_align'],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['skey']] == 'radio'): ?>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_radio.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['sname'],'value' => $this->_tpl_vars['var_value'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['skey']],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['skey']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['skey']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['skey']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['back_label_style'],'options' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['options'],'optgroups' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['optgroups'],'on_change' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['on_change'],'sequences' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['sequences'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['skey']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['skey']],'origin' => 'config','js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['skey']],'options_align' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['options_align'],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['skey']],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['skey']] == 'checkbox_group'): ?>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_checkbox_group.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['sname'],'value' => $this->_tpl_vars['var_value'],'options' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['options'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['skey']],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['skey']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['skey']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['skey']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['back_label_style'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['skey']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['skey']],'origin' => 'config','options_align' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['options_align'],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['skey']],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['skey']] == 'autocompleter'): ?>
                  <?php $this->assign('id_var_name', $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['autocomplete']['id_var']); ?>
                  <?php ob_start(); ?><?php if ($this->_tpl_vars['configData']['params']): ?><?php echo $this->_tpl_vars['configData']['params'][$this->_tpl_vars['id_var_name']]; ?>
<?php else: ?><?php echo $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['value_id']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_value_id', ob_get_contents());ob_end_clean(); ?>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_autocompleter.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['sname'],'value' => $this->_tpl_vars['var_value'],'value_id' => $this->_tpl_vars['var_value_id'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['skey']],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['skey']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['skey']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['skey']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['back_label_style'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['skey']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['skey']],'autocomplete' => $this->_tpl_vars['var']['autocomplete'][$this->_tpl_vars['sname']],'width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['skey']],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['skey']],'restrict' => $this->_tpl_vars['var']['js_filters'][$this->_tpl_vars['skey']],'show_placeholder' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['show_placeholder'],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['skey']] == 'file_upload'): ?>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_file_upload.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['sname'],'value' => $this->_tpl_vars['var_value'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['skey']],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['skey']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['skey']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['skey']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['back_label_style'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['skey']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['skey']],'origin' => 'config','width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['skey']],'view_mode' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['view_mode'],'thumb_width' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['thumb_width'],'thumb_height' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['thumb_height'],'deleteid' => $this->_tpl_vars['var']['deleteids'][$this->_tpl_vars['sname']],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['skey']] == 'map'): ?>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_map.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['sname'],'value' => $this->_tpl_vars['var_value'],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['skey']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['skey']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['skey']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['back_label_style'],'map_params' => $this->_tpl_vars['var'][$this->_tpl_vars['sname']]['map_params'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['skey']],'width' => $this->_tpl_vars['var_width'],'origin' => 'config','height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['skey']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                <?php endif; ?>
              </td>
            <?php else: ?>
              <td style="border-right: none!important;" class="unrequired">&nbsp;</td>
              <td style="border-left: none!important;">&nbsp;</td>
            <?php endif; ?>
          <?php endforeach; endif; unset($_from); ?>
        </tr>
      <?php endif; ?>
    <?php endforeach; endif; unset($_from); ?>
  <?php endif; ?>
</table>
<?php if ($this->_tpl_vars['var']['source']): ?>
  <br clear="all" />
  <div>
    <?php echo ''; ?><?php echo ''; ?><?php if ($this->_tpl_vars['calculate_buttons']): ?><?php echo ''; ?><?php echo smarty_function_csv2array(array('assign' => 'calculate_buttons_array','csvlist' => $this->_tpl_vars['calculate_buttons']), $this);?><?php echo ''; ?><?php $_from = $this->_tpl_vars['calculate_buttons_array']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['calc_button']):
?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php echo $this->_tpl_vars['execute_calculation']; ?><?php echo 'calc(this, '; ?><?php echo $this->_tpl_vars['calc_button']; ?><?php echo ');'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('execute_calculation', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>

    <button type="button" class="button" name="addRow_<?php echo $this->_tpl_vars['var']['config']; ?>
" id="addRow_<?php echo $this->_tpl_vars['var']['config']; ?>
" onclick="<?php echo $this->_tpl_vars['execute_calculation']; ?>
<?php if ($this->_tpl_vars['config_with_file_upload'] || $this->_tpl_vars['var']['config_with_file_upload']): ?>uploadViaAjax(this.form, {source:'franky', module:'<?php echo $this->_tpl_vars['module']; ?>
', config_id:0, model_id:<?php echo $this->_tpl_vars['model_id']; ?>
, div_id:'table_franky_<?php echo $this->_tpl_vars['var']['config']; ?>
', config_num:<?php echo $this->_tpl_vars['var']['config']; ?>
})<?php else: ?>saveFranky(this.form, '<?php echo $this->_tpl_vars['module']; ?>
', 0, <?php echo $this->_tpl_vars['model_id']; ?>
, 'table_franky_<?php echo $this->_tpl_vars['var']['config']; ?>
',<?php echo $this->_tpl_vars['var']['config']; ?>
)<?php endif; ?>;<?php if ($this->_tpl_vars['var']['reset_form']): ?>editFranky(<?php echo $this->_tpl_vars['model_id']; ?>
, '<?php echo $this->_tpl_vars['module']; ?>
', 0, <?php echo $this->_tpl_vars['var']['config']; ?>
);<?php endif; ?>"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['add'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
    <?php if ($this->_tpl_vars['configData']['model_id'] > 0): ?>
      <button type="button" class="button" name="editRow_<?php echo $this->_tpl_vars['var']['config']; ?>
" id="editRow_<?php echo $this->_tpl_vars['var']['config']; ?>
" onclick="<?php echo $this->_tpl_vars['execute_calculation']; ?>
<?php if ($this->_tpl_vars['config_with_file_upload'] || $this->_tpl_vars['var']['config_with_file_upload']): ?>uploadViaAjax(this.form, {source:'franky', module:'<?php echo $this->_tpl_vars['module']; ?>
', config_id:0, model_id:<?php echo $this->_tpl_vars['model_id']; ?>
, div_id:'table_franky_<?php echo $this->_tpl_vars['var']['config']; ?>
', config_num:<?php echo $this->_tpl_vars['var']['config']; ?>
})<?php else: ?>saveFranky(this.form,'<?php echo $this->_tpl_vars['module']; ?>
',<?php echo $this->_tpl_vars['configData']['id']; ?>
, <?php echo $this->_tpl_vars['model_id']; ?>
, 'table_franky_<?php echo $this->_tpl_vars['var']['config']; ?>
',<?php echo $this->_tpl_vars['var']['config']; ?>
)<?php endif; ?>"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
      <input type="hidden" name="franky_row_id_<?php echo $this->_tpl_vars['var']['config']; ?>
" id="franky_row_id_<?php echo $this->_tpl_vars['var']['config']; ?>
" value="<?php echo $this->_tpl_vars['configData']['id']; ?>
" />
    <?php endif; ?>
  </div>
<?php endif; ?>