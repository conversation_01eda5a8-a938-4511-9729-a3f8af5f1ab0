<?php /* Smarty version 2.6.33, created on 2025-06-20 16:46:57
         compiled from /var/www/Nzoom-Hella/_libs/modules/customers/view/templates/_contact_data.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'counter', '/var/www/Nzoom-Hella/_libs/modules/customers/view/templates/_contact_data.html', 1, false),array('function', 'help', '/var/www/Nzoom-Hella/_libs/modules/customers/view/templates/_contact_data.html', 61, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/customers/view/templates/_contact_data.html', 13, false),array('modifier', 'replace', '/var/www/Nzoom-Hella/_libs/modules/customers/view/templates/_contact_data.html', 28, false),)), $this); ?>
<?php echo smarty_function_counter(array('name' => 'k','start' => 0,'print' => false), $this);?>

<?php if ($this->_tpl_vars['object']->contactParameters): ?>
<table cellpadding="0" cellspacing="0" border="0" class="t_table" width="100%">
<?php $_from = $this->_tpl_vars['object']->contactParameters; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['z'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['z']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['contact_param']):
        $this->_foreach['z']['iteration']++;
?>
  <?php if ($this->_tpl_vars['object']->get($this->_tpl_vars['contact_param'])): ?>
    <?php ob_start(); ?>customers_<?php echo $this->_tpl_vars['contact_param']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('contact_param_label', ob_get_contents());ob_end_clean(); ?>
    <?php ob_start(); ?><?php echo $this->_tpl_vars['contact_param']; ?>
_note<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('contact_param_note_var', ob_get_contents());ob_end_clean(); ?>
    <?php $this->assign('contact_param_notes', $this->_tpl_vars['object']->get($this->_tpl_vars['contact_param_note_var'])); ?>
    <?php $_from = $this->_tpl_vars['object']->get($this->_tpl_vars['contact_param']); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['n'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['n']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['contact_num'] => $this->_tpl_vars['contact']):
        $this->_foreach['n']['iteration']++;
?>
      <?php ob_start(); ?><?php echo $this->_tpl_vars['contact_param']; ?>
_<?php echo $this->_tpl_vars['contact_num']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('contact_param_error', ob_get_contents());ob_end_clean(); ?>
      <?php if (! $this->_tpl_vars['cnt']): ?>
      <tr>
        <td class="t_panel_caption"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_link'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
        <td class="t_border t_panel_caption" style="width: 200px"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_link_note'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
      </tr>
      <?php endif; ?>
      <tr>
        <td class="t_border">
          <input type="checkbox" id="lk<?php echo smarty_function_counter(array('name' => 'k','assign' => 'cnt','print' => true), $this);?>
" name="link[<?php echo $this->_tpl_vars['cnt']-1; ?>
]" value="<?php echo $this->_tpl_vars['contact']; ?>
"<?php if (! $this->_tpl_vars['messages']->getErrors($this->_tpl_vars['contact_param_error'])): ?> checked="checked"<?php endif; ?><?php if ($this->_tpl_vars['layout'] && ! $this->_tpl_vars['layout']['edit']): ?> disabled="disabled"<?php endif; ?> />
          <?php if ($this->_tpl_vars['layout'] && ! $this->_tpl_vars['layout']['edit'] && ! $this->_tpl_vars['messages']->getErrors($this->_tpl_vars['contact_param_error'])): ?>
            <input type="hidden" name="link[<?php echo $this->_tpl_vars['cnt']-1; ?>
]" value="<?php echo $this->_tpl_vars['contact']; ?>
" />
          <?php endif; ?>
          <?php if ($this->_tpl_vars['messages']->getErrors($this->_tpl_vars['contact_param_error'])): ?><a name="error_<?php echo $this->_tpl_vars['contact_param_error']; ?>
"></a><?php endif; ?>
          <?php if ($this->_tpl_vars['use_asterisk'] && ( $this->_tpl_vars['contact_param'] == 'fax' || $this->_tpl_vars['contact_param'] == 'phone' || $this->_tpl_vars['contact_param'] == 'gsm' )): ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_asterisk_contact.html", 'smarty_include_vars' => array('contact_type' => $this->_tpl_vars['contact_param'],'number' => $this->_tpl_vars['contact'],'label' => $this->_config[0]['vars'][$this->_tpl_vars['contact_param_label']],'error' => $this->_tpl_vars['messages']->getErrors($this->_tpl_vars['contact_param_error']))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php else: ?>
            <?php if ($this->_tpl_vars['contact_param'] == 'web'): ?>
              <a href="http://<?php echo ((is_array($_tmp=$this->_tpl_vars['contact'])) ? $this->_run_mod_handler('replace', true, $_tmp, 'http://', '') : smarty_modifier_replace($_tmp, 'http://', '')); ?>
" target="_blank">
            <?php elseif ($this->_tpl_vars['contact_param'] == 'email'): ?>
              <a href="mailto:<?php echo $this->_tpl_vars['contact']; ?>
" target="_self">
            <?php else: ?>
              <label for="lk<?php echo $this->_tpl_vars['cnt']; ?>
">
            <?php endif; ?>
                <i class="material-icons" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['contact_param_label']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo $this->_tpl_vars['theme']->getIconForRecord("customers_contact_".($this->_tpl_vars['contact_param'])); ?>
</i>&nbsp;<?php if ($this->_tpl_vars['messages']->getErrors($this->_tpl_vars['contact_param_error'])): ?><span class="error"><?php echo $this->_tpl_vars['contact']; ?>
</span><?php else: ?><?php echo $this->_tpl_vars['contact']; ?>
<?php endif; ?>
            <?php if ($this->_tpl_vars['contact_param'] == 'web' || $this->_tpl_vars['contact_param'] == 'email'): ?>
              </a>
            <?php else: ?>
              </label>
            <?php endif; ?>
          <?php endif; ?>
        </td>
        <td>
          <input type="hidden" name="link_types[<?php echo $this->_tpl_vars['cnt']-1; ?>
]" id="ln<?php echo $this->_tpl_vars['cnt']; ?>
" value="<?php echo $this->_tpl_vars['contact_param']; ?>
" />
          <input type="text" maxlength="100" name="link_notes[<?php echo $this->_tpl_vars['cnt']-1; ?>
]" id="lnn<?php echo $this->_tpl_vars['cnt']; ?>
" value="<?php echo $this->_tpl_vars['contact_param_notes'][$this->_tpl_vars['contact_num']]; ?>
"<?php if ($this->_tpl_vars['layout'] && ! $this->_tpl_vars['layout']['edit']): ?> readonly="readonly" class="txtbox readonly"<?php else: ?> class="txtbox"<?php endif; ?> />
        </td>
      </tr>
    <?php endforeach; endif; unset($_from); ?>
  <?php endif; ?>
<?php endforeach; endif; unset($_from); ?>
</table>
<?php endif; ?>
<?php if (! $this->_tpl_vars['layout'] || $this->_tpl_vars['layout']['edit']): ?>
<table cellpadding="0" cellspacing="0" border="0" id="link_tbl_1" class="t_table" width="100%">
  <tr>
    <td class="t_panel_caption"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_link_type'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_border t_panel_caption"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_link_url'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_border t_panel_caption" style="width: 200px">
      <div class="t_panel_caption_title">
        <div class="floatl"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_link_note'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
        <div class="t_buttons">
          <div id="link_tbl_1_plusButton" onclick="addHyperlink('link_notes', 'link', 'link_types','link_tbl_1', <?php echo $this->_tpl_vars['cnt']+1; ?>
)" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['add_row'],'popup_only' => 1), $this);?>
><div class="t_plus"></div></div>
          <div id="link_tbl_1_minusButton"<?php if ($this->_tpl_vars['cnt'] > 1): ?> class="disabled"<?php endif; ?> onclick="removeField('link_tbl_1')" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['remove_row'],'popup_only' => 1), $this);?>
><div class="t_minus"></div></div>
        </div>
      </div>
    </td>
  </tr>
  <?php $this->assign('cnt_', $this->_tpl_vars['cnt']); ?>
  <?php $_from = $this->_tpl_vars['predefined_contact_params']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['j'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['j']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['predefined_contact_param']):
        $this->_foreach['j']['iteration']++;
?>
    <?php ob_start(); ?><?php if ($this->_tpl_vars['required_fields'] && in_array ( $this->_tpl_vars['predefined_contact_param'] , $this->_tpl_vars['required_fields'] ) && ! $this->_tpl_vars['object']->get($this->_tpl_vars['predefined_contact_param'])): ?>1<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('display_required', ob_get_contents());ob_end_clean(); ?>
    <?php if (! $this->_tpl_vars['cnt_'] || ( $this->_tpl_vars['cnt_'] > 0 && ($this->_foreach['j']['iteration'] == $this->_foreach['j']['total']) ) || $this->_tpl_vars['display_required']): ?>
    <tr>
      <td class="t_border" nowrap="nowrap">
        <select class="selbox" name="link_types[<?php echo $this->_tpl_vars['cnt']; ?>
]" id="link_types<?php echo smarty_function_counter(array('name' => 'k','assign' => 'cnt','print' => true), $this);?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['link_type'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)">
          <?php $_from = $this->_tpl_vars['object']->contactParameters; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['l'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['l']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['contact_param']):
        $this->_foreach['l']['iteration']++;
?>
            <?php ob_start(); ?>customers_<?php echo $this->_tpl_vars['contact_param']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('contact_param_label', ob_get_contents());ob_end_clean(); ?>
            <option value="<?php echo $this->_tpl_vars['contact_param']; ?>
"<?php if ($this->_tpl_vars['predefined_contact_param'] == $this->_tpl_vars['contact_param']): ?> selected="selected"<?php endif; ?> class="customers_<?php echo $this->_tpl_vars['contact_param']; ?>
"><?php echo $this->_config[0]['vars'][$this->_tpl_vars['contact_param_label']]; ?>
</option>
          <?php endforeach; endif; unset($_from); ?>
        </select>
        <?php if ($this->_tpl_vars['messages']->getErrors($this->_tpl_vars['predefined_contact_param'])): ?><a name="error_<?php echo $this->_tpl_vars['predefined_contact_param']; ?>
"></a><?php endif; ?>
        <?php if ($this->_tpl_vars['required_fields'] && in_array ( $this->_tpl_vars['predefined_contact_param'] , $this->_tpl_vars['required_fields'] ) && ( $this->_tpl_vars['display_required'] || ! ($this->_foreach['j']['iteration'] == $this->_foreach['j']['total']) )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php endif; ?>
      </td>
      <td class="t_border">
        <input type="text" class="txtbox" name="link[<?php echo $this->_tpl_vars['cnt']-1; ?>
]" id="link<?php echo $this->_tpl_vars['cnt']; ?>
" value="" onfocus="highlight(this)" onblur="unhighlight(this)" />
      </td>
      <td>
        <input type="text" maxlength="100" class="txtbox" name="link_notes[<?php echo $this->_tpl_vars['cnt']-1; ?>
]" id="link_notes<?php echo $this->_tpl_vars['cnt']; ?>
" value="" onfocus="highlight(this)" onblur="unhighlight(this)" />
      </td>
    </tr>
    <?php endif; ?>
  <?php endforeach; endif; unset($_from); ?>
</table>
<?php endif; ?>