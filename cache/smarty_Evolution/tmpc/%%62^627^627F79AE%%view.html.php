<?php /* Smarty version 2.6.33, created on 2025-06-20 13:59:19
         compiled from /var/www/Nzoom-Hella/_libs/modules/customers/view/templates/view.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/customers/view/templates/view.html', 3, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/customers/view/templates/view.html', 41, false),)), $this); ?>
<div class="nz-page-wrapper<?php if (! empty ( $this->_tpl_vars['side_panels'] ) && count ( $this->_tpl_vars['side_panels'] )): ?> nz--has-side-panel<?php endif; ?>">
  <div class="nz-page-main-column nz-content-surface<?php if (empty ( $this->_tpl_vars['_isPopup'] )): ?> nz-elevation--z3<?php endif; ?>">
    <div class="nz-page-title"><h1><?php echo ((is_array($_tmp=$this->_tpl_vars['title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1>
      <div class="nz-page-title-tools">
        <?php if (isset ( $this->_tpl_vars['available_page_actions']['general'] )): ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array('available_actions' => $this->_tpl_vars['available_page_actions']['general'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php endif; ?>
      </div>
      <div class="nz-page-title-sidetools">
        <?php if (isset ( $this->_tpl_vars['available_page_actions']['quick'] )): ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array('available_actions' => $this->_tpl_vars['available_page_actions']['quick'],'onlyIcons' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php endif; ?>
              </div>
    </div>
    <div class="nz-page-actions">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array('available_actions' => $this->_tpl_vars['available_page_actions']['context'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </div>

    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_add_popout_xtemplate.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_translations_menu.html", 'smarty_include_vars' => array('translations' => $this->_tpl_vars['translations'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

    <div id="form_container" class="nz-page-content main_panel_container">
      
                  
      <form name="customers" action="<?php echo $this->_tpl_vars['submitLink']; ?>
" method="post">
        <input type="hidden" name="id" id="id" value="<?php echo $this->_tpl_vars['customer']->get('id'); ?>
" />
        <input type="hidden" name="is_company" id="is_company" value="<?php echo $this->_tpl_vars['customer']->get('is_company'); ?>
" />
        <input type="hidden" name="model_lang" id="model_lang" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['customer']->get('model_lang'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lang']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lang'])); ?>
" />

        <a id="vars_index"></a>
        <table border="0" cellpadding="0" cellspacing="0" class="t_table">
          <tr><td>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => $this->_tpl_vars['view_template'], 'smarty_include_vars' => array('layouts_vars' => $this->_tpl_vars['customer']->get('vars'))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            </td></tr>
        </table>
      </form>
    </div>
  </div>
  <?php if (isset ( $this->_tpl_vars['side_panels'] )): ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_side_panel.html", 'smarty_include_vars' => array('side_panels' => $this->_tpl_vars['side_panels'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  <?php endif; ?>
</div>

<?php if (isset ( $this->_tpl_vars['side_panels'] ) && in_array ( 'related_records' , $this->_tpl_vars['side_panels'] ) && $this->_tpl_vars['related_records_modules']): ?>
  <?php ob_start(); ?>related_subpanel_customer<?php echo $this->_tpl_vars['customer']->get('id'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents(); ob_end_clean(); ?>
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_related_records.html", 'smarty_include_vars' => array('related_records_modules' => $this->_tpl_vars['related_records_modules'],'subpanel_name' => ($this->_tpl_vars['subpanel_name']),'coockiename' => 'customers_selected_related_tab','available_actions_related_records' => $this->_tpl_vars['available_actions_related_records'],'session_params' => $this->_tpl_vars['session_params'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php endif; ?>