<?php /* Smarty version 2.6.33, created on 2025-06-20 13:59:31
         compiled from view_textarea.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'help', 'view_textarea.html', 2, false),array('modifier', 'escape', 'view_textarea.html', 5, false),array('modifier', 'mb_wordwrap', 'view_textarea.html', 5, false),array('modifier', 'url2href', 'view_textarea.html', 5, false),array('modifier', 'default', 'view_textarea.html', 5, false),)), $this); ?>
        <tr<?php if ($this->_tpl_vars['var']['hidden']): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['var']['label'],'text_content' => $this->_tpl_vars['var']['help']), $this);?>
</td>
          <td class="required"><?php if ($this->_tpl_vars['var']['required']): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
              <div class="nz-view-wrapper"><?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</div>

                        <?php if ($this->_tpl_vars['var']['value'] || $this->_tpl_vars['var']['value'] === '0'): ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_back_label.html", 'smarty_include_vars' => array('back_label' => $this->_tpl_vars['var']['back_label'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php endif; ?>
          </td>
        </tr>