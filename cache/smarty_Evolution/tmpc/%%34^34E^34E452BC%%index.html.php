<?php /* Smarty version 2.6.33, created on 2025-06-20 16:54:38
         compiled from /var/www/Nzoom-Hella/_libs/modules/nomenclatures/view/templates/index.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/view/templates/index.html', 27, false),)), $this); ?>
<div class="nz-page-wrapper nz-page__min-width">
    <div class="nz-page-main-column nz-content-surface<?php if (empty ( $this->_tpl_vars['_isPopup'] )): ?> nz-elevation--z3<?php endif; ?>">
        <div class="nz-page-title">
            <h1><?php echo $this->_tpl_vars['title']; ?>
</h1>
            <div class="nz-page-title-tools">
            </div>
        </div>
        <div id="form_container" class="nz-page-content">
            <div class="nz-tree">
        <?php $_from = $this->_tpl_vars['nomenclatures_components']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['component_name'] => $this->_tpl_vars['component_options']):
        $this->_foreach['i']['iteration']++;
?>
            <?php ob_start(); ?>nom_index_<?php echo $this->_tpl_vars['component_name']; ?>
_box<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('box_name', ob_get_contents());ob_end_clean(); ?>
            <?php ob_start(); ?><?php if (! empty ( $this->_tpl_vars['component_options']['options'] )): ?>1<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('hasChildren', ob_get_contents());ob_end_clean(); ?>
            <div class="nz-tree-node" id="comp_<?php echo $this->_tpl_vars['box_name']; ?>
">
                <?php if ($this->_tpl_vars['hasChildren']): ?>
                    <span class="nz-toggle nz-toggle-autoinit nz-tree-indicator"
                       data-toggle-target="#comp_<?php echo $this->_tpl_vars['box_name']; ?>
"
                       data-toggle-toggleClass="nz--opened"
                       data-toggle-personalsettings-section="switch"
                       data-toggle-personalsettings-name="<?php echo $this->_tpl_vars['box_name']; ?>
"
                       data-toggle-personalsettings-value="1"><i class="material-icons nz-tree-indicator-icon">chevron_right</i></span>
                <?php else: ?>
                    <span class="nz-tree-indicator"></span>
                <?php endif; ?>
                <div class="nz-tree-node-content">
                    <div class="nz-tree-label">
                        <i class="nz-tree-label__graphic material-icons"><?php echo $this->_tpl_vars['component_options']['i']; ?>
</i><a href="<?php echo $this->_tpl_vars['component_options']['url']; ?>
" class=""><?php echo $this->_tpl_vars['component_options']['i18n']; ?>

                            <?php if ($this->_tpl_vars['hasChildren']): ?> (<?php echo count($this->_tpl_vars['component_options']['options']); ?>
)<?php endif; ?></a>
                    </div>
                    <?php if ($this->_tpl_vars['hasChildren']): ?>
                    <div class="nz-tree-node-subtree">
                        <?php $_from = $this->_tpl_vars['component_options']['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['j'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['j']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['option']):
        $this->_foreach['j']['iteration']++;
?>
                            <div class="nz-tree-label">
                                <i class="nz-tree-label__graphic material-icons"><?php echo $this->_tpl_vars['option']['i']; ?>
</i><a href="<?php echo $this->_tpl_vars['option']['url']; ?>
"><?php echo $this->_tpl_vars['option']['i18n']; ?>
</a>
                            </div>
                        <?php endforeach; endif; unset($_from); ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endforeach; endif; unset($_from); ?>
            </div>
        </div>
    </div>
</div>