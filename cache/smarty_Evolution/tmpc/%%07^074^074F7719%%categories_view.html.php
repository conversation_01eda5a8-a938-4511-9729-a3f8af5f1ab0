<?php /* Smarty version 2.6.33, created on 2025-06-20 16:54:47
         compiled from /var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/categories_view.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'help', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/categories_view.html', 13, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/categories_view.html', 16, false),array('modifier', 'mb_wordwrap', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/categories_view.html', 51, false),array('modifier', 'url2href', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/categories_view.html', 51, false),)), $this); ?>
<h1><?php echo $this->_tpl_vars['title']; ?>
</h1>

<div id="form_container">

<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."translate_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'categories_name'), $this);?>
</td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['category']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'categories_ancestor'), $this);?>
</td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['category']->get('ancestor_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'categories_path'), $this);?>
</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
<?php $_from = $this->_tpl_vars['categories_parents']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['item']):
?>
           &raquo;
           <?php if ($this->_tpl_vars['item']->isDeleted() || ! $this->_tpl_vars['item']->isActivated()): ?>
             <span class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['item']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
           <?php else: ?>
             <?php echo ((is_array($_tmp=$this->_tpl_vars['item']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

           <?php endif; ?>
<?php endforeach; endif; unset($_from); ?>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'categories_position'), $this);?>
</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['category']->get('position'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'categories_description'), $this);?>
</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['category']->get('description'))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp) : smarty_modifier_mb_wordwrap($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)); ?>

          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."system_settings_box.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['category'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
</div>