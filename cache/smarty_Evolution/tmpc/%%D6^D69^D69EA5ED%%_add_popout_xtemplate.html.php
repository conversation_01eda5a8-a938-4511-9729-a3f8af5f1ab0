<?php /* Smarty version 2.6.33, created on 2025-06-20 13:59:19
         compiled from /var/www/Nzoom-Hella/_libs/modules/customers/view/templates/_add_popout_xtemplate.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/customers/view/templates/_add_popout_xtemplate.html', 6, false),)), $this); ?>
<script id="add-popout" type="text/x-template">
  <?php if (isset ( $this->_tpl_vars['actionData'] )): ?>
    <aside class="nz-add-panel nz-popout-panel nz-pointer-top-center">
      <div class="nz-popout-surface nz-surface nz-elevation--z6">
        <div class="nz-add-wrapper">
          <div class="nz-popout-body available_options" id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['actionData']['name'])) ? $this->_run_mod_handler('default', true, $_tmp, '') : smarty_modifier_default($_tmp, '')); ?>
_options">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => '_action_common.html', 'smarty_include_vars' => array('action' => $this->_tpl_vars['actionData'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </div>
        </div>
      </div>
    </aside>
  <?php endif; ?>
</script>