<?php /* Smarty version 2.6.33, created on 2025-06-20 16:46:57
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/popup_autocomplete_fields.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/popup_autocomplete_fields.html', 3, false),)), $this); ?>
<?php if ($_REQUEST['uniqid'] && $this->_tpl_vars['object'] && $this->_tpl_vars['object']->get('autocomplete_params')): ?>
<input type="hidden" name="uniqid" id="uniqid" value="<?php echo ((is_array($_tmp=$_REQUEST['uniqid'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
<input type="hidden" name="autocomplete_params" id="autocomplete_params" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['object']->get('autocomplete_params'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
<input type="hidden" name="aa_uniqid" id="aa_uniqid" value="<?php echo ((is_array($_tmp=$_REQUEST['uniqid'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
<input type="hidden" name="aa_autocomplete_filter" id="aa_autocomplete_filter" value="session" />
<input type="hidden" name="after_action" id="after_action" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['action'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
<?php endif; ?>