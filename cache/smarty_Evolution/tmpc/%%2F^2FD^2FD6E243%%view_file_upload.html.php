<?php /* Smarty version 2.6.33, created on 2025-06-20 13:59:19
         compiled from view_file_upload.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'help', 'view_file_upload.html', 2, false),array('modifier', 'default', 'view_file_upload.html', 5, false),)), $this); ?>
        <tr<?php if (! empty ( $this->_tpl_vars['var']['hidden'] )): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['var']['label'],'text_content' => $this->_tpl_vars['var']['help']), $this);?>
</td>
          <td class="required"><?php if ($this->_tpl_vars['var']['required']): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap" style="vertical-align: top;">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "view_file_upload_raw.html", 'smarty_include_vars' => array('file_info' => $this->_tpl_vars['var']['value'],'view_mode' => ((is_array($_tmp=@$this->_tpl_vars['var']['view_mode'])) ? $this->_run_mod_handler('default', true, $_tmp, '') : smarty_modifier_default($_tmp, '')),'thumb_width' => ((is_array($_tmp=@$this->_tpl_vars['var']['thumb_width'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)),'thumb_height' => ((is_array($_tmp=@$this->_tpl_vars['var']['thumb_height'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)),'label' => $this->_tpl_vars['var']['label'],'theme' => $this->_tpl_vars['theme'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                        <?php if (! empty ( $this->_tpl_vars['file_info'] ) && is_object ( $this->_tpl_vars['file_info'] ) && ! $this->_tpl_vars['file_info']->get('deleted_by')): ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_back_label.html", 'smarty_include_vars' => array('back_label' => $this->_tpl_vars['var']['back_label'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php endif; ?>
          </td>
        </tr>