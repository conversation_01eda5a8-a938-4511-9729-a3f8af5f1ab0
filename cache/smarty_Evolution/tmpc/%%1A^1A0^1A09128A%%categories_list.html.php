<?php /* Smarty version 2.6.33, created on 2025-06-20 16:54:00
         compiled from /var/www/Nzoom-Hella/_libs/modules/nomenclatures/view/templates/categories_list.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/view/templates/categories_list.html', 15, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/view/templates/categories_list.html', 20, false),array('modifier', 'htmlspecialchars', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/view/templates/categories_list.html', 29, false),)), $this); ?>
<div class="nz-page-wrapper">
  <div class="nz-page-title"><h1><?php echo $this->_tpl_vars['title']; ?>
</h1>

    <div class="nz-page-title-tools">
      <?php if (isset ( $this->_tpl_vars['available_actions']['add'] )): ?>
        <a class="nz-add-button nz-button nz-action__add"
            href="<?php echo $this->_tpl_vars['available_actions']['add']['url']; ?>
">
                <i class="material-icons"><?php echo $this->_tpl_vars['available_actions']['add']['icon']; ?>
</i>
                <?php echo $this->_tpl_vars['available_actions']['add']['label']; ?>

            </a>
      <?php endif; ?>
    </div>
  </div>

  <?php if ($this->_tpl_vars['subtitle']): ?><h2><?php echo ((is_array($_tmp=$this->_tpl_vars['subtitle'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h2><?php endif; ?>
  <div>
    <form name="documents" action="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=nomenclatures" method="post" enctype="multipart/form-data">
      <div class="nz-grid-wrapper">
        <div class="nz-grid-toobar">
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."multiple_actions_list.html", 'smarty_include_vars' => array('tags' => $this->_tpl_vars['tags'],'include' => "",'session_param' => ((is_array($_tmp=@$this->_tpl_vars['session_param'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['pagination']['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['pagination']['session_param'])))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <div class="nz-grid-pagination-wrapper">
          </div>
        </div>
        <div class="clear"></div>
        <div id="nz-grid" class="nz-list-grid"
             data-endpoint="<?php echo ((is_array($_tmp=$this->_tpl_vars['listData'])) ? $this->_run_mod_handler('htmlspecialchars', true, $_tmp) : htmlspecialchars($_tmp)); ?>
"
             data-columns-endpoint="<?php echo ((is_array($_tmp=$this->_tpl_vars['columnsDefinitions'])) ? $this->_run_mod_handler('htmlspecialchars', true, $_tmp) : htmlspecialchars($_tmp)); ?>
"
                          data-key="<?php echo $this->_tpl_vars['searchKey']; ?>
"
             data-module-controller="<?php echo $this->_tpl_vars['available_actions']['search']['module']; ?>
|<?php if ($this->_tpl_vars['available_actions']['search']['controller']): ?><?php echo $this->_tpl_vars['available_actions']['search']['controller']; ?>
<?php else: ?><?php echo $this->_tpl_vars['available_actions']['search']['module']; ?>
<?php endif; ?>"
        ></div>
      </div>
    </form>
  </div>
</div>
<?php echo '
<script id="paginator-template" type="text/x-template">

</script>

<script id="grid-col-name" type="text/x-template">
    <a class="" href="${Nz.getViewUrl(properties.id)}"
         title="${properties.name}"><span class="nz-depth-indicator nz-depth-${properties.level}"></span>${properties.name}</a>
</script>

<script id="grid-col-tools" type="text/x-template">
    <div class="nz-grid-tools-wrapper">
        <a href="${Nz.getEditUrl(properties.id)}" class="material-icons nz-icon-button" title="Редакция">edit</a>
        <a href="${Nz.getViewUrl(properties.id)}" class="material-icons nz-icon-button" title="Разглеждане">search</a>
        <i class="material-icons nz-tooltip-trigger nz-tooltip-autoinit nz-tooltip-helpcursor"
           data-tooltip-element="#nom-info-${properties.id}"
           data-tooltip-title="'; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo '"
           data-tooltip-position="panel: bottom right at: bottom left">info</i>
        <div id="nom-info-${properties.id}" class="nz-tooltip-content nz-tooltip-notch__right-bottom">
            <div>'; ?>
<strong><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['name'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['nomenclatures_categories_name']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['nomenclatures_categories_name'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</strong><?php echo ': ${properties.name}</div>
            <div>'; ?>
<strong><?php echo ((is_array($_tmp=$this->_config[0]['vars']['added'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</strong><?php echo ': ${Nz.formatDate(properties.added)}<br>
                '; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo ' ${properties.added_by_name}</div>
            <div>'; ?>
<strong><?php echo ((is_array($_tmp=$this->_config[0]['vars']['modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</strong><?php echo ': ${Nz.formatDate(properties.modified)}<br>
                '; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo ' ${properties.modified_by_name}</div>
            ${if(properties.deleted_by_name)}
            <div>'; ?>
<strong><?php echo ((is_array($_tmp=$this->_config[0]['vars']['deleted'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</strong><?php echo ': ${Nz.formatDate(properties.deleted)}<br>
                '; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo ' ${properties.deleted_by_name}</div>
            ${/if}
            <div>
                <strong>'; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['translations'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo ':</strong>
                <span class="translations">
                  ${for(trans of properties.translations)}
                    <img src="'; ?>
<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
flags<?php echo '/${trans}.png" alt="${trans}" title="${trans}" border="0" align="absmiddle"${if(trans==properties.model_lang)} class="selected"${/if} />
                  ${/for}
                </span>
            </div>
        </div>
    </div>
</script>


'; ?>


