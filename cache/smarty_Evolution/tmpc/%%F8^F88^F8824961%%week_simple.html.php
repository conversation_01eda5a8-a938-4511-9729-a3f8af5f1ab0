<?php /* Smarty version 2.6.33, created on 2025-06-19 18:30:16
         compiled from /var/www/Nzoom-Hella/_libs/modules/calendars/view/templates/week_simple.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'math', '/var/www/Nzoom-Hella/_libs/modules/calendars/view/templates/week_simple.html', 9, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/calendars/view/templates/week_simple.html', 14, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/calendars/view/templates/week_simple.html', 19, false),array('modifier', 'string_format', '/var/www/Nzoom-Hella/_libs/modules/calendars/view/templates/week_simple.html', 61, false),)), $this); ?>
<?php $this->assign('table_cols', 8); ?>
<?php ob_start(); ?><?php if ($this->_tpl_vars['monday_start'] && $this->_tpl_vars['calendar']->day_of_week == 0): ?>7<?php else: ?><?php echo $this->_tpl_vars['calendar']->day_of_week; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('current_day_of_week', ob_get_contents());ob_end_clean(); ?>
<?php $this->assign('week_num', $this->_tpl_vars['calendar']->day->week); ?>
<?php $this->assign('week', $this->_tpl_vars['calendar']->month->weeks[$this->_tpl_vars['week_num']]); ?>
<?php $this->assign('first_week_day_index', $this->_tpl_vars['week']['0']); ?>
<?php $this->assign('last_week_day_index', $this->_tpl_vars['week']['6']); ?>
<?php $this->assign('first_week_day', $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['first_week_day_index']]->dateISO); ?>
<?php $this->assign('last_week_day', $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['last_week_day_index']]->dateISO); ?>
<?php echo smarty_function_math(array('assign' => 'day_width','equation' => '98/x','x' => 7,'format' => '%d'), $this);?>

<table border="0" cellpadding="0" cellspacing="0" class="nz-calendar-tbl nz-week-simple">
  <thead>
    <tr class="nz-calendar-tbl-head">
      <th  class="nz-calendar_caption" colspan="<?php echo $this->_tpl_vars['table_cols']; ?>
">
        <div class=""><?php echo ((is_array($_tmp=$this->_tpl_vars['first_week_day'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_calendar_day']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_calendar_day'])); ?>
 - <?php echo ((is_array($_tmp=$this->_tpl_vars['last_week_day'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_calendar_day']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_calendar_day'])); ?>
 (<?php echo $this->_config[0]['vars']['week']; ?>
 <?php echo $this->_config[0]['vars']['num']; ?>
<?php echo $this->_tpl_vars['week_num']; ?>
)</div>
      </th>
    </tr>
    <tr  class="nz-calendar-tbl-head">
      <th class="nz-calendar-tbl__weeknum nz-tooltip-trigger nz-tooltip-autoinit"width="2%"
          data-tooltip-content="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['calendars_week_number'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
          data-tooltip-position="panel: bottom left at: top center"><?php echo $this->_config[0]['vars']['num']; ?>
</th>
      <?php if (! $this->_tpl_vars['monday_start']): ?>
        <th class="nz-calendar-tbl__weekend" width="<?php echo $this->_tpl_vars['day_width']; ?>
%"><?php echo $this->_config[0]['vars']['weekday_0']; ?>
</th>
      <?php endif; ?>

      <?php unset($this->_sections['weekdays']);
$this->_sections['weekdays']['name'] = 'weekdays';
$this->_sections['weekdays']['loop'] = is_array($_loop=7) ? count($_loop) : max(0, (int)$_loop); unset($_loop);
$this->_sections['weekdays']['start'] = (int)1;
$this->_sections['weekdays']['show'] = true;
$this->_sections['weekdays']['max'] = $this->_sections['weekdays']['loop'];
$this->_sections['weekdays']['step'] = 1;
if ($this->_sections['weekdays']['start'] < 0)
    $this->_sections['weekdays']['start'] = max($this->_sections['weekdays']['step'] > 0 ? 0 : -1, $this->_sections['weekdays']['loop'] + $this->_sections['weekdays']['start']);
else
    $this->_sections['weekdays']['start'] = min($this->_sections['weekdays']['start'], $this->_sections['weekdays']['step'] > 0 ? $this->_sections['weekdays']['loop'] : $this->_sections['weekdays']['loop']-1);
if ($this->_sections['weekdays']['show']) {
    $this->_sections['weekdays']['total'] = min(ceil(($this->_sections['weekdays']['step'] > 0 ? $this->_sections['weekdays']['loop'] - $this->_sections['weekdays']['start'] : $this->_sections['weekdays']['start']+1)/abs($this->_sections['weekdays']['step'])), $this->_sections['weekdays']['max']);
    if ($this->_sections['weekdays']['total'] == 0)
        $this->_sections['weekdays']['show'] = false;
} else
    $this->_sections['weekdays']['total'] = 0;
if ($this->_sections['weekdays']['show']):

            for ($this->_sections['weekdays']['index'] = $this->_sections['weekdays']['start'], $this->_sections['weekdays']['iteration'] = 1;
                 $this->_sections['weekdays']['iteration'] <= $this->_sections['weekdays']['total'];
                 $this->_sections['weekdays']['index'] += $this->_sections['weekdays']['step'], $this->_sections['weekdays']['iteration']++):
$this->_sections['weekdays']['rownum'] = $this->_sections['weekdays']['iteration'];
$this->_sections['weekdays']['index_prev'] = $this->_sections['weekdays']['index'] - $this->_sections['weekdays']['step'];
$this->_sections['weekdays']['index_next'] = $this->_sections['weekdays']['index'] + $this->_sections['weekdays']['step'];
$this->_sections['weekdays']['first']      = ($this->_sections['weekdays']['iteration'] == 1);
$this->_sections['weekdays']['last']       = ($this->_sections['weekdays']['iteration'] == $this->_sections['weekdays']['total']);
?>
        <?php ob_start(); ?>weekday_<?php echo $this->_sections['weekdays']['index']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('week_label', ob_get_contents());ob_end_clean(); ?>
        <th class="<?php if ($this->_sections['weekdays']['iteration'] >= 6): ?>nz-calendar-tbl__weekend<?php endif; ?>" width="<?php echo $this->_tpl_vars['day_width']; ?>
%"><?php echo $this->_config[0]['vars'][$this->_tpl_vars['week_label']]; ?>
</th>
      <?php endfor; endif; ?>
      <?php if ($this->_tpl_vars['monday_start']): ?>
        <th class="nz-calendar-tbl__weekend" width="<?php echo $this->_tpl_vars['day_width']; ?>
%"><?php echo $this->_config[0]['vars']['weekday_0']; ?>
</th>
      <?php endif; ?>
    </tr>
  </thead>
  <tbody>
    <tr class="nz-calendar-tbl-body">
      <td class="nz-calendar-tbl__weeknum">
        <?php $this->assign('week_num', $this->_tpl_vars['calendar']->day->week); ?>
        <?php $this->assign('week', $this->_tpl_vars['calendar']->month->weeks[$this->_tpl_vars['week_num']]); ?>
        <?php $this->assign('w_day', $this->_tpl_vars['week']['0']); ?>
        <?php ob_start(); ?>cal_week_simple_num_<?php echo $this->_tpl_vars['week_num']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('week_num_help_id', ob_get_contents());ob_end_clean(); ?>
        <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=calendars&amp;calendars=week&amp;date=<?php echo $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['w_day']]->year; ?>
-<?php echo $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['w_day']]->month; ?>
-<?php echo $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['w_day']]->day; ?>
"
           class="nz-tooltip-trigger nz-tooltip-autoinit"
           data-tooltip-element="#<?php echo $this->_tpl_vars['week_num_help_id']; ?>
"
           data-tooltip-position="panel: bottom left at: top center"
          ><?php echo $this->_tpl_vars['week_num']; ?>
</a>
        <div id="<?php echo $this->_tpl_vars['week_num_help_id']; ?>
" class="nz-tooltip-content nz-tooltip-notch__bottom-left">
          <?php echo $this->_config[0]['vars']['calendars_view_event']; ?>
 <b><?php echo $this->_config[0]['vars']['week']; ?>
 <?php echo $this->_config[0]['vars']['num']; ?>
<?php echo $this->_tpl_vars['week_num']; ?>
</b>
        </div>
      </td>
      <?php $_from = $this->_tpl_vars['week']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['week_days'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['week_days']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['weekDay'] => $this->_tpl_vars['day']):
        $this->_foreach['week_days']['iteration']++;
?>
        <?php $this->assign('day_num', $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->day); ?>
        <?php $this->assign('month_num', $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->month); ?>
        <td class="nz-calendar-tbl-day<?php if ($this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->isToday()): ?> nz--active<?php endif; ?><?php if ($this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->month != $this->_tpl_vars['calendar']->month->getMonth()): ?> nz--disabled<?php endif; ?><?php if ($this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->isWeekend()): ?> nz-calendar-tbl__weekend<?php endif; ?>">
          <?php ob_start(); ?><?php if ($this->_tpl_vars['weekDay'] < 2): ?>left<?php elseif ($this->_tpl_vars['weekDay'] > 4): ?>right<?php else: ?>center<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('tooltip_pos', ob_get_contents());ob_end_clean(); ?>
          <div class="nz-calendar-tbl__daynum">
            <?php ob_start(); ?>daynum_help_tooltip_<?php echo $this->_tpl_vars['day_num']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('daynum_help_content', ob_get_contents());ob_end_clean(); ?>
            <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=calendars&amp;calendars=day&amp;date=<?php echo $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->year; ?>
-<?php echo $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->month; ?>
-<?php echo $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->day; ?>
"
               class="nz-tooltip-trigger nz-tooltip-autoinit"
               data-tooltip-element="#<?php echo $this->_tpl_vars['daynum_help_content']; ?>
"
               data-tooltip-position="panel: bottom <?php echo $this->_tpl_vars['tooltip_pos']; ?>
 at: top center"
            ><?php echo ((is_array($_tmp=$this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->day)) ? $this->_run_mod_handler('string_format', true, $_tmp, '%d') : smarty_modifier_string_format($_tmp, '%d')); ?>
</a>
            <div id="<?php echo $this->_tpl_vars['daynum_help_content']; ?>
" class="nz-tooltip-content nz-tooltip-notch__bottom-<?php echo $this->_tpl_vars['tooltip_pos']; ?>
">
              <?php echo $this->_config[0]['vars']['calendars_view_event']; ?>
 <b><?php echo ((is_array($_tmp=$this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->timestamp)) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_calendar_day']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_calendar_day'])); ?>
</b>
            </div>
          </div>

          <div class="nz-calendar-_controls">
            <?php ob_start(); ?>addevent_help_tooltip_<?php echo $this->_tpl_vars['day_num']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('addevent_help_content', ob_get_contents());ob_end_clean(); ?>
            <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=events&amp;events=add&amp;date=<?php echo $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->year; ?>
-<?php echo $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->month; ?>
-<?php echo $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->day; ?>
%20<?php echo ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, '%T') : smarty_modifier_date_format($_tmp, '%T')); ?>
"
               class="nz-icon-button nz-tooltip-trigger nz-tooltip-autoinit"
               data-tooltip-element="#<?php echo $this->_tpl_vars['addevent_help_content']; ?>
"
               data-tooltip-position="panel: bottom <?php echo $this->_tpl_vars['tooltip_pos']; ?>
 at: top center"
            ><?php echo $this->_tpl_vars['theme']->getIconForAction('addevent'); ?>
</a>
            <div id="<?php echo $this->_tpl_vars['addevent_help_content']; ?>
" class="nz-tooltip-content nz-tooltip-notch__bottom-<?php echo $this->_tpl_vars['tooltip_pos']; ?>
">
              <?php echo $this->_config[0]['vars']['calendars_add_event']; ?>
 <b><?php echo ((is_array($_tmp=$this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->timestamp)) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_calendar_day']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_calendar_day'])); ?>
</b>
            </div>
          </div>

          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_events_week_simple.html", 'smarty_include_vars' => array('num_events' => $this->_tpl_vars['months_count_events'][$this->_tpl_vars['month_num']][$this->_tpl_vars['day_num']],'day' => $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        </td>
      <?php endforeach; endif; unset($_from); ?>
    </tr>
  </tbody>
</table>