<?php

require_once PH_MODULES_DIR . 'soap/controllers/soap.controller.php';

class Custom_Soap_Controller extends Soap_Controller {

    /**
     * Array with permited test actions for the client
     */
    public $actions = array('testCustomers', 'testBranches', 'testContactPersons', 'testLangs', 'testMessages',
                            'testDepartments', 'testUsers', 'testModelsSections', 'testAdd');

    /**
     * Actions which will need user's credentials and we cannot use
     * automated system of nZoom
     * @var array
     */
    public $authenticateActions = array('handle'/*, 'getdocs'*/);

    /**
    * WSDL data including login details
    */
    public static $wsdlData = array(
             'trace'          => true,
             'allow_self_signed' => true,
             'verify_peer' => false,
             'cache_wsdl'     => WSDL_CACHE_NONE,
             'features'       => SOAP_SINGLE_ELEMENT_ARRAYS,
             'authentication' => SOAP_AUTHENTICATION_DIGEST);

    /**
    * Test function for 'nzCustomers' function
    */
    public function testCustomers() {

        $params = new Object();
        $params->oInput = $this->registry['request']->get('oInput');
        $params->oModelType = $this->registry['request']->get('oModelType');
        $params->oLang = $this->registry['request']->get('oLang');

        //check if username is requested
        if ($this->registry['request']->isRequested('username')) {
            self::$wsdlData['login'] = $this->registry['request']->get('username');
        } else {
            self::$wsdlData['login'] = '';
        }

        //check if password is requested
        if ($this->registry['request']->isRequested('password')) {
            self::$wsdlData['password'] = $this->registry['request']->get('password');
        } else {
            self::$wsdlData['password'] = '';
        }

        require_once $this->pluginModelsDir . 'custom.soap.handler.php';
        $this->_test('nzCustomers', $params, self::$wsdlData);
    }

    /**
    * Test function for 'nzBranches' function
    */
    public function testBranches() {

        $params = new Object();
        $params->oCustomer = $this->registry['request']->get('oCustomer');
        $params->oLang = $this->registry['request']->get('oLang');

        //check if username is requested
        if ($this->registry['request']->isRequested('username')) {
            self::$wsdlData['login'] = $this->registry['request']->get('username');
        } else {
            self::$wsdlData['login'] = '';
        }

        //check if password is requested
        if ($this->registry['request']->isRequested('password')) {
            self::$wsdlData['password'] = $this->registry['request']->get('password');
        } else {
            self::$wsdlData['password'] = '';
        }

        require_once $this->pluginModelsDir . 'custom.soap.handler.php';
        $this->_test('nzBranches', $params, self::$wsdlData);
    }

    /**
     * Test function for 'nzBranches' function
     */
    public function testContactPersons() {

        $params = new Object();
        $params->oBranch = $this->registry['request']->get('oBranch');
        $params->oLang = $this->registry['request']->get('oLang');

        //check if username is requested
        if ($this->registry['request']->isRequested('username')) {
            self::$wsdlData['login'] = $this->registry['request']->get('username');
        } else {
            self::$wsdlData['login'] = '';
        }

        //check if password is requested
        if ($this->registry['request']->isRequested('password')) {
            self::$wsdlData['password'] = $this->registry['request']->get('password');
        } else {
            self::$wsdlData['password'] = '';
        }

        require_once $this->pluginModelsDir . 'custom.soap.handler.php';
        $this->_test('nzContactPersons', $params, self::$wsdlData);
    }

    /**
     * Test function for 'nzLangs' function
     */
    public function testLangs() {

        //check if username is requested
        if ($this->registry['request']->isRequested('username')) {
            self::$wsdlData['login'] = $this->registry['request']->get('username');
        } else {
            self::$wsdlData['login'] = '';
        }

        //check if password is requested
        if ($this->registry['request']->isRequested('password')) {
            self::$wsdlData['password'] = $this->registry['request']->get('password');
        } else {
            self::$wsdlData['password'] = '';
        }

        require_once $this->pluginModelsDir . 'custom.soap.handler.php';
        $this->_test('nzLanguages', null, self::$wsdlData);
    }

    /**
     * Test function for 'nzMessages' function
     */
    public function testMessages() {

        //check if username is requested
        if ($this->registry['request']->isRequested('username')) {
            self::$wsdlData['login'] = $this->registry['request']->get('username');
        } else {
            self::$wsdlData['login'] = '';
        }

        //check if password is requested
        if ($this->registry['request']->isRequested('password')) {
            self::$wsdlData['password'] = $this->registry['request']->get('password');
        } else {
            self::$wsdlData['password'] = '';
        }

        $params = new Object();
        $params->oLang = $this->registry['request']->get('oLang');

        require_once $this->pluginModelsDir . 'custom.soap.handler.php';
        $this->_test('nzMessages', $params, self::$wsdlData);
    }

    /**
     * Test function for 'nzDepartments' function
     */
    public function testDepartments() {

        //check if username is requested
        if ($this->registry['request']->isRequested('username')) {
            self::$wsdlData['login'] = $this->registry['request']->get('username');
        } else {
            self::$wsdlData['login'] = '';
        }

        //check if password is requested
        if ($this->registry['request']->isRequested('password')) {
            self::$wsdlData['password'] = $this->registry['request']->get('password');
        } else {
            self::$wsdlData['password'] = '';
        }

        $params = new Object();
        $params->oLang = $this->registry['request']->get('oLang');

        require_once $this->pluginModelsDir . 'custom.soap.handler.php';
        $this->_test('nzDepartments', $params, self::$wsdlData);
    }

    /**
     * Test function for 'nzDepartmentUsers' function
     */
    public function testUsers() {

        //check if username is requested
        if ($this->registry['request']->isRequested('username')) {
            self::$wsdlData['login'] = $this->registry['request']->get('username');
        } else {
            self::$wsdlData['login'] = '';
        }

        //check if password is requested
        if ($this->registry['request']->isRequested('password')) {
            self::$wsdlData['password'] = $this->registry['request']->get('password');
        } else {
            self::$wsdlData['password'] = '';
        }

        $params = new Object();
        $params->oLang = $this->registry['request']->get('oLang');
        $params->oDepartment = $this->registry['request']->get('oDepartment');

        require_once $this->pluginModelsDir . 'custom.soap.handler.php';
        $this->_test('nzDepartmentUsers', $params, self::$wsdlData);
    }

    /**
     * Test function for 'nzModelsSections' function
     */
    public function testModelsSections() {

        //check if username is requested
        if ($this->registry['request']->isRequested('username')) {
            self::$wsdlData['login'] = $this->registry['request']->get('username');
        } else {
            self::$wsdlData['login'] = '';
        }

        //check if password is requested
        if ($this->registry['request']->isRequested('password')) {
            self::$wsdlData['password'] = $this->registry['request']->get('password');
        } else {
            self::$wsdlData['password'] = '';
        }

        $params = new Object();
        $params->oLang = $this->registry['request']->get('oLang');

        require_once $this->pluginModelsDir . 'custom.soap.handler.php';
        $this->_test('nzModelsSections', $params, self::$wsdlData);
    }

    /**
     * Test function for 'nzModelsSections' function
     */
    public function testAdd() {

        //check if username is requested
        if ($this->registry['request']->isRequested('username')) {
            self::$wsdlData['login'] = $this->registry['request']->get('username');
        } else {
            self::$wsdlData['login'] = '';
        }

        //check if password is requested
        if ($this->registry['request']->isRequested('password')) {
            self::$wsdlData['password'] = $this->registry['request']->get('password');
        } else {
            self::$wsdlData['password'] = '';
        }

        $params = new Object();
        $params->oLang = $this->registry['request']->get('oLang');
        $params->oRecord = new Object();
        $params->oRecord->name = 'addTest';
        $params->oRecord->type = 'document_1';
        $params->oRecord->customer = 12;
        $params->oRecord->branch = 13;
        $params->oRecord->contact_person = 14;
        $params->oRecord->custom_num = 112331;
        $params->oRecord->date = General::strftime('%Y-%m-%d');
        $params->oRecord->notes = 'Some notes goes here';
        $params->oRecord->department = 6;
        $params->oRecord->users = array(46, 50, 54);
        $params->oRecord->files = array();

        $files = FilesLib::readDir('D:/a/', false, 'files_only', 'pdf', true);
        foreach ($files as $f) {
            $content = base64_encode(gzdeflate(FilesLib::readContent($f)));
            $name = basename($f);
            $params->oRecord->files[] = array('name' => $name, 'description' => $name, 'filename' => $name, 'content' => $content);
        }

        require_once $this->pluginModelsDir . 'custom.soap.handler.php';
        $this->_test('oAddRecord', $params, self::$wsdlData);
    }

}
