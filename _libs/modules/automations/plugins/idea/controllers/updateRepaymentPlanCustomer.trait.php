<?php

trait updateRepaymentPlanCustomer {
    public $automation_params;

    /**
     * Function to update the payment sum of the repayment plan in related incomes reason
     *
     * @param array $params - arguments for the method, containing registry
     * @return boolean - result of the operation
     */
    public function changeRepaymentPlanCustomer(array $params): bool
    {
        $this->automation_params = $params;
        $old_model = clone $params['model']->get('old_model');
        $new_model = clone $params['model'];

        // complete the original incomes reason
        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $assoc_vars = $new_model->getAssocVars();
        $new_gt2 = $new_model->getGT2Vars();

        $left_to_pay = 0;
        if (!empty($new_gt2['values'])) {
            $left_to_pay = array_sum(array_column($new_gt2['values'], 'price_with_discount'));
        }
         $this->getCustomAutomationSettings(array('credits', 'autoDistributePayment'), true);

        require_once PH_MODULES_DIR . 'automations/plugins/credits/controllers/credits.automations.controller.php';
        $automations_controller = new Credits_Automations_Controller($this->registry);
        $automations_controller->settings = $this->settings;

        // load automations i18n files
        $i18n_files = FilesLib::readDir(PH_MODULES_DIR . 'automations/plugins/credits/i18n/' . $this->registry['lang'],
            false, '', '', true);
        if (!empty($i18n_files)) {
            $this->registry['translater']->loadFile($i18n_files);
        }
        $adapter = $automations_controller->getAdapter();

        $incomes_reason_exist = $adapter->checkExistingFinIncomesReason($new_model->get('id'), $assoc_vars['currency']['value'],
            $this->settings['incomes_reason_type_id'], $old_model->get('customer'));

        $this->registry['db']->StartTrans();
        $adapter->reason_for_change = $new_model->get('id');
        if (!$adapter->issueFinIncomeReasonCorrection($incomes_reason_exist, $left_to_pay * -1,
            $this->settings['nom_contract_obligations_id'])) {
            $this->registry['messages']->setError($this->i18n('error_automation_customer_obligation_equalling_failed'));
            $this->registry['messages']->insertInSession($this->registry);
            $this->registry['db']->FailTrans();
        }

        // create a new incomes reason
        $filters = array(
            'where' => array('fir.id = \'' . $incomes_reason_exist . '\''),
            'model_lang' => $this->registry['lang']
        );
        $old_income_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

        $params_reason = array(
            'type'         => $old_income_reason->get('type'),
            'customer'     => $new_model->get('customer'),
            'issue_date'   => $new_model->get('date'),
            'company'      => $old_income_reason->get('company'),
            'office'       => $old_income_reason->get('office'),
            'payment_type' => $old_income_reason->get('payment_type'),
            'container_id' => $old_income_reason->get('container_id'),
            'total'        => $left_to_pay,
            'article_id'   => $this->settings['nom_contract_obligations_id'],
            'document_id'  => $new_model->get('id'),
            'parent_reason'=> '',
            'currency'     => $old_income_reason->get('currency')
        );

        if (!$adapter->createContractIncomesReason($params_reason)) {
            $this->registry['messages']->setError($this->i18n('error_automation_new_customer_obligation_creation_failed'));
            $this->registry['messages']->insertInSession($this->registry);
            $this->registry['db']->FailTrans();
        }

        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();
        $this->registry->set('get_old_vars', $get_old_vars, true);

        return $result;
    }

}
