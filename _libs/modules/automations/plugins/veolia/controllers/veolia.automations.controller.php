<?php

include_once __DIR__ . '/controlMeasurements.trait.php';
include_once __DIR__ . '/syncProtocols.trait.php';
include_once __DIR__ . '/validateAddDevices.trait.php';
include_once __DIR__ . '/syncObjectsCustomers.trait.php';
include_once __DIR__ . '/syncPsiroDatabase.trait.php';
include_once __DIR__ . '/syncFileServer.php';

class Veolia_Automations_Controller extends Automations_Controller {

    use controlMeasurements;
    use syncProtocols;
    use validateAddDevices;
    use syncObjectsCustomers;
    use SyncPsiroDatabaseTrait;
    use syncFileServer;

    /**
     * Update indications in nomenclatures and documents
     *
     * @param array $params
     * @return bool - result of operation
     */
    public function updateIndications($params) {
        // Prepare some basics
        $registry = &$this->registry;
        /**
         * @var $db ADOConnection
         */
        $db       = $registry['db'];
        $messages = $registry['messages'];
        $lang     = $registry['lang'];
        $model = $params['model'];

        $gov = $registry->get('get_old_vars');
        $ea = $registry->get('edit_all');
        $registry->set('edit_all', true, true);
        $registry->set('get_old_vars', true, true);

        $nomTypes = preg_split('#\s*,\s*#', $this->settings['nom_types']??'');

        //get documents
        $dAssocVars = $model->getAssocVars();

        $db->StartTrans();
        foreach($dAssocVars['device_id']['value'] as $rIdx => $deviceId) {
            if (empty($deviceId)) {
                continue;
            }

            $deviceName = $dAssocVars['device']['value'][$rIdx]??'';
            $nom = Nomenclatures::searchOne($registry, ['where' => ["n.id = {$deviceId}"], 'model_lang' => $lang]);
            if (!is_a($nom, Nomenclature::class)) {
                $messages->setError($this->i18n('error_no_device_found', [$deviceName]));
                $messages->insertInSession($this->registry);
                $db->FailTrans();
                break;
            }

            //check type
            if (!in_array($nom->get('type'), $nomTypes)) {
                continue;
            }

            $nAssocVars = $nom->getAssocVars(true);
            $nAssocVars['indication_old']['value'] = $nAssocVars['indication_new']['value'];
            $nAssocVars['indication_old_doc']['value'] = $nAssocVars['indication_doc']['value'];
            $nAssocVars['indication_old_doc_id']['value'] = $nAssocVars['indication_doc_id']['value'];
            $nAssocVars['indication_old_date']['value'] = $nAssocVars['indication_date']['value'];
            $nAssocVars['indication_new']['value'] = $dAssocVars['indication_new']['value'][$rIdx];
            $nAssocVars['indication_date']['value'] = $dAssocVars['indication_date']['value'][$rIdx]??$model->get('date');
            $nAssocVars['indication_doc_id']['value'] = $model->get('id');
            $nAssocVars['indication_doc']['value'] = $model->get('name');

            $nomOld = clone $nom;
            $nom->set('assoc_vars', $nAssocVars, true);
            $nom->set('vars', array_values($nAssocVars), true);

            if ($nom->save()) {
                $nomNew = Nomenclatures::searchOne($registry, ['where' => ["n.id = {$deviceId}"], 'model_lang' => $lang]);
                $nomNew->getVars();
                $params_history = array(
                    'action_type' => 'edit',
                    'old_model' => $nomOld,
                    'model' => $nom,
                    'new_model' => $nomNew
                );
                Nomenclatures_History::saveData($registry, $params_history);
            } else {
                $messages->setError($this->i18n('error_device_save_failed', [$deviceName]));
                $messages->insertInSession($this->registry);
                $db->FailTrans();
                break;
            }
        }

        $result = !$db->HasFailedTrans();
        $db->CompleteTrans();

        $registry->set('edit_all', $ea, true);
        $registry->set('get_old_vars', $gov, true);

        if ($result) {
            $messages->setMessage($this->i18n('message_devices_save_success'));
            $messages->insertInSession($this->registry);
            $this->updateAutomationHistory($params, $model, $result);
        }

        return $result;
    }

    /**
     * Update date of indications of a bulding from documents
     *
     * @param array $params
     * @return bool - result of operation
     */
    public function updateBuildingIndicationDate($params)
    {

        // Prepare some basics
        $registry = &$this->registry;
        /**
         * @var $db ADOConnection
         */
        $db = $registry['db'];
        /**
         * @var $messages Messages
         */
        $messages = $registry['messages'];
        $lang = $registry['lang'];
        $model = $params['model'];

        //get documents
        $dAssocVars = $model->getAssocVars();

        try {
            $buildings = $this->_getBuildings($model);
            $allBuildingIDs = [];
            foreach ($buildings as $building) {
                $allBuildingIDs[] = $building->get('id');
            }
            $buildingDevicesCount = $this->_getBuildingsDeviceCount($allBuildingIDs);


        } catch (\Exception $e) {
            $messages->setError($this->i18n('error_building_date_save_failed', ['']));
            $messages->setError($e->getMessage());
            $messages->insertInSession($this->registry);
            return false;
        }

        $gov = $registry->get('get_old_vars');
        $ea = $registry->get('edit_all');
        $registry->set('edit_all', true, true);
        $registry->set('get_old_vars', true, true);

        $messageLinkBase = sprintf(
            '<a target="_blank" href="%s?%s=nomenclatures&nomenclatures=view&view=%%s">"%%s"</a>',
            $registry['config']->getParam('crontab', 'base_host'),
            $registry['module_param']
        );


        $db->StartTrans();
        foreach ($buildings as $building) {
            $buildingId = $building->get('id');
            $buildingName = $building->get('name');
            if (!$this->_checkBuildingDate($building, $model, $buildingDevicesCount)) {
                //do not stop saving the status, just ignore this building
                $messages->setWarning($this->i18n('warning_building_date_not_saved', [
                    sprintf($messageLinkBase, $buildingId, $buildingName)
                ]));
                continue;
            }
            $building->unsanitize();
            $bAssocVars = $building->getAssocVars(true);

            $buildingOld = clone $building;
            $bAssocVars['indication_date']['value'] = $this->_getBuildingIndicationDate($building, $model)->format('Y-m-d');
            $bAssocVars['indication_doc_id']['value'] = $model->get('id');
            $bAssocVars['indication_doc']['value'] = $model->get('name');
            $building->set('assoc_vars', $bAssocVars, true);
            $building->set('vars', array_values($bAssocVars), true);

            if ($building->save()) {
                $buildingNew = Nomenclatures::searchOne($registry, [
                    'where' => ["n.id = {$buildingId}"], 'model_lang' => $lang
                ]);
                $buildingNew->getVars();
                $params_history = array(
                    'action_type' => 'edit',
                    'old_model' => $buildingOld,
                    'model' => $building,
                    'new_model' => $buildingNew
                );
                Nomenclatures_History::saveData($registry, $params_history);

                //report successful change of date
                $messages->setMessage($this->i18n('message_building_date_save_success', [
                    sprintf($messageLinkBase, $buildingId, $buildingName)
                ]));
            } else {
                //remove the messages
                $messages->unset_vars('messages');
                $messages->setError($this->i18n('error_building_date_save_failed', [
                    sprintf($messageLinkBase, $buildingId, $buildingName)
                ]));
                $db->FailTrans();
                break;
            }

        }

        $result = !$db->HasFailedTrans();
        $db->CompleteTrans();

        $registry->set('edit_all', $ea, true);
        $registry->set('get_old_vars', $gov, true);

        $messages->insertInSession($this->registry);
        if ($result) {
            $this->updateAutomationHistory($params, $model, $result);
        }

        return $result;
    }

    /**
     * Retrieve all buildings' device count
     *
     * @param array $buildingIds
     * @return array Associative array K<buildingId> => V<DeviceCount>
     */
    private function _getBuildingsDeviceCount(array $buildingIds) : array
    {
        $implodedIds = implode(',', $buildingIds);
        $query = <<<SQL
        SELECT objectToBuilding.building, COUNT(n.id)
        FROM nom AS n
         JOIN _fields_meta AS fm ON fm.model = 'Nomenclature' AND n.`type` IN ({$this->settings['nom_types']}) AND fm.`name` = 'to_object_id'
         JOIN nom_cstm AS nc ON fm.id = nc.var_id AND nc.model_id = n.id
         JOIN _fields_meta AS fm1 ON fm1.model = 'Nomenclature' AND fm1.model_type = fm.model_type AND fm1.`name` = 'nom_status'
         JOIN nom_cstm AS nc1 ON fm1.id = nc1.var_id AND nc1.model_id = n.id
         JOIN (SELECT n.id as objectId, nc.`value` as building
               FROM nom AS n
                        JOIN _fields_meta AS fm ON fm.model = 'Nomenclature' AND fm.model_type = n.type AND fm.`name` = 'building_id'
                        JOIN nom_cstm AS nc ON fm.`id` = nc.`var_id` AND nc.model_id = n.id
               WHERE nc.`value` IN ({$implodedIds})) as objectToBuilding ON objectToBuilding.objectId = nc.`value`
        WHERE nc1.`value` IN ('1', '2')
        GROUP BY objectToBuilding.building
        SQL;
        return $this->registry['db']->getAssoc($query);
    }

    /**
     * Check whether the date of a building should be changed depending on the percent of devices that have been reported
     *
     * @param Nomenclature $building
     * @param Document $document
     * @param array $buildingDevicesCount
     * @return bool
     */
    private function _checkBuildingDate(Nomenclature $building, Document $document, array $buildingDevicesCount) : bool {
        $buildingId = $building->get('id');
        if (!array_key_exists($buildingId, $buildingDevicesCount)) {
            return false;
        }
        $allDeviceCount = $buildingDevicesCount[$buildingId];
        $devicesCount = count($this->_getDevicesForThisBuildingInTheDocument($document, $building));

        //the devices in the document should be at least 80%
        if (($devicesCount / $allDeviceCount) * 100 < 80) {
            return false;
        }

        return true;
    }

    /**
     * Get date for building in specific conditions
     *
     * @param Nomenclature $building
     * @param Document $document - document of type 5 or 6
     * @return \DateTime
     */
    private function _getBuildingIndicationDate(Nomenclature $building, Document $document):\DateTime {
        //for document 5 take the date from "date" field
        if ($document->get('type') == 5) {
            return new DateTime($document->get('date'));
        }


        // document is type 6
        $deviceIds = $this->_getDevicesForThisBuildingInTheDocument($document, $building);
        $deviceIdsCSV = implode(',', $deviceIds);
        $devicesCount = count($deviceIds);

        //get all the devices for this building (active, not deleted and with nom_status 1,2)
        $query = "SELECT id FROM " . DB_TABLE_NOMENCLATURES . " WHERE id IN ({$deviceIdsCSV})";
        $heatDevicesIds = $this->registry['db']->getCol("{$query} AND type IN (19,20)");
        $heatDevicesCount = count($heatDevicesIds);
        $waterDevicesIds = $this->registry['db']->getCol("{$query} AND type IN (21)");
        $waterDevicesCount = count($waterDevicesIds);

        $indicationDateByDeviceId = array_combine(
            $document->getAssocVars()['device_id']['value'],
            $document->getAssocVars()['indication_date']['value'],
        );
        if (($waterDevicesCount/$devicesCount)*100 >= 60 ||
            ($heatDevicesCount/$devicesCount)*100 < 60 && $waterDevicesCount > $heatDevicesCount) {
            //get the date from water devices
            $dateStr = '';
            foreach($deviceIds as $deviceId) {
                if (in_array($deviceId, $waterDevicesIds) && array_key_exists($deviceId, $indicationDateByDeviceId) && $indicationDateByDeviceId[$deviceId] > $dateStr) {
                    $dateStr = $indicationDateByDeviceId[$deviceId];
                }
            }
        } else {
            //get the date from heat devices
            $dateStr = '';
            foreach($deviceIds as $deviceId) {
                if (in_array($deviceId, $heatDevicesIds) && array_key_exists($deviceId, $indicationDateByDeviceId) && $indicationDateByDeviceId[$deviceId] > $dateStr) {
                    $dateStr = $indicationDateByDeviceId[$deviceId];
                }
            }
        }

        return new DateTime($dateStr);
    }

    /**
     * Gets buildings for the indication document
     *
     * @param Document $document
     * @return array $buildings - array of nomenclature Builging (type 9)
     * @throws \Exception - if no accurate buildings found
     */
    private function _getBuildings(Document $document):array {
        if ($document->get('type') == 5) {
            //the building is within the document
            $buildingIds = array_filter(array_unique($document->getVarValue('building_id')));
            if (empty($buildingIds)) {
                throw new \Exception($this->i18n('error_no_building_specified'));
            }
            $buildingIdsCSV = implode(',', $buildingIds);
            $buildings = Nomenclatures::search($this->registry, [
                'where' => [
                    "n.id IN ({$buildingIdsCSV})"
                ], 'model_lang' => $document->get('model_lang')
            ]);
            if (empty($buildings)) {
                throw new \Exception($this->i18n('error_no_building_found'));
            }
            return $buildings;
        }

        if ($document->get('type') == 6) {
            //devices are related to objects and each object is related to a building
            //get devices ids in the document
            $deviceIds = array_filter($document->getVarValue('device_id'));
            if (empty($deviceIds)) {
                throw new \Exception($this->i18n('error_no_building_found'));
            }
            $deviceIdsCSV = implode(',', $deviceIds);

            //get building ids via object ids
            //nom_status should be 1 or 2
            $query = "SELECT n3.id as buildingId
            -- n.id as deviceId, nc1.value as deviceStatus, n2.id as objectId, n3.id as buildingId
            FROM " . DB_TABLE_NOMENCLATURES . " as n
            JOIN " . DB_TABLE_FIELDS_META . " as fm1
                ON n.id IN ({$deviceIdsCSV})
                AND n.type IN ({$this->settings['nom_types']})
                AND fm1.model='Nomenclature'
                AND fm1.model_type IN ({$this->settings['nom_types']})
                AND n.active=1
                AND n.deleted=0
                AND fm1.name='nom_status'
            JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " as nc1
              on n.id=nc1.model_id AND nc1.var_id=fm1.id AND nc1.num=1 AND nc1.value IN (1,2)
            JOIN " . DB_TABLE_FIELDS_META . " as fm2
                ON fm2.model='Nomenclature'
                AND fm2.model_type IN ({$this->settings['nom_types']})
                AND fm2.name='to_object_id'
            JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " as nc2
              on nc2.var_id=fm2.id AND nc2.model_id=nc1.model_id AND nc2.num=1
            JOIN " . DB_TABLE_NOMENCLATURES . " as n2
              ON n2.id=nc2.value
                 AND n2.active=1
                 AND n2.deleted=0
            JOIN " . DB_TABLE_FIELDS_META . " as fm3
                ON fm3.model='Nomenclature'
                AND fm3.name='building_id'
            JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " as nc3
              on nc3.var_id=fm3.id AND nc2.value=nc3.model_id AND nc3.num=1
            JOIN " . DB_TABLE_NOMENCLATURES . " as n3
              ON n3.id=nc3.value
                 AND n3.active=1
                 AND n3.deleted=0
            GROUP BY buildingId
            ";
            $buildingIds = $this->registry['db']->getCol($query);

            if (empty($buildingIds)) {
                throw new \Exception($this->i18n('error_no_building_found'));
            }
            $buildingIdCSV = implode(',', $buildingIds);
            $buildings = Nomenclatures::search($this->registry, [
                'where' => [
                    "n.id IN ({$buildingIdCSV})"
                ], 'model_lang' => $document->get('model_lang')
            ]);
            if (empty($buildings)) {
                throw new \Exception($this->i18n('error_no_building_found'));
            }
            return $buildings;
        }

        return array();
    }

    /**
     * Get devices ids for certain building
     * @param Document $document
     * @param Nomenclature $building
     * @return array
     */
    public function _getDevicesForThisBuildingInTheDocument(Document $document, Nomenclature $building): array
    {
        $buildingId = $building->get('id');

        //get devices for this building in the document
        $deviceIds = $document->getVarValue('device_id');
        $deviceIdsCSV = implode(',', array_filter($deviceIds));
        $query = "SELECT n.id as deviceId
            -- n.id as deviceId, nc1.value as deviceStatus, n2.id as objectId, n3.id as buildingId
            FROM " . DB_TABLE_NOMENCLATURES . " as n
            JOIN " . DB_TABLE_FIELDS_META . " as fm1
                ON n.id IN ({$deviceIdsCSV})
                AND n.type IN ({$this->settings['nom_types']})
                AND fm1.model='Nomenclature'
                AND fm1.model_type IN ({$this->settings['nom_types']})
                AND n.active=1
                AND n.deleted=0
                AND fm1.name='nom_status'
            JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " as nc1
              on n.id=nc1.model_id AND nc1.var_id=fm1.id AND nc1.num=1 AND nc1.value IN (1,2)
            JOIN " . DB_TABLE_FIELDS_META . " as fm2
                ON fm2.model='Nomenclature'
                AND fm2.model_type IN ({$this->settings['nom_types']})
                AND fm2.name='to_object_id'
            JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " as nc2
              on nc2.var_id=fm2.id AND nc2.model_id=nc1.model_id AND nc2.num=1
            JOIN " . DB_TABLE_NOMENCLATURES . " as n2
              ON n2.id=nc2.value
                 AND n2.active=1
                 AND n2.deleted=0
            JOIN " . DB_TABLE_FIELDS_META . " as fm3
                ON fm3.model='Nomenclature'
                AND fm3.name='building_id'
            JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " as nc3
              on nc3.var_id=fm3.id AND nc2.value=nc3.model_id AND nc3.num=1
            JOIN " . DB_TABLE_NOMENCLATURES . " as n3
              ON n3.id=nc3.value
                 AND n3.active=1
                 AND n3.deleted=0
            WHERE n3.id={$buildingId}
            ";
        return $this->registry['db']->getCol($query);
    }

    /**
     * Function to inform users for the upcoming device mantanance
     *
     * @param array $params
     * @return bool - result of operation
     */
    public function upcomingDeviceMaintanance($params) {
        // Prepare some basics
        $registry = &$this->registry;
        $db       = $registry['db'];

        // get the settings for months
        $months_in_advance = array_filter(preg_split('/\s*,\s*/', $this->settings['months_in_advance']));
        $primary_devices = array_filter(preg_split('/\s*,\s*/', $this->settings['primary_devices']));
        $secondary_devices = array_filter(preg_split('/\s*,\s*/', $this->settings['secondary_devices']));
        $skip_statuses = array_filter(preg_split('/\s*,\s*/', $this->settings['skip_statuses']));
        $devices_list = array_merge($primary_devices, $secondary_devices);

        $nom_vars = array($this->settings['device_var_date'], $this->settings['device_var_status'], $this->settings['device_var_station']);

        $sql = 'SELECT `name`, GROUP_CONCAT(id) FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Nomenclature" AND `model_type` IN ("' . implode('","', $devices_list) . '") AND `name` IN ("' . implode('","', $nom_vars) . '") GROUP BY `name`';
        $vars_ids_list = $db->GetAssoc($sql);

        $devices_detailed_list = array();
        foreach ($months_in_advance as $months) {
            $months_count = intval($months)-1;
            $current_date = new DateTime();
            $match_date = $current_date->add(new DateInterval('P' . $months_count . 'M'));

            $sql = 'SELECT n.id, n.type, nti18n.name as type_name, ni18n.name, fo.label as status,
                           ni18n_station.name as station, "' . sprintf($this->i18n('automations_veolia_months_interval'), $months) . '" as month_maintenance' . "\n" .
                   'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                   'INNER JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                   ' ON (n.id=ni18n.parent_id AND ni18n.lang="' . $registry['lang'] . '" AND
                         n.type IN (' . implode(',', $devices_list) . ') AND n.deleted_by=0 AND n.active=1)' . "\n" .
                   'INNER JOIN ' . DB_TABLE_NOMENCLATURES_TYPES_I18N . ' AS nti18n' . "\n" .
                   ' ON (n.type=nti18n.parent_id AND nti18n.lang="' . $registry['lang'] . '")' . "\n" .
                   'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_date' . "\n" .
                   ' ON (n_cstm_date.model_id=n.id AND n_cstm_date.var_id IN (' . (!empty($vars_ids_list[$this->settings['device_var_date']]) ? $vars_ids_list[$this->settings['device_var_date']] : '""') . ')
                         AND DATE_FORMAT(n_cstm_date.value, "%Y-%m")="' . $match_date->format('Y-m') . '")' . "\n" .
                   'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_status' . "\n" .
                   ' ON (n_cstm_status.model_id=n.id AND n_cstm_status.var_id IN (' . (!empty($vars_ids_list[$this->settings['device_var_status']]) ? $vars_ids_list[$this->settings['device_var_status']] : '""') . ')
                         AND n_cstm_status.value NOT IN ("' . (!empty($skip_statuses) ? implode('","', $skip_statuses) : '') . '"))' . "\n" .
                   'INNER JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS fo' . "\n" .
                   ' ON (fo.parent_name="' . $this->settings['device_var_status'] . '" AND fo.option_value=n_cstm_status.value AND fo.lang="' . $registry['lang'] . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_station' . "\n" .
                   ' ON (n_cstm_station.model_id=n.id AND n_cstm_station.var_id IN (' . (!empty($vars_ids_list[$this->settings['device_var_station']]) ? $vars_ids_list[$this->settings['device_var_station']] : '""') . '))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n_station' . "\n" .
                   ' ON (n_cstm_station.value=ni18n_station.parent_id AND ni18n_station.lang="' . $registry['lang'] . '")' . "\n";
            $temp_list = $db->GetAll($sql);

            // reorder the results
            usort($temp_list, function ($a, $b) use ($primary_devices) {
                // Sort by the Child SKU's size (by it's position into the size filter)
                return in_array($a['type'], $primary_devices) ? -1 : 1;
            });

            $devices_detailed_list = array_merge($devices_detailed_list, $temp_list);
        }

        // prepare the email
        $receiver_users = array_filter(preg_split('/\s*,\s*/', $this->settings['email_receiver_users']));
        if (!$receiver_users || empty($devices_detailed_list))
        {
            $this->updateAutomationHistory($params, 0, true);
            return true;
        }

        $sql = 'SELECT u.email, CONCAT(ui18n.firstname, " ", ui18n.lastname) as name'  . "\n" .
               'FROM ' . DB_TABLE_USERS . ' AS u' . "\n" .
               'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
               ' ON (ui18n.parent_id=u.id AND ui18n.lang="' . $this->registry['lang'] . '")' . "\n" .
               'WHERE u.id IN (' . implode(',', $receiver_users) . ')';
        $users = $db->GetAssoc($sql);

        require_once PH_MODULES_DIR . 'emails/models/emails.factory.php';
        $filters_mail = array('where' => array('e.id = ' . $this->settings['email_id']), 'sanitize' => true);
        $email = Emails::searchOne($this->registry, $filters_mail);

        $mail = new Mailer($this->registry);
        $config = $this->registry['config'];
        $mail->template['sender'] = $config->getParam('emails', 'from_email');
        $mail->template['from_name'] = $config->getParam('emails', 'from_name');
        $mail->template['replyto_email'] = $config->getParam('emails', 'replyto_email') ?: $mail->template['from_email'];
        $mail->template['replyto_name'] = $config->getParam('emails', 'replyto_name') ?: $mail->template['from_name'];
        $mail->template['subject'] = $email->get('subject');
        $mail->template['body'] = $email->get('body');

        $mail->template['recipient'] = array_keys($users);
        $mail->template['names_to'] = array_values($users);

        $viewer = new Viewer($registry);
        $viewer->loadCustomI18NFiles(PH_MODULES_DIR . 'automations/plugins/veolia/i18n/' . $registry['lang'] . '/veolia.ini');
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'automations/plugins/veolia/templates/';
        $viewer->template     = '_devices_list.html';
        $viewer->data['device_list'] = $devices_detailed_list;
        $viewer->data['link_base'] = sprintf('%s?%s=nomenclatures&nomenclatures=view&view=', $this->registry['config']->getParam('crontab', 'base_host'), $this->registry['module_param']);

        $mail->placeholder->add('detailed_devices_list', $viewer->fetch());
        $mail->templateName = 'custom_template';
        $mail->send();

        $this->updateAutomationHistory($params, 0, true);
        return true;
    }


    public function syncPsiroDb($params)
    {
        set_time_limit(0);
        $start = time();
        $psiroDb = $this->getPsiroDatabaseConnection();

        // Lower chunks, maybe add to settings
        $step = 50;
        if (!$psiroDb) {
            $this->updateAutomationHistory($params, 0, 0);
            $msg = $this->i18n('error_while_connecting');
            General::log($this->registry, __METHOD__, $msg);
            $this->executionErrors[] = $msg;
            return false;
        }
        $method = $this->settings['import_method'];
        $this->psiroDbSyncSettings = $this->$method();
        $psiroQuery = $this->psiroDbSyncSettings->getPsiroQuery();
        $rowCount = 'SELECT COUNT(*) FROM ' . implode(',', $psiroQuery['from']);

        $sql = 'SELECT ' . implode(",\n", $psiroQuery['select']) . " FROM " . implode(',', $psiroQuery['from']);
        if (isset($psiroQuery['join'])) {
            $rowCount .=  " " . implode("\n", $psiroQuery['join']);
            $sql .=  " " . implode("\n", $psiroQuery['join']);
        }

        if (isset($this->settings['modified_after'])) {
            if ($this->settings['modified_after'] != ' 1') {
                if (empty($this->settings['modified_after'])) {
                    $this->settings['modified_after'] = $this->registry['db']->GetOne(
                        sprintf(
                            'SELECT added FROM %s WHERE parent_id="%d" AND model_id="%d" AND result!=""',
                            DB_TABLE_AUTOMATIONS_HISTORY,
                            $params['id'],
                            '0'
                        )
                    );
                }
                $psiroQuery['where'][] = 'tm.ModifiedDate > \'' . $this->settings['modified_after'] . '\'';
            }
        }

        if (isset($psiroQuery['where'])) {
            $rowCount .= " WHERE " . implode(" AND ", $psiroQuery['where']);
            $sql .= " WHERE " . implode(" AND ", $psiroQuery['where']);
        }

        if (isset($psiroQuery['order'])) {
            $sql .= " ORDER BY " . implode(",", $psiroQuery['order']);
        }

        $limit = 'WITH results AS ( %s ) SELECT %s FROM results WHERE RowNum BETWEEN %s AND %s';
        if (!isset($this->settings['limit'])) {
            $totalRows = $psiroDb->GetOne($rowCount);
        } else {
            $totalRows = $this->settings['limit'];
        }
        $total = intdiv($totalRows, $step);
        $total = $totalRows % $step !== 0 ? $total + 1 : $total;
        $logMsg = sprintf($this->i18n('log_total_rows'), $method, $totalRows, $total);
        if ($this->psiroDbSyncSettings->hasExtraLog()) {
            $extraLogQuery = $this->psiroDbSyncSettings->getExtraLogQuery();
            $extraLog = $psiroDb->GetOne($extraLogQuery['query']);
            $logMsg .= sprintf($this->i18n($extraLogQuery['format']), $extraLog);
        }

        $controller = $this->psiroDbSyncSettings->getController();
        $modelName = $this->psiroDbSyncSettings->getModelName();
        $this->registry->set('action', 'search', true);
        General::log($this->registry, __METHOD__, $logMsg);
        General::log($this->registry, __METHOD__, "STARTED AT " . date('Y-m-d H:i:s:v', $start));
        $i = 0;
        $totalAdded = 0;
        while ($i < $total) {
            $lower = ($i * $step) + 1;
            $upper = ($i + 1) * $step;
            $mainPsiroQuery = sprintf($limit, $sql, $psiroQuery['columns'], $lower, $upper);
            $this->results = $psiroDb->getAssoc($mainPsiroQuery);
            if (empty($this->results)) {
                $i++;
                continue;
            }
            $rowRangeStr = "$lower - $upper";
            if ($this->psiroDbSyncSettings->hasCustomSearch()) {
                $filters = false;
                $keys = array_map(function ($v) {
                    return trim($v);
                }, array_keys($this->results));
                $query = sprintf($this->psiroDbSyncSettings->getCustomModelSearch(), implode($this->psiroDbSyncSettings->getCustomSearchSeparator(), $keys));
                $ids = $this->registry['db']->GetCol($query);
                if (sizeof($ids) > 0) {
                    $filters = [
                        'where' => [
                            $controller::getAlias($params['module'], $params['module']) . '.id IN  (' . implode(',', $ids). ')',
                            $controller::getAlias($params['module'], $params['module']) . '.active IN  (0,1)',
                        ],
                        'sanitize' => false,
                    ];
                }
            } else {
                $filters = $this->prepareFilters($this->results, $this->psiroDbSyncSettings->getExistingModelsSearchFilters());
            }
            if ($filters) {
                $this->processExistingModels($filters, $controller, $params);
            }



            $failed = [];
            $added = 0;
            foreach ($this->results as $id => $psiroRow) {
                $modelProperties = $this->psiroDbSyncSettings->getEmptyProperties();
                if (isset($psiroRow['type'])) {
                    $modelProperties['type'] = $psiroRow['type'];
                }
                $model = new $modelName($this->registry, $modelProperties);

                $res = $this->processResult($psiroRow, $model, $params);
                if (!$res) {
                    $failed[$id] =  var_export($this->registry['messages']->getErrors(), true);
                    $this->registry['messages']->flush();
                } else {
                    unset($this->results[$id]);
                    $totalAdded++;
                }
            }
            $i++;
            if (!empty($failed)) {
                $format = $this->i18n('failed_added_psiro_ids');
                $psiroIDsStr = implode(',', array_keys($failed));
                $this->executionErrors[] = sprintf($format, $rowRangeStr, $psiroIDsStr);
                General::log($this->registry, __METHOD__, sprintf("FAILED ADDING PSIRO CODES: %s, REASON(S): %s", $psiroIDsStr, var_export($failed, true)));
            }
        }
        $end = time();
        $diff = $end - $start;
        $hrs = intdiv($diff, 3600);
        $mins = intdiv($diff, 60) - $hrs*60;
        $seconds = $diff - $mins*60 - $hrs*3600;
        General::log($this->registry, __METHOD__, "ENDED AT " . date('Y-m-d H:i:s:v'));
        General::log($this->registry, __METHOD__, sprintf("DURATION (s): %02d:%02d:%02d, ADDED MODELS: %d", $hrs, $mins, $seconds, $totalAdded));
        $this->updateAutomationHistory($params, 0, 1);
        return true;
    }

    public function addMissingMainDevicesToImport($params)
    {
        $model = $params['model'];
        $assoc = $model->getAssocVars();
        $ACids = array_filter(array_unique($assoc['to_station_id']['value']));
        $db = $this->registry['db'];
        $stationParams = [
            'n' => DB_TABLE_NOMENCLATURES,
            'currIds' => implode(',', $ACids),
        ];
        $missingStationSql = <<<SQL
        SELECT n.id AS id, nc1.`value` AS `to_station_psiro_code`
        FROM {$stationParams['n']} n
        LEFT JOIN _fields_meta fm1 ON fm1.`model_type` = n.`type` AND fm1.`model` = 'Nomenclature' AND fm1.`name` = 'psiro_code'
        LEFT JOIN nom_cstm nc1 ON nc1.`model_id` = n.`id` AND nc1.`num` = 1 AND nc1.`lang` = '' AND nc1.`var_id` = fm1.`id`
        WHERE n.`id` NOT IN ({$stationParams['currIds']}) AND n.`active` = 1 AND n.`deleted_by` = 0 AND n.`type` = 15
        SQL;
        $missingStations = $db->getAssoc($missingStationSql);
        $missingStationIds = array_keys($missingStations);
        $deviceParams = [
          'n' => DB_TABLE_NOMENCLATURES,
          'fm' => DB_TABLE_FIELDS_META,
          'nc' => DB_TABLE_NOMENCLATURES_CSTM,
          'ni' => DB_TABLE_NOMENCLATURES_I18N,
          'acIds' => implode(",", $missingStationIds),
          'lang' => $this->registry->get('lang'),
        ];
        $deviceSql = <<<SQL
        SELECT n.id AS device_id, ni.`name` AS device, nc.`value` AS to_station_id, nc1.`value` AS to_station, nc2.`value` AS device_psiro_code, nc3.`value` AS indication_old, ''  AS indication_new, '' AS indication_date, '' AS indication_deviation
        FROM {$deviceParams['n']} n
        LEFT JOIN {$deviceParams['ni']} ni ON ni.`parent_id` = n.id AND n.`type` IN (16,17,18) AND ni.`lang` = '{$deviceParams['lang']}'
        LEFT JOIN {$deviceParams['fm']} fm ON fm.`model_type` = n.`type` AND fm.`model` = 'Nomenclature' AND fm.`name` = 'to_station_id'
        LEFT JOIN {$deviceParams['nc']} nc ON nc.`model_id` = ni.`parent_id` AND nc.`num` = 1 AND nc.`lang` = '' AND nc.`var_id` = fm.`id`
        LEFT JOIN {$deviceParams['fm']} fm1 ON fm1.`model_type` = n.`type` AND fm1.`model` = 'Nomenclature' AND fm1.`name` = 'to_station'
        LEFT JOIN {$deviceParams['nc']} nc1 ON nc1.`model_id` = ni.`parent_id` AND nc1.`num` = 1 AND nc1.`lang` = '' AND nc1.`var_id` = fm1.`id`
        LEFT JOIN {$deviceParams['fm']} fm2 ON fm2.`model_type` = n.`type` AND fm2.`model` = 'Nomenclature' AND fm2.`name` = 'psiro_code'
        LEFT JOIN {$deviceParams['nc']} nc2 ON nc2.`model_id` = ni.`parent_id` AND nc2.`num` = 1 AND nc2.`lang` = '' AND nc2.`var_id` = fm2.`id`
        LEFT JOIN {$deviceParams['fm']} fm3 ON fm3.`model_type` = n.`type` AND fm3.`model` = 'Nomenclature' AND fm3.`name` = 'indication_new'
        LEFT JOIN {$deviceParams['nc']} nc3 ON nc3.`model_id` = ni.`parent_id` AND nc3.`num` = 1 AND nc3.`lang` = '' AND nc3.`var_id` = fm3.`id`
        WHERE n.active = 1 AND nc.`value` IN ({$deviceParams['acIds']}) AND n.deleted_by = 0
        ORDER BY nc.`value` ASC
        SQL;
        $results = $db->getAll($deviceSql);
        foreach ($results as $result) {
            $stationPsiro = $missingStations[$result['to_station_id']] ?? '';
            $assoc['to_station_psiro_code']['value'][] = $stationPsiro;
            foreach ($result as $key => $value) {
                $assoc[$key]['value'][] = $value;
            }
        }
        $db->StartTrans();
        $oldDoc = clone $model;
        $model->unsanitize();
        $model->set('vars', array_values($assoc), true);
        $model->set('assoc_vars', $assoc, true);
        if ($model->save()) {
            $newDoc = Documents::searchOne($this->registry, [
                'skip_permissions_check' => true,
                'where' => [
                    "d.id = {$params['model_id']}"
                ],
                'model_lang' => $this->registry->get('lang')
            ]);
            $newDoc->getVars();
            $historyParams = array(
                'action_type' => 'edit',
                'old_model' => $oldDoc,
                'model' => $model,
                'new_model' => $newDoc
            );
            Documents_History::saveData($this->registry, $historyParams);

        } else {
            $db->FailTrans();
        }
        $result = !$db->HasFailedTrans();
        $db->CompleteTrans();
        $this->updateAutomationHistory($params, $model, $result);
        return $result;
    }

    public function addRadiatorsFromReport($params)
    {
        $docParams = [
            'd' => DB_TABLE_DOCUMENTS,
            'fm' => DB_TABLE_FIELDS_META,
            'dc' => DB_TABLE_DOCUMENTS_CSTM,
            'ah' => DB_TABLE_AUTOMATIONS_HISTORY,
            'type' => $params['start_model_type'],
            'acceptValue' => '1',
            'automation_id' => $params['id'],

        ];

        $sql = <<<SQL
        SELECT d.id,  GROUP_CONCAT(dc.num)
        FROM {$docParams['d']} d
        LEFT JOIN {$docParams['fm']} fm ON fm.`model` = 'Document' AND fm.`model_type` = d.`type` AND fm.`name` = 'device_radiator_add'
        LEFT JOIN {$docParams['dc']} dc ON dc.model_id = d.id AND dc.var_id = fm.id AND dc.lang = ''
        WHERE d.`type` = {$docParams['type']}
          AND d.active = 1
          AND d.deleted_by = 0
          AND d.`status` = 'opened'
          AND d.id NOT IN (
          SELECT ah.model_id
          FROM {$docParams['ah']} ah
          WHERE ah.parent_id = {$docParams['automation_id']})
          AND dc.`value` = '{$docParams['acceptValue']}'
        GROUP BY d.id
        SQL;
        $db = $this->registry['db'];
        $documentIdsNRows = $db->GetAssoc($sql);
        if (empty($documentIdsNRows)) {
            return false;
        }

        $allDocs = Documents::search($this->registry,
            [
                'skip_permission_check' => true,
                'where' => [
                    'd.id IN (' . implode(',', array_keys($documentIdsNRows)) . ')',
                    ]
            ]);
        $nomVarsToDocGroupingVars = [
            'device' => 'device',
            'device_id' => 'device_id',
            'second_valve_available' => 'second_valve_available',
            'heating_element_type_name' => 'heating_element_type_name',
            'heating_element_type_id' => 'heating_element_type_id',
            'heating_body_type_name' => 'heating_body_type_name',
            'heating_body_type_id' => 'heating_body_type_id',
            'radiator_catalog_name' => 'radiator_catalog_name',
            'radiator_catalog' => 'radiator_catalog',
            'heating_element_description' => 'radiator_catalog_name',
            'depth' => 'depth',
            'overall_height' => 'overall_height',
            'width' => 'width',
            'gliders_number' => 'gliders_number',
            'gliders_count' => 'gliders_count',
            'gliders_structure' => 'gliders_structure',
            'structure' => 'structure',
            'length' => 'length',
            'double_body' => 'double_body',
            'pipe_diameter' => 'pipe_diameter',
            'pipes_in_parallel' => 'pipes_in_parallel',
            'power_heating_capacity_per_unit' => 'power_heating_capacity_per_unit',
            'power_heating_capacity' => 'power_heating_capacity',
        ];
        foreach ($allDocs as $doc) {
            $doc->unsanitize();
            $docAssoc = $doc->getAssocVars();
            $docId = $doc->get('id');
            $rows = explode(',', $documentIdsNRows[$docId]);
            $stationDetails = $this->_getStationDetailsForReport($docAssoc['building_id']['value'])[0] ?? ['to_station_id' => '', 'to_station' => ''];
            foreach ($rows as $row) {
                $roomId = $docAssoc['room_id']['value'][$row];
                $roomName = $docAssoc['room']['value'][$row];
                if (empty($roomId)) {
                    $roomId = $docAssoc['room_list_id']['value'][$row];
                    $roomName = $docAssoc['room_list']['value'][$row];
                    if (empty($roomId)) {
                        continue;
                    }
                }

                $this->_deactivateExistingRadiator($docAssoc['to_object_id']['value'], $roomId);
                $properties = [
                    'type' => '22',
                    'code' => '',
                    'name' => $roomName . ' ' . $docAssoc['heating_element_type_name']['value'][$row],
                    'group' => 1,
                    'active' => 1,
                    'model_lang' => $this->registry['lang']
                ];
                $radiator = new Nomenclature($this->registry, $properties);
                $radiator->getVars();
                $nomAssoc = $radiator->getAssocVars();
                $nomAssoc['to_object']['value'] = $docAssoc['to_object']['value'];
                $nomAssoc['to_object_id']['value'] = $docAssoc['to_object_id']['value'];
                $nomAssoc['to_station']['value'] = $stationDetails['to_station'];
                $nomAssoc['to_station_id']['value'] = $stationDetails['to_station_id'];
                $nomAssoc['room']['value'] = $roomName;
                $nomAssoc['room_id']['value'] = $roomId;
                $nomAssoc['nom_status']['value'] = '1';
                foreach ($nomVarsToDocGroupingVars as $nomVar => $docVar) {
                    $nomAssoc[$nomVar]['value'] = $docAssoc[$docVar]['value'][$row];
                }
                $radiator->set('vars', array_values($nomAssoc), true);
                $radiator->set('assoc_vars', $nomAssoc, true);
                $db->StartTrans();
                if ($radiator->save()) {
                    $filters = [
                        'where' => [
                            'n.id = ' . $radiator->get('id')
                        ],
                        'model_lang' => $radiator->get('model_lang')
                    ];
                    $radiator = Nomenclatures::searchOne($this->registry, $filters);
                    $old_device = new Nomenclature($this->registry);
                    $old_device->set('type', $radiator->get('type'), true);

                    $radiator->getVars();
                    $old_device->getVars();

                    $historyParams = [
                        'action_type' => 'add',
                        'old_model'   => $old_device,
                        'model'       => $radiator,
                        'new_model'   => $radiator
                    ];
                    Nomenclatures_History::saveData($this->registry, $historyParams);
                    $this->executeActionAutomations($old_device, $radiator, 'add');
                    $docAssoc['heating']['value'][$row] = $radiator->get('name');
                    $docAssoc['heating_id']['value'][$row] = $radiator->get('id');
                } else {
                    $db->FailTrans();
                    General::log($this->registry, __METHOD__, "FAILED ADDING RADIATOR FROM ROW {$row} FOR DOCUMENT ID {$docId}");
                }
                $db->CompleteTrans();
            }
            $oldDoc = clone $doc;
            $doc->set('assoc_vars', $docAssoc, true);
            $doc->set('vars', array_values($docAssoc), true);
            $db->StartTrans();
            if ($doc->save()) {
                $filters = [
                    'where' => [
                        'd.id = ' . $doc->get('id')
                    ],
                    'model_lang' => $doc->get('model_lang')
                ];
                $currDoc = Documents::searchOne($this->registry, $filters);
                $currDoc->loadI18NFiles(PH_MODULES_DIR . 'documents/i18n/' . $this->registry['lang'] . '/documents.ini');
                $currDoc->set('status', 'closed', true);
                $currDoc->setStatus();
                $historyParams = [
                    'action_type' => 'edit',
                    'old_model'   => $oldDoc,
                    'model'       => $doc,
                    'new_model'   => $currDoc
                ];
                Documents_History::saveData($this->registry, $historyParams);
            } else {
                $db->FailTrans();
                General::log($this->registry, __METHOD__, "FAILED SAVING DOCUMENT ID {$docId}");

            }
            $db->CompleteTrans();
            $this->updateAutomationHistory($params, $doc, 1);
        }
        return true;
    }

    public function _deactivateExistingRadiator($objectId, $roomId)
    {
        $search = Nomenclatures::search($this->registry, [
            'skip_permissions_check' => true,
            'where' => [
                'n.type = 22',
                'n.active = 1',
                sprintf("a__to_object_id = '%s'", $objectId),
                sprintf("a__room_id = '%s'", $roomId),
            ],
        ]);
        if (empty($search)) {
            return true;
        }
        foreach ($search as $rad) {
            $rad->unsanitize();
            $ids = [$rad->get('id')];
            $radAssoc = $rad->getAssocVars();
            $oldRad = clone $rad;
            $radAssoc['nom_status']['value'] = '4';
            $rad->set('vars', array_values($radAssoc), true);
            $rad->set('assoc_vars', $radAssoc, true);
            $rad->save();
            $currRad = Nomenclatures::searchOne($this->registry, [
                'skip_permissions_check' => true,
                'where' => [
                    'n.type = 22',
                    'n.id = ' . $rad->get('id'),
                ],
            ]);
            Nomenclatures_History::saveData($this->registry, [
                'action_type' => 'edit',
                'old_model' => $oldRad,
                'model' => $rad,
                'new_model'   => $currRad
            ]);
            $oldRad = clone $currRad;
            Nomenclatures::changeStatus($this->registry, $ids, 'deactivate');
            $currRad = Nomenclatures::searchOne($this->registry, [
                'skip_permissions_check' => true,
                'where' => [
                    'n.type = 22',
                    'n.id = ' . $rad->get('id'),
                ],
            ]);
            Nomenclatures_History::saveData($this->registry, [
                'action_type' => 'deactivate',
                'old_model' => $oldRad,
                'model' => $rad,
                'new_model'   => $currRad
            ]);
        }
        return true;

    }

    private function _getStationDetailsForReport($buildingID)
    {
        $sql = <<<SQL
        SELECT n.id as to_station_id, ni.`name` as to_station
        FROM nom n
        JOIN nom_i18n ni ON n.id = ni.parent_id
        LEFT JOIN _fields_meta fm ON fm.`model` = 'Nomenclature' AND fm.`model_type` = n.`type` AND fm.`name` = 'building_id'
        LEFT JOIN nom_cstm nc ON nc.var_id = fm.id AND nc.model_id = n.id AND nc.num = 1 AND nc.lang = ''
        WHERE n.`type` = 15 AND nc.`value` = '{$buildingID}';
        SQL;
        return $this->registry['db']->GetAll($sql);
    }


    public function addRoomsToObject($params)
    {

        $docParams = [
            'd' => DB_TABLE_DOCUMENTS,
            'fm' => DB_TABLE_FIELDS_META,
            'dc' => DB_TABLE_DOCUMENTS_CSTM,
            'ah' => DB_TABLE_AUTOMATIONS_HISTORY,
            'type' => $params['start_model_type'],
            'automation_id' => $params['id'],

        ];

        $sql = <<<SQL
        SELECT d.id, dc1.`value` AS to_object_id
        FROM {$docParams['d']} d
        LEFT JOIN {$docParams['fm']} fm1 ON fm1.`model` = 'Document' AND fm1.`model_type` = d.`type` AND fm1.`name` = 'to_object_id'
        LEFT JOIN {$docParams['dc']} dc1 ON dc1.model_id = d.id AND dc1.var_id = fm1.id AND dc1.lang = ''
        WHERE d.`type` = {$docParams['type']}
          AND d.active = 1
          AND d.deleted_by = 0
          AND d.`status` = 'closed'
          AND d.id NOT IN (
          SELECT ah.model_id
          FROM {$docParams['ah']} ah
          WHERE ah.parent_id = {$docParams['automation_id']})
        SQL;
        $db = $this->registry['db'];
        $documentIdsNRows = $db->GetAssoc($sql);

        if (empty($documentIdsNRows)) {
            General::log($this->registry, __METHOD__, "NO REPORTS FOUND");
            $this->updateAutomationHistory($params, 0, 1);
            return true;
        }

        $allDocs = Documents::search($this->registry,
            [
                'skip_permission_check' => true,
                'where' => [
                    'd.id IN (' . implode(',', array_keys($documentIdsNRows)) . ')',
                ]
            ]);
        $allObjects = $this->_getAllObjectsAssoc(array_values($documentIdsNRows));
        foreach ($allDocs as $doc) {
            $assoc = $doc->getAssocVars();
            $object = $allObjects[$assoc['to_object_id']['value']] ?? false;
            if (!$object) {
                continue;
            }
            $object->unsanitize();
            $filtered = array_filter($assoc['device_radiator_add']['value'], function($v) {
                return $v === '1';
            });

            $rows = array_keys($filtered);
            $rooms = array_filter($assoc['room_id']['value'], function($v, $k) use ($rows){
                return in_array($k, $rows) && $v !== '';
            }, ARRAY_FILTER_USE_BOTH);
            $roomList = array_filter($assoc['room_list_id']['value'], function($v, $k) use ($rows){
                return in_array($k, $rows) && $v !== '';
            }, ARRAY_FILTER_USE_BOTH);
            $res = array_unique($roomList + $rooms);
            $objAssoc = $object->getAssocVars();
            $oldObject = clone $object;
            $invNameDiff = array_diff($res, $objAssoc['inv_name']['value']);
            if (empty($invNameDiff)) {
                General::log($this->registry, __METHOD__, "NO ROOMS TO ADD FOR OBJECT ID {$object->get('id')}");;
                $this->updateAutomationHistory($params, $doc, 1);
                continue;
            }
            foreach($invNameDiff as $name) {
                $objAssoc['inv_name']['value'][] = $name;
            }
            $object->set('vars', array_values($objAssoc), true);
            $object->set('assoc_vars', $objAssoc, true);
            $db->StartTrans();
            if ($object->save()) {
                $filters = [
                    'where' => [
                        'n.id = ' . $object->get('id')
                    ],
                    'model_lang' => $object->get('model_lang')
                ];
                $currObj = Nomenclatures::searchOne($this->registry, $filters);
                $currObj->getVars();
                $historyParams = [
                    'action_type' => 'edit',
                    'old_model'   => $oldObject,
                    'model'       => $object,
                    'new_model'   => $currObj
                ];
                Nomenclatures_History::saveData($this->registry, $historyParams);
            } else {
                $db->FailTrans();
                General::log($this->registry, __METHOD__, "FAILED SAVING OBJECT ID {$object->get('id')}");

            }
            $result = !$db->HasFailedTrans();
            $db->CompleteTrans();
            $this->updateAutomationHistory($params, $doc, $result);
        }
        $this->updateAutomationHistory($params, 0, 1);
        return true;
    }

    private function _getAllObjectsAssoc(array $objectIds)
    {
        $noms = Nomenclatures::search($this->registry,
            [
                'skip_permission_check' => true,
                'where' => [
                    'n.id IN (' . implode(',', $objectIds) . ')',
                ]
            ]);
        $res = [];
        foreach ($noms as $nom) {
            $res[$nom->get('id')] = $nom;
        }
        return $res;
    }
}
