payment_automation_distributed_successfuly = Payment distributed successfully to the related repayment plan!
error_document_updated_unsuccessfully = Penalty interests for document with id %d were not corrected because an error occurred!
error_payments_not_distributed_successfuly = An error occurred while distributing the payment for repayment schedule <a href="%s" target="_blank">'%s'</a>! Please, contact nZoom support!
error_no_defined_rows_in_related_contract = An error occurred while distributing the payment! Missing data for payment in repayment p <a href="%s" target="_blank">'%s'</a>!

creditins_adddress_district = district
creditins_adddress_street = street
creditins_adddress_entrance = entrance
creditins_adddress_floor = floor
creditins_adddress_apartment = apartment

message_creditins_create_reasons_incomes_reason_created = New incomes reason (%s) <a href="%s" target="_blank">'%s'</a>, related with the current proceeding, was added successfully!
message_creditins_create_reasons_expense_reason_created = New expenses reason (%s) <a href="%s" target="_blank">'%s'</a>, related with the current proceeding, was added successfully!
message_creditins_correct_reasons_incomes_reason_created = Correction for the incomes reason <a href="%s" target="_blank">'%s'</a> was added successfully!
message_creditins_correct_reasons_expense_reason_created = Correction for the expenses reason <a href="%s" target="_blank">'%s'</a> was added successfully!

error_import_contracts_customer_import_failed = Customer with UCN %s (KreditIns ID %s) import FAILED because an error occurred!
error_import_contracts_customer_import_data_save_failed = Customer with UCN %s (KreditIns ID %s) import FAILED because an error occurred when editing its additional data!
error_import_contracts_missing_customer = Contract num %s import FAILED because its customer is missing in nZoom (KreditIns customer ID: %s)!
error_import_contracts_documents_add_failed = Contract num %s import FAILED because an error occurred while trying to add the contract!
error_import_contracts_documents_gt2_save_failed = Contract num %s import FAILED because an error occurred while trying to save the contract GT2 table!
error_import_contracts_documents_status_change_failed = Contract num %s import FAILED because an error occurred while changing contract status!
error_import_contracts_documents_tag_set_failed = Contract num %s import FAILED because an error occurred while changing contract tags!
error_import_contracts_documents_incomes_reason_add_failed = Contract num %s import FAILED because an error occurred while creating the related incomes reason!
error_import_contracts_documents_incomes_reason_create_relation_failed = Contract num %s import FAILED because an error occurred creating the relation between the incomes reason and the contract!
error_import_contracts_customer_tag_not_updated = Contract num %s import FAILED because an error occurred while changing the tags of the related customer!
error_import_contracts_customer_not_updated_workplace = Contract num %s import FAILED because an error occurred while changing the current working place of the customer!
error_change_contract_to_proceeding_changing_related_contract_status = An error occurred while changing status of the contract related with the proceeding!
error_change_contract_to_proceeding_issue_correct_document = An error occurred while issueing a correction for the reason related with the contract!
error_creditins_create_reasons_incomes_reason_failed = Adding incomes reason for '%s' (%s) failed! Please, contact the nZoom support!
error_creditins_create_reasons_expense_reason_failed = Adding expenses reason for '%s' (%s) failed! Please, contact the nZoom support!
error_creditins_correct_reasons_incomes_reason_failed = Adding correction for incomes reason <a href="%s" target="_blank">'%s'</a> failed! Please, contact the nZoom support!
error_creditins_correct_reasons_expense_reason_failed = Adding correction for expenses reason <a href="%s" target="_blank">'%s'</a> failed! Please, contact the nZoom support!
error_creditins_correct_incomes_reason_failed_overpay = The field '%s' cannot be changed with the selected value because this will make the related incomes reason <a href="%s" target="_blank">'%s'</a> to be overpaid!
error_creditins_correct_expenses_reason_failed_overpay = The field '%s' cannot be changed with the selected value because this will make the related expenses reason <a href="%s" target="_blank">'%s'</a> to be overpaid!
error_creditins_loan_contract_cannot_be_annulled = Loan contract cannot be annulled because there are distributed payments!
error_creditins_loan_contract_incomes_reason_annulled_failed = Error occurred while annulling the incomes reason, related to the loan contract!

error_add_incomes_reason_failed = Adding incomes reason, related to the contract has failed! Please, contact the nZoom support!
error_create_reason_relation_failed = Creating relation between the incomes reason and the loan contract has failed! Please, contact the nZoom support!
error_loan_contract_status_failed = Changing the status of the loan contract has failed! Please, contact the nZoom support!
error_loan_contract_add_failed = Adding loan contract has failed! Please, contact the nZoom support!
error_contract_tag_failed = Tag loan contract failed! Please, contact the nZoom support!
error_customer_data_change_failed = Editing client data has failed! Please, contact nZoom support!

success_payments_proceedings_distribution = Successfully distributed payment to <a href="%s" target="_blank">Proceeding against %s</a>!
error_proceeding_payments_not_distributed_successfuly = Error occured while distributing payment to <a href="%s" target="_blank">Proceeding against %s</a>! Please, contact the nZoom support!
error_payments_not_distributed_project_status = Error occured while changing the status of <a href="%s" target="_blank">Proceeding against %s</a>! Please, contact the nZoom support!
error_payments_not_distributed_project_edit = Error occured while editing the data of the <a href="%s" target="_blank">Proceeding against %s</a>! Please, contact the nZoom support!
error_payment_is_not_distributed_singular = Payment was not distributed because its sum is greater than the owed amount of the active contract %s! Please correct the data and try again!
error_payment_is_not_distributed_plural = Payment was not distributed because there are more than one active contracts (%s)! Please correct the data <a href="%s" target="_blank">here</a> and try again!
error_payment_tag_undistributed_failed = An error occured when tagging the payment with 'Not distributed' tag! Please, contact the nZoom support!
error_payment_tag_undistributed_remove_failed = An error occured when trying to remove the 'Not distributed' tag! Please, contact the nZoom support!
error_payment_autodistribution_failed = Payment auto distribution failed! Please, contact the nZoom support!
error_payment_is_not_distributed_plural_email = Payment %s was not distributed because there are more than one active loan contracts (%s) for %s! Please correct the contracts and remove the tag 'Undistributed payment'!

error_automation_creditins_payment_already_issued = Date can not be changed - there is an issued payment!
error_automation_creditins_contract_not_active = Date can not be changed - the contract is not active!
error_automation_creditins_contract_already_passed_to_ckr = Date can not be changed - the contract was already sent to CKR!
error_automation_creditins_contract_already_extended = Date can not be changed - the contract is already extended!
error_automation_creditins_please_complete_date = Please, complete 'Issue date'!

warning_payment_is_not_fully_distributed = Payment was not fully distributed because its full amount will overpay the contract %s! Please, correct the contract <a href="%s" target="_blank">here</a>!

automations_creditins_copycreditrequestdatatoclient_missing_required_settings = Missing required settings!
automations_creditins_copycreditrequestdatatoclient_copy_success = Successfully copied data to client %s!
automations_creditins_copycreditrequestdatatoclient_copy_failed = FAILED to copy data to client %s!