<?php

/**
 * Automations from HR plugin (used in multiple installations)
 */
class Human_Resources_Automations_Controller extends Automations_Controller {

    /**
     * Custom validation when adding a document of "Leave request" type
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool         - the result of the operations
     */
    public function validateAddLeaveRequest($params) {
        // Prepare some basics
        $registry = &$this->registry;
        $request  = &$registry['request'];
        $action   = $registry['action'];

        // Prepare the messages object
        $messages = &$registry['messages'];

        // Require settings
        $required_settings = array('free_days_year', 'free_days_type', 'free_days_count', 'free_days_deputy',
                                   'free_days_start_date', 'free_days_end_date', 'leave_request_paid', 'free_days_substatus_disapproved');
        foreach ($required_settings as $setting_name) {
            if (empty($this->settings[$setting_name])) {
                $messages->setError($this->i18n('error_automation_hr_missing_settings'));
                return false;
            }
        }

        $disapprove_statuses = array_filter(preg_split('#\s*,\s*#', $this->settings['free_days_substatus_disapproved']));

        // Get the number of remaining paid days off for the selected year and employee
        $report_name = 'hr_employee_file';
        require_once PH_MODULES_DIR . 'reports/models/reports.factory.php';
        require_once PH_MODULES_DIR . 'reports/plugins/' . $report_name . '/custom.report.query.php';
        //load plugin i18n files
        $i18n_file = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report_name,
            '/i18n/',
            $registry['lang'],
            '/reports.ini'
        );
        $registry['translater']->loadFile($i18n_file);

        // This part of automation should execute only: if we add data and the request is NOT POST
        if (preg_match('#^add(s|portal)?$#', $action) && !$request->isPost()) {
            if (!empty($this->settings['complete_user_employee'])) {
                // set employee of current user as customer in request
                $this->registry['request']->set('customer', $this->registry['originalUser']->get('employee'), 'all', true);

                if ($this->registry['originalUser']->get('employee')) {
                    $paid_days = 0;
                    $reports_results = array();
                    $report = Reports::getReports($registry,
                                                  array('name'     => $report_name,
                                                        'sanitize' => true));
                    if (isset($report[0])) {
                        $report = $report[0];
                        Reports::getReportSettings($registry, $report->get('type'));
                        $filters = array('employee'                     => $this->registry['originalUser']->get('employee'),
                                         'years'                        => date('Y'),
                                         'validation_required_days_off' => true);
                        $report_results = Hr_Employee_File::buildQuery($registry, $filters);

                        if (!empty($report_results['days_off_list'])) {
                            ksort($report_results['days_off_list']);
                            foreach ($report_results['days_off_list'] as $yr => $yr_data) {
                                if ($yr_data['left_days'] && $yr >= $this->settings['earliest_year_allowed']) {
                                    $this->registry['request']->set($this->settings['free_days_year'], strval($yr), 'get', true);
                                    break;
                                }
                            }
                        }
                    }
                }
            }

            return true;
        } elseif (!(($action == 'add' || $action == 'edit') && $request->isPost())) {
            return true;
        }

        // Get all request vars
        $request_vars = $request->getAll();

        // Get the current document model
        $document = $params['model'];

        // Get the additional vars of the document
        $vars = $document->getAssocVars();

        // Validation: year is required if this is a paid leave request
        $invalid_year = false;
        if ($request_vars[$this->settings['free_days_type']] == $this->settings['leave_request_paid'] && empty($request_vars[$this->settings['free_days_year']])) {
            $messages->setError(sprintf($this->i18n('error_automation_hr_empty_field'), $vars[$this->settings['free_days_year']]['label']), $this->settings['free_days_year']);
            $invalid_year = true;
        }

        if (!empty($this->settings['allow_only_days_off_for_current_year']) && $request_vars[$this->settings['free_days_year']] != date('Y')) {
            $messages->setError($this->i18n('error_automation_hr_validate_add_leave_request_select_current_year'), $this->settings['free_days_year']);
            $invalid_year = true;
        }

        // Validation: days count can not be less than 1
        $invalid_days = false;
        if (!empty($request_vars[$this->settings['free_days_count']]) && Validator::isValidNumber($request_vars[$this->settings['free_days_count']]) && !filter_var($request_vars[$this->settings['free_days_count']], FILTER_VALIDATE_INT, array('options' => array('min_range' => 1)))) {
            $messages->setError(sprintf($this->i18n('error_automation_hr_validate_add_leave_request_days'), $vars[$this->settings['free_days_count']]['label']), $this->settings['free_days_count']);
            $invalid_days = true;
        }

        require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
        if (empty($request_vars[$this->settings['free_days_end_date']]) && !empty($this->settings['complete_days_off_end_date']) &&
            $request_vars[$this->settings['free_days_start_date']] && $request_vars[$this->settings['free_days_count']] > 0) {
            $days_off_end_date = Calendars_Calendar::calcDateOnWorkingDays($registry, $request_vars[$this->settings['free_days_start_date']],
                                                                           ($request_vars[$this->settings['free_days_count']]-1));
            $this->registry['request']->set($this->settings['free_days_end_date'], $days_off_end_date, 'all', true);
            $request_vars[$this->settings['free_days_end_date']] = $days_off_end_date;
        }

        // It this is a paid leave request
        $process_request_type = array($this->settings['leave_request_paid'], $this->settings['leave_request_school']);
        if (!$invalid_year && in_array($request_vars[$this->settings['free_days_type']], $process_request_type)) {
            $error_requests_days = false;
            $paid_days = 0;
            $reports_results = array();
            $report = Reports::getReports($registry, array('name' => $report_name,
                                                           'sanitize' => true));

            // get the current value of customer field from request
            if ($request_vars[$this->settings['free_days_type']] == $this->settings['leave_request_paid']) {
                // get the current value of customer field from request
                if (isset($report[0]) && $request->get('customer')) {
                    $report = $report[0];
                    Reports::getReportSettings($registry, $report->get('type'));
                    $filters = array('employee' => $request->get('customer'),
                                     'years' => $request_vars[$this->settings['free_days_year']],
                                     'validation_required_days_off' => true);
                    if ($document->get('id')) {
                        $filters['exclude_documents'] = array($document->get('id'));
                    }
                    $report_results = Hr_Employee_File::buildQuery($registry, $filters);
                    if (isset($report_results['free_days_left'])) {
                        $paid_days = $report_results['free_days_left'];
                    } else {
                        // Error: incorrect data from the report. Check report settings
                        $messages->setError($this->i18n('error_automation_hr_validate_add_leave_request_missing_report_settings'));
                        $error_requests_days = true;
                    }

                    if (isset($this->settings['require_chronological_selection_of_year']) &&
                        $this->settings['require_chronological_selection_of_year'] &&
                        !empty($report_results['days_off_list'])) {
                        ksort($report_results['days_off_list']);
                        foreach ($report_results['days_off_list'] as $yr => $yr_data) {
                            //IMPORTANT: consider the first year of adding leave request EVER is set in the options as EARLIEST_YEAR_ALLOWED
                            //do not require years with left days before EARLIEST_YEAR_ALLOWED
                            if (($yr_data['left_days'] - $yr_data['requested_days']) > 0 && $yr >= $this->settings['earliest_year_allowed']) {
                                if ($yr != $request_vars[$this->settings['free_days_year']]) {
                                    $messages->setError(sprintf($this->i18n('error_automation_hr_validate_add_leave_request_earliest_year'), $yr));
                                    $error_requests_days = true;
                                }
                                break;
                            }
                        }
                    }
                } elseif (!$report) {
                    $messages->setError($this->i18n('error_automation_hr_validate_add_leave_request_missing_report_settings'));
                }

                // If there are no paid days for the selected year
                if ($paid_days < 1) {
                    // Error: no paid days for the selected year
                    $messages->setError(sprintf($this->i18n('error_automation_hr_validate_add_leave_request_empty_year'),
                                                $request_vars[$this->settings['free_days_year']]),
                                        $this->settings['free_days_year']);
                    $error_requests_days = true;
                } else {
                    // If the selected days are more than the available ones for the selected year
                    if (!$invalid_days && $request_vars[$this->settings['free_days_count']] > $paid_days) {
                        // Error: selected days overflow
                        $messages->setError(sprintf($this->i18n('error_automation_hr_validate_add_leave_request_days_overflow_year'),
                            $vars[$this->settings['free_days_count']]['label'],
                            $request_vars[$this->settings['free_days_count']],
                            $request_vars[$this->settings['free_days_year']],
                            $paid_days),
                            $this->settings['free_days_count']);
                        $error_requests_days = true;
                    }
                }

                // check if the selected employee total request (and still unfinished documents for free days) does not exceed the available days off
                if (!$error_requests_days) {
                    // find unfinished days off documents for the selected year, for the selected employee
                    $sql = 'SELECT d.id, d.full_num, d_cstm_free_days.value as free_days' . "\n" .
                           'FROM ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                           'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as d_cstm_year' . "\n" .
                           '  ON (d_cstm_year.var_id="' . $vars[$this->settings['free_days_year']]['id'] . '" AND d_cstm_year.model_id=d.id AND d_cstm_year.value="' . $request_vars[$this->settings['free_days_year']] . '")' . "\n" .
                           'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_free_days_type' . "\n" .
                           '  ON d_cstm_free_days_type.model_id = d.id' . "\n" .
                           '    AND d_cstm_free_days_type.var_id = \'' . $vars[$this->settings['free_days_type']]['id'] . '\'' . "\n" .
                           '    AND d_cstm_free_days_type.value = \'' . $this->settings['leave_request_paid'] . '\'' . "\n" .
                           'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as d_cstm_free_days' . "\n" .
                           '  ON (d_cstm_free_days.var_id="' . $vars[$this->settings['free_days_count']]['id'] . '" AND d_cstm_free_days.model_id=d.id)' . "\n" .
                           'WHERE d.type="' . $params['start_model_type'] . '" AND d.customer="' . $request_vars['customer'] . '"' . "\n" .
                           '  AND d.active = 1 AND d.deleted_by = 0';
                    if ($document->get('id')) {
                        $sql .= ' AND d.id!=' . $document->get('id');
                    }

                    // always exclude documents in 'closed' status
                    // exclude documents in approved and disapproved substatuses - get them from report settings
                    $exclude_substatuses = '';
                    if (defined('DAYS_OFF_SUBSTATUS_APPROVED') && DAYS_OFF_SUBSTATUS_APPROVED) {
                        $exclude_substatuses = DAYS_OFF_SUBSTATUS_APPROVED;
                    }
                    if (defined('DAYS_OFF_SUBSTATUS_DISAPPROVED') && DAYS_OFF_SUBSTATUS_DISAPPROVED) {
                        $exclude_substatuses .= ($exclude_substatuses ? ',' : '') . DAYS_OFF_SUBSTATUS_DISAPPROVED;
                    }
                    $sql .= ' AND NOT (d.status="closed"' . ($exclude_substatuses ? ' OR d.substatus IN (' . $exclude_substatuses . ')' : '') . ')';

                    $unfinished_free_days_requests = $registry['db']->GetAll($sql);

                    $paid_days_left_including_the_unfinished = $paid_days;
                    $documents_listing = array();
                    foreach ($unfinished_free_days_requests as $unfinished_document) {
                        $paid_days_left_including_the_unfinished = $paid_days_left_including_the_unfinished - $unfinished_document['free_days'];
                        $documents_listing[] = sprintf(
                            '<a href="%s" target="_blank">%s</a>',
                            sprintf('%s?%s=documents&amp;documents=view&amp;view=%s',
                                $_SERVER['PHP_SELF'],
                                $registry['module_param'],
                                $unfinished_document['id']),
                            $unfinished_document['full_num']
                        );
                    }

                    if ($request_vars[$this->settings['free_days_count']] > $paid_days_left_including_the_unfinished) {
                        // ERROR
                        $messages->setError(sprintf($this->i18n('error_automation_hr_validate_add_leave_request_days_requested_unfinished'), implode(', ', $documents_listing)));
                    }
                }
            } elseif ($request_vars[$this->settings['free_days_type']] == $this->settings['leave_request_school']) {
                // get the current value of customer field from request
                if (isset($report[0]) && $request->get('customer')) {
                    $report = $report[0];
                    Reports::getReportSettings($registry, $report->get('type'));
                    $filters = array('employee' => $request->get('customer'),
                                     'years' => $request_vars[$this->settings['free_days_year']],
                                     'validation_required_days_off' => true);
                    $report_results = Hr_Employee_File::buildQuery($registry, $filters);

                    $period_start_date = $request_vars[$this->settings['free_days_start_date']];
                    $period_end_date = $request_vars[$this->settings['free_days_end_date']];
                    $period_found = false;

                    foreach ($report_results['school_days_off'] as $k_sdp => $school_days_periods) {
                        if ($school_days_periods['period_from']<=$period_start_date && $period_end_date<=$school_days_periods['period_to']) {
                            if ($school_days_periods['left_days'] >= $request_vars[$this->settings['free_days_count']]) {
                                $report_results['school_days_off'][$k_sdp]['left_days'] = $report_results['school_days_off'][$k_sdp]['left_days'] - $request_vars[$this->settings['free_days_count']];
                                $period_found = true;
                            } else {
                                $messages->setError(sprintf($this->i18n('error_automation_hr_validate_add_leave_request_left_school_days_already_used'),
                                                            $school_days_periods['left_days'],
                                                            $request_vars[$this->settings['free_days_count']])
                                );
                                $period_found = true;
                            }
                            break;
                        }
                    }

                    if (!$period_found) {
                        $messages->setError($this->i18n('error_automation_hr_validate_add_leave_request_no_school_period_found'));
                    }
                } elseif (!$report) {
                    $messages->setError($this->i18n('error_automation_hr_validate_add_leave_request_missing_report_settings'));
                }
            }
        } else if (!$invalid_year && $request_vars[$this->settings['free_days_type']] == $this->settings['leave_request_school']) {
            $error_school_requests_days = false;
            $school_days = 0;
            $reports_results = array();
            $report = Reports::getReports($registry, array('name' => $report_name,
                                                           'sanitize' => true));
        }

        if ($request_vars[$this->settings['free_days_deputy']]) {
            if ($request_vars[$this->settings['free_days_deputy']] == $request_vars['customer']) {
                $messages->setError(sprintf($this->i18n('error_automation_hr_same_deputy_employee'), $vars[$this->settings['free_days_deputy']]['label']), $this->settings['free_days_deputy']);
            }

            // validate if the deputy is resting in that period
            $sql = 'SELECT d.id' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as d_cstm_start' . "\n" .
                   '  ON (d_cstm_start.var_id="' . $vars[$this->settings['free_days_start_date']]['id'] . '" AND d_cstm_start.model_id=d.id)' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as d_cstm_end' . "\n" .
                   '  ON (d_cstm_end.var_id="' . $vars[$this->settings['free_days_end_date']]['id'] . '" AND d_cstm_end.model_id=d.id)' . "\n" .
                   'WHERE d.deleted_by=0 AND d.active=1 AND d.type="' . $params['start_model_type'] . '"' . "\n" .
                   '      AND d.customer="' . $request_vars[$this->settings['free_days_deputy']] . '" AND d.substatus NOT IN ("' . implode('","', $disapprove_statuses) . '")' . "\n" .
                   '      AND d_cstm_start.value<="' . $request_vars[$this->settings['free_days_end_date']] . '" AND d_cstm_end.value>="' . $request_vars[$this->settings['free_days_start_date']] . '"' . "\n";
            $free_days_deputy = $registry['db']->GetCol($sql);

            if (!empty($free_days_deputy)) {
                $messages->setError($this->i18n('error_automation_hr_validate_add_leave_request_deputy_not_working'), $this->settings['free_days_deputy']);
            }

            // check if the selected employee is not deputy for some other free days request document
            $sql = 'SELECT d.id' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as d_cstm_deputy' . "\n" .
                   '  ON (d_cstm_deputy.var_id="' . $vars[$this->settings['free_days_deputy']]['id'] . '" AND d_cstm_deputy.model_id=d.id AND d_cstm_deputy.value="' . $request_vars['customer'] . '")' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as d_cstm_start' . "\n" .
                   '  ON (d_cstm_start.var_id="' . $vars[$this->settings['free_days_start_date']]['id'] . '" AND d_cstm_start.model_id=d.id)' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as d_cstm_end' . "\n" .
                   '  ON (d_cstm_end.var_id="' . $vars[$this->settings['free_days_end_date']]['id'] . '" AND d_cstm_end.model_id=d.id)' . "\n" .
                   'WHERE d.deleted_by=0 AND d.active=1 AND d.type="' . $params['start_model_type'] . '" AND d.substatus NOT IN ("' . implode('","', $disapprove_statuses) . '")' . "\n" .
                   '      AND d_cstm_start.value<="' . $request_vars[$this->settings['free_days_end_date']] . '" AND d_cstm_end.value>="' . $request_vars[$this->settings['free_days_start_date']] . '"' . "\n";
            $free_days_deputy = $registry['db']->GetCol($sql);

            if (!empty($free_days_deputy)) {
                $messages->setError($this->i18n('error_automation_hr_validate_add_leave_request_employee_assigned_as_deputy'), 'customer');
            }
        }

        // check if another free day document is added for the selected period
        if (!empty($request_vars[$this->settings['free_days_start_date']]) && !empty($request_vars[$this->settings['free_days_end_date']])) {
            $skip_statuses = preg_split('#\s*,\s*#', $this->settings['skip_overlap_days_statuses']);
            $skip_statuses = array_filter($skip_statuses);
            $sql = 'SELECT d.id, d.full_num, dti18n.name as type_name' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' as dti18n' . "\n" .
                   '  ON (dti18n.parent_id="' . $params['start_model_type'] . '" AND dti18n.lang="' . $this->registry['lang'] . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as d_cstm_date_from' . "\n" .
                   '  ON (d_cstm_date_from.var_id="' . $vars[$this->settings['free_days_start_date']]['id'] . '" AND d_cstm_date_from.model_id=d.id)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as d_cstm_to_from' . "\n" .
                   '  ON (d_cstm_to_from.var_id="' . $vars[$this->settings['free_days_end_date']]['id'] . '" AND d_cstm_to_from.model_id=d.id)' . "\n" .
                   'WHERE d.type="' . $params['start_model_type'] . '" AND d.customer="' . $request_vars['customer'] . '" AND' . "\n" .
                   '      d.substatus NOT IN ("' . implode('","', $disapprove_statuses) . '") AND' . "\n" .
                   '      d.deleted = 0 AND d.active = 1 AND' . "\n" .
                   '      d_cstm_date_from.value<="' . $request_vars[$this->settings['free_days_end_date']] . '" AND d_cstm_to_from.value>="' . $request_vars[$this->settings['free_days_start_date']] . '"';
            if ($document->get('id')) {
                $sql .= ' AND d.id!=' . $document->get('id');
            }
            if (!empty($skip_statuses)) {
                $sql .= ' AND CONCAT(d.status, IF(d.substatus, CONCAT("_", d.substatus), "")) NOT IN ("' . implode('","', $skip_statuses) . '")';
            }

            $requests_overlap = $registry['db']->GetAll($sql);
            if ($requests_overlap) {
                foreach ($requests_overlap as $req_ol) {
                    $messages->setError(sprintf($this->i18n('error_automation_hr_validate_add_leave_request_days_overlap_period'),
                            sprintf('%s?%s=documents&amp;documents=view&amp;view=%s',
                                    $_SERVER['PHP_SELF'],
                                    $registry['module_param'],
                                    $req_ol['id']),
                            $req_ol['type_name']
                        )
                    );
                }
                $invalid_days = true;
            }
        }

        // If the days and the period are filled
        if (!$invalid_days && !empty($request_vars[$this->settings['free_days_count']]) && !empty($request_vars[$this->settings['free_days_start_date']]) && !empty($request_vars[$this->settings['free_days_end_date']])) {
            if (!empty($this->settings['ignore_working_days_interval_validation'])) {
                $datetime_start = new DateTime($request_vars[$this->settings['free_days_start_date']]);
                $datetime_end = new DateTime($request_vars[$this->settings['free_days_end_date']]);
                $interval = $datetime_start->diff($datetime_end)->format("%a") + 1;

                if ($interval < $request_vars[$this->settings['free_days_count']]) {
                    // Error: the difference in calendar dates is less than the asked days
                    $messages->setError(sprintf($this->i18n('error_automation_hr_validate_add_leave_request_asked_days_more_than_calendar'),
                                                $vars[$this->settings['free_days_count']]['label'],
                                                $request_vars[$this->settings['free_days_count']],
                                                $interval,
                                                $vars[$this->settings['free_days_start_date']]['label'],
                                                General::strftime('%d.%m.%Y', $request_vars[$this->settings['free_days_start_date']]),
                                                $vars[$this->settings['free_days_end_date']]['label'],
                                                General::strftime('%d.%m.%Y', $request_vars[$this->settings['free_days_end_date']])),
                                                array($this->settings['free_days_count'],
                                                    $this->settings['free_days_start_date'],
                                                    $this->settings['free_days_end_date']));
                }
            } else {
                // If the days are NOT equal to the working days count from the selected period
                $period_working_days = Calendars_Calendar::getWorkingDays($registry, $request_vars[$this->settings['free_days_start_date']], $request_vars[$this->settings['free_days_end_date']]);
                if ($request_vars[$this->settings['free_days_count']] != $period_working_days) {
                    // Error: selected days are different than the period days
                    $messages->setError(sprintf($this->i18n('error_automation_hr_validate_add_leave_request_days_overflow_period'),
                                            $vars[$this->settings['free_days_count']]['label'],
                                            $request_vars[$this->settings['free_days_count']],
                                            $period_working_days,
                                            $vars[$this->settings['free_days_start_date']]['label'],
                                            General::strftime('%d.%m.%Y', $request_vars[$this->settings['free_days_start_date']]),
                                            $vars[$this->settings['free_days_end_date']]['label'],
                                            General::strftime('%d.%m.%Y', $request_vars[$this->settings['free_days_end_date']])),
                                        array($this->settings['free_days_count'],
                                              $this->settings['free_days_start_date'],
                                              $this->settings['free_days_end_date']));
                }
            }
        }

        // If there are any errors
        if ($messages->getErrors()) {
            // Cancel this action
            return false;
        }

        return true;
    }

    /**
     * Add "Leave" type event and reminders when approving some "Leave request" type document
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool         - the result of the operations
     */
    public function notifyLeaveRequest($params) {

        // Execute the automation only if changing the status of the document or changing assignments
        if (!in_array($this->registry['action'], array('setstatus', 'multistatus', 'assign'))) {
            return true;
        }

        // Require settings
        $required_settings = array('document_status_approved', 'document_substatus_approved', 'document_status_unapproved',
                                   'document_substatus_unapproved', 'field_start_date', 'field_end_date', 'event_type_keyword', 'reminder_type',
                                   'reminder_offset_days', 'reminder_offset_hours', 'reminder_offset');
        foreach ($required_settings as $setting_name) {
            if (!isset($this->settings[$setting_name]) || $this->settings[$setting_name] == '') {
                $this->registry['messages']->setError($this->i18n('error_automation_hr_notify_leave_request_settings'));
                $this->registry['messages']->insertInSession($this->registry);
                return true;
            }
        }

        // Prepare some basics
        $registry = &$this->registry;
        $db       = &$registry['db'];
        $messages = &$registry['messages'];
        $msg      = array();
        $settings = $this->settings;
        $document = $params['model'];

        // Unsanitize the document model
        $document_sanitize_after = false;
        if ($document->isSanitized()) {
            $document->unsanitize();
            $document_sanitize_after = true;
        }

        // Start transaction
        $db->StartTrans();

        // Include some required classes
        require_once PH_MODULES_DIR . 'events/models/events.factory.php';
        require_once PH_MODULES_DIR . 'events/models/events.history.php';
        require_once PH_MODULES_DIR . 'events/models/events.audit.php';
        require_once PH_MODULES_DIR . 'events/models/events.types.factory.php';
        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';

        // Load the lang file for events
        $lang_file = PH_MODULES_DIR . 'events/i18n/' . $registry['lang'] . '/events.ini';
        $registry['translater']->loadFile($lang_file);

        // Set flag in registry, so that the assignments are derived from the DB
        $registry->set('getAssignments', true, true);

        $document_substatus_unapproved = preg_split('#\s*,\s*#', $this->settings['document_substatus_unapproved']);
        $document_assoc_vars = $document->getAssocVars();

        // check if there are cancelled free days requests for this employee
        $sql = 'SELECT d.id, d.full_num' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as d_cstm_start' . "\n" .
               '  ON (d_cstm_start.var_id="' . $document_assoc_vars[$this->settings['field_start_date']]['id'] . '"' . "\n" .
               '    AND d_cstm_start.model_id=d.id' . "\n" .
               '    AND d_cstm_start.value<="' . $document_assoc_vars[$this->settings['field_end_date']]['value'] . '")' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as d_cstm_end' . "\n" .
               '  ON (d_cstm_end.var_id="' . $document_assoc_vars[$this->settings['field_end_date']]['id'] . '"' . "\n" .
               '    AND d_cstm_end.model_id=d.id' . "\n" .
               '    AND d_cstm_end.value>="' . $document_assoc_vars[$this->settings['field_start_date']]['value'] . '")' . "\n" .
               'WHERE d.deleted_by=0' . "\n" .
               '  AND d.active=1' . "\n" .
               '  AND d.type="' . $document->get('type') . '"' . "\n" .
               '  AND d.substatus IN ("' . implode('","', $document_substatus_unapproved) . '")' . "\n" .
               '  AND d.customer = \'' . $document->get('customer') . '\'' . "\n" .
               '  AND d.id!="' . $document->get('id') . '"' . "\n";
        $free_days_cancelled = $registry['db']->GetAll($sql);

        if (!empty($free_days_cancelled)) {
            $cancelled_documents_list = array();
            foreach ($free_days_cancelled as $fdc) {
                $url = "{$_SERVER['PHP_SELF']}?{$registry['module_param']}=documents&amp;documents=view&amp;view=" . $fdc['id'] . "&amp;model_lang={$registry['lang']}";
                $cancelled_documents_list[] = "<a href='{$url}' target='_blank'>" . $fdc['full_num'] . "</a>";
            }
            $messages->setWarning(sprintf($this->i18n('warning_automation_hr_notify_leave_request_employee_has_free_days_cancelled'), implode(', ', $cancelled_documents_list)));
        }

        // Get the "Leave" event type
        $filters_event_type = array('model_lang' => $registry['lang'],
                                    'where'      => array('`et`.`keyword`    = \'' . $settings['event_type_keyword'] . '\'',
                                                          '`et`.`deleted_by` = \'0\'',
                                                          '`et`.`active`     = \'1\''),
                                    'sanitize'   => true);
        $event_type = Events_Types::searchOne($registry, $filters_event_type);

        // If no event type
        if (!$event_type) {
            // Error: no event type
            $messages->setError($this->i18n('error_automation_hr_notify_leave_request_event_type'));
        } else {
            // Get all current assignments of the document
            $assignments = array();
            $assignments += $document->getAssignments();
            $assignments += $document->getAssignments('responsible');
            $assignments += $document->getAssignments('observer');
            $assignments += $document->getAssignments('decision');

            // Get the employee of the "Leave request" document
            $filters_employee = array('where'      => array('`c`.`id` = \'' . $document->get('customer') . '\''),
                                      'model_lang' => $registry['lang'],
                                      'sanitize'   => true);
            $employee = Customers::searchOne($registry, $filters_employee);

            // Prepare the start and the end date for the event
            $event_start_date = $document_assoc_vars[$this->settings['field_start_date']]['value'];
            $event_start      = $event_start_date . ' 00:00:00';
            $event_end_date   = $document_assoc_vars[$this->settings['field_end_date']]['value'];
            // if end date is not filled in, assume that it is the same as start date
            if (!$event_end_date) {
                $event_end_date = $event_start_date;
            }
            $event_end        = $event_end_date . ' 00:00:00';
            $today_date       = General::strftime($this->i18n('date_iso_short'));

            // Prepare the event status
            $event_status = 'planning';
            if ($document->get('status') == $settings['document_status_unapproved'] && in_array($document->get('substatus'), $document_substatus_unapproved)) {
                $event_status = 'unstarted';
            } elseif ($event_start_date <= $today_date && $today_date <= $event_end_date) {
                $event_status = 'progress';
            } elseif ($event_end_date < $today_date) {
                $event_status = 'finished';
            }

            // Check if there is already an event for this "Leave request" document
            $filters_event = array('where'      => array('`e`.`deleted_by`  = \'0\'',
                                                         '`e`.`active`      = \'1\'',
                                                         '`e`.`type`        = \'' . $event_type->get('id') . '\'',
                                                         '`e`.`customer`    = \'' . $employee->get('id') . '\'',
                                                         '`e`.`event_start` = \'' . $event_start . '\''),
                                   'model_lang' => $registry['lang']);
            $event = Events::searchOne($registry, $filters_event);

            // If no event
            if (!$event) {
                // If we are currently changing the status of the document to "Approved"
                if (in_array($registry['action'], array('setstatus', 'multistatus')) && $document->get('status') == $settings['document_status_approved'] && $document->get('substatus') == $settings['document_substatus_approved']) {
                    // Prepare the "Leave" event
                    $event = Events::buildModel($registry);
                    $event->set('id',           null,                              true);
                    $event->set('name',         sprintf($this->i18n('automation_hr_notify_leave_request_event_name'), trim($event_type->get('name')), trim($employee->get('name') . ' ' . $employee->get('lastname'))), true);
                    $event->set('customer',     $employee->get('id'),              true);
                    $event->set('type',         $event_type->get('id'),            true);
                    $event->set('event_start',  $event_start,                      true);
                    $event->set('event_end',    $event_end,                        true);
                    $event->set('allday_event', '1',                               true);
                    $event->set('status',       $event_status,                     true);
                    $event->set('group', ($document->get('group') ? $document->get('group') : 1), true);

                    // Try to add the event
                    if ($event->save()) {
                        // Prepare a view URL for the event
                        $event_view_url = sprintf('%s?%s=events&amp;events=view&amp;view=%s',
                                                  $_SERVER['PHP_SELF'],
                                                  $registry['module_param'],
                                                  $event->get('id'));

                        // Message: add success
                        $msg[] = sprintf($this->i18n('message_automation_hr_notify_leave_request_add_event_success'), $event_view_url, $event_type->get('name'));

                        // Write history
                        $filters_new_event = array('where'      => array('`e`.`id` = \'' . $event->get('id') . '\''),
                                                   'model_lang' => $event->get('model_lang'));
                        $new_event = Events::searchOne($registry, $filters_new_event);
                        $old_event = new Event($registry);
                        $old_event->sanitize();
                        Events_History::saveData($registry, array('model'       => $event,
                                                                  'action_type' => 'add',
                                                                  'new_model'   => $new_event,
                                                                  'old_model'   => $old_event));

                        // Try to set the relation between the document and the event
                        if ($new_event->updateDocumentRelatives(array($document->get('id')))) {
                            // Write history
                            Events_History::saveData($registry, array('model'       => $event,
                                                                      'action_type' => 'relatives'));
                        } else {
                            $messages->setError('error_automation_hr_notify_leave_request_event_relatives_failed');
                        }

                        // Get the new event as current event
                        $event = clone $new_event;

                        // Prepare the old event (for the history)
                        $old_event = clone $event;
                        $old_event->sanitize();

                        // Prepare a list of assigned users for the event
                        $new_users = array();
                        foreach (array_keys($assignments) as $user_id) {
                            $new_users[$user_id] = array(
                                'participant_id' => $user_id,
                                'ownership'      => 'other',
                                'access'         => 'view');
                        }
                        $query = 'SELECT `id` FROM `' . DB_TABLE_USERS . '` WHERE `employee` = \'' . $employee->get('id') . '\'';
                        $leave_request_user_id = $db->GetOne($query);

                        if ($leave_request_user_id) {
                            $new_users[$leave_request_user_id] = array(
                                'participant_id' => $leave_request_user_id,
                                'ownership'      => 'mine',
                                'access'         => 'edit');
                        }

                        // Set the list of assigned users into the event model
                        $event->set('new_users', $new_users, true);

                        // Try to update the list of assigned users, without sending notifications
                        if ($event->assign(false)) {
                            // Set that the user of the "Leave request" document has confirmed the event assignment
                            $query = 'UPDATE `' . DB_TABLE_EVENTS_ASSIGNMENTS . '`' . "\n" .
                                     '  SET `user_status` = \'confirmed\',' . "\n" .
                                     '    `status_date`   = NOW()' . "\n" .
                                     '  WHERE `parent_id`        = \'' . $event->get('id') . '\'' . "\n" .
                                     '    AND `participant_type` = \'user\'' . "\n" .
                                     '    AND `participant_id`   = \'' . $leave_request_user_id . '\'';
                            $db->Execute($query);

                            // Message: assign success
                            $msg[] = sprintf($this->i18n('message_automation_hr_notify_leave_request_event_assign_success'), $event_view_url, $event_type->get('name'));

                            // After the success message, add the messages that come from the assign() method
                            $assign_msgs = $messages->getMessages();
                            if (!empty($assign_msgs) && is_array($assign_msgs)) {
                                $messages->unset_vars('messages');
                                foreach ($assign_msgs as $assign_msg) {
                                    $msg[] = $assign_msg;
                                }
                            }

                            // Write history
                            $filters_new_event = array('where'      => array('`e`.`id` = \'' . $event->get('id') . '\''),
                                                       'model_lang' => $event->get('model_lang'));
                            $new_event = Events::searchOne($registry, $filters_new_event);
                            Events_History::saveData($registry, array('model'       => $event,
                                                                      'action_type' => 'assign',
                                                                      'new_model'   => $new_event,
                                                                      'old_model'   => $old_event));
                        } else {
                            // Error: assign failed
                            $messages->setError(sprintf($this->i18n('error_automation_hr_notify_leave_request_event_assign_failed'), $event_type->get('name')));
                        }

                        // Unsanitize the event
                        $event_remind_sanitize_after = false;
                        if ($event->isSanitized()) {
                            $event->unsanitize();
                            $event_remind_sanitize_after = true;
                        }

                        // Prepare the request to be used for adding of reminders
                        $registry['request']->set('reminder_type',         $settings['reminder_type'],         true);
                        $registry['request']->set('selected_panel',        'offset',                           true);
                        $registry['request']->set('reminder_offset',       $settings['reminder_offset'],       true);
                        $registry['request']->set('reminder_offset_hours', $settings['reminder_offset_hours'], true);
                        $registry['request']->set('reminder_offset_days',  $settings['reminder_offset_days'],  true);

                        // Prepare the reminder message
                        $document_assoc_vars = $document->getAssocVars();
                        $reminder_custom_message = sprintf("%s \r\n%s: %s \r\n%s: %s",
                                                           $event->get('name'),
                                                           $document_assoc_vars[$settings['field_start_date']]['label'],
                                                           General::strftime('%d.%m.%Y', $event_start_date),
                                                           $document_assoc_vars[$settings['field_end_date']]['label'],
                                                           General::strftime('%d.%m.%Y', $event_end_date));
                        $registry['request']->set('custom_message', $reminder_custom_message, true);

                        // Get users whom reminders should be added for
                        $reminder_users = $document->getAssignments('decision');
                        $reminder_users = array_keys($reminder_users);
                        $add_remind_users = preg_split('#\s*,\s*#', $settings['reminder_users']);
                        if (empty($add_remind_users)) {
                            $add_remind_users = array();
                        }
                        $reminder_users = array_merge($reminder_users, $add_remind_users);
                        $reminder_users = array_unique($reminder_users);

                        // Add reminders
                        if (count(array_filter($reminder_users)) > 0) {
                            foreach ($reminder_users as $reminder_user) {
                                $registry['request']->set('reminder_user_id', trim($reminder_user), 'all', true);
                                if (!$event->remind()) {
                                    $messages->setError(sprintf($this->i18n('error_automation_hr_notify_leave_request_add_reminders_failed'), $event_type->get('name')));
                                    break;
                                }
                            }
                        }

                        // Sanitize the event
                        if ($event_remind_sanitize_after) {
                            $event->sanitize();
                        }
                    } else {
                        // Error: add event failed
                        $messages->setError($this->i18n('error_automation_hr_notify_leave_request_add_event_failed'));
                    }
                }
            } else {
                // Prepare a view URL for the event
                $event_view_url = sprintf('%s?%s=events&amp;events=view&amp;view=%s',
                                          $_SERVER['PHP_SELF'],
                                          $registry['module_param'],
                                          $event->get('id'));

                // Set the corresponding status for the event
                $event_status_changed = false;
                if ($event->get('status') != $event_status) {
                    $status_params = array(
                        'model_id'   => $event->get('id'),
                        'module'     => 'events',
                        'model'      => $event,
                        'new_status' => $event_status,
                        'send_mail'  => false);
                    // Try to set the status
                    if ($this->status($status_params)) {
                        // Message: event status change success
                        $msg[] = sprintf($this->i18n('message_automation_hr_notify_leave_request_event_status_success'), $event_view_url, $event_type->get('name'));
                        $event_status_changed = true;
                    } else {
                        // Error: event status change failed
                        $messages->setError(sprintf($this->i18n('error_automation_hr_notify_leave_request_event_status_failed'), $event_view_url, $event_type->get('name')));
                    }
                }

                // If we are changing the assignments
                if ($registry['action'] == 'assign') {
                    // If the event status have been changed
                    if ($event_status_changed) {
                        // Get the event from the database
                        $filters_new_event = array('where'      => array('`e`.`id` = \'' . $event->get('id') . '\''),
                                                   'model_lang' => $event->get('model_lang'));
                        $new_event = Events::searchOne($registry, $filters_new_event);

                        // Set the new event as current event
                        $event = clone $new_event;
                    }

                    // Prepare the old event (for the history)
                    $old_event = clone $event;
                    $old_event->sanitize();

                    // Prepare the new list of assigned users for the event
                    $event->getAssignments();
                    $old_users = array();
                    foreach ($event->get('users_view') as $user_view) {
                        $old_users[$user_view['participant_id']] = $user_view;
                    }
                    foreach ($event->get('users_edit') as $user_edit) {
                        $old_users[$user_edit['participant_id']] = $user_edit;
                    }
                    $new_users = array();
                    foreach (array_keys($assignments) as $user_id) {
                        if (!array_key_exists($user_id, $old_users)) {
                            $new_users[$user_id] = array(
                                'participant_id' => $user_id,
                                'ownership'      => 'other',
                                'access'         => 'view');
                        } else {
                            $new_users[$user_id] = $old_users[$user_id];
                        }
                    }
                    $query = 'SELECT `id` FROM `' . DB_TABLE_USERS . '` WHERE `employee` = \'' . $employee->get('id') . '\'';
                    $leave_request_user_id = $db->GetOne($query);
                    if (!array_key_exists($leave_request_user_id, $old_users)) {
                        $new_users[$leave_request_user_id] = array(
                            'participant_id' => $leave_request_user_id,
                            'ownership'      => 'mine',
                            'access'         => 'edit');
                    } else {
                        $new_users[$leave_request_user_id] = $old_users[$leave_request_user_id];
                    }

                    // Set the new list of assigned users into the event model
                    $event->set('new_users', $new_users, true);

                    // Try to update the list of assigned users, without sending notifications
                    if ($event->assign(false)) {
                        // Message: assign success
                        $msg[] = sprintf($this->i18n('message_automation_hr_notify_leave_request_event_assign_success'), $event_view_url, $event_type->get('name'));

                        // After the success message, add the messages that comes from the assign() method
                        $assign_msgs = $messages->getMessages();
                        if (!empty($assign_msgs) && is_array($assign_msgs)) {
                            $messages->unset_vars('messages');
                            foreach ($assign_msgs as $assign_msg) {
                                $msg[] = $assign_msg;
                            }
                        }

                        // Write history
                        $filters_new_event = array('where'      => array('`e`.`id` = \'' . $event->get('id') . '\''),
                                                   'model_lang' => $event->get('model_lang'));
                        $new_event = Events::searchOne($registry, $filters_new_event);
                        Events_History::saveData($registry, array('model'       => $event,
                                                                  'action_type' => 'assign',
                                                                  'new_model'   => $new_event,
                                                                  'old_model'   => $old_event));
                    } else {
                        // Error: assign failed
                        $messages->setError(sprintf($this->i18n('error_automation_hr_notify_leave_request_event_assign_failed'), $event_type->get('name')));
                    }
                }

                if ($event_status == 'unstarted' && $event_status_changed && !empty($settings['delete_event_on_not_approved'])) {
                    $del_result = Events::delete($registry, array($event->get('id')));
                    if ($del_result) {
                        Events_History::saveData($registry, array('model'       => $event,
                                                                  'action_type' => 'delete'));
                    } else {
                        $messages->setError(sprintf($this->i18n('error_automation_hr_notify_leave_request_event_mark_as_delete_failed'), $event_type->get('name')));
                    }
                }
            }
        }

        // Sanitize the document
        if ($document_sanitize_after) {
            $document->sanitize();
        }

        // Fail the transaction if there are any errors
        if ($messages->getErrors()) {
            $db->FailTrans();
        }

        // Get the result of the transaction
        $dbTransError = $db->HasFailedTrans();

        // Complete the transaction
        $db->CompleteTrans();

        // Load the automation messages if there are no errors
        if (!$dbTransError && count($msg) > 0) {
            foreach ($msg as $m) {
                $messages->setMessage($m);
            }
        }

        // Load all messages into the session
        $messages->insertInSession($registry);

        // The result of the transaction is the result of the automation
        return !$dbTransError;
    }

    /**
     * Check for sick notes which dates match already added leave request and gives warning message if so
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool         - the result of the operations
     */
    public function checkMatchingSickNoteAndLeaveRequests($params) {
        // Execute the automation only if changing the status of the document or changing assignments
        $trigger_actions = preg_split('#\s*,\s*#', $this->settings['trigger_actions']);
        $registry = &$this->registry;
        if (!in_array($this->registry['action'], $trigger_actions)) {
            return true;
        }

        $document = $params['model'];

        $document_substatus_approved = preg_split('#\s*,\s*#', $this->settings['request_leave_substatus_approved']);
        $document_assoc_vars = $document->getAssocVars();

        if (empty($document_assoc_vars[$this->settings['sick_leave_end_date']]['value']) || empty($document_assoc_vars[$this->settings['sick_leave_start_date']]['value'])) {
            return true;
        }

        // get leave request vars
        $leave_request_vars = array($this->settings['leave_request_start_date'], $this->settings['leave_request_end_date']);
        $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `model_type`="' . $this->settings['leave_request_type'] . '" AND `name` IN ("' . implode('","', $leave_request_vars) . '")' . "\n";
        $leave_request_vars = $this->registry['db']->GetAssoc($sql);

        // check if there are cancelled free days requests for this employee
        $sql = 'SELECT d.id, d.full_num' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as d_cstm_start' . "\n" .
               '  ON (d_cstm_start.var_id="' . $leave_request_vars[$this->settings['leave_request_start_date']] . '"' . "\n" .
               '    AND d_cstm_start.model_id=d.id' . "\n" .
               '    AND d_cstm_start.value<="' . $document_assoc_vars[$this->settings['sick_leave_end_date']]['value'] . '")' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as d_cstm_end' . "\n" .
               '  ON (d_cstm_end.var_id="' . $leave_request_vars[$this->settings['leave_request_end_date']] . '"' . "\n" .
               '    AND d_cstm_end.model_id=d.id' . "\n" .
               '    AND d_cstm_end.value>="' . $document_assoc_vars[$this->settings['sick_leave_start_date']]['value'] . '")' . "\n" .
               'WHERE d.deleted_by=0' . "\n" .
               '  AND d.active=1' . "\n" .
               '  AND d.type="' . $this->settings['leave_request_type'] . '"' . "\n" .
               '  AND d.substatus IN ("' . implode('","', $document_substatus_approved) . '")' . "\n" .
               '  AND d.customer = \'' . $document->get('employee') . '\'' . "\n";
        $free_days_match = $registry['db']->GetAll($sql);

        if (!empty($free_days_match)) {
            $match_documents_list = array();
            foreach ($free_days_match as $fdm) {
                $url = "{$_SERVER['PHP_SELF']}?{$this->registry['module_param']}=documents&amp;documents=view&amp;view=" . $fdm['id'] . "&amp;model_lang={$this->registry['lang']}";
                $match_documents_list[] = "<a href='{$url}' target='_blank'>" . $fdm['full_num'] . "</a>";
            }
            if (!empty($match_documents_list)) {
                $this->registry['messages']->setWarning(sprintf($this->i18n('warning_automation_hr_leave_request_match_sick_note_dates'), implode(', ', $match_documents_list)));
                $this->registry['messages']->insertInSession($this->registry);
            }
        }

        return true;
    }

    /**
     * Create Gdpr access data for the document
     */
    public function createGdprAccess($params) {
        $model = $params['model'];
        $this->registry->set('get_old_vars', true, true);

        $generate_new_access = true;
        $gt2_var = $model->getGT2Vars();

        foreach ($gt2_var['values'] as $row_values) {
            if ($row_values['article_barcode'] == 1) {
                $generate_new_access = false;
                break;
            }
        }

        if ($generate_new_access) {
            $gt2_table = $model->getGT2Vars();

            $current_password = '';
            $customer = '';
            $old_customer = '';
            $customer_password = '';

            if ($model->get('customer') != $this->settings['customer_unknown_id']) {
                // complete the password in the customer file
                require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
                require_once PH_MODULES_DIR . 'customers/models/customers.history.php';
                $customer = Customers::searchOne($this->registry, array('where' => array('c.id="' . $model->get('customer') . '"')));
                $old_customer = clone $customer;
                $assoc_vars_cust = $customer->getAssocVars();
                if ($assoc_vars_cust[$this->settings['customer_password_field']]['value']) {
                    $customer_password = $current_password = $assoc_vars_cust[$this->settings['customer_password_field']]['value'];
                }
            }

            if (!$current_password) {
                $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
                $current_password = '';
                for ($i = 0; $i<6; $i++) {
                    $current_password .= $characters[mt_rand(0, strlen($characters) - 1)];
                }
            }

            $new_access_rows_value = array(
                // link
                'free_field1'         => md5(microtime() . sprintf('%04s', rand(0, 9999))),
                // download attempts
                'free_field2'         => 0,
                // password
                'free_field3'         => $current_password,
                // created_by
                'free_field4'         => $this->registry['originalUser']->get('id'),
                // date created
                'free_field5'         => General::strftime($this->i18n('date_iso')),
                // flag to show if the link is active
                'article_barcode'     => 1,
                // downloaded by IP
                'article_code'        => '',
                // date downloaded
                'article_second_code' => '',
                'quantity'            => 1,
                'article_id'          => 1,
                'article_measure_name'=> 1
            );

            $gt2_table['values'][] = $new_access_rows_value;

            $model->set('grouping_table_2', $gt2_table, true);
            $model->calculateGT2();
            $model->set('table_values_are_set', true, true);

            if ($model->saveGT2Vars() && $customer && $current_password != $customer_password) {
                // we need to update the password in the customer's data
                // prepare queries to update
                $insert = array();
                $update = array();

                $insert[] = 'model_id="' . $customer->get('id') . '"';
                $insert[] = 'var_id="' . $assoc_vars_cust[$this->settings['customer_password_field']]['id'] . '"';
                $insert[] = 'num=1';
                $insert[] = 'value="' . $new_access_rows_value['free_field3'] . '"';
                $insert[] = 'lang=""';
                $insert[] = 'modified=now()';
                $insert[] = 'modified_by=' . $this->registry['currentUser']->get('id');
                $update = $insert;

                $insert[] = 'added=now()';
                $insert[] = 'added_by=' . $this->registry['currentUser']->get('id');

                $query = 'INSERT INTO ' . DB_TABLE_CUSTOMERS_CSTM . "\n" .
                         'SET ' . implode(', ', $insert) . "\n" .
                         'ON DUPLICATE KEY UPDATE ' . "\n" .
                         implode(', ', $update);
                $this->registry['db']->Execute($query);

                // get the updated nomenclature to write history
                $filters = array('where' => array('c.id = ' . $customer->get('id')),
                                 'model_lang' => $customer->get('model_lang'));
                $new_customer = Customers::searchOne($this->registry, $filters);
                $new_customer->getVars();

                Customers_History::saveData($this->registry, array('model' => $new_customer, 'action_type' => 'edit', 'new_model' => $new_customer, 'old_model' => $old_customer));
            }
        }

        return true;
    }

    /**
     * Validate the school days list
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool         - the result of the operations
     */
    public function validateSchoolDaysList($params) {
        // Prepare some basics
        $result = true;
        $registry = &$this->registry;
        $messages = &$registry['messages'];
        require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';

        // Get all request vars
        $request_vars = $registry['request']->getAll();

        if ($this->settings['days_count'] && isset($request_vars[$this->settings['days_count']])) {
            $school_days_rows = array_filter($request_vars[$this->settings['days_count']]);
            if (count($school_days_rows)) {
                $school_days_period_from = $request_vars[$this->settings['date_from']];
                $school_days_period_to = $request_vars[$this->settings['date_to']];

                $previous_periods = array();
                foreach ($school_days_rows as $row_key => $days_included) {
                    $current_period_from = date_create($school_days_period_from[$row_key]);
                    $current_period_to = date_create($school_days_period_to[$row_key]);

                    if ($current_period_to < $current_period_from) {
                        // ending date is before the start date
                        $messages->setError(
                            sprintf(
                                $this->i18n('error_automation_validate_school_days_end_date_before_start_date'),
                                $current_period_to->format('d.m.Y'),
                                $current_period_from->format('d.m.Y'),
                                $row_key
                            )
                        );
                        $result = false;
                        break;
                    }

                    // calculate the working days
                    $period_working_days = Calendars_Calendar::getWorkingDays($registry, $current_period_from->format('Y-m-d'), $current_period_to->format('Y-m-d'));
                    if ($days_included > $period_working_days ) {
                        // number of school days entered is greater than the working days in period
                        $messages->setError(
                            sprintf(
                                $this->i18n('error_automation_validate_school_days_entered_more_working_days_than_available'),
                                $days_included,
                                $period_working_days,
                                $row_key
                            )
                        );
                        $result = false;
                        break;
                    }

                    // check for periods overlapping
                    foreach ($previous_periods as $k_pp => $pp) {
                        if ($pp['from'] <= $current_period_to->format('Y-m-d') && $pp['to'] >= $current_period_from->format('Y-m-d')) {
                            // overlapping periods error
                            $messages->setError(
                                sprintf(
                                    $this->i18n('error_automation_validate_school_days_periods_overlapping'),
                                    $row_key,
                                    $k_pp
                                )
                            );
                            $result = false;
                            break 2;
                        }
                    }

                    $previous_periods[$row_key] = array(
                        'from' => $current_period_from->format('Y-m-d'),
                        'to'   => $current_period_to->format('Y-m-d')
                    );
                }
            }
        }

        return $result;
    }

    /**
     * Crontab automation to create schedule based on certain structure define in a document
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool         - the result of the operations
     */
    public function createWorkingSchedule($params) {
        $team_member_var = '';
        $schedule_document_type = false;
        $shifts_list = array();
        $default_doc_properties = array();

        // find the working schemes which will be processed
        $sql = 'SELECT n.id' . "\n" .
               'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
               'INNER JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm' . "\n" .
               ' ON (tm.model_id=n.id AND tm.model="Nomenclature" AND tm.tag_id="' . $this->settings['nom_tag_create_schedule'] . '")' . "\n" .
               'WHERE n.type="' . $this->settings['nom_type_working_scheme'] . '" AND n.active=1 AND n.deleted_by=0';
        $working_schemes = $this->registry['db']->GetCol($sql);

        // process the schedules
        foreach ($working_schemes as $ws) {
            // get the model of the current nomenclature
            $working_scheme_filt = array('where'      => array('`n`.`id` = \'' . $ws . '\''),
                                   'model_lang' => $this->registry['lang']);
            $working_scheme = Nomenclatures::searchOne($this->registry, $working_scheme_filt);

            // get the assoc vars ant take the needed vars
            $assoc_vars = $working_scheme->getAssocVars();

            $teams_ids = array_values(array_filter(!empty($assoc_vars[$this->settings['nom_working_scheme_var_team']]) ? $assoc_vars[$this->settings['nom_working_scheme_var_team']]['value'] : array()));
            $shifts_ids = array_values(array_filter(!empty($assoc_vars[$this->settings['nom_working_scheme_var_shift']]) ? $assoc_vars[$this->settings['nom_working_scheme_var_shift']]['value'] : array()));
            $start_date = (!empty($assoc_vars[$this->settings['nom_working_scheme_var_start']]) ? $assoc_vars[$this->settings['nom_working_scheme_var_start']]['value'] : '');
            $end_date = (!empty($assoc_vars[$this->settings['nom_working_scheme_var_end']]) ? $assoc_vars[$this->settings['nom_working_scheme_var_end']]['value'] : '');

            if (empty($shifts_ids) || empty($start_date) || empty($end_date)) {
                // not completed needed data so skip this working scheme
                continue;
            }

            // get the available shifts
            if (empty($shifts_list)) {
                $shift_vars = array($this->settings['nom_shift_var_start_hour'], $this->settings['nom_shift_var_duration']);
                $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Nomenclature" AND `model_type`="' . $this->settings['nom_type_shift'] . '" AND `name` IN ("' . implode('","', $shift_vars) . '")' . "\n";
                $shift_vars = $this->registry['db']->GetAssoc($sql);

                $sql = 'SELECT n.id, ni18n.name, n_cstm_duration.value as duration, n_cstm_start.value as start_hour' . "\n" .
                       'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                       'INNER JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                       ' ON (n.type="' . $this->settings['nom_type_shift'] . '" AND n.active=1 AND n.deleted_by=0 AND ni18n.parent_id=n.id AND ni18n.lang="' . $this->registry['lang'] . '")' . "\n" .
                       'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_start' . "\n" .
                       '  ON (n_cstm_start.model_id=n.id AND n_cstm_start.var_id="' . (!empty($shift_vars[$this->settings['nom_shift_var_start_hour']]) ? $shift_vars[$this->settings['nom_shift_var_start_hour']] : '') . '")' . "\n" .
                       'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_duration' . "\n" .
                       '  ON (n_cstm_duration.model_id=n.id AND n_cstm_duration.var_id="' . (!empty($shift_vars[$this->settings['nom_shift_var_duration']]) ? $shift_vars[$this->settings['nom_shift_var_duration']] : '') . '")' . "\n";
                $shifts_list = $this->registry['db']->GetAssoc($sql);
            }

            // get the team members
            if (empty($team_member_var)) {
                $sql = 'SELECT `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Nomenclature" AND `model_type`="' . $this->settings['nom_type_team'] . '" AND `name`="' . $this->settings['nom_team_var_team_member'] . '"' . "\n";
                $team_member_var = $this->registry['db']->GetOne($sql);
            }

            $sql = 'SELECT n_cstm.value as idx, CONCAT(ci18n.name, " ", ci18n.lastname)' . "\n" .
                   'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                   'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm' . "\n" .
                   '  ON (n_cstm.model_id IN ("' . implode('","', $teams_ids) . '") AND n_cstm.var_id="' . $team_member_var . '")' . "\n" .
                   'INNER JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                   '  ON (ci18n.parent_id=n_cstm.value AND ci18n.lang="' . $this->registry['lang'] . '")' . "\n";
            $team_members = $this->registry['db']->GetAssoc($sql);

            // get the document type
            $this->registry['db']->StartTrans();
            $error = false;
            if (empty($schedule_document_type)) {
                // get the model of the current nomenclature
                $type_filt = array('where'      => array('dt.id = \'' . $this->settings['document_type_schedule'] . '\''),
                                   'model_lang' => $this->registry['lang'],
                                   'sanitize'   => true);
                $schedule_document_type = Documents_Types::searchOne($this->registry, $type_filt);

                // prepare the new document for the next month
                $default_doc_properties = array(
                    'type' => $schedule_document_type->get('id'),
                    'date' => date('Y-m-d'),
                    'generate_system_task' => $schedule_document_type->get('generate_system_task'),
                    'direction' => $schedule_document_type->get('direction'),
                    'type_name' => $schedule_document_type->get('name'),
                    'name' => $working_scheme->get('name'),
                    'department' => $schedule_document_type->getDefaultDepartment(),
                    'group' => $schedule_document_type->getDefaultGroup(),
                    'media' => $schedule_document_type->get('media'),
                    'media_name' => $schedule_document_type->get('media_name'),
                    'customer' => $schedule_document_type->get('default_customer')
                );
            }

            $current_date = new DateTime($start_date);
            $datetime_end = new DateTime($end_date);
            $current_document_month = '';
            $new_schedule_document = '';
            $new_document_vars = array();
            $shifts_idx = 0;
            $date_idx = 0;

            while ($current_date <= $datetime_end) {
                $current_date_month = $current_date->format('Y-m');
                if ($current_document_month != $current_date_month) {
                    if (!empty($new_schedule_document)) {
                        // add the document
                        $members_idx = 1;
                        foreach ($team_members as $tm_mem_id => $tm_mem_name) {
                            $current_document_vars = $new_document_vars;
                            $current_doc = clone $new_schedule_document;

                            if ($new_document_vars[$this->settings['document_schedule_var_technician']]['grouping']) {
                                $current_document_vars[$this->settings['document_schedule_var_technician']]['value'][$members_idx] = $tm_mem_id;
                                $current_document_vars[$this->settings['document_schedule_var_technician_name']]['value'][$members_idx] = $tm_mem_name;
                            } else {
                                $current_document_vars[$this->settings['document_schedule_var_technician']]['value'] = $tm_mem_id;
                                $current_document_vars[$this->settings['document_schedule_var_technician_name']]['value'] = $tm_mem_name;
                            }

                            $current_doc->set('vars', array_values($current_document_vars), true);
                            if (!$this->createScheduleDocument($current_doc, $ws, $current_document_month . '-01')) {
                                $error = true;
                                $new_document_vars = array();
                                break 2;
                            }
                            unset($current_doc);
                        }
                        unset($new_schedule_document);
                    }

                    $current_document_month = $current_date_month;
                    $new_schedule_document = new Document($this->registry, $default_doc_properties);
                    $new_schedule_document->getVars();
                    $new_document_vars = $new_schedule_document->getAssocVars();

                    $new_document_vars[$this->settings['document_schedule_var_month']]['value'] = $current_date->format('n');
                    $new_document_vars[$this->settings['document_schedule_var_year']]['value'] = $current_date->format('Y');

                    $date_idx = 0;
                }

                // CREATE THE NEW ROW FOR THE SHIFT
                // get the shift idx
                if (!isset($shifts_ids[$shifts_idx])) {
                    $shifts_idx = 0;
                }

                if ($shifts_ids[$shifts_idx] != $this->settings['nom_rest']) {
                    $new_document_vars[$this->settings['document_schedule_var_month_date']]['value'][$date_idx] = $current_date->format('j');
                    $new_document_vars[$this->settings['document_schedule_var_weekday']]['value'][$date_idx] = $current_date->format('w');
                    $new_document_vars[$this->settings['document_schedule_var_workshift']]['value'][$date_idx] = $shifts_ids[$shifts_idx];
                    $new_document_vars[$this->settings['document_schedule_var_workshift_name']]['value'][$date_idx] = $shifts_list[$shifts_ids[$shifts_idx]]['name'];
                    $new_document_vars[$this->settings['document_schedule_var_start_hour']]['value'][$date_idx] = $shifts_list[$shifts_ids[$shifts_idx]]['start_hour'];
                    $new_document_vars[$this->settings['document_schedule_var_duration']]['value'][$date_idx] = $shifts_list[$shifts_ids[$shifts_idx]]['duration'];
                    $date_idx++;
                }

                $shifts_idx++;
                $current_date->modify('+1 day');
            }

            if (!empty($new_document_vars)) {
                $members_idx = 1;
                foreach ($team_members as $tm_mem_id => $tm_mem_name) {
                    $current_document_vars = $new_document_vars;
                    $current_doc = clone $new_schedule_document;

                    if ($new_document_vars[$this->settings['document_schedule_var_technician']]['grouping']) {
                        $current_document_vars[$this->settings['document_schedule_var_technician']]['value'][$members_idx] = $tm_mem_id;
                        $current_document_vars[$this->settings['document_schedule_var_technician_name']]['value'][$members_idx] = $tm_mem_name;
                        //$members_idx++;
                    } else {
                        $current_document_vars[$this->settings['document_schedule_var_technician']]['value'] = $tm_mem_id;
                        $current_document_vars[$this->settings['document_schedule_var_technician_name']]['value'] = $tm_mem_name;
                    }

                    $current_doc->set('vars', array_values($current_document_vars), true);
                    if (!$this->createScheduleDocument($current_doc, $ws, $current_document_month . '-01')) {
                        $error = true;
                        break;
                    }
                }
                unset($new_schedule_document);
            }

            if ($error) {
                $this->registry['db']->FailTrans();
            } else {
                // manage the tags
                if (!empty($this->settings['nom_tag_created_schedule'])) {
                    $tag_params = array(
                        'id'       => $params['id'],
                        'module'   => 'nomenclatures',
                        'model'    => $working_scheme,
                        'model_id' => $working_scheme->get('id'),
                        'new_tags' => $this->settings['nom_tag_created_schedule']
                    );
                    if (!$this->tag($tag_params)) {
                        $this->executionErrors[] = 'Removing tag for creation of schedule from working scheme with ID ' . $working_scheme->get('id') .  ' failed!';
                        $this->registry['db']->FailTrans();
                    }
                }

                // remote the tag which triggers the automation
                $tag_params = array(
                    'id'       => $params['id'],
                    'module'   => 'nomenclatures',
                    'model'    => $working_scheme,
                    'model_id' => $working_scheme->get('id'),
                    'tags'     => $this->settings['nom_tag_create_schedule']
                );
                if (!$this->removeTags($tag_params)) {
                    $this->executionErrors[] = 'Adding tag for already created schedule to working scheme with ID ' . $working_scheme->get('id') .  ' failed!';
                    $this->registry['db']->FailTrans();
                }
            }
            $this->registry['db']->CompleteTrans();
        }

        $this->updateAutomationHistory($params, 0, 1);
        return true;
    }

    /*
     * Function to create the schedule document and write history
     *
     * @param object $new_model - the object that is previously filled with data
     * @param integer $working_scheme_id - the id of the working scheme from which the schedule is created
     * @param string $month_first_date - the first date of the month for which the schedule is created for
     * @return bool             - the result of the operations
     */
    private function createScheduleDocument($new_model, $working_scheme_id, $month_first_date) {
        $result = true;
        $old_model = new Document($this->registry);

        $i18n_files_dir = PH_MODULES_DIR . 'documents/i18n/' . $this->registry['lang'] . '/';
        $files_list = FilesLib::readDir($i18n_files_dir, false, '', '', true);
        $this->loadI18NFiles($files_list);

        if ($new_model->save()) {
            // write history and audit
            $filters    = array('where'    => array('d.id = ' . $new_model->get('id')),
                                'sanitize' => true);
            $new_model  = Documents::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $new_model->getVars();

            Documents_History::saveData($this->registry,
                                        array('model'       => $new_model,
                                              'action_type' => 'add',
                                              'new_model'   => $new_model,
                                              'old_model'   => $old_model));
        } else {
            $errors = $this->registry['messages']->getErrors();
            $this->executionErrors[] = sprintf('Error occurred while creating schedule for %s from working scheme with ID %d! More information may be found below:' . "\n" . '%s',
                                               General::strftime('%B %Y', $month_first_date),
                                               $working_scheme_id,
                                               implode("\n", $errors));
            $this->registry['messages']->flush();
            $result = false;
        }

        return $result;
    }
}

?>
