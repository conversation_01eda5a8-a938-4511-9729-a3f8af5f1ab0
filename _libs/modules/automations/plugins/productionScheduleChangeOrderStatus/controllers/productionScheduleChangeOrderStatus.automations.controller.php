<?php

class productionScheduleChangeOrderStatus_Automations_Controller extends Automations_Controller {

    /**
     * Function to change order status if needed
     *
     * @param array $params - arguments for the method, containing registry
     * @return boolean - result of the operation
     */
    public function productionScheduleChangeOrderStatus($params) {
        // get the related order
        $sql = 'SELECT dr.link_to as id, CONCAT(d.status, "_", d.substatus) as status' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               ' ON (d.id=dr.link_to AND dr.parent_id="' . $params['model']->get('id') . '"
                     AND dr.parent_model_name="Document" AND dr.link_to_model_name="Document"
                     AND d.type="' . $this->settings['order_type_id'] . '")' . "\n";
        $order_data = $this->registry['db']->GetRow($sql);

        if (!$order_data) {
            // no related order
            return false;
        }

        // find all the requests related to the order
        $sql = 'SELECT dr.parent_id, CONCAT(d.status, "_", d.substatus) as status' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               ' ON (dr.link_to="' . $order_data['id'] . '" AND dr.parent_id=d.id
                     AND dr.parent_model_name="Document" AND dr.link_to_model_name="Document"
                     AND d.type="' . $params['start_model_type'] . '" AND d.active=1 AND d.deleted_by=0)' . "\n";
        $requests_statuses = $this->registry['db']->GetAssoc($sql);

        $statuses_count = array_count_values($requests_statuses);

        $new_status = '';
        if (!empty($statuses_count[$this->settings['request_status_completed']])) {
            if ($statuses_count[$this->settings['request_status_completed']] == count($requests_statuses)) {
                $new_status = $this->settings['order_status_fully_completed'];
            } elseif ($statuses_count[$this->settings['request_status_completed']] > 0 &&
                      $statuses_count[$this->settings['request_status_completed']] < count($requests_statuses)) {
                $new_status = $this->settings['order_status_partially_completed'];
            }
        }

        if (!$new_status || $order_data['status'] == $new_status) {
            return false;
        }

        // CHANGE THE STATUS OF THE ORDER
        // get the model of the order
        $filters = array('where'      => array('d.id = ' . $order_data['id']),
                         'model_lang' => $this->registry['lang']);
        $new_model = Documents::searchOne($this->registry, $filters);
        $new_model->getVars();

        $old_model = clone $new_model;
        preg_match('#(.*)_(\d+)#', $new_status, $matches);

        $new_model->set('status', $matches[1], true);
        $new_model->set('substatus', $new_status, true);
        $substatus_name_sql = 'SELECT `name` FROM ' . DB_TABLE_DOCUMENTS_STATUSES . "\n" .
                              'WHERE `id`="' . $matches[2] . '" AND `lang`="' . $this->registry['lang'] . '"';
        $new_model->set('substatus_name', $this->registry['db']->GetOne($substatus_name_sql), true);

        if ($new_model->setStatus()) {
            $new_model->set('substatus', preg_replace('#.*_(\d+)#', '$1', $new_model->get('substatus')), true);
            Documents_History::saveData($this->registry, array('action_type' => 'status', 'new_model' => $new_model, 'old_model' => $old_model));
        } else {
            $messages = &$this->registry['messages'];
            $messages->setError($this->i18n('error_automations_order_status_change'));
            $messages->insertInSession($this->registry);
            return false;
        }

        return true;
    }
}

?>
