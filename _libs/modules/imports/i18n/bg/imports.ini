imports_name = Тип
imports_description = Описание
imports_upload_file = Файл за импорт
imports_import = Импорт

import_log_status = Преминал
import_log_ok = успешно
import_log_failed = неуспешно
import_log_messages = Съобщения
import_log_user = Потребител
import_log_date = Дата/час
import_log_date_dropdown = Дата

message_import_success = Успешно импортиране!
error_import = Неуспешно импортиране!
error_no_file = Няма избран файл
help_imports_name = Моля, изберете тип импорт!
help_imports_upload_file = Моля, изберете файл за импортиране!
error_no_such_import = Нямате възможност да прегледате този запис!
message_imports_background_import_started = Стартирана е процедура за импорт на избраните файлове. След приключването ѝ ще бъдете информирани с и-мейл.
message_imports_background_import_results = Резултати от изпълнение на импорт %s
error_import_is_locked = Избраният импорт е бил стартиран и все още се изпълнява.

error_invoice_exist_num = Съществува фактура с номер %s от ред %d<br /> %s
error_invoice_col_row_type = Стойността '%s' трябва да е 'D' или 'R' на ред %d<br /> %s
error_invoice_no_eik = Няма стойност за ЕИК на ред %d<br /> %s
error_add_customer = Грешка при добавяне на контрагент от ред %d<br /> %s
error_add_reason = Грешка при добавяне на приходен документ от ред %d<br /> %s
error_add_invoice = Грешка при добавяне на фактура от ред %d<br /> %s
error_no_nomenclature = Няма номенклатура от ред %d<br /> %s
error_invoice_not_exist_num = Не съществува фактура с номер %s от ред %d<br /> %s

error_payment_more_amount = Сумата по плащането '%s' е по-голяма от оставащата за плащане '%s' по фактура на ред %d<br /> %s
error_no_invoice = Няма въведена фактура за ред %d<br /> %s
error_add_payment = Грешка при добавяне на плащане от ред %d<br /> %s
error_payment_count_cols = Броят на колоните на ред трябва да е %d на ред %d<br /> %s
error_payment_col_amount = Сумата '%s' трябва да е по-голяма от '0' на ред %d<br /> %s

imports_tables_file_to_import = Файл за импорт
imports_tables_rows_to_import = Редове за импорт
imports_tables_column_file = Колона от файл
imports_tables_column_table = Колона от таблица
imports_tables_column_value = Стойност по подразбиране

help_imports_tables_column_value = Стойността от тази колона ще се използва за импортиране в таблицата, когато импортираната стойност от файла е празна или не е открита, или когато в първата колона е зададено импортиране на твърда стойност.

imports_tables_configurator_title = Шаблони за импорт
imports_tables_configurator_load_save = Зареди/Запази
imports_tables_configurator_reload = Зареждане на шаблон за импорт
imports_tables_configurator_save = Запис на шаблон за импорт
imports_tables_configurator_delete = Изтриване на шаблон за импорт

error_imports_tables_configurator_save = Възникна грешка при запис на шаблон за импорт!
error_imports_tables_configurator_delete = Възникна грешка при изтриване на шаблон за импорт!
error_imports_tables_configurator_load = Невалиден избор на шаблон за импорт!
error_imports_tables_configurator_action = Невалидна операция с шаблон за импорт!
