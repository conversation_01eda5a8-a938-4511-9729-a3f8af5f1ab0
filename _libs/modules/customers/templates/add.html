<h1>{$title|escape}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="vtop">
      <div id="form_container" class="main_panel_container">

        {include file=`$theme->templatesDir`actions_box.html}

        <form name="customers_add" id="customers_add" action="{$submitLink}" method="post" enctype="multipart/form-data" onsubmit="return calculateBeforeSubmit(this);">
        <input type="hidden" name="check_similar_names" id="check_similar_names" value="1" />
        <input type="hidden" name="id" id="id" value="{$customer->get('id')}" />
        <input type="hidden" name="model_id" id="model_id" value="{$model_id}" />
        <input type="hidden" name="model_lang" id="model_lang" value="{$customer->get('model_lang')|default:$lang}" />
          <table border="0" cellpadding="0" cellspacing="0" class="t_table">
            <tr>
              <td class="t_footer"></td>
            </tr>
            <tr>
              <td>
                <div class="action_tabs add_customer">
                  <ul>
                    <li title="{$available_action.label}" id="tab_company"{if $customer_tabs eq 'person'} style="display: none;"{/if}><span{if $customer_selected_tab eq 'company'} class="selected"{/if}><a href="#" id="company_1" onclick="addCustomer($('customers_add'), 1, 'customer_data'); return false;"><img src="{$theme->imagesUrl}customers_add_company.png" width="16" height="16" alt="" title="{#customers_add_new_company#|escape}" border="0" />{#customers_add_new_company#|escape}</a></span></li>
                    <li title="{$available_action.label}" id="tab_person"{if $customer_tabs eq 'company'} style="display: none;"{/if}><span{if $customer_selected_tab eq 'person'} class="selected"{/if}><a href="#" id="company_0" onclick="addCustomer($('customers_add'), 0, 'customer_data'); return false;"><img src="{$theme->imagesUrl}customers_add_person.png" width="16" height="16" alt="" title="{#customers_add_new_person#|escape}" border="0" />{#customers_add_new_person#|escape}</a></span></li>
                  </ul>
                </div>
                <div class="clear"></div>
                <div id="customer_data">
                  {include file=$add_template layouts_vars=$customer->get('vars')}
                </div>
              </td>
            </tr>
          </table>
          {include file=`$theme->templatesDir`help_box.html}
          {include file=`$theme->templatesDir`system_settings_box.html object=$customer}
          {include file=`$theme->templatesDir`after_actions_box.html}
        </form>
      </div>
    </td>
  </tr>
</table>