<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox">{help label='module_name'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {capture assign='module_name'}menu_{$help->get('module_name')}{/capture}
            {$smarty.config.$module_name|escape|default:"&nbsp;"}
          </td>
        </tr>
        {if $help->get('type')}
        <tr>
          <td class="labelbox">{help label='type'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {$help->get('type_name')|escape}
          </td>
        </tr>
        {/if}
        <tr>
          <td class="labelbox">{help label='action_name'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {$help->get('action_label')|escape|default:"&nbsp;"}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='content'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">&nbsp;</td>
        </tr>
        <tr>
          <td nowrap="nowrap" colspan="3">
            {$editor_content}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$help exclude='groups'}
</div>
