<h1>{$title|escape}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}
{include file=`$theme->templatesDir`_submenu_actions_box.html}

<form name="projects" action="{$submitLink}" method="post">
<input type="hidden" name="id" id="id" value="{$project->get('id')}" />
<input type="hidden" name="type" id="type" value="{$project->get('type')}" />
<input type="hidden" name="model_lang" id="model_lang" value="{$project->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td class="vtop">
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td colspan="3">
            {#message_translatable_items#|escape}
          </td>
          <td class="vtop t_border divider_cell" rowspan="7">&nbsp;</td>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="2">&nbsp;</td>
          {capture assign='source_lang'}lang_{$project->get('model_lang')}{/capture}
          <td><img src="{$theme->imagesUrl}flags/{$project->get('model_lang')}.png" alt="" title="{$smarty.config.$source_lang}" class="t_flag" /> {$smarty.config.$source_lang}</td>
          <td>&nbsp;</td>
          {capture assign='target_lang'}lang_{$base_model->get('model_lang')}{/capture}
          <td colspan="2"><img src="{$theme->imagesUrl}flags/{$base_model->get('model_lang')}.png" alt="" title="{$smarty.config.$target_lang}" class="t_flag" /> {$smarty.config.$target_lang}</td>
        </tr>
      {foreach from=$project->getLayoutsDetails() key='lkey' item='layout'}
        {if $lkey eq 'name'}
        {if ($layout.view && $layout.edit)}{counter assign='translate_fields_count'}{/if}
        <tr{if !($layout.view && $layout.edit)} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            <input type="text" class="txtbox distinctive" name="name" id="name" value="{if !($layout.view && $layout.edit)}{$base_model->get('name')|escape}{else}{$project->get('name')|escape}{/if}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_name" id="copy_name" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <input type="text" class="txtbox distinctive" name="bm_name" id="bm_name" value="{$base_model->get('name')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly" />
          </td>
        </tr>
        {elseif $lkey eq 'description'}
        {if ($layout.view && $layout.edit)}{counter assign='translate_fields_count'}{/if}
        <tr{if !($layout.view && $layout.edit)} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_description"><label for="description"{if $messages->getErrors('description')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td>
            <textarea class="areabox distinctive" name="description" id="description" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{if !($layout.view && $layout.edit)}{$base_model->get('description')|escape}{else}{$project->get('description')|escape}{/if}</textarea>
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_description" id="copy_description" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <textarea class="areabox distinctive" name="bm_description" id="bm_description" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly">{$base_model->get('description')|escape}</textarea>
          </td>
        </tr>
        {elseif $lkey eq 'notes'}
        {if ($layout.view && $layout.edit)}{counter assign='translate_fields_count'}{/if}
        <tr{if !($layout.view && $layout.edit)} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_notes"><label for="notes"{if $messages->getErrors('notes')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td>
            <textarea class="areabox distinctive" name="notes" id="notes" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{if !($layout.view && $layout.edit)}{$base_model->get('notes')|escape}{else}{$project->get('notes')|escape}{/if}</textarea>
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_notes" id="copy_notes" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <textarea class="areabox distinctive" name="bm_notes" id="bm_notes" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly">{$base_model->get('notes')|escape}</textarea>
          </td>
        </tr>
        {/if}
      {/foreach}
        <tr>
          <td colspan="3">&nbsp;</td>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#translate#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
          <td>&nbsp;</td>
          <td colspan="2">
            {if $translate_fields_count}
            <button type="button" name="copyAll" class="button" title="{#copy_all#|escape}" onclick="return confirmAction('copy_all', function(el) {ldelim} copyAllFields(el); {rdelim}, this);">&laquo; {#copy_all#|escape}</button>
            {/if}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$project}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
