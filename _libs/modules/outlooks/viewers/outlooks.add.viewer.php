<?php

class Outlooks_Add_Viewer extends Viewer {
    public $template = 'add.html';

    public function prepare() {

        $request = &$this->registry['request'];

        require_once $this->modelsDir . 'outlooks.dropdown.php';
        require_once $this->modelsDir . 'outlooks.factory.php';

        $this->model = $this->registry['outlook'];

        $this->registry['include_dragNdrop'] = true;

        $this->model->clearNotPermittedVars();
        $this->model->sortColumns();

        $this->prepareTitleBar();

        if (!$this->model->get('user')) {
            //prepare roles options
            $params = array(0 => $this->registry,
                            'table' => 'DB_TABLE_ROLES',
                            'table_i18n' => 'DB_TABLE_ROLES_I18N',
                            'label' => 'ti18n.name',
                            'value' => 't.id',
                            'where' => 't.active = 1',
                            'assoc' => true);
            $roles = Outlooks_Dropdown::getCustomDropdown($params);

            $filters = array ('where' => array('o.module = \'' . $this->model->get('module') . '\'',
                                               'o.controller = \'' . $this->model->get('controller') . '\'',
                                               'o.model_id = \'' . ($this->model->get('model_id') ?: '0') . '\'',
                                               'o.section = \'' . ($this->model->get('section') ?: '0') . '\'',
                                               'oa.assigned_to IN (\'' . implode('\', \'', array_map(function($a) { return 'Roles-' . $a; }, array_keys($roles))) . '\')'),
                              'sanitize' => true,
                              'skip_settings' => true);
            $outlooks = Outlooks::search($this->registry, $filters);
            foreach ($outlooks as $outlook) {
                $roles = array_diff_key($roles, $outlook->get('assignments'));
            }

            $this->data['roles'] = $roles;
        } else {
            $filters = array('where' => array('u.id = ' . $this->model->get('user')), 'sanitize' => true);
            require_once PH_MODULES_DIR . 'users/models/users.factory.php';
            $user = Users::searchOne($this->registry, $filters);
            $this->model->set('user_assigned', $user->get('firstname') . ' ' . $user->get('lastname'));
            $this->model->set('assigned_to', $user->get('id'));
            $this->model->set('assignments_type', 'Users');
        }
    }

    public function prepareTitleBar() {
        $title = $this->i18n('outlooks_add');
        $this->data['title'] = $title;
    }
}

?>
