<?php

class Finance_Incomes_Reasons_Audit_Viewer extends Viewer {

    public $template = '_audit.html';

    public function prepare() {
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.audit.php';
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php';

        // prepare model audit
        $history = Finance_Incomes_Reasons_History::getData(
            $this->registry,
            array(
                'h_id' => $this->registry['request']->get('audit'),
                'model' => 'Finance_Incomes_Reason'
            ));
        if ($history) {
            $filters = array(
                'where' => array(
                    'fir.id = ' . $history[0]['model_id']
                ),
                'sanitize' => true
            );
            $model = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

            $this->data['audit'] = Finance_Incomes_Reasons_Audit::getData(
                $this->registry,
                array(
                    'parent_id' => $this->registry['request']->get('audit'),
                    'model_name' => 'Finance_Incomes_Reason',
                    'model_type' => $model->get('type'),
                    'action_type' => $history[0]['action_type']
                ));
            $this->data['audit_title'] = $this->i18n('audit_vars', array(
                $model->get('type_name') ?: $this->i18n('finance_incomes_reason_audit')
            ));
            $this->data['audit_legend'] = $this->i18n('audit_legend', array(
                $history[0]['user_name'],
                date('d.m.Y, H:i', strtotime($history[0]['h_date']))
            ));
        }

        $this->setFrameset('frameset_blank.html');
    }
}

?>
