<?php

class Finance_Documents_Statuses_List_Viewer extends Viewer {
    public $template = 'documents_statuses_list.html';
    public $filters = array();

    public function prepare() {
        require_once $this->modelsDir . 'finance.documents_statuses.factory.php';
        $filters = Finance_Documents_Statuses::saveSearchParams($this->registry);
        list($finance_documents_statuses, $pagination) = finance_Documents_Statuses::pagedSearch($this->registry, $filters);

        $this->data['finance_documents_statuses'] = $finance_documents_statuses;
        $this->data['pagination'] = $pagination;

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('finance_documents_statuses');
        $this->data['title'] = $title;
    }
}

?>
