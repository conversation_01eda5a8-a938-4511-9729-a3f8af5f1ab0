<?php

require_once('finance.repayment_plans_statuses.model.php');

/**
 * Finance_Repayment_Plan_Statuses model class
 */
Class Finance_Repayment_Plan_Statuses extends Model_Factory {
    public static $modelName = 'Finance_Repayment_Plan_Status';

    public static $itemsPerPage = 10;

    /**
     * Searches(prepare) IDs for the models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  IDs
     */
    public static function getIds(&$registry, &$filters = array(), &$sql = array()) {

        if (empty($sql)) {
            $sql = array('select' => '',
                         'from' => '',
                         'where' => '',
                         'group' => '',
                         'order' => '',
                         'limit' => '');
        }

        //where clause
        $where = self::constructWhere($registry, $filters);

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        //ORDER BY clause
        if (!empty($filters['sort'])) {
            $sort = implode(', ', $filters['sort']);
            if (!preg_match('#fds.active#', $sort)) {
                $sort = 'ORDER BY fds.active desc, ' . $sort;
            } else {
                $sort = 'ORDER BY ' . $sort;
            }
        } else {
            $sort = 'ORDER BY fds.active desc, fds.id ASC';
        }

        //select clause
        $sql['select']  = 'SELECT DISTINCT (fds.id) ';

        //select clause
        $sql['select'] = 'SELECT DISTINCT(fds.id) ';

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_STATUSES . ' AS fds ' . "\n" .
                        //relate to user to fetch added by info
                       'LEFT JOIN ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_STATUSES . ' AS fdsi18n' . "\n" .
                       '  ON (fdsi18n.id=fds.id AND fdsi18n.lang="' . $model_lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_TYPES . ' AS fdt' . "\n" .
                       '  ON (fds.doc_type=fdt.id AND fdt.lang="' . $model_lang . '")' . "\n" .
                        //relate to user to fetch added by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                       '  ON (fdsi18n.added_by=ui18n1.parent_id AND ui18n1.lang="' . $model_lang . '")' . "\n" .
                        //relate to user to fetch modified by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                       '  ON (fdsi18n.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $model_lang . '")' . "\n" .
                        //relate to user to fetch deleted by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n3' . "\n" .
                       '  ON (fdsi18n.deleted_by=ui18n3.parent_id AND ui18n3.lang="' . $model_lang . '")';

        //where clause
        $sql['where'] = $where . "\n";

        //group clause
        $sql['group_by'] = 'GROUP BY fds.id' . "\n";

        $sql['order'] = $sort  . "\n";

        //limit (for pagination)
        $sql['limit'] = (!empty($filters['limit'])) ? 'LIMIT ' . $filters['limit'] . "\n" : '';

        //search basic details with current lang parameters
        $query = implode("\n", $sql);
        $ids = $registry['db']->GetCol($query);

        return $ids;
    }

    public static function search(&$registry, $filters = array()) {

        //where clause
        $where = self::constructWhere($registry, $filters);

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        //ORDER BY clause
        if (!empty($filters['sort'])) {
            $sort = implode(', ', $filters['sort']);
            if (!preg_match('#fds.active#', $sort)) {
                $sort = 'ORDER BY fds.active desc, ' . $sort;
            } else {
                $sort = 'ORDER BY ' . $sort;
            }
        } else {
            $sort = 'ORDER BY fds.active desc, fds.id ASC';
        }

        //select clause
        $sql['select'] = 'SELECT fds.*, fdsi18n.name, fdsi18n.description, fdti18n.name as type_name,  ' . "\n" .
                         '  fdt.model AS type_model, "' . $model_lang . '" as model_lang,' . "\n" .
                         '  CONCAT(ui18n1.firstname, " ", ui18n1.lastname) as added_by_name, ' . "\n" .
                         '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as modified_by_name, ' . "\n" .
                         '  CONCAT(ui18n3.firstname, " ", ui18n3.lastname) as deleted_by_name ' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_STATUSES . ' AS fds ' . "\n" .
                        //relate to user to fetch added by info
                       'LEFT JOIN ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_STATUSES . ' AS fdsi18n' . "\n" .
                       '  ON (fdsi18n.id=fds.id AND fdsi18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to user to fetch added by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                       '  ON (fds.added_by=ui18n1.parent_id AND ui18n1.lang="' . $model_lang . '")' . "\n" .
                        //relate to user to fetch modified by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                       '  ON (fds.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $model_lang . '")' . "\n" .
                        //relate to user to fetch deleted by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n3' . "\n" .
                       '  ON (fds.deleted_by=ui18n3.parent_id AND ui18n3.lang="' . $model_lang . '")';

        //where clause
        $sql['where'] = $where . "\n";

        //group clause
        $sql['group_by'] = 'GROUP BY fds.id' . "\n";

        $sql['order'] = $sort  . "\n";

        //limit (for pagination)
        $sql['limit'] = (!empty($filters['limit'])) ? 'LIMIT ' . $filters['limit'] . "\n" : '';

        //search basic details with current lang parameters
        $query = implode("\n", $sql);
        $records = $registry['db']->GetAll($query);

        //create array of model instances
        if (isset($filters['sanitize'])) {
            $sanitize = $filters['sanitize'];
        } elseif (count($records) == 1) {
            $sanitize = false;
        } else {
            $sanitize = true;
        }
        $models = self::createModels($registry, $records, self::$modelName, $sanitize);

        if (!empty($filters['paginate'])) {
            //get the total count
            if ($sql['limit']) {
                //get the total number of records for this search
                $sql['select'] = 'SELECT COUNT(DISTINCT fds.id) AS total';
                $sql['limit'] = '';
                $sql['from1'] = '';
                $sql['group_by'] = '';
                $sql['order'] = '';
                $query = implode("\n", $sql);
                $total = $registry['db']->GetOne($query);
            } else {
                //there is no limit set,
                //get the count from the found records
                $total = count($models);
            }

            $results = array($models, $total);
        } else {
            //no pagination required return only the models
            $results = $models;
        }

        return $results;
    }

    /**
     * Searches exactly one model with specified filters
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return mixed - searched object model or false
     */
    public static function searchOne(&$registry, $filters = array()) {
        return self::getOne($registry, $filters, __CLASS__);
    }

    /**
     * Searches models for page with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array - with all necessary data for pagination of models
     */
    public static function pagedSearch(&$registry, &$filters = array()) {
        return self::paginatedSearch($registry, $filters, __CLASS__);
    }

    /**
     * construct the where clause
     */
    public static function constructWhere(&$registry, $filters = array()) {
        $where[] = 'WHERE (';

        if (!empty($filters['key'])) {
            if (!empty($filters['field'])) {
                $where[] = sprintf('(%s)',
                                    General::buildClause($filters['field'], trim($filters['key']), true, 'like'));
            } else {//search in all fields
                $module = $registry->get('module');
                $controller = $registry->get('controller');
                require_once(PH_MODULES_DIR . 'filters/models/filters.factory.php');
                $vars = Filters::getSimpleSearchDefinitions($registry);
                foreach ($vars as $row) {
                    $var = $row['option_value'];
                    $key_where[] = General::buildClause($var, trim($filters['key']), true, 'like');
                }
                $where []= '(' . implode(" OR \n\t", $key_where) . ')';
            }
        } elseif (isset($filters['where'])) {
            $current_user_id = $registry['currentUser']->get('id');
            foreach ($filters['where'] as $filter) {
                if(preg_match('/=\s*$/', $filter)) {
                    continue;
                }
                $filter = preg_replace('#currentUser#', $current_user_id, $filter);
                if(!preg_match('/(AND|OR)\s*$/', $filter)) {
                    $filter = $filter . ' AND ' . "\n";
                }
                $where[] = preg_replace('/\sAND$/', ') AND (', $filter) . "\n";
            }
        }

        $where = implode("\n\t", $where);

        $where = preg_replace('/\)\s(AND|OR)\s\(\n*$/', '', $where);
        $where = preg_replace('/\s(AND|OR)\s\n*$/', '', $where);
        $where .= ')';
        $where = preg_replace('/\s\(\)/', ' 1', $where);
        if (!preg_match('#fds.deleted#', $where)) {
            $where .= ' AND fds.deleted = 0';
        }

        return $where;
    }

    /**
     * Saves search params in the session
     */
    public static function saveSearchParams(&$registry, $filters = array(), $sessionPrefix = 'list_') {
        $sessionParam = strtolower($sessionPrefix . self::$modelName);

        $search = self::saveSearchFilters($registry, $sessionParam, $filters);

        return $search;
    }

    /**
     *
     */
    public static function buildModel(&$registry) {
        $model = self::buildFromRequest($registry, self::$modelName);

        return $model;
    }

    /**
     * Deletes specified models
     * Deletion is fake only mark records as deleted
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function delete(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        //get only unused stauses
        $ids = self::checkItemsForDeletion($registry, $ids);

        //multiple deletion is part of the transaction
        $deleted = self::deleteMultiple($registry, $ids, DB_TABLE_FINANCE_REPAYMENT_PLANS_STATUSES);

        if (!$deleted) {
            $db->FailTrans();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Check models for deletion
     *
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function checkItemsForDeletion(&$registry, $ids) {
        $db = $registry['db'];

        if (!is_array($ids)) {
            $ids = array($ids);
        }

        if (!count($ids)) {
            return $ids;
        }

        $sql['select'] = 'SELECT fds.id';
        $sql['from'] = 'FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_STATUSES . ' AS fds ' . "\n";
        $sql['from1'] = 'LEFT JOIN ' . DB_TABLE_FINANCE_REPAYMENT_PLANS . ' AS t' . "\n" .
                        '  ON (t.substatus=fds.id AND t.deleted=0)' . "\n";
        $sql['where'] = 'WHERE fds.id in (' . implode(', ', $ids) .')' . "\n";
        $sql['group_by'] = 'GROUP BY fds.id' . "\n";
        $sql['having'] = 'HAVING(count(t.id)) = 0';
        $query = implode("\n", $sql);
        $new_ids = $db->GetCol($query);

        return $new_ids;
    }

    /**
     * Restores deleted records (only those that are marked as deleted)
     * ATTENTION: Purged models cannot be restored!
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function restore(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        //multiple deletion is part of the transaction
        $restored = self::restoreMultiple($registry, $ids, DB_TABLE_FINANCE_REPAYMENT_PLANS_STATUSES);

        if (!$restored) {
            $db->FailTrans();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Changes status of specified models
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be changed
     * @param string $status - activate or deactivate
     * @return bool - result of the operations
     */
    public static function changeStatus(&$registry, $ids, $status) {
        $db = $registry['db'];
        if (empty($ids)) {
            return false;
        }

        $where = array();
        $where[] = General::buildClause('id', $ids);

        //INSERT INTO THE MAIN TABLE OF THE MODEL
        $set = array();
        $set['status']       = sprintf("active=%d", ($status == 'activate') ? 1 : 0);
        $set['modified']     = sprintf("modified=now()");

        $set['modified_by'] = sprintf("modified_by=%d", $registry['currentUser']->get('id'));

        //query to insert into the main table
        $query = 'UPDATE ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_STATUSES . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                 'WHERE ' . implode(' AND ', $where);

        //start transaction
        $db->StartTrans();
        $db->Execute($query);

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

}

?>
