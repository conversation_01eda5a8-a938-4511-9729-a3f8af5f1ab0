<?php

/**
 * Finance_Analysis_Type model class
 */
Class Finance_Analysis_Type extends Model {
    public $modelName = 'Finance_Analysis_Type';

    /**
     * Factories of elements
     */
    private $elements_factories = array('customers', 'offices', 'nomenclatures', 'projects');

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);
    }

    /**
     * Checks the validity of the model
     *
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {

        if (!$this->get('name')) {
            $this->raiseError('error_no_name_specified', 'name');
        }

        return $this->valid;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            //edit mode
            $action = 'edit';
        } else {
            $action = 'add';
        }

        if ($this->validate()) {
            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();

            if ($this->$action()) {
                return true;
            } else {
                $this->slashesStrip();

                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = $this->registry['db'];

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();
        $set['added']         = sprintf("added=now()");
        $set['added_by']      = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));

        //query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_FINANCE_ANALYSIS_CENTERS_TYPES . "\n" .
                  'SET ' . implode(', ', $set) . "\n";

        //start transaction
        $db->StartTrans();
        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError("add new analysis type base details", $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = $this->registry['db'];

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_FINANCE_ANALYSIS_CENTERS_TYPES . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');

        //start transaction
        $db->StartTrans();
        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError("edit analysis type base details", $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Update I18N table of the model
     *
     * @return bool - result of the operation
     */
    public function updateI18N() {
        $db = $this->registry['db'];
        //UPDATE THE I18N TABLE OF THE MODEL
        $update = array();
        if ($this->isDefined('name')){
            $update['name'] = sprintf("name='%s'", $this->get('name'));
        }
        if ($this->isDefined('description')){
            $update['description']  = sprintf("description='%s'", $this->get('description'));
        }

        $insert = $update;
        if (count($update)) {
            $insert['parent_id']  = sprintf("parent_id=%d", $this->get('id'));
            $insert['lang']       = sprintf("lang='%s'", $this->get('model_lang'));
            $insert['translated'] = sprintf("translated=now()");

            //query to insert/update the i18n table for the selected model language
            $query2 = 'INSERT INTO ' . DB_TABLE_FINANCE_ANALYSIS_CENTERS_TYPES_I18N . "\n" .
                      'SET ' . implode(', ', $insert) . "\n" .
                      'ON DUPLICATE KEY UPDATE ' . "\n" .
                      implode(', ', $update);

            $db->Execute($query2);

            if ($db->ErrorMsg()) {
                $this->registry['messages']->setError($db->ErrorMsg());
                $this->registry['logger']->dbError('editing analysis type i18n details', $db, $query2);
            }

            return !$db->HasFailedTrans();
        }
    }

    /**
     * Prepare data for insert/update in main table
     *
     * @return array - data
     */
    public function prepareMainData() {
        $set = array();

        if ($this->isDefined('kind')){
            $set['kind'] = sprintf("`kind`='%s'", $this->get('kind'));
        }

        $set['modified']       = sprintf("modified=now()");
        $set['modified_by']    = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));

        if ($this->isDefined('active')) {
            $set['active'] = sprintf("active=%d", $this->get('active'));
        }
        if ($this->isDefined('group')) {
            $set['group'] = sprintf("`group`=%d", $this->get('group'));
        }

        return $set;
    }

    /**
     * Checks model translations
     *
     * @return bool - array of available languages
     */
    public function getTranslations() {
        if (!$this->get('id')) {
            return array();
        }

        if ($this->isDefined('translations')) {
            return $this->get('translations');
        }

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        //select clause
        $sql['select'] = 'SELECT fati18n.lang ' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FINANCE_ANALYSIS_CENTERS_TYPES_I18N . ' AS fati18n' . "\n";

        //where clause
        $sql['where'] = 'WHERE fati18n.parent_id=' . $this->get('id') . "\n";

        $sql['order'] = 'ORDER BY fati18n.lang' . "\n";

        $query = implode("\n", $sql);

        $records = $this->registry['db']->GetCol($query);

        if ($records) {
            $this->set('translations', $records, true);
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $records;
    }

    /**
     * Prepares elements of analysis items and gets default distribution values of main center
     *
     * @return bool - result of operation
     */
    public function prepareElements() {

        //get distribution values from DB
        $elements_distribution = $this->getElementsDefaultDistributionValues();

        //get centers
        require_once PH_MODULES_DIR . 'finance/models/finance.analysis_centers.factory.php';
        $filters = array('model_lang' => $this->get('model_lang'),
                         'sanitize' => true,
                         'where' => array('fac.type=' . $this->get('id'),
                                          'fac.deleted_by=0',
                                          'fac.active=1'));
        $centers = Finance_Analysis_Centers::search($this->registry, $filters);

        //get elements
        require_once PH_MODULES_DIR . 'finance/models/finance.analysis_items.factory.php';
        $filters_all = array('model_lang' => $this->get('model_lang'),
                             'sanitize' => true,
                             'where' => array('fai.active=1', 'fai.deleted_by=0'));
        //if main center is income/expense, get only items of this kind
        if (in_array($this->get('kind'), array('income', 'expense'))) {
            $filters_all['where'][] = 'fai.type="' . $this->get('kind') . '"';
        }

        $this->registry->set('getElementsFilters', true, true);

        foreach ($this->elements_factories as $elements_factory) {
            $filters = $filters_all;
            $filters['where'][] = 'fai.elements_factory="' . $elements_factory . '"';
            $analysis_items = Finance_Analysis_Items::search($this->registry, $filters);

            $elements = array();
            $elements_ids = array();

            foreach ($analysis_items as $analysis_item) {
                $elements_ids_item = $this->getElementsIdsOfAnalysisItem($analysis_item);

                $elements_ids = array_merge($elements_ids, $elements_ids_item);
            }

            $elements_arrays = array('names' => array(), 'elements' => array(), 'percentage' => array());
            foreach ($centers as $center) {
                $elements_arrays['percentage'][$center->get('id')] = array();
            }

            if ($elements_ids) {
                $elements_ids = array_values(array_unique($elements_ids));
                $elements = $elements_factory::getElementsForDistribution($this->registry, $elements_ids);

                foreach ($elements as $idx => $element) {
                    $elements_arrays['names'][$idx] = $element['name'];
                    $elements_arrays['elements'][$idx] = $element['element'];

                    foreach ($elements_distribution as $row) {
                        $count_centers = 0;
                        if ($elements_factory == $row['elements_factory'] && $element['element'] == $row['element'] &&
                        array_key_exists($row['center'], $elements_arrays['percentage'])) {
                            $elements_arrays['percentage'][$row['center']][$idx] = $row['percentage'];
                            $count_centers++;
                        }
                        if ($count_centers == count($centers)) {
                            break;
                        }
                    }
                }
            }

            $this->set($elements_factory, $elements_arrays, true);
        }

        return true;
    }

    /**
     * Gets default distribution values of main center from db
     *
     * @return array - result of operation
     */
    public function getElementsDefaultDistributionValues() {

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $precision = $this->registry['config']->getParam('precision', 'finance_analysis_percentage');

        $query = 'SELECT *, ROUND(percentage, ' . $precision . ') AS percentage' . "\n" .
                 '  FROM ' . DB_TABLE_FINANCE_ANALYSIS_CENTERS_DISTRIBUTION . "\n" .
                 '  WHERE parent_id=' . $this->get('id');
        $elements_distribution = $this->registry['db']->GetAll($query);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $elements_distribution;
    }

    /**
     * Deletes default distribution values of main center
     *
     * @return bool - result of operation
     */
    public function deleteElementsDefaultDistributionValues() {

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $query = 'DELETE FROM ' . DB_TABLE_FINANCE_ANALYSIS_CENTERS_DISTRIBUTION . "\n" .
                 '  WHERE parent_id=' . $this->get('id');
        $result = $this->registry['db']->Execute($query);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $result;
    }

    /**
     * Saves default distribution values of main center
     *
     * @return bool - result of operation
     */
    public function updateElementsDefaultDistributionValues() {
        //round every percentage up to a certain digit after decimal point
        $precision = $this->registry['config']->getParam('precision', 'finance_analysis_percentage');

        $db = $this->registry['db'];

        $elements_distribution_post = array();

        //go through sub-arrays with values of each elements_factory in model
        foreach ($this->elements_factories as $elements_factory) {
            if ($this->isDefined($elements_factory)) {
                $distribution = $this->get($elements_factory);

                //if main center has no centers, return and display error message
                if (!isset($distribution['percentage'])) {
                    $this->registry['messages']->setError($this->i18n('error_no_centers'));
                    return false;
                }

                foreach ($distribution['percentage'] as $center => $percentage) {
                    $distribution['percentage'][$center] = array_map('round',
                                                                     $percentage,
                                                                     array_fill(0, count($percentage), $precision));
                }

                // validate each element for total percentage
                $elements_num = count($distribution['elements']);

                for ($i = 0; $i < $elements_num; $i++) {
                    $total_percentage_element = 0;
                    foreach ($distribution['percentage'] as $center => $percentage) {
                        $total_percentage_element += $percentage[$i];
                    }

                    $total_percentage_element = round($total_percentage_element, $precision);

                    if ($total_percentage_element !== (float)100) {
                        $this->raiseError('error_total_centers_percentage', 'percentage');
                        return false;
                    }
                }

                //assemble rows from POST
                for ($i = 0; $i < $elements_num; $i++) {
                    foreach ($distribution['percentage'] as $center => $percentage) {
                        $elements_distribution_post[] = array('parent_id' => $this->get('id'),
                                                              'elements_factory' => $elements_factory,
                                                              'element' => $distribution['elements'][$i],
                                                              'center' => $center,
                                                              'percentage' => sprintf('%.' . $precision . 'F', $percentage[$i]));
                    }
                }
            }
        }

        $elements_insert = array();
        $elements_update = array();
        $elements_delete = array();

        //get old values for elements distribution from DB
        $elements_distribution_old = $this->getElementsDefaultDistributionValues();

        if ($elements_distribution_old) {
            foreach ($elements_distribution_old as $idx => $row) {
                $row_found = false;
                foreach ($elements_distribution_post as $idx2 => $row2) {
                    if ($row['elements_factory'] == $row2['elements_factory'] && $row['element'] == $row2['element'] && $row['center'] == $row2['center']) {
                        if ($row['percentage'] != $row2['percentage']) {
                            //row should be updated
                            $elements_update[] = 'percentage = ' . $row2['percentage'] . ' WHERE id = ' . $row['id'];
                        }
                        $row_found = true;

                        unset($elements_distribution_post[$idx2]);

                        break;
                    }
                }
                if (!$row_found) {
                    //row should be deleted
                    $elements_delete[] = $row['id'];
                }
            }
        }

        $elements_distribution_post = array_values($elements_distribution_post);
        foreach ($elements_distribution_post as $idx => $row) {
            //row should be inserted
            $elements_insert[] = '("' . implode('", "', $row) . '")';
        }

        //start transaction
        $db->StartTrans();

        if ($elements_delete) {
            $query_delete = 'DELETE FROM ' . DB_TABLE_FINANCE_ANALYSIS_CENTERS_DISTRIBUTION . "\n" .
                            'WHERE id IN (' . implode(', ', $elements_delete) . ')';
            $db->Execute($query_delete);
        }

        if ($elements_update) {
            $sql = 'UPDATE ' . DB_TABLE_FINANCE_ANALYSIS_CENTERS_DISTRIBUTION . ' SET ' . "\n";
            foreach ($elements_update as $idx => $row) {
                $query_update = $sql . $row;
                $db->Execute($query_update);
            }
        }

        if ($elements_insert) {
            $sql = 'INSERT INTO ' . DB_TABLE_FINANCE_ANALYSIS_CENTERS_DISTRIBUTION . "\n" .
                   '(parent_id, elements_factory, element, center, percentage) VALUES' . "\n";
            $max_num_rows = PH_MAX_ROWS_INSERT;
            $i = 0;
            while ($insert_chunk = array_slice($elements_insert, $i++*$max_num_rows, $max_num_rows)) {
                $query_insert = $sql . implode(",\n", $insert_chunk);
                $db->Execute($query_insert);
            }
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }
}

?>
