<h1>{$title}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td id="form_container">
      <form name="incomes_reasons" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=incomes_reasons" method="post" enctype="multipart/form-data">
      {if $smarty.request.autocomplete_filter}
        {assign var='uniqid' value=$smarty.request.uniqid}
        {assign var='autocomplete_params' value=$smarty.session.autocomplete_params.$uniqid}
        {json assign='autocomplete_params_json' encode=$autocomplete_params}
        <input type="hidden" name="autocomplete_params" id="autocomplete_params" value="{$autocomplete_params_json|escape}" />
      {/if}
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall">
          {if !$autocomplete_params || $autocomplete_params.select_multiple}
            {include file="`$theme->templatesDir`_select_items.html"
              pages=$pagination.pages|default:1
              total=$pagination.total|default:$incomes_reasons|@count
              session_param=$session_param|default:$pagination.session_param
            }
          {/if}
          </td>
          <td class="t_caption t_border" nowrap="nowrap" width="15"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#finance_incomes_reasons_name#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#finance_incomes_reasons_customer#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#finance_type#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#date#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#finance_incomes_reasons_date_of_payment#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#gt2_total_with_vat#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#finance_repayment_plans_unpaid_amount#|escape}</div></td>
          <td class="t_caption">&nbsp;</td>
        </tr>
      {foreach name='i' from=$incomes_reasons item='incomes_reason'}
      {strip}
      {capture assign='info'}
        <strong>{#finance_incomes_reasons_name#|escape}:</strong> {$incomes_reason->get('name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$incomes_reason->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$incomes_reason->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$incomes_reason->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$incomes_reason->get('modified_by_name')|escape}<br />

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$incomes_reason->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $incomes_reason->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span>
      {/capture}
      {/strip}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="t_border">
          {if $autocomplete_params && !$autocomplete_params.select_multiple}
            <input type="checkbox" name='items[]' value="{$incomes_reason->get('id')}" title="{#check_to_include#|escape}" onclick="return clickOnce(this);" />
          {else}
            <input type="checkbox"
                   onclick="setCheckAllBox(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: '{$controller}',
                                            action: '{$action}',
                                            button_id: '{$module}_{$controller}_{$action}_checkall_1'
                                           {rdelim});"  
                   name='items[]' 
                   value="{$incomes_reason->get('id')}"
                   title="{#check_to_include#|escape}" />
            {/if}
          </td>
          <td class="t_border hright">{counter name='item_counter' print=true}</td>
          <td class="t_border {$sort.name.isSorted}" nowrap="nowrap"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=incomes_reasons&amp;{$action_param}=view&amp;view={$incomes_reason->get('id')}">{$incomes_reason->get('name')|escape}</a></td>
            <div id="rf{$incomes_reason->get('id')}" style="display: none">{$incomes_reason->get('name')|escape|default:"&nbsp;"} <input type="hidden" id="unpaid{$incomes_reason->get('id')}" value="{$incomes_reason->get('unpaid_amount')|escape}" /></div>
          <td class="t_border" nowrap="nowrap">
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$incomes_reason->get('customer')}" title="{#view#|escape}: {$incomes_reason->get('customer_name')|escape}">{$incomes_reason->get('customer_name')|escape|default:"&nbsp;"}</a>
          </td>
          <td class="t_border" nowrap="nowrap">
            {$incomes_reason->get('type_name')|escape}
          </td>
          <td class="t_border" nowrap="nowrap" align="right">
            {$incomes_reason->get('status_modified')|date_format:#date_long#}
          </td>
          <td class="t_border" nowrap="nowrap" align="right">
            {$incomes_reason->get('issue_date')|date_format:#date_short#}
          </td>
          <td class="t_border" nowrap="nowrap" align="right">
            {$incomes_reason->get('total_with_vat')|escape} {$incomes_reason->get('currency')|escape}
          </td>
          <td class="t_border" nowrap="nowrap" align="right">
            {$incomes_reason->get('unpaid_amount')|escape} {$incomes_reason->get('currency')|escape}
          </td>
          <td class="hcenter" nowrap="nowrap">
            {include file=`$theme->templatesDir`single_actions_list.html object=$incomes_reason exclude='edit,delete,view'}
          </td>
        </tr>
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="14">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="14"></td>
        </tr>
      </table>
      <br />
      <br />
      <table border="0" cellpadding="0" cellspacing="0">
        <tr>
          <td>
          {strip}
          {if $smarty.request.autocomplete_filter}
            {if $autocomplete_params.select_multiple}
              <button type="button" name="linktButton" class="button" onclick="updateParentAutocomplete({ldelim}close_window: false{rdelim});">{#select#|escape}</button>
            {/if}
            <button type="button" name="linktButton" class="button" onclick="updateParentAutocomplete({ldelim}close_window: true{rdelim});">{#select#|escape} &amp; {#close#|escape}</button>
          {else}
            <button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, 'items')) {ldelim}return confirmAction('link', function(el) {ldelim}updateRepaymentReferers(el.form, 0);{rdelim}, this, '{#confirm_link_incomes_reasons#|escape:'quotes'|escape}');{rdelim}else{ldelim}alert('{#alert_link_incomes_reasons#|escape:'quotes'|escape}'); return false;{rdelim}">{#select#|escape}</button>
            <button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, 'items')) {ldelim}return confirmAction('link', function(el) {ldelim}updateRepaymentReferers(el.form, 1);{rdelim}, this, '{#confirm_link_incomes_reasons#|escape:'quotes'|escape}');{rdelim}else{ldelim}alert('{#alert_link_incomes_reasons#|escape:'quotes'|escape}'); return false;{rdelim}">{#select#|escape} &amp; {#close#|escape}</button>
          {/if}
          {/strip}
          </td>
        </tr>
      </table>
      </form>
    </td>
  </tr>
</table>
