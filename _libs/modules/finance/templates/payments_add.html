<h1>{$title|escape}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<form name="finance" enctype="multipart/form-data" action="{$submitLink}" method="post">
<input type="hidden" name="model_lang" id="model_lang" value="{$finance_payment->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td class="t_footer"></td>
  </tr>
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
      {foreach from=$finance_payment->get('layouts_details') key='lkey' item='layout'}

        {if in_array($finance_payment->get('type'), array('PN', 'RKO')) && $lkey eq 'customer_num' || in_array($finance_payment->get('type'), array('PKO', 'RKO')) && $lkey eq 'currency' || $finance_payment->get('type') eq 'PN' && $lkey eq 'payment_way' || !in_array($lkey, array('num', 'customer_num', 'currency', 'payment_way'))}
        <tr{if !$layout.view || !$layout.visible} style="display: none;"{/if}>
          <td colspan="3" class="t_caption3 pointer">
            <div class="floatr index_arrow_anchor">
              <a href="#vars_index"><img src="{$theme->imagesUrl}arrow_top.png" border="0" title="{#back_to_index#|escape}" alt="{#back_to_index#|escape}" /></a>
            </div>
            <div class="layout_switch" onclick="toggleViewLayouts(this)" id="finance_payment_{$layout.keyword}_switch">
              <a name="finance_payment_{$layout.keyword}_index"></a><div class="switch_{if $layout.cookie eq 'off'}expand{else}collapse{/if}"></div><div class="t_caption2_title">{$layout.name|escape}</div>
            </div>
          </td>
        </tr>
        {/if}

        {if $lkey eq 'type'}
        <tr id="finance_payment_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_type"><label for="type"{if $messages->getErrors('type')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            {$finance_payment->get('type_name')|escape}
            <input type="hidden" name="type" id="type" value="{$finance_payment->get('type')}" />
          </td>
        </tr>
        {elseif $lkey eq 'customer'}
        <tr id="finance_payment_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_customer"><label for="customer"{if $messages->getErrors('customer')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            {capture assign='ac_readonly'}{if $layout.edit}0{else}1{/if}{/capture}
            {include file=`$theme->templatesDir`input_autocompleter.html
                     name='customer'
                     autocomplete_type='customers'
                     autocomplete_var_type='basic'
                     autocomplete_buttons='add search clear'
                     value=$finance_payment->get('customer')
                     value_code=$finance_payment->get('customer_code')
                     value_name=$finance_payment->get('customer_name')
                     readonly=$ac_readonly
                     width=266
                     standalone=true
                     label=$layout.name
                     help=$layout.description
            }
          </td>
        </tr>
        {elseif $lkey eq 'trademark'}
        <tr id="finance_payment_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_trademark"><label for="trademark"{if $messages->getErrors('trademark')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {capture assign='ac_readonly'}{if $layout.edit}0{else}1{/if}{/capture}
            {include file=`$theme->templatesDir`input_autocompleter.html
                     name='trademark'
                     autocomplete_type='nomenclatures'
                     autocomplete_var_type='basic'
                     autocomplete_buttons='search clear'
                     value=$finance_payment->get('trademark')
                     value_name=$finance_payment->get('trademark_name')
                     readonly=$ac_readonly
                     width=244
                     standalone=true
                     label=$layout.name
                     help=$layout.description
            }
          </td>
        </tr>
        {elseif $lkey eq 'project'}
        <tr id="finance_payment_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_project"><label for="project"{if $messages->getErrors('project')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {capture assign='ac_readonly'}{if $layout.edit}0{else}1{/if}{/capture}
            {include file=`$theme->templatesDir`input_autocompleter.html
                     name='project'
                     autocomplete_type='projects'
                     autocomplete_var_type='basic'
                     autocomplete_buttons='add search clear'
                     value=$finance_payment->get('project')
                     value_code=$finance_payment->get('project_code')
                     value_name=$finance_payment->get('project_name')
                     readonly=$ac_readonly
                     width=266
                     standalone=true
                     label=$layout.name
                     help=$layout.description
            }
            {if !$ac_readonly}
              <span class="help" {help label='payments_phase' popup_only='1'}>&nbsp;</span>
              {include file='input_dropdown.html'
                       standalone=true
                       name='phase'
                       options=$phases
                       no_select_records_label=$smarty.config.project_phase
                       first_option_label=$smarty.config.project_phase
                       width=100
                       value=$finance_payment->get('phase')
                       label=#finance_payments_phase#
              }
            {else}
              {if $finance_payment->get('phase')} <span class="labelbox">{help label_content=#finance_payments_phase#}</span> {$finance_payment->get('phase_name')|escape}{/if}
              <input type="hidden" name="phase" id="phase" value="{$finance_payment->get('phase')|default:0}" />
            {/if}
          </td>
        </tr>
        {elseif in_array($finance_payment->get('type'), array('PN', 'RKO')) && $lkey eq 'customer_num'}
        <tr id="finance_payment_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_customer_num"><label for="customer_num"{if $messages->getErrors('customer_num')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $layout.edit}
              <input type="text" class="txtbox" name="customer_num" id="customer_num" value="{$finance_payment->get('customer_num')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
            {else}
              {$finance_payment->get('customer_num')|escape}
              <input type="hidden" name="customer_num" id="customer_num" value="{$finance_payment->get('customer_num')|escape}" />
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'company_data'}
        <tr id="finance_payment_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_company_data"><label for="company_data"{if $messages->getErrors('company_data')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            {if $layout.edit}
              {capture assign='company_data_value'}{if $finance_payment->get('company_data') && $finance_payment->get('company_data') ne '0_0_0_0'}{$finance_payment->get('company_data')}{/if}{/capture}
              {include file='input_dropdown.html'
                       standalone=true
                       name='company_data'
                       index=0
                       optgroups=$companies_data
                       value=$company_data_value
                       required=1
                       really_required=1
                       readonly=0
                       hidden=0
                       width=200
                       label=$layout.name
                       help=$layout.description
              }
            {else}
              {if $finance_payment->get('company_data')}
                {foreach from=$companies_data key='company_name' item='company_data'}
                  {foreach from=$companies_data.$company_name item='data'}
                    {if $data.option_value eq $finance_payment->get('company_data')}<span class="strong">{$company_name|escape}</span> {$data.label|escape}{/if}
                  {/foreach}
                {/foreach}
              {/if}
              <input type="hidden" name="company_data" id="company_data" value="{$finance_payment->get('company_data')}" />
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'amount'}
        <tr id="finance_payment_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_amount"><label for="amount"{if $messages->getErrors('amount')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            {if $layout.edit}
              <input type="text" class="txtbox short hright" name="amount" id="amount" value="{$finance_payment->get('amount')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="return changeKey(this, event, insertOnlyFloats);" />
            {else}
              {$finance_payment->get('amount')|escape}
              <input type="hidden" name="amount" id="amount" value="{$finance_payment->get('amount')|escape}" />
            {/if}
          </td>
        </tr>
        {elseif in_array($finance_payment->get('type'), array('PKO', 'RKO')) && $lkey eq 'currency'}
        <tr id="finance_payment_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_currency"><label for="currency"{if $messages->getErrors('currency')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            {if $layout.edit}
              <select class="selbox short" name="currency" id="currency" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">
                {foreach from=$currency item='option'}
                  <option value="{$option.currency_code|escape}"{if $option.currency_code eq $finance_payment->get('currency')} selected="selected"{/if}>{$option.currency_code|escape}</option>
                {/foreach}
              </select>
            {else}
              {$finance_payment->get('amount')|escape}
              {foreach from=$currency item='option'}
                {if $option.currency_code eq $finance_payment->get('currency')}{$option.currency_code|escape}{/if}
              {/foreach}
              <input type="hidden" name="currency" id="currency" value="{$finance_payment->get('currency')|escape}" />
            {/if}
          </td>
        </tr>
        {elseif $finance_payment->get('type') eq 'PN' && $lkey eq 'payment_way'}
        <tr id="finance_payment_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_payment_way"><label for="payment_way"{if $messages->getErrors('payment_way')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            {if $layout.edit}
              {array assign='payment_ways' eval='array(\'RINGS\', \'internal\', \'local\', \'international\')'}
              <select class="selbox" name="payment_way" id="payment_way" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">
                {foreach from=$payment_ways item='option_value'}
                  {capture assign='option_label'}finance_payments_payment_way_{$option_value}{/capture}
                  <option value="{$option_value}"{if $option_value eq $finance_payment->get('payment_way')} selected="selected"{/if}>{$smarty.config.$option_label|escape}</option>
                {/foreach}
              </select>
            {else}
              {if $finance_payment->get('payment_way')}
                {capture assign='option_label'}finance_payments_payment_way_{$finance_payment->get('payment_way')}{/capture}
                {$smarty.config.$option_label|escape}
              {else}
                -
              {/if}
              <input type="hidden" name="payment_way" id="payment_way" value="{$finance_payment->get('payment_way')|escape}" />
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'issue_date'}
        <tr id="finance_payment_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_issue_date"><label for="issue_date"{if $messages->getErrors('issue_date')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $layout.edit}
              {include file="input_date.html"
                       standalone=true
                       name='issue_date'
                       value=$finance_payment->get('issue_date')
                       width=200
                       show_calendar_icon=true
                       disallow_date_after=true
                       label=$layout.name
                       help=$layout.description
              }
            {else}
              {if $finance_payment->get('issue_date') ne 0}{$finance_payment->get('issue_date')|escape|date_format:#date_short#}{/if}
              <input type="hidden" name="issue_date" id="issue_date" value="{$finance_payment->get('issue_date')|escape}" />
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'reason'}
        <tr id="finance_payment_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_reason"><label for="reason"{if $messages->getErrors('reason')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $layout.edit}
              <textarea class="areabox" name="reason" id="reason" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$finance_payment->get('reason')|escape}</textarea>
            {else}
              {$finance_payment->get('reason')|escape|nl2br}
              <input type="hidden" name="reason" id="reason" value="{$finance_payment->get('reason')|escape}" />
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'note'}
        <tr id="finance_payment_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_note"><label for="note"{if $messages->getErrors('note')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $layout.edit}
              <textarea class="areabox" name="note" id="note" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$finance_payment->get('note')|escape}</textarea>
            {else}
              {$finance_payment->get('note')|escape|nl2br}
              <input type="hidden" name="note" id="note" value="{$finance_payment->get('note')|escape}" />
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'employee'}
        <tr id="finance_payment_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_employee"><label for="employee"{if $messages->getErrors('employee')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $layout.edit}
              {include file=`$theme->templatesDir`input_autocompleter.html
                       name='employee'
                       autocomplete_type='customers'
                       stop_customer_details=1
                       autocomplete_var_type='basic'
                       autocomplete_buttons='search clear'
                       value=$finance_payment->get('employee')
                       value_name=$finance_payment->get('employee_name')
                       filters_array=$autocomplete_employee_filters
                       width=244
                       standalone=true
                       label=$layout.name
                       help=$layout.description
              }
            {else}
              {$finance_payment->get('employee_name')|escape}
              <input type="hidden" name="employee" id="employee" value="{$finance_payment->get('employee')|default:0}" />
            {/if}
          </td>
        </tr>
        {/if}
      {/foreach}
        <tr>
          <td colspan="3" style="padding: 15px;">
            {strip}
            <input type="hidden" name="status" id="status" value="" />
            {foreach from=$after_action_options item='fin_action' name='ai' key='ak'}
              <button type="submit" name="saveButton1" id="submit_{$ak}" class="button" onclick="$('status').value='{$fin_action.option_value}';" title="{$fin_action.description|escape}">{$fin_action.label|escape}</button>
            {/foreach}
            {include file=`$theme->templatesDir`cancel_button.html}
            {/strip}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$finance_payment exclude='is_portal,active'}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
