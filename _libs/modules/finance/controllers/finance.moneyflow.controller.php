<?php

class Finance_Moneyflow_Controller extends Controller {
    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array();

    /**
     * After action definitions for this controller
     */
    public $afterActionDefinitions = array();

    /**
     * generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch($this->action) {
        case 'index':
        default:
            $this->setAction('index');
            $this->_index();
        }
    }

    /**
     * Frontend home page
     */
    public function _index() {
        //everything is in the viewer
        return true;
    }


}

?>
