/**
 * Object for the current dashlet
 */
var dashletLazyReporting = {
    /**
     * Set some defaults for the dashlet
     *
     * @param dashlet_id             - the ID of the current dashlet
     * @param current_active_task_id - the ID of the currently active task
     * @return boolean true|false
     */
    setDefaults: function (dashlet_id, current_active_task_id) {
        // Set the dashlet ID
        this.id = dashlet_id;

        // Set the current active task id
        this.current_active_task_id = current_active_task_id;

        return true;
    },

    /**
     * Activate the currently selected task button (i.e. show the lightbox)
     *
     * @param current_button           - the DOM object of the current button
     * @param current_selected_task_id - the ID of the currently selected task
     * @return boolean true
     */
    activate: function (current_button, current_selected_task_id) {
        // Disable the current button (prevent from doubleclick)
        current_button.disabled = true;

        // Show loading effect
        Effect.Center('loading');
        Effect.Appear('loading');

        // If the current active task is the "rest" one
        if (this.current_active_task_id == 'rest') {
            // Deactivate the "rest" task button
            $('stopwatch_task_rest').style.display  = 'none';
            $('startwatch_task_rest').style.display = '';

            // Start the watch for the current selected task
            if (this.startWatch(current_selected_task_id)) {
                // Set that the current selected task is the current active task
                this.current_active_task_id = current_selected_task_id;
            }

            // Fade the loading effect
            Effect.Fade('loading');

            // Enable the current button
            current_button.disabled = false;
        } else {
            // Set the current selected task id as variable of the current dashlet object so that it can be used further by the object`s functions
            this.current_selected_task_id = current_selected_task_id;

            // Prepare the AJAX URL
            var url = env.base_url + '?' + env.module_param + '=dashlets' +
                '&dashlets=custom_action' +
                '&plugin=lazy_reporting' +
                '&dashlet=' + this.id +
                '&custom_plugin_action=prepareLightbox' +
                '&task_id=' + this.current_active_task_id +
                '&customer_name=' + $('stopwatch_task_' + this.current_active_task_id).innerHTML.replace('<hr>', ' (') + ')';
            // Prepare the AJAX options
            var opt = {
                method:    'get',
                onSuccess: function(t) {
                    if (!checkAjaxResponse(t.responseText)) {
                        return;
                    }
                    // Get the AJAX result
                    eval('var ajax_result = ' + t.responseText);

                    // Set default lightbox width
                    if (!ajax_result.width) {
                        ajax_result.width = '400px';
                    }

                    // Set default lightbox height
                    if (!ajax_result.height) {
                        ajax_result.height = '';
                    }

                    // Build the lightbox
                    lb = new lightbox(ajax_result);

                    // Activate the lightbox
                    lb.activate();

                    // Enable the current button
                    current_button.disabled = false;
                },
                on404: function(t) {
                    alert('Error 404: location "' + t.statusText + '" was not found.');
                },
                onFailure: function(t) {
                    alert('Error ' + t.status + ' -- ' + t.statusText);
                },
                onComplete: function () {
                    // Fade the loading effect
                    Effect.Fade('loading');
                }
            };

            // Execute the AJAX
            new Ajax.Request(url, opt);
        }

        return true;
    },

    /**
     * Add timesheet and start the watch for the currently selected task
     *
     * @param form - the DOM object of the current form
     */
    addTimesheet: function (form) {
        // Disable the current button (prevent from doubleclick)
        form.addTimeSheet.disabled = true;

        /// If the timesheet description (i.e. the content) is empty
        if (form.content.value.trim() == '') {
            // Alert: description is required
            alert(i18n['messages']['error_valid_content']);

            // Enable the current button
            form.addTimeSheet.disabled = false;
        } else {
            // Show loading effect
            Effect.Appear('loading');
            Effect.Center('loading');

            // Prepare the AJAX URL
            var url = env.base_url + '?' + env.module_param + '=tasks' +
                '&tasks=ajax_watch' +
                '&model_id=' + this.current_active_task_id +
                '&model=task' +
                '&action=stop';
            // Prepare the AJAX options
            var opt = {
                method:    'post',
                onSuccess: function(t) {
                    if (!checkAjaxResponse(t.responseText)) {
                        return;
                    }
                    // If there is a response and it starts with: data=
                    if (t.responseText && t.responseText.match(/^data=/)) {
                        // Eval the response
                        eval(t.responseText);

                        // Remove the timer icon
                        $('m_started_timers').innerHTML = '';

                        // Get the ID of the currently active task
                        var task_id = dashletLazyReporting.current_active_task_id;

                        // If there's no result from the AJAX
                        if (!data.result) {
                            // Alert: invalid stop watch
                            alert(i18n['messages']['stop_watch_invalid']);
                            // If there are stop and start buttons for this task
                            if ($('startwatch_task_' + task_id) && $('stopwatch_task_' + task_id)) {
                                // Show the stop button
                                $('startwatch_task_' + task_id).style.display = 'none';
                                $('stopwatch_task_' + task_id).style.display = '';
                            }

                            // Enable the currently pressed button
                            form.addTimeSheet.disabled = false;

                            // Exit
                            return false;
                        } else {
                            // Show the start button for the currently active task
                            $('stopwatch_task_' + task_id).style.display = 'none';
                            $('startwatch_task_' + task_id).style.display = '';
                        }

                        // Get the current date and time
                        var now = new Date();
                        var end_date = now.getFullYear() + '-' +
                            ((now.getMonth() < 9) ? '0'+(now.getMonth()+1) : now.getMonth()+1) + '-' +
                            ((now.getDate() < 10) ? '0'+now.getDate() : now.getDate()) + ' ' +
                            ((now.getHours() < 10) ? '0'+now.getHours() : now.getHours()) + ':' +
                            ((now.getMinutes() < 10) ? '0'+now.getMinutes() : now.getMinutes()) + ':00';

                        // Set the start date into the form
                        form.startperiod_dates.value = data.start_date;
                        // Set the end date into the form
                        form.endperiod_dates.value   = end_date;

                        // Save the timesheet
                        saveTimesheet(form,'timesheets_ajax_tasks_' + task_id);

                        // If the currently selected task is "rest"
                        if (dashletLazyReporting.current_selected_task_id == 'rest') {
                            // Show the stop button for it
                            $('startwatch_task_rest').style.display = 'none';
                            $('stopwatch_task_rest').style.display  = '';
                        } else {
                            // Start the watch for the selected task
                            dashletLazyReporting.startWatch(dashletLazyReporting.current_selected_task_id);
                        }

                        // Set that the currently selected task is already the current active task
                        dashletLazyReporting.current_active_task_id = dashletLazyReporting.current_selected_task_id;

                        // Hide the lightbox
                        lb.deactivate();

                        // Enable the current button
                        form.addTimeSheet.disabled = false;
                    }
                },
                on404: function(t) {
                    alert('Error 404: location "' + t.statusText + '" was not found.');
                },
                onFailure: function(t) {
                    alert('Error ' + t.status + ' -- ' + t.statusText);
                },
                onComplete: function () {
                    // Fade the loading effect
                    Effect.Fade('loading');
                }
            };

            // Execute the AJAX
            new Ajax.Request(url, opt);
        }
    },

    /**
     * Start the watch for a given task
     *
     * @param model_id - the ID of the selected task
     * @return boolean true
     */
    startWatch: function (model_id) {
        var result = false;

        // Get the current date and time
        var now = new Date();
        var start_date = now.getFullYear() + '-' +
            ((now.getMonth() < 9) ? '0'+(now.getMonth()+1) : now.getMonth()+1) + '-' +
            ((now.getDate() < 10) ? '0'+now.getDate() : now.getDate()) + ' ' +
            ((now.getHours() < 10) ? '0'+now.getHours() : now.getHours()) + ':' +
            ((now.getMinutes() < 10) ? '0'+now.getMinutes() : now.getMinutes()) + ':00';

        // Prepare the AJAX URL
        var url = env.base_url + '?' + env.module_param + '=tasks' +
            '&tasks=ajax_watch' +
            '&model_id=' + model_id +
            '&model=task' +
            '&date='+ start_date +
            '&action=start';
        // Prepare the AJAX options
        var opt = {
            method:       'post',
            asynchronous: false,
            onSuccess:    function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return;
                }
                // If there is a response and it starts with: data=
                if (t.responseText && t.responseText.match(/^data=/)) {
                    // Eval the response
                    eval(t.responseText);

                    if (!data.result) {
                        started_timer = $('m_started_timers').getElementsByTagName('img');
                        if (started_timer && started_timer.length) {
                            model = started_timer[0].className.replace(/icon_button pointer /, '');
                            alert(i18n['messages']['start_watch_invalid_' + model]);
                            Effect.Pulsate(started_timer[0], {pulses: 3});
                        } else {
                            alert(i18n['messages']['start_watch_invalid_other']);
                        }
                        return false;
                    }

                    if ($('startwatch') && $('stopwatch')) {
                        startwatch = $('startwatch');
                        stopwatch = $('stopwatch');
                    } else if ($('startwatch_task' + '_' + model_id) && $('stopwatch_task' + '_' + model_id)) {
                        startwatch = $('startwatch_task' + '_' + model_id);
                        stopwatch = $('stopwatch_task' + '_' + model_id);
                    }
                    startwatch.style.display = 'none';
                    stopwatch.style.display = '';

                    overlib_text = i18n['labels']['stopwatch_started_prefix'] +  i18n['labels']['stopwatch_'+data.model] + ' <strong>' + ((data.full_num) ? '['+data.full_num+']' : '') + ' ' + data.name +  '</strong>' + i18n['labels']['stopwatch_started_suffix'] + data.start_date;
                    overlib_text = overlib_text.replace(/([\\'])/g, "\\$1").replace(/([\\"])/g, "&quot;");
                    overlib_title = i18n['labels']['stopwatch'];
                    img = '<img src="' + env.themeUrl + 'images/stopwatch.png" ' +
                        'class="icon_button pointer ' + data.model + '" id="startedwatch_' + data.model + '_' + data.id + '" ' +
                        'alt="' + i18n['labels']['stopwatch'] + '" title="' + i18n['labels']['stopwatch'] + '" ' +
                        'onclick="confirmAction(\'stop_watch\', function(el) { stopWatch(el, \'' + data.model + '\', ' + data.id + '); }, this)" ' +
                        'onmouseover="return overlib(\'' + overlib_text + '\',CAPTION,\'' + overlib_title + '\');" onmouseout="nd();" ' +
                        'border="0" width="16" height="16" style="vertical-align: middle;" />';

                    $('m_started_timers').innerHTML = img;

                    result = true;
                }
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        // Execute the AJAX
        new Ajax.Request(url, opt);

        return result;
    }
};
