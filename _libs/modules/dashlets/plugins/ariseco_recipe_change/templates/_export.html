<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
    {if $results}
      <table border="1" cellpadding="0" cellspacing="0">
        <tr>
          <td style="vertical-align: middle; text-align: center;"><strong>{#plugin_ariseco_rec_change_num#|escape}</strong></td>
          <td style="vertical-align: middle; text-align: center;"><strong>{#plugin_ariseco_rec_change_sku_code#|escape}</strong></td>
          <td style="vertical-align: middle; text-align: center;"><strong>{#plugin_ariseco_rec_change_name#|escape}</strong></td>
          <td style="vertical-align: middle; text-align: center;"><strong>{#plugin_ariseco_rec_change_categories#|escape}</strong></td>
          <td style="vertical-align: middle; text-align: center;"><strong>{#plugin_ariseco_rec_change_color#|escape}</strong></td>
        </tr>
        {foreach from=$results item=res name=re}
          <tr>
            <td>
              {$smarty.foreach.re.iteration}
            </td>
            <td>
              {$res.code|escape|default:"&nbsp;"}
            </td>
            <td>
              {$res.name|escape|default:"&nbsp;"}
            </td>
            <td>
              {$res.categories|escape|default:"&nbsp;"}
            </td>
            <td>
              {$res.color|escape|default:"&nbsp;"}
            </td>
          </tr>
        {/foreach}
      </table>
    {else}
      <div style="color: red;">{#plugin_ariseco_rec_change_no_results#|escape}</div>
    {/if}
  </body>
</html>
