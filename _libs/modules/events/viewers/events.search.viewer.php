<?php

class Events_Search_Viewer extends Viewer {
    use \Nzoom\Mvc\ViewTrait\TotalResultsTrait;

    public $template = 'search.html';
    public $filters = array();

    public function prepare() {
        require_once $this->modelsDir . 'events.factory.php';
        require_once $this->modelsDir . 'events.types.factory.php';

        //get calendar settings
        require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
        $this->data['calendar_settings'] = Calendars_Calendar::getSettings($this->registry);

        $filters = Events::saveSearchParams($this->registry, array(), 'search_');

        $customize = array();
        $found = 0;
        if (!empty($filters['where'])) {
            foreach ($filters['where'] as $where) {
                if (preg_match('/e\.type\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', $where)) {
                    $val          = trim(preg_replace('/e\.type\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', '$1', $where));
                    $customize    = array('name' => 'type', 'value' => $val);
                    $found++;

                    //get type for multi actions
                    $type = $val;
                }
                if (preg_match('/et\.type_section\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', $where)) {
                    $val                        = trim(preg_replace('/et\.type_section\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', '$1', $where));
                    $customize                  = array('name' => 'section', 'value' => $val);
                    $this->data['type_section'] = $customize['value'];
                    $found++;
                }
            }
        } elseif(!empty($filters['hidden_type'])) {
            $customize = array('name' => 'type', 'value' => $filters['hidden_type']);
            $found++;

            //get type for multi actions
            $type = $filters['hidden_type'];
        } elseif(!empty($filters['hidden_type_section'])) {
            $customize = array('name' => 'section', 'value' => $filters['hidden_type_section']);
            $found++;
        }

        if ($found == 1 && $customize) {
            if (!$this->setCustomTemplate($customize)) {
                //the list of types is only shown when there is no custom outlook template
                $this->data['type'] = $customize['value'];
            }
        } else {
            $this->setCustomTemplate();
        }
        if ($found > 1) {
            unset($type);
        }

        // modelFields contains visible columns
        if (!empty($this->modelFields)) {

            $filters['get_fields'] = $this->modelFields;

            if (in_array('participants', $this->modelFields)) {
                //set flag in registry, so that the assignments are derived from the DB
                $this->registry->set('getAssignments', true, true);
            }
        }

        list($events, $pagination) = Events::pagedSearch($this->registry, $filters);

        $this->data['events'] = $events;
        $this->data['pagination'] = $pagination;
        if (!empty($this->registry['request']->get('paginate'))) {
            $sql = [];
            $this->totalResults(Customers::class, $filters, $sql);
        }

        if (!empty($type)) {
            //prepare statuses
            require_once PH_MODULES_DIR . 'events/models/events.dropdown.php';
            $statuses = Events_Dropdown::getStatuses(array($this->registry));
            $_options_statuses = array();
            foreach ($statuses as $status) {
                $_options_statuses[] = array('id' => $status['option_value'], 'name' => $status['label']);
            }
            $this->data['statuses'] = $_options_statuses;

            //prepare print patterns
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters_patterns = array('where' => array(
                                              'p.model = \'Event\'',
                                              'p.model_type = \'' . $type . '\'',
                                              'p.active = 1',
                                              'p.format = "pdf"',
                                              'p.list = 0'),
                                      'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                                      'sanitize' => true);
            if ($this->registry['currentUser']->get('is_portal')) {
                $filters_patterns['where'][] = 'p.is_portal = 1';
            }
            $patterns = Patterns::search($this->registry, $filters_patterns);

            $patterns_options = array();
            foreach ($patterns as $pattern) {
                $patterns_options[] = array(
                    'id'   => $pattern->get('id'),
                    'name' => $pattern->get('name'),
                );
            }
            $this->data['patterns_grouped'] = $patterns_options ? array('' => $patterns_options) : array();
        }

        $filters_menu_types_custom_listing = array('where'      => array('et.active=1'),
                                                   'sort'       => array('eti18n.name ASC'),
                                                   'sanitize'   => true);
        // gets the non permitted events types ids
        $not_permitted_events_types = $this->registry->get('currentUser')->getNotPermittedTypes('event', 'list');
        if (!empty($not_permitted_events_types)) {
            $filters_menu_types_custom_listing['where'][] = 'et.id NOT IN (' . implode(',', $not_permitted_events_types) . ')';
        }
        $this->data['menu_types_custom_listing'] = Events_Types::search($this->registry, $filters_menu_types_custom_listing);

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        if (isset($this->title)) {
            $title = $this->title;
        } else {
            $title = $this->i18n('events');
        }
        if (isset($this->subtitle)) {
            $subtitle = $this->subtitle;
        } else {
            $subtitle = '';
        }

        $this->data['title'] = $title;
        $this->data['subtitle'] = $subtitle;
    }
}

?>
