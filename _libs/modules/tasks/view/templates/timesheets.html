<div class="nz-page-wrapper nz-page__min-width{if !empty($side_panels) && count($side_panels)} nz--has-side-panel{/if}">
  <div class="nz-page-main-column nz-content-surface{if empty($_isPopup)} nz-elevation--z3{/if}">
    <div class="nz-page-title"><h1>{$title|escape}</h1>
      <div class="nz-page-title-tools">
        {if isset($available_page_actions.general)}
          {include file="`$theme->templatesDir`actions_box.html" available_actions=$available_page_actions.general}
        {/if}
      </div>
      {if isset($available_page_actions.quick)}
        <div class="nz-page-title-sidetools">
          {include file="`$theme->templatesDir`actions_box.html" available_actions=$available_page_actions.quick  onlyIcons=true}
        </div>
      {/if}
    </div>
    <div class="nz-page-actions">
      {include file="`$theme->templatesDir`actions_box.html" available_actions=$available_page_actions.context}
    </div>

    {include file="`$templatesDir`_add_popout_xtemplate.html"}
    {include file="`$theme->templatesDir`_translations_menu.html" translations=$translations}

    <div id="form_container" class="nz-page-content">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_layout_table">
        <tr>
          <td class="nopadding">
            <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
              {include file="`$templatesDir`_info_header.html"}
            </table>
          </td>
        </tr>
        <tr class="nz-record-section__caption">
          <td>
            <a id="documents_documents_referers_index"></a>
            <div class="nz-record-section__wrapper" >
              <div class="nz-record-section__title">{#timesheets#}</div>
            </div>
          </td>
        </tr>
        <tr id="tasks_timesheets" class="nz-record-section__body nz--opened">
          <td class="nz-record-section--outline">
            <div id="panel_{$timesheets_session_param}">
              {include file="_timesheets_panel.html" model_id=$task->get('id') model_name='Task'}
            </div>
          </td>
        </tr>
      </table>
      {include file="`$theme->templatesDir`help_box.html"}
    </div>
  </div>
</div>
