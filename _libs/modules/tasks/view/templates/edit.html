{if !$smarty.get.reload}
<div class="nz-page-wrapper{if !empty($side_panels) && count($side_panels)} nz--has-side-panel{/if}">
  <div class="nz-page-main-column nz-content-surface{if empty($_isPopup)} nz-elevation--z3{/if}">

  {if $action ne 'ajax_edit'}
    <div class="nz-page-title"><h1 id="taksMainTitle">{$title|escape}</h1>
      <div class="nz-page-title-tools">
        {if isset($available_page_actions.general) and $action ne 'ajax_edit'}
          {include file="`$theme->templatesDir`actions_box.html" available_actions=$available_page_actions.general}
        {/if}
      </div>
      <div class="nz-page-title-sidetools">
        {if isset($available_page_actions.quick)}
          {include file="`$theme->templatesDir`actions_box.html" available_actions=$available_page_actions.quick  onlyIcons=true}
        {/if}
      </div>
    </div>
    <div class="nz-page-actions">
      {if $action ne 'ajax_edit'}
        {include file="`$theme->templatesDir`actions_box.html" available_actions=$available_page_actions.context}
      {else}
        <div id="lightbox_messages_container" class="collapsible" style="display: none;"></div>
        {if $assignments_settings}
          {include file="`$smarty.const.PH_MODULES_DIR`/assignments/templates/_assignments_configurator_panel.html"
            config_templates=$assignments_settings.config_templates}
          <script type="text/javascript" src="{$smarty.const.PH_MODULES_URL}assignments/javascript/assignments.js?{$system_options.build}" defer="defer"></script>
        {/if}
      {/if}
    </div>
  {/if}
    {include file="`$theme->templatesDir`_translations_menu.html" translations=$translations}

    <div id="form_container" class="nz-page-content main_panel_container">
{/if}

      <form name="tasks" action="{$submitLink}" method="post">
        <input type="hidden" name="id" id="id" value="{$task->get('id')}" />
        <input type="hidden" name="model_lang" id="model_lang" value="{$task->get('model_lang')|default:$lang}" />
        {if $action eq 'ajax_edit' && $assignments_settings}
          <input type="hidden" name="model_name" id="model_name" value="{$task->modelName}" />
          <input type="hidden" name="model_type" id="model_type" value="{$task->get('type')}" />
        {/if}
        <table border="0" cellpadding="0" cellspacing="0" class="t_table">
          <tr>
            <td>
              {include file='_timesheet_stopwatch_button.html' model=$task}
              {if $action eq 'ajax_edit'}
                {include file='layouts_index.html' display='abs_div'}
                <script type="text/javascript" defer="defer">positionAbsoluteDivs();</script>
              {/if}
                <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
                {foreach from=$task->get('layouts_details') key='lkey' item='layout'}

                  <tr{if !$layout.view || !$layout.visible} style="display: none;"{/if} class="nz-form-input">
                    <td colspan="3" class="t_caption3 pointer">
                      <div class="floatr index_arrow_anchor">
                        <a href="#vars_index"><img src="{$theme->imagesUrl}arrow_top.png" border="0" title="{#back_to_index#|escape}" alt="{#back_to_index#|escape}" /></a>
                      </div>
                      <div class="layout_switch" onclick="toggleViewLayouts(this)" id="task_{$layout.keyword}_switch">
                        <a name="task_{$layout.keyword}_index"></a><div class="switch_{if $layout.cookie eq 'off'}expand{else}collapse{/if}"></div><div class="t_caption2_title">{$layout.name|escape}</div>
                      </div>
                    </td>
                  </tr>

                  {if $lkey eq 'configurator'}
                  <tr id="task_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if} class="nz-form-input">
                    <td colspan="3">
                    {include file="`$templatesDir`_tasks_configurator_panel.html"}
                    </td>
                  </tr>
                  {elseif $lkey eq 'full_num'}
                  <tr id="task_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if} class="nz-form-input">
                    <td class="labelbox"><a name="error_full_num"><label for="full_num"{if $messages->getErrors('full_num')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                    <td>{#required#}</td>
                    <td>
                      {$task->get('full_num')|escape}
                      <input type="hidden" value="{$task->get('full_num')}" name="full_num" id="full_num" />
                    </td>
                  </tr>
                  {elseif $lkey eq 'name'}
                  <tr id="task_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if} class="nz-form-input">
                    <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                    <td class="required">{#required#}</td>
                    <td>
                      {if $layout.edit}
                        <input type="text" class="txtbox" name="name" id="name" value="{$task->get('name')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
                      {else}
                        {include file=`$templatesDir`_info.html assign='info'}
                        <span {popup text=$info|escape caption=#system_info#|escape width=250}>{mb_truncate_overlib text=$task->get('name')|escape|default:"&nbsp;"}</span>
                        <input type="hidden" name="name" id="name" value="{$task->get('name')|escape}" />
                      {/if}
                    </td>
                  </tr>
                  {elseif $lkey eq 'type'}
                  <tr id="task_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if} class="nz-form-input">
                    <td class="labelbox"><a name="error_type"><label for="type"{if $messages->getErrors('type')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                    <td class="required">{#required#}</td>
                    <td>
                      {$task_type->get('name')|escape} <input type="hidden" name="type" id="type" value="{$task_type->get('id')}" />
                    </td>
                  </tr>
                  {elseif $lkey eq 'status'}
                  <tr id="task_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if} class="nz-form-input">
                    <td class="labelbox"><a name="error_status"><label for="status"{if $messages->getErrors('status')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                    <td>&nbsp;</td>
                    <td>
                      {capture assign='task_status_icon_path'}tasks_statuses_{$task->get('status')}{/capture}
                      <span class="material-icons nz-status-icon nz-status__{$task->get('status')}">{$theme->getIconForRecord($task_status_icon_path)}</span>
                      {if $task->get('status') eq 'planning'}{#tasks_status_planning#|escape}
                      {elseif $task->get('status') eq 'progress'}{#tasks_status_progress#|escape}
                      {elseif $task->get('status') eq 'finished'}{#tasks_status_finished#|escape}
                      {/if}
                      {if $action ne 'ajax_edit' && $task->checkPermissions('setstatus') && $layout.edit}
                        <a href="javascript:void(0)" onclick="changeStatus({$task->get('id')}, 'tasks')">{#tasks_setstatus#|escape}</a>
                      {/if}
                    </td>
                  </tr>
                  {elseif $lkey eq 'customer'}
                  <tr id="task_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if} class="nz-form-input">
                    <td class="labelbox"><a name="error_customer"><label for="customer"{if $messages->getErrors('customer')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                    <td class="required">{#required#}</td>
                    <td>
                      {capture assign='ac_readonly'}{if $layout.edit && !$currentUser->get('is_portal')}0{else}1{/if}{/capture}
                      {include file=`$theme->templatesDir`input_autocompleter.html
                               name='customer'
                               autocomplete_type='customers'
                               autocomplete_var_type='basic'
                               autocomplete_buttons='add search clear'
                               value=$task->get('customer')
                               value_code=$task->get('customer_code')
                               value_name=$task->get('customer_name')
                               readonly=$ac_readonly
                               width=266
                               standalone=true
                               label=$layout.name
                               help=$layout.description
                      }
                      <span id="branch_container" style="display: {if $task->get('customer_is_company')}inline{else}none{/if};">
                        {if !$ac_readonly}
                          <span class="help" {help label_content=$task->getBranchLabels('tasks_branch')|escape text_content=$task->getBranchLabels('help_tasks_branch')|escape popup_only='1'}>&nbsp;</span>
                          <select name="branch" id="branch" onchange="changeContactPersonsOptions(this, 'contact_person');" class="selbox{if empty($customer_branches)} missing_records{/if}" style="width: 100px!important;" title="{$task->getBranchLabels('tasks_branch')|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">
                            {if empty($customer_branches)}
                              <option value="" class="missing_records" selected="selected">[{$task->getBranchLabels('empty_branch')|escape}]</option>
                            {else}
                              {foreach from=$customer_branches item='customer_branch'}
                                {if (!$customer_branch->isDeleted() && $customer_branch->isActivated()) || $customer_branch->get('id') === $task->get('branch')}
                                <option value="{$customer_branch->get('id')|escape}"{if $customer_branch->get('id') === $task->get('branch')} selected="selected"{/if}{if $customer_branch->isDeleted() || !$customer_branch->isActivated()} class="inactive_option" title="{#inactive_option#}">*&nbsp;{else}>{/if}{$customer_branch->get('name')|default:'&nbsp;'}</option>
                                {/if}
                              {/foreach}
                            {/if}
                          </select>
                        {else}
                          <span class="labelbox">{help label_content=$task->getBranchLabels('tasks_branch')|escape}</span>
                          <span class="branch">
                          {if $task->get('branch')}
                            <span{if !$task->get('branch_active')} class="inactive_option" title="{#inactive_option#}"> *{else}>{/if}{$task->get('branch_name')|escape}</span>
                          {/if}
                          </span>
                          <input type="hidden" name="branch" id="branch" value="{$task->get('branch')|default:0}" />
                        {/if}
                      </span>
                      <span id="contact_person_container" style="display: {if $task->get('customer_is_company') && ($layout.edit && !$currentUser->get('is_portal') || $task->get('contact_person'))}inline{else}none{/if};">
                        {if !$ac_readonly}
                          <span class="help" {help label_content=#tasks_contact_person#|escape text_content=$task->getBranchLabels('help_tasks_contact_person')|escape popup_only='1'}>&nbsp;</span>
                          <select name="contact_person" id="contact_person" class="selbox{if empty($contact_persons)} missing_records{elseif !$task->get('contact_person')} undefined{/if}" style="width: 100px!important;" title="{#tasks_contact_person#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);">
                            {if empty($contact_persons)}
                              <option value="" class="missing_records" selected="selected">[{#empty_contact_person#|escape}]</option>
                            {else}
                              <option value="" class="undefined"{if !$task->get('contact_person')} selected="selected"{/if}>[{#please_select#|escape}]</option>
                              {foreach from=$contact_persons item='contact_person'}
                                {if (!$contact_person->isDeleted() && $contact_person->isActivated()) || $contact_person->get('id') === $task->get('contact_person')}
                                <option value="{$contact_person->get('id')|escape}"{if $contact_person->get('id') === $task->get('contact_person')} selected="selected"{/if}{if $contact_person->isDeleted() || !$contact_person->isActivated()} class="inactive_option" title="{#inactive_option#}">*&nbsp;{else}>{/if}{$contact_person->get('name')|default:'&nbsp;'}{if $contact_person->get('lastname')} {$contact_person->get('lastname')|default:'&nbsp;'}{/if}</option>
                                {/if}
                              {/foreach}
                            {/if}
                          </select>
                        {else}
                          <span class="labelbox">{help label_content=#tasks_contact_person#|escape}</span>
                          <span class="contact_person">
                          {if $task->get('contact_person')}
                            <span{if !$task->get('contact_person_active')} class="inactive_option" title="{#inactive_option#}"> *{else}>{/if}{$task->get('contact_person_name')|escape}</span>
                          {/if}
                          </span>
                          <input type="hidden" name="contact_person" id="contact_person" value="{$task->get('contact_person')|default:0}" />
                        {/if}
                      </span>
                    </td>
                  </tr>
                  {elseif $lkey eq 'trademark'}
                  <tr id="task_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if} class="nz-form-input">
                    <td class="labelbox"><a name="error_trademark"><label for="trademark"{if $messages->getErrors('trademark')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                    <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                    <td>
                      {capture assign='ac_readonly'}{if $layout.edit && !$currentUser->get('is_portal')}0{else}1{/if}{/capture}
                      {include file=`$theme->templatesDir`input_autocompleter.html
                               name='trademark'
                               autocomplete_type='nomenclatures'
                               autocomplete_var_type='basic'
                               autocomplete_buttons='search clear'
                               value=$task->get('trademark')
                               value_name=$task->get('trademark_name')
                               readonly=$ac_readonly
                               width=244
                               standalone=true
                               label=$layout.name
                               help=$layout.description
                      }
                    </td>
                  </tr>
                  {elseif $lkey eq 'project'}
                  <tr id="task_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if} class="nz-form-input">
                    <td class="labelbox"><a name="error_project"><label for="project"{if $messages->getErrors('project')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                    <td class="required">{if $project_required || in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                    <td>
                      {capture assign='ac_readonly'}{if $layout.edit && !$currentUser->get('is_portal')}0{else}1{/if}{/capture}
                      {include file=`$theme->templatesDir`input_autocompleter.html
                               name='project'
                               autocomplete_type='projects'
                               autocomplete_var_type='basic'
                               autocomplete_buttons='add search clear'
                               value=$task->get('project')
                               value_code=$task->get('project_code')
                               value_name=$task->get('project_name')
                               readonly=$ac_readonly
                               width=266
                               standalone=true
                               label=$layout.name
                               help=$layout.description
                      }
                      {if !$ac_readonly}
                        <span class="help" {help label='phase' popup_only='1'}>&nbsp;</span>
                        {include file='input_dropdown.html'
                                 standalone=true
                                 name='phase'
                                 options=$phases
                                 no_select_records_label=$smarty.config.project_phase
                                 first_option_label=$smarty.config.project_phase
                                 width=100
                                 value=$task->get('phase')
                                 label=#tasks_phase#
                        }
                      {else}
                        {if $task->get('phase')} <span class="labelbox">{help label_content=#tasks_phase#}</span> {$task->get('phase_name')|escape}{/if}
                        <input type="hidden" name="phase" id="phase" value="{$task->get('phase')|escape}" />
                      {/if}
                    </td>
                  </tr>
                  {elseif $lkey eq 'planned_start_date'}
                  <tr id="task_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if} class="nz-form-input">
                    <td class="labelbox"><a name="error_planned_start_date"><label for="planned_start_date"{if $messages->getErrors('planned_start_date')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                    <td class="required">{#required#}</td>
                    <td>
                      {if $task->isDefined('planned_start_date')}
                        {assign var='planned_start_date' value=$task->get('planned_start_date')}
                      {else}
                        {assign var='planned_start_date' value=$smarty.now|date_format:#date_iso#}
                      {/if}
                      {if $layout.edit}
                        {include file="input_datetime.html"
                                 standalone=true
                                 name='planned_start_date'
                                 width=200
                                 label=$layout.name
                                 help=$layout.description
                                 value=$planned_start_date
                        }
                      {else}
                        {$task->get('planned_start_date')|escape|date_format:#date_mid#}
                        <input type="hidden" name="planned_start_date" id="planned_start_date" value="{$planned_start_date|escape}" />
                      {/if}
                    </td>
                  </tr>
                  {elseif $lkey eq 'planned_finish_date'}
                  <tr id="task_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if} class="nz-form-input">
                    <td class="labelbox"><a name="error_planned_finish_date"><label for="planned_finish_date"{if $messages->getErrors('planned_finish_date')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                    <td class="required">{#required#}</td>
                    <td>
                      {if $layout.edit}
                        {include file="input_datetime.html"
                                 standalone=true
                                 width=200
                                 name='planned_finish_date'
                                 label=$layout.name
                                 help=$layout.description
                                 value=$task->get('planned_finish_date')
                        }
                      {else}
                        {$task->get('planned_finish_date')|escape|date_format:#date_mid#}
                        <input type="hidden" name="planned_finish_date" id="planned_finish_date" value="{$task->get('planned_finish_date')}" />
                      {/if}
                    </td>
                  </tr>
                  {if $task->get('status') != 'finished' && $task->get('planned_finish_date') && $task->get('planned_finish_date')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#}
                    <tr class="nz-form-input">
                      <td colspan="3" class="warning">
                        <span class="material-icons">warning</span>
                        {#tasks_expired_legend#}: <strong>{$task->get('planned_finish_date')|date_format:#date_mid#}</strong>!
                      </td>
                    </tr>
                  {/if}
                  {elseif $lkey eq 'planned_time'}
                  <tr id="task_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if} class="nz-form-input">
                    <td class="labelbox"><a name="error_planned_time"><label for="planned_time"{if $messages->getErrors('planned_time')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                    <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                    <td>
                      {if $layout.edit}
                        <input type="text" class="txtbox short hright" name="planned_time" id="planned_time" value="{$task->get('planned_time')|default:0|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="return changeKey(this, event, insertOnlyDigits)" /> {#minutes#}
                      {else}
                        {$task->get('planned_time')|default:0|escape} {#minutes#}
                        <input type="hidden" name="planned_time" id="planned_time" value="{$task->get('planned_time')|default:0|escape}" />
                      {/if}
                    </td>
                  </tr>
                  {elseif $lkey eq 'timesheet_time'}
                  <tr id="task_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if} class="nz-form-input">
                    <td class="labelbox"><a name="error_timesheet_time"><label for="timesheet_time"{if $messages->getErrors('timesheet_time')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                    <td class="unrequired">&nbsp;</td>
                    <td>
                      {$task->get('timesheet_time_formatted')|escape}
                      {if $task->get('timesheet_time') gt 0}
                        ({#total#}: {$task->get('timesheet_time')} {#minutes#})
                      {/if}
                    </td>
                  </tr>
                  {elseif $lkey eq 'severity'}
                  <tr id="task_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if} class="nz-form-input">
                    <td class="labelbox"><a name="error_severity"><label for="severity"{if $messages->getErrors('severity')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                    <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                    <td>
                      {if $layout.edit}
                        <select class="selbox" name="severity" id="severity" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">
                          {foreach from=$severitys item='item' key='key'}
                            <option value="{$item.option_value}"{if $item.option_value eq $task->get('severity')} selected="selected"{/if}>{$item.label|escape}</option>
                          {/foreach}
                        </select>
                      {else}
                        {foreach from=$severitys item='item' key='key'}
                          {if $item.option_value eq $task->get('severity')}{$item.label|escape}{/if}
                        {/foreach}
                        <input type="hidden" name="severity" id="severity" value="{$task->get('severity')}" />
                      {/if}
                    </td>
                  </tr>
                  {elseif $lkey eq 'progress'}
                  <tr id="task_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if} class="nz-form-input">
                    <td class="labelbox"><a name="error_progress"><label for="progress"{if $messages->getErrors('progress')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                    <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                    <td>
                      {if $layout.edit}
                        <select name="progress" id="progress" class="selbox short" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">
                          {section name='i' loop=101 step=10}
                            <option value="{$smarty.section.i.index}"{if $smarty.section.i.index eq $task->get('progress')} selected="selected"{/if}>{$smarty.section.i.index}%</option>
                          {/section}
                        </select>
                      {else}
                        {$task->get('progress')|escape}%
                        <input type="hidden" name="progress" id="progress" value="{$task->get('progress')}" />
                      {/if}
                    </td>
                  </tr>
                  {elseif $lkey eq 'equipment'}
                  <tr id="task_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if} class="nz-form-input">
                    <td class="labelbox"><a name="error_equipment"><label for="equipment"{if $messages->getErrors('equipment')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                    <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                    <td>
                      {if $layout.edit}
                        <input type="text" class="txtbox" name="equipment" id="equipment" value="{$task->get('equipment')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
                      {else}
                        {$task->get('equipment')|escape}
                        <input type="hidden" name="equipment" id="equipment" value="{$task->get('equipment')}" />
                      {/if}
                    </td>
                  </tr>
                  {elseif $lkey eq 'task_field'}
                  <tr id="task_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if} class="nz-form-input">
                    <td class="labelbox"><a name="error_task_field"><label for="task_field"{if $messages->getErrors('task_field')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                    <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                    <td>
                      {if $layout.edit}
                        <input type="text" class="txtbox" name="task_field" id="task_field" value="{$task->get('task_field')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
                      {else}
                        {$task->get('task_field')|escape}
                        <input type="hidden" name="task_field" id="task_field" value="{$task->get('task_field')}" />
                      {/if}
                    </td>
                  </tr>
                  {elseif $lkey eq 'source'}
                  <tr id="task_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if} class="nz-form-input">
                    <td class="labelbox"><a name="error_source"><label for="source"{if $messages->getErrors('source')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                    <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                    <td>
                      {if $layout.edit}
                        <select name="source" id="source" class="selbox{if !$task->get('source')} undefined{/if}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);">
                          <option value="" class="undefined">[{#please_select#|escape}]</option>
                          <option value="email"{if $task->get('source') eq 'email'} selected="selected"{/if}>{#tasks_email#|escape}</option>
                          <option value="phone"{if $task->get('source') eq 'phone'} selected="selected"{/if}>{#tasks_phone#|escape}</option>
                          <option value="meeting"{if $task->get('source') eq 'meeting'} selected="selected"{/if}>{#tasks_meeting#|escape}</option>
                          <option value="inside"{if $task->get('source') eq 'inside'} selected="selected"{/if}>{#tasks_inside#|escape}</option>
                          <option value="other"{if $task->get('source') eq 'other'} selected="selected"{/if}>{#tasks_other#|escape}</option>
                        </select>
                      {else}
                        {if $task->get('source')}
                          {capture assign='source_lang_var'}tasks_{$task->get('source')}{/capture}
                          {$smarty.config.$source_lang_var}
                        {else}
                          &nbsp;
                        {/if}
                        <input type="hidden" name="source" id="source" value="{$task->get('source')}" />
                      {/if}
                    </td>
                  </tr>
                  {elseif $lkey eq 'description'}
                  <tr id="task_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if} class="nz-form-input">
                    <td class="labelbox"><a name="error_description"><label for="description"{if $messages->getErrors('description')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                    <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                    <td>
                    {if $layout.edit}
                      <textarea class="areabox doubled higher" name="description" id="description" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$task->get('description')|escape}</textarea>
                    {else}
                      {$task->get('description')|escape|mb_wordwrap:70|url2href|default:"&nbsp;"}
                      <input type="hidden" name="description" id="description" value="{$task->get('description')|escape}" />
                    {/if}
                    </td>
                  </tr>
                  {elseif $lkey eq 'notes'}
                  <tr id="task_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if} class="nz-form-input">
                    <td class="labelbox"><a name="error_notes"><label for="notes"{if $messages->getErrors('notes')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                    <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                    <td>
                    {if $layout.edit}
                      <textarea class="areabox doubled higher" name="notes" id="notes" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$task->get('notes')|escape}</textarea>
                    {else}
                      {$task->get('notes')|escape|mb_wordwrap:70|url2href|default:"&nbsp;"}
                      <input type="hidden" name="notes" id="notes" value="{$task->get('notes')|escape}" />
                    {/if}
                    </td>
                  </tr>
                  {elseif $lkey eq 'department'}
                  <tr id="task_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if} class="nz-form-input">
                    <td class="labelbox"><a name="error_department"><label for="department"{if $messages->getErrors('department')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                    <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                    <td>
                      {if $layout.edit}
                        <select class="selbox{if !$task->get('department')} undefined{/if}" name="department" id="department" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);" onkeypress="dropdownTypingSearch(this, event);">
                          <option value="" class="undefined">[{#please_select#|escape}]</option>
                          {foreach from=$departments item='item'}
                            {if (!$item->isDeleted() && $item->isActivated()) || $item->get('id') eq $task->get('department')}
                            <option value="{$item->get('id')}"{if $item->get('id') eq $task->get('department')} selected="selected"{/if}{if $item->isDeleted() || !$item->isActivated()} class="inactive_option" title="{#inactive_option#}"{/if}>{$item->get('name')|indent:$item->get('level'):"-"}</option>
                            {/if}
                          {/foreach}
                        </select>
                      {else}
                        {foreach from=$departments item='item'}
                          {if $item->get('id') eq $task->get('department')}
                            {$item->get('name')|indent:$item->get('level'):"-"}
                            <input type="hidden" name="department" id="department" value="{$task->get('department')}" />
                          {/if}
                        {/foreach}
                      {/if}
                    </td>
                  </tr>
                  {elseif strpos($lkey, 'assignments_') === 0}
                  <tr id="task_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if} class="nz-form-input">
                    <td class="labelbox">
                      {assign var='at' value=$layout.keyword|replace:'assignments_':''}
                      <a name="error_{$layout.keyword}"><label for="{$at}_ac"{if $messages->getErrors($layout.keyword)} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a>
                      {include file=`$smarty.const.PH_MODULES_DIR`/assignments/templates/_assignments_group.html
                               type=$at
                               users=$assignments_settings.$at.users}
                      {if $at eq 'owner'}
                      <script type="text/javascript" defer="defer">
                        Event.observe('department', 'change', updateAssignmentsParams);
                      </script>
                      {/if}
                    </td>
                    <td class="unrequired">&nbsp;</td>
                    {include file=`$smarty.const.PH_MODULES_DIR`/assignments/templates/_assign.html
                             type=$at
                             data=$assignments_settings.$at
                             borderless=true
                             model=$task
                             width=200
                             display_always=true
                    }
                  </tr>
                  {/if}
                {/foreach}
                  <tr>
                    <td colspan="3">&nbsp;</td>
                  </tr>
                  <tr>
                    <td colspan="3">
                      <div class="nz-quick-buttons ">
                      {strip}
                        {if $task->get('buttons')}
                          {foreach from=$task->get('buttons') item='button'}
                            {include file=`$theme->templatesDir`input_button.html
                                    label=$button.label
                                    standalone=true
                                    name=$button.name
                                    source=$button.source
                                    disabled=$button.disabled
                                    hidden=$button.hidden
                                    width=$button.width
                                    height=$button.height}
                          {/foreach}
                        {/if}

                        {if $action eq 'ajax_edit'}
                          <button type="button" name="saveButton1" class="nz-form-button nz-button-primary" onclick="ajaxSaveTask('{$action}', this.form)">{#save#|escape}</button>
                          <button type="button" name="cancel" class="nz-form-button nz-button-cancel" onclick="confirmAction('cancel', function() {ldelim} {if !empty($task->lockedByMe)} unlockRecord({ldelim}module: '{$module}', controller: '{$controller}', action: '{$action}', unlock: '{$task->lockedInfo.id}', model_id: '{$task->get('id')}', skip_message: true{rdelim}); {/if} lb.deactivate(); {rdelim}, this);">{#cancel#|escape}</button>
                        {else}
                          <button type="submit" name="saveButton1" class="nz-form-button nz-button-primary">{#save#|escape}</button>
                          {include file="`$theme->templatesDir`cancel_button.html"}
                        {/if}
                      {/strip}
                      </div>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        {include file="`$theme->templatesDir`help_box.html"}
        {include file="`$theme->templatesDir`system_settings_box.html" object=$task}
        {if $action ne 'ajax_edit'}
          {include file="`$theme->templatesDir`after_actions_box.html"}
        {/if}
      </form>

      {if !$smarty.get.reload}
    </div>
  </div>
  {if isset($side_panels) and !$smarty.get.reload}
    {include file="`$theme->templatesDir`_side_panel.html" side_panels=$side_panels}
  {/if}
</div>
{/if}
