<?php

/**
 * Tasks_Counter model class
 */
Class Tasks_Counter extends Model {
    public $modelName = 'Tasks_Counter';

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        //get task types that use this counter
        if ($this->get('id')) {
            $this->getTypes();
        }

        $this->defineFormulaElements();
    }

    // Function to prepare the options for date format
    public function prepare() {
        parent::prepare();
        $current_delimiter = '';

        if(!$this->isDefined('date_delimiter') || $this->get('date_delimiter') === false) {
            if (substr($this->get('date_format'), 0, 1) == '0') {
                $current_delimiter = substr($this->get('date_format'), 3, 1);
            } else {
                $current_delimiter = substr($this->get('date_format'), 2, 1);
            }

            if($current_delimiter === false) {
                $current_delimiter = '/';
            } elseif($current_delimiter == '%') {
                $current_delimiter = '';
            }
        } else {
             $current_delimiter = $this->get('date_delimiter');
        }
        $this->set('date_delimiter', $current_delimiter, true);

        $date_format_options = array(
            array(
                'date_format'   => '%Y',
                'class_name'    => $this->i18n('tasks_counters_formula_date_format1'),
                'label'         => $this->i18n('tasks_counters_formula_date_format_year')
            ),
            array(
                'date_format'   => '%m' . $current_delimiter . '%Y',
                'class_name'    => $this->i18n('tasks_counters_formula_date_format2'),
                'label'         => $this->i18n('tasks_counters_formula_date_format_month') . $current_delimiter . $this->i18n('tasks_counters_formula_date_format_year')
            ),
            array(
                'date_format'   => '%m' . $current_delimiter . '%y',
                'class_name'    => $this->i18n('tasks_counters_formula_date_format3'),
                'label'         => $this->i18n('tasks_counters_formula_date_format_month') . $current_delimiter . $this->i18n('tasks_counters_formula_date_format_year_short')
            ),
            array(
                'date_format'   => '%Y' . $current_delimiter . '%m',
                'class_name'    => $this->i18n('tasks_counters_formula_date_format4'),
                'label'         => $this->i18n('tasks_counters_formula_date_format_year') . $current_delimiter . $this->i18n('tasks_counters_formula_date_format_month')
            ),
            array(
                'date_format'   => '%y' . $current_delimiter . '%m',
                'class_name'    => $this->i18n('tasks_counters_formula_date_format5'),
                'label'         => $this->i18n('tasks_counters_formula_date_format_year_short') . $current_delimiter . $this->i18n('tasks_counters_formula_date_format_month')
            ),
            array(
                'date_format'   => '%d' . $current_delimiter . '%m' . $current_delimiter . '%Y',
                'class_name'    => $this->i18n('tasks_counters_formula_date_format6'),
                'label'         => $this->i18n('tasks_counters_formula_date_format_day') . $current_delimiter . $this->i18n('tasks_counters_formula_date_format_month') . $current_delimiter . $this->i18n('tasks_counters_formula_date_format_year')
            ),
            array(
                'date_format'   => '%d' . $current_delimiter . '%m' . $current_delimiter . '%y',
                'class_name'    => $this->i18n('tasks_counters_formula_date_format7'),
                'label'         => $this->i18n('tasks_counters_formula_date_format_day') . $current_delimiter . $this->i18n('tasks_counters_formula_date_format_month') . $current_delimiter . $this->i18n('tasks_counters_formula_date_format_year_short')
            ),
            array(
                'date_format'   => '%m' . $current_delimiter . '%d' . $current_delimiter . '%Y',
                'class_name'    => $this->i18n('tasks_counters_formula_date_format8'),
                'label'         => $this->i18n('tasks_counters_formula_date_format_month') . $current_delimiter . $this->i18n('tasks_counters_formula_date_format_day') . $current_delimiter . $this->i18n('tasks_counters_formula_date_format_year')
            ),
            array(
                'date_format'   => '%m' . $current_delimiter . '%d' . $current_delimiter . '%y',
                'class_name'    => $this->i18n('tasks_counters_formula_date_format9'),
                'label'         => $this->i18n('tasks_counters_formula_date_format_month') . $current_delimiter . $this->i18n('tasks_counters_formula_date_format_day') . $current_delimiter . $this->i18n('tasks_counters_formula_date_format_year_short')
            ),
            array(
                'date_format'   => '%Y' . $current_delimiter . '%d' . $current_delimiter . '%m',
                'class_name'    => $this->i18n('tasks_counters_formula_date_format10'),
                'label'         => $this->i18n('tasks_counters_formula_date_format_year') . $current_delimiter . $this->i18n('tasks_counters_formula_date_format_day') . $current_delimiter . $this->i18n('tasks_counters_formula_date_format_month')
            ),
            array(
                'date_format'   => '%y' . $current_delimiter . '%d' . $current_delimiter . '%m',
                'class_name'    => $this->i18n('tasks_counters_formula_date_format11'),
                'label'         => $this->i18n('tasks_counters_formula_date_format_year_short') . $current_delimiter . $this->i18n('tasks_counters_formula_date_format_day') . $current_delimiter . $this->i18n('tasks_counters_formula_date_format_month')
            ),
            array(
                'date_format'   => '%Y' . $current_delimiter . '%m' . $current_delimiter . '%d',
                'class_name'    => $this->i18n('tasks_counters_formula_date_format12'),
                'label'         => $this->i18n('tasks_counters_formula_date_format_year') . $current_delimiter . $this->i18n('tasks_counters_formula_date_format_month') . $current_delimiter . $this->i18n('tasks_counters_formula_date_format_day')
            ),
            array(
                'date_format'   => '%y' . $current_delimiter . '%m' . $current_delimiter . '%d',
                'class_name'    => $this->i18n('tasks_counters_formula_date_format13'),
                'label'         => $this->i18n('tasks_counters_formula_date_format_year_short') . $current_delimiter . $this->i18n('tasks_counters_formula_date_format_month') . $current_delimiter . $this->i18n('tasks_counters_formula_date_format_day')
            ),
            array(
                'date_format'   => '%y',
                'class_name'    => $this->i18n('tasks_counters_formula_date_format14'),
                'label'         => $this->i18n('tasks_counters_formula_date_format_year_short')
            ),
            array(
                'date_format'   => '0%y' . $current_delimiter . '%m',
                'class_name'    => $this->i18n('tasks_counters_formula_date_format15'),
                'label'         => mb_substr($this->i18n('tasks_counters_formula_date_format_year'), 0, 3, 'UTF8') . $current_delimiter . $this->i18n('tasks_counters_formula_date_format_month')
            )
        );

        $this->set('date_format_options', $date_format_options, true);
    }

    /**
     * Checks the validity of the model
     *
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {
        if (!$this->get('name')) {
            $this->raiseError('error_no_counter_name_specified', 'name');
        }

        if ($this->isDefined('formula') && !$this->get('formula')) {
            $this->raiseError('error_no_counter_formula_specified', 'formula');
        }

        if ($this->isDefined('next_number') && !intval($this->get('next_number'))>0) {
            $this->raiseError('error_invalid_next_number', 'next_number');
        }

        return $this->valid;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            //edit mode
            $action = 'edit';
        } else {
            $action = 'add';
        }

        if ($this->validate()) {
            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();

            if ($this->$action()) {
                return true;
            } else {
                $this->slashesStrip();

                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = &$this->registry['db'];

        //start transaction
        $db->StartTrans();
        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();
        $set['added']    = sprintf("added=now()");
        $set['added_by'] = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));

        //query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_TASKS_COUNTERS . "\n" .
                  'SET ' . implode(', ', $set) . "\n";
        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError('add new counter base details', $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Edit model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = &$this->registry['db'];

        //start transaction
        $db->StartTrans();
        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_TASKS_COUNTERS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');
        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError('edit counter base details', $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Prepare data for insert/update in main table
     *
     * @return array - data
     */
    public function prepareMainData() {

        $set = array();
        $set['id']             = sprintf("id=%d", $this->get('id'));
        if ($this->isDefined('formula')) {
            $set['formula']        = sprintf("formula='%s'", $this->get('formula'));
        }
        if ($this->isDefined('prefix')) {
            $set['prefix']         = sprintf("prefix='%s'", $this->get('prefix'));
        }
        if ($this->isDefined('leading_zeroes')) {
            $set['leading_zeroes'] = sprintf("leading_zeroes='%s'", $this->get('leading_zeroes'));
        }
        if ($this->isDefined('delimiter')) {
            $set['delimiter']      = sprintf("delimiter='%s'", $this->get('delimiter'));
        }
        if ($this->isDefined('date_format')) {
            $set['date_format']    = sprintf("date_format='%s'", $this->get('date_format'));
        }
        if ($this->isDefined('next_number')) {
            $set['next_number'] = sprintf("next_number=%d", (intval($this->get('next_number')) > 0) ? ($this->get('next_number')) : 0);
        }
        $set['modified']       = sprintf("modified=now()");
        $set['modified_by']    = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));

        if ($this->isDefined('active')) {
            $set['active'] = sprintf("active=%d", $this->get('active'));
        } elseif (!$this->get('id')) {
            $set['active'] = sprintf("active=%d", 1);
        }
        if ($this->isDefined('group')) {
            $set['group'] = sprintf("`group`=%d", $this->get('group'));
        }

        return $set;
    }

    /**
     * Update I18N table of the model
     *
     * @return bool - result of the operation
     */
    public function updateI18N() {
        $db = $this->registry['db'];

        //UPDATE THE I18N TABLE OF THE MODEL
        $update = array();
        $update['name'] = sprintf("name='%s'", $this->get('name'));
        $update['description']  = sprintf("description='%s'", $this->get('description'));

        $insert = $update;
        $insert['parent_id']  = sprintf("parent_id=%d", $this->get('id'));
        $insert['lang']       = sprintf("lang='%s'", $this->get('model_lang'));
        $insert['translated'] = sprintf("translated=now()");

        //query to insert/update the i18n table for the selected model language
        $query2 = 'INSERT INTO ' . DB_TABLE_TASKS_COUNTERS_I18N . "\n" .
                  'SET ' . implode(', ', $insert) . "\n" .
                  'ON DUPLICATE KEY UPDATE ' . "\n" .
                  implode(', ', $update);

        $db->Execute($query2);

        return !$db->HasFailedTrans();
    }

    /**
     * Gets types using this counter
     *
     * @return bool - result of the operation
     */
    public function getTypes() {
        if (!$this->get('id')) {
            return false;
        }

        if ($this->get('types')) {
            return true;
        }

        require_once('tasks.types.factory.php');
        $filters = array('where' => array('tt.counter = ' . $this->get('id'),
                                          'tt.system IS NOT NULL'),
                         'sanitize' => true);
        $records = Tasks_Types::search($this->registry, $filters);

        $types = array();
        if (!empty($records)) {
            foreach($records as $record) {
                $types[$record->get('id')] = $record->get('name');
            }
        }

        $this->set('types', $types);

        return true;
    }

    /**
     * Parses formula and defines formula elements
     *
     * @return bool - result of the operation
     */
    public function defineFormulaElements() {
        $formula = $this->get('formula');

        if (preg_match('#\[prefix\]#', $formula)) {
            $this->set('prefix_used', true);
        } else {
            $this->set('prefix_used', false);
        }

        if (preg_match('#\[task_num\]#', $formula)) {
            $this->set('task_num', true);
        } else {
            $this->set('task_num', false);
        }

        if (preg_match('#\[customer_code\]#', $formula)) {
            $this->set('customer_code', true);
        } else {
            $this->set('customer_code', false);
        }

        if (preg_match('#\[project_code\]#', $formula)) {
            $this->set('project_code', true);
        } else {
            $this->set('project_code', false);
        }

        if (preg_match('#\[office_code\]#', $formula)) {
            $this->set('office_code', true);
        } else {
            $this->set('office_code', false);
        }

        if (preg_match('#\[user_code\]#', $formula)) {
            $this->set('user_code', true);
        } else {
            $this->set('user_code', false);
        }

        if (preg_match('#\[task_date\]#', $formula)) {
            $this->set('task_date', true);
        } else {
            $this->set('task_date', false);
        }

        return true;
    }

    /**
     * Increments the current number of the counter
     *
     * @return bool - result of the operation
     */
    public function increment() {
        $db = $this->registry['db'];

        $query = 'UPDATE ' . DB_TABLE_TASKS_COUNTERS . "\n" .
                'SET next_number=next_number+1' . "\n" .
                'WHERE id=' . $this->get('id');

        $result = $db->Execute($query);

        if ($result) {
            //increment the proper
            $this->set('next_number', $this->get('next_number')+1, true);
        }

        return $result;
    }


}

?>
