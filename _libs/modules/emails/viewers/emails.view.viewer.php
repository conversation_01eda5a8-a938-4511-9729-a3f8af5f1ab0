<?php

class Emails_View_Viewer extends Viewer {
    public $template = 'view.html';

    public function prepare() {
        $this->model = $this->registry['email'];
        $this->data['email'] = $this->model;

        if ($this->model->get('model')) {
            $model = $this->model->get('model');

            $types = array();
            if (!preg_match('#system#i', $model)) {
                $types = Emails::getModelTypes(
                    $this->registry,
                    array(
                        'model' => $model,
                        'id' => $this->model->get('model_type'),
                        'model_lang' => $this->model->get('model_lang'),
                    )
                );
            }

            foreach ($types as $type) {
                if ($type->get('id') == $this->model->get('model_type')) {
                    if ($model == 'Document') {
                        $direction_labels[PH_DOCUMENTS_INCOMING] = $this->i18n('emails_documents_incoming');
                        $direction_labels[PH_DOCUMENTS_OUTGOING] = $this->i18n('emails_documents_outgoing');
                        $direction_labels[PH_DOCUMENTS_INTERNAL] = $this->i18n('emails_documents_internal');
                        $type_name = $direction_labels[$type->get('direction')] . ' - ' . $type->get('name');
                    } else {
                        $type_name = $type->get('name');
                    }
                    $this->model->set('model_type_name', $type_name, true);
                    // for this report templates can be created for e-mail and for sms
                    if ($model == 'Report' && preg_match('#send_sms\s*:=\s*[1-9]+[0-9]*#', $type->get('settings'))) {
                        $this->data['send_sms'] = 1;
                    }
                    break;
                }
            }
        }

        //prepare the fck editor
        $editor = new Editor($this->registry, 'body', array('width' => 1200, 'toolbar' => 'Preview'));
        $editor->setContent($this->model->get('body'));
        $this->data['editor_content'] = $editor->create();

        $this->prepareTranslations();

        $this->prepareTitleBar();

        //get langs
        $langs = preg_split('#\s*,\s*#', $this->registry['config']->getParam('i18n', 'model_langs'));
        $this->data['multi_langs'] = $langs;

        //PLACEHOLDERS
        $placeholders = $this->model->getPlaceholders();
        $this->data['basic_placeholders'] = $placeholders['basic'];
        $this->data['additional_vars'] = $placeholders['additional'];

        //prepare groups
        if ($this->model->get('group')) {
            require_once PH_MODULES_DIR . 'groups/models/groups.factory.php';
            $filters = array('model_lang' => $this->model->get('model_lang'),
                            'id'=>$this->model->get('group'));
            $group = Groups::searchOne($this->registry, $filters);
            if ($group) {
                $this->data['group'] = $group->get('name');
            }
        }
    }

    public function prepareTitleBar() {
        $title = $this->i18n('emails_view');
        $this->data['title'] = $title;
    }
}

?>
