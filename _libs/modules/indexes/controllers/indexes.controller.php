<?php

class Indexes_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Index';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Indexes';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'search', 'add', 'view', 'edit'
    );

    /**
     * After action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'search', 'add', 'view', 'edit', 'translate'
    );

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
        case 'add':
            $this->_add();
            break;
        case 'translate':
            $this->_translate();
            break;
        case 'edit':
            $this->_edit();
            break;
        case 'view':
            $this->_view();
            break;
        case 'activate':
        case 'deactivate':
            $this->_changeStatus($this->action);
            break;
        case 'delete':
            $this->_delete($this->registry['request'][$this->action]);
            break;
        case 'restore':
            $this->_restore($this->registry['request'][$this->action]);
            break;
        case 'getoptions':
            $this->_getOptions();
            break;
        case 'insertids':
            $this->_insertIds();
            break;
        case 'search':
            $this->_search();
            break;
        case 'ajax_add_row':
            $this->_addRow();
            break;
        case 'list':
        default:
            $this->setAction('list');
            $this->_list();
        }
    }

    /**
     * listing of all models
     */
    private function _list() {
        //all the actions are within the viewer

        return true;
    }

    /**
     * search of models
     */
    private function _search() {
        //all the actions are within the viewer

        return true;
    }

    /**
     * add a single model
     */
    private function _add() {
        $request = &$this->registry['request'];

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $index = Indexes::buildModel($this->registry);

            if ($index->save()) {
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_indexes_add_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_indexes_add_failed'), '', -1);
            }
        } else {
            //create empty user model
            $index = Indexes::buildModel($this->registry);
        }

        if (!empty($index)) {
            $this->modelName = 'varindex';
            $index->getData();
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('varindex')) {
                $this->registry->set('varindex', $index->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_index'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Edits of a single model
     */
    private function _edit() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $index = Indexes::buildModel($this->registry);

            $filters = array('where' => array('i.id = ' . $id));
            $old_index = Indexes::searchOne($this->registry, $filters);
            $old_dates = array();
            foreach ($old_index->getData(true) as $row) {
                $old_dates[] = $row['from'];
            }

            if ($index->save()) {
                //show message 'message_notes_edit_success'
                $this->registry['messages']->setMessage($this->i18n('message_indexes_edit_success'), '', -1);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;

                //check if we have report for contracted vs. invoiced
                require_once PH_MODULES_DIR . 'reports/models/reports.factory.php';
                $report = Reports::getReports($this->registry, array('name' => 'invoiced_vs_contracted'));
                $report = $report[0];
                if (!empty($report) && is_object($report)) {
                    $first_added_date = '';
                    foreach ($index->get('data') as $row) {
                        if (!empty($row['from']) && !in_array($row['from'], $old_dates)) {
                            $first_added_date = $row['from'];
                            break;
                        }
                    }
                    if ($first_added_date) {
                        //set default filter
                        $company = '';
                        if ($this->registry['currentUser']->get('default_company')) {
                            $company = $this->registry['currentUser']->get('default_company');
                        } else {
                            require_once PH_MODULES_DIR . 'finance/models/finance.companies.factory.php';
                            $finCompany = Finance_Companies::searchOne($this->registry, array());
                            if ($finCompany) {
                                $company = $finCompany->get('id');
                            }
                        }
                        $report_location = sprintf('%s://%s%s?%s=%s&%s=%s&%s=%s&%s=%s&%s=%s&%s=%s&%s=%s',
                                    (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                                    $_SERVER["HTTP_HOST"], $_SERVER['PHP_SELF'],
                                    $this->registry->get('module_param'), 'reports', 'reports', 'generate_report',
                                    'report_type', $report->get('type'),
                                    'custom_generate', '1',
                                    'index', $index->get('id'),
                                    'from_date', $first_added_date,
                                    'company', $company);
                        $this->registry['messages']->setMessage(sprintf($this->i18n('message_indexes_edit_success2'), $report_location));
                    }
                }
                $this->registry['messages']->insertInSession($this->registry);
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_indexes_edit_failed'), '', -1);
                //register the model, with all the posted details
                $this->registry->set('varindex', $index);
            }

        } elseif ($id > 0) {
            // the model from the DB
            $filters = array('where' => array('i.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $index = Indexes::searchOne($this->registry, $filters);

            //check access and ownership of the model
            $this->checkAccessOwnership($index);
        }

        if (!empty($index)) {
            $this->modelName = 'varindex';
            $index->getData();
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('varindex')) {
                $this->registry->set('varindex', $index->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_index'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Translates existing model
     */
    private function _translate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $index = Indexes::buildModel($this->registry);

            if ($index->save()) {
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_indexes_translate_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_indexes_translate_failed'), '', -1);
            }

        } elseif ($id > 0) {
            //get the model from the DB
            $filters = array('where' => array('i.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $index = Indexes::searchOne($this->registry, $filters);

            //check access and ownership of the model
            $this->checkAccessOwnership($index);
        }

        if (!empty($index)) {
            $this->modelName = 'varindex';
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('varindex', $index->sanitize());
        } else {
            //no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_index'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * view model
     */
    private function _view() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);
        $filters['where'] = array('i.id = ' . $id);
        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }

        $index = Indexes::searchOne($this->registry, $filters);

        if (!empty($index)) {
            $this->modelName = 'varindex';
            $index->getData();
            //check access and ownership of the model
            $this->checkAccessOwnership($index);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('varindex')) {
                $this->registry->set('varindex', $index->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_index'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Activates or deactives the selected models
     *
     * @param string $status - activate or deactivate
     * @param mixed $ids - list of ids to be changed
     */
    private function _changeStatus($status, $ids = '') {
        //ids of the models to be activated/deactivated
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //activate/deactivate
        $result = Indexes::changeStatus($this->registry, $ids, $status);

        if ($result) {
            //change status successful
            $text = ($this->action == 'activate') ?
                      $this->i18n('message_items_activated') :
                      $this->i18n('message_items_deactivated');
            $this->registry['messages']->setMessage($text);
        } else {
            //change status failed
            $text = ($this->action == 'activate') ?
                      $this->i18n('error_items_not_activated') :
                      $this->i18n('error_items_not_deactivated');
            $this->registry['messages']->setError($text);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Deletes selected models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _delete($ids = '') {
        //ids of the models to be deleted
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //delete notes
        $result = Indexes::delete($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_deleted'));
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_deleted'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Restores selected deleted models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _restore($ids = '') {
        //ids of the models to be restored
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //restore items
        $result = Indexes::restore($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_restored'));
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_restored'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Purges selected models
     * Attention: purge has no restore!
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _purge() {
        //ids of the models to be purged
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //purge items
        $result = Indexes::purge($this->registry, $ids);

        if ($result) {
            //purge successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_purged'));
        } else {
            //purge failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_purged'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Sets custom actions definitions
     */
    public function getActions($action_defs = array()) {
        //get model for this class
        $this->getModel();

        $actions = parent::getActions($action_defs);

        /*if ($this->model && $this->model->get('model_type')) {
            require_once PH_MODULES_DIR . 'contracts/models/contracts.types.factory.php';
            $type = Contracts_Types::searchOne($this->registry,
                                        array('where' => array('cot.id= \'' . $this->model->get('model_type') . '\'')));
        }*/

        if (isset($actions['add'])) {

            $options = Dropdown::getContractsTypes(array($this->registry));
            foreach ($options as $idx => $option) {
                if (!$option['active_option']) {
                    unset($options[$idx]);
                }
            }

            if (!empty($options)) {
                $add_options = array(
                    array(
                        'custom_id' => 'type_',
                        'name' => 'model_type',
                        'type' => 'dropdown',
                        'required' => 1,
                        'label' => $this->i18n('indexes_model_type'),
                        'help' => $this->i18n('indexes_model_type'),
                        'options' => $options,
                        'value' => ($this->registry['request']->get('model_type')) ?
                                    $this->registry['request']->get('model_type') : ''),
                    array(
                        'custom_id' => 'model_',
                        'name' => 'model',
                        'type' => 'hidden',
                        'required' => 1,
                        'value' => 'Contract',
                    )
                );

                // check if there is only one type and if so
                //  disables the pre-add screen
                $selected_type = '';
                if (count($options) == 1) {
                    $first_type = reset($options);
                    $selected_type = $first_type['option_value'];
                }

                // set options if there is only one type available
                if ($selected_type) {
                    $actions['add']['url'] .= '&amp;model_type=' . $selected_type . '&amp;model=Contract';
                    $actions['add']['options'] = '';
                } else {
                    $actions['add']['options'] = $add_options;
                    $actions['add']['ajax_no'] = 1;
                }
            } else {
                unset($actions['add']);
            }
        } else {
            unset($actions['add']);
        }

        return $actions;
    }

    /**
     * Sets custom after actions definitions
     *
     * @param array $action_defs - actions definitions
     * @return array - available actions
     */
    public function getAfterActions($action_defs = array()) {
        //get model for this class
        $this->getModel();

        $actions = parent::getAfterActions($action_defs);

        if (isset($actions['add'])) {
            $add_options = array(
                array(
                    'custom_id'     => 'model_type_____',
                    'name'          => 'aa1_model_type',
                    'type'          => 'dropdown',
                    'required'      => 1,
                    'label'         => $this->i18n('indexes_model_type'),
                    'help'          => $this->i18n('indexes_model_type'),
                    'options'       => Dropdown::getContractsTypes(array($this->registry)),
                ),
                array(
                    'custom_id'     => 'model____',
                    'name'          => 'aa1_model',
                    'type'          => 'hidden',
                    'value'         => 'Contract',
                ),
            );

            $actions['add']['options'] = $add_options;
            $actions['add']['ajax_no'] = 1;
        } else {
            unset($actions['add']);
        }

        return $actions;
    }

    /**
     * Adds row for data table
     */
    private function _addRow() {
        $this->registry->set('action', 'add', true);
        $index = new Index($this->registry, array());
        $viewer = new Viewer($this->registry);
        $viewer->setFrameset('frameset_blank.html');
        $viewer->template = '_data_row.html';
        $viewer->data['formulas'] = $index->getFormulas();
        $viewer->data['row_num'] = $this->registry['request']->get('num');
        $viewer->action = 'add';
        echo $viewer->fetch();
        exit;
    }
}

?>
