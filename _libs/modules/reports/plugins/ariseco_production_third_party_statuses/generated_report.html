<table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
  <tr class="reports_title_row hcenter">
    <td class="t_border" rowspan="2" style="vertical-align: middle;"><div style="">{#arise_atelier#|escape}</div></td>
    <td class="t_border" colspan="3" style="vertical-align: middle;"><div style="">{#arise_child_sku_lbl#|escape}</div></td>
    <td class="t_border" colspan="2" style="vertical-align: middle;"><div style="">{#arise_sew_information#|escape}</div></td>
    <td class="t_border" colspan="2" style="vertical-align: middle;"><div style="">{#arise_cut_information#|escape}</div></td>
    <td class="t_border" colspan="2" style="vertical-align: middle;"><div style="">{#arise_production_order_statuses#|escape}</div></td>
    <td colspan="10" style="vertical-align: middle;"><div style="">{#arise_order_attributes#|escape}</div></td>
  </tr>
  <tr class="reports_title_row hcenter">
    <td class="t_border" style="vertical-align: middle;"><div style="">{#arise_child_sku#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#arise_category#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#arise_main_fabric#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#arise_total_issued#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#arise_total_opened#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#arise_total_for_cut#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#arise_total_remaining_cut#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#arise_order_num#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#arise_order_status#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#arise_order_id#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#arise_order_date#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#arise_production_deadline#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#arise_production_comments#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#arise_production_quantity#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#arise_production_produced_quantity#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#arise_production_produced_price#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#arise_production_produced_value#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#arise_production_qc_passed#|escape}</div></td>
    <td style="vertical-align: middle;"><div style="">{#arise_production_qc_remained#|escape}</div></td>
  </tr>

  {foreach from=$reports_results item=atelier name=atl}
    {capture assign='current_row_class'}{cycle name='cycle_rq' values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
    {foreach from=$atelier.child_skus item=child_sku name=sku}
      {foreach from=$child_sku.production item=production name=prod}
        {foreach from=$production.orders item=order name=ord}
          <tr class="{$current_row_class}">
            {if $smarty.foreach.ord.first && $smarty.foreach.prod.first && $smarty.foreach.sku.first}
              <td class="t_border" rowspan="{$atelier.rowspan}" style="vertical-align: middle;">
                {$atelier.name|escape|default:"&nbsp;"}
              </td>
            {/if}
            {if $smarty.foreach.ord.first && $smarty.foreach.prod.first}
              <td class="t_border" rowspan="{$child_sku.rowspan}" style="vertical-align: middle;">
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={$child_sku.id}" target="_blank">
                  {$child_sku.name|escape|default:"&nbsp;"}
                </a>
              </td>
              <td class="t_border" rowspan="{$child_sku.rowspan}" style="vertical-align: middle;">
                {foreach from=$child_sku.category item=category name=cat}
                  {$category|escape|default:"&nbsp;"}{if !$smarty.foreach.cat.last}<br />{/if}
                {foreachelse}
                  &nbsp;
                {/foreach}
              </td>
              <td class="t_border" rowspan="{$child_sku.rowspan}" style="vertical-align: middle;">
                {$child_sku.main_fabric|escape|default:"&nbsp;"}
              </td>
              <td class="t_border hright" rowspan="{$child_sku.rowspan}" style="vertical-align: middle;">
                {$child_sku.sew_issued|escape|default:"0"|string_format:"%d"}
              </td>
              <td class="t_border hright" rowspan="{$child_sku.rowspan}" style="vertical-align: middle;">
                {$child_sku.sew_opened|escape|default:"0"|string_format:"%d"}
              </td>
              <td class="t_border hright" rowspan="{$child_sku.rowspan}" style="vertical-align: middle;">
                {$child_sku.cut_issued|escape|default:"0"|string_format:"%d"}
              </td>
              <td class="t_border hright" rowspan="{$child_sku.rowspan}" style="vertical-align: middle;">
                {$child_sku.cut_remain|escape|default:"0"|string_format:"%d"}
              </td>
            {/if}
            {if $smarty.foreach.ord.first}
              <td class="t_border" rowspan="{$production.rowspan}" style="vertical-align: middle;">
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$production.id}" target="_blank">
                  {$production.name|escape|default:"&nbsp;"}
                </a>
              </td>
              <td class="t_border" rowspan="{$production.rowspan}" style="vertical-align: middle;">
                {$production.status|escape|default:"&nbsp;"}
              </td>
            {/if}
            <td class="t_border" style="vertical-align: middle;">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$order.id}" target="_blank">
                {$order.name|escape|default:"&nbsp;"}
              </a>
            </td>
            <td class="t_border" style="vertical-align: middle;">
              {$order.date|date_format:#date_short#|escape|default:"&nbsp;"}
            </td>
            <td class="t_border" style="vertical-align: middle;">
              {$order.deadline|date_format:#date_short#|escape|default:"&nbsp;"}
            </td>
            <td class="t_border" style="vertical-align: middle;">
              {$order.comments|escape|nl2br|default:"&nbsp;"}
            </td>
            <td class="t_border hright" style="vertical-align: middle;">
              {$order.sent|escape|default:"0"|string_format:"%d"}
            </td>
            <td class="t_border hright" style="vertical-align: middle;">
              {$order.produced|escape|default:"0"|string_format:"%d"}
            </td>
            <td class="t_border hright" style="vertical-align: middle;">
              {$order.price|escape|default:"0.00"|string_format:"%.4f"}
            </td>
            <td class="t_border hright" style="vertical-align: middle;">
              {$order.produced_value|escape|default:"0.00"|string_format:"%.4f"}
            </td>
            <td class="t_border hright" style="vertical-align: middle;">
              {$order.passed_qc|escape|default:"0"|string_format:"%d"}
            </td>
            <td class="hright" style="vertical-align: middle;">
              {$order.remaining_quantity|escape|default:"0"|string_format:"%d"}
            </td>
          </tr>
        {foreachelse}
          <tr class="{$current_row_class}">
            {if $smarty.foreach.prod.first && $smarty.foreach.sku.first}
              <td class="t_border" rowspan="{$atelier.rowspan}" style="vertical-align: middle;">
                {$atelier.name|escape|default:"&nbsp;"}
              </td>
            {/if}
            {if $smarty.foreach.prod.first}
              <td class="t_border" rowspan="{$child_sku.rowspan}" style="vertical-align: middle;">
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={$child_sku.id}" target="_blank">
                  {$child_sku.name|escape|default:"&nbsp;"}
                </a>
              </td>
              <td class="t_border" rowspan="{$child_sku.rowspan}" style="vertical-align: middle;">
                {foreach from=$child_sku.category item=category name=cat}
                  {$category|escape|default:"&nbsp;"}{if !$smarty.foreach.cat.last}<br />{/if}
                {foreachelse}
                  &nbsp;
                {/foreach}
              </td>
              <td class="t_border" rowspan="{$child_sku.rowspan}" style="vertical-align: middle;">
                {$child_sku.main_fabric|escape|default:"&nbsp;"}
              </td>
              <td class="t_border hright" rowspan="{$child_sku.rowspan}" style="vertical-align: middle;">
                {$child_sku.sew_issued|escape|default:"0"|string_format:"%d"}
              </td>
              <td class="t_border hright" rowspan="{$child_sku.rowspan}" style="vertical-align: middle;">
                {$child_sku.sew_opened|escape|default:"0"|string_format:"%d"}
              </td>
              <td class="t_border hright" rowspan="{$child_sku.rowspan}" style="vertical-align: middle;">
                {$child_sku.cut_issued|escape|default:"0"|string_format:"%d"}
              </td>
              <td class="t_border hright" rowspan="{$child_sku.rowspan}" style="vertical-align: middle;">
                {$child_sku.cut_remain|escape|default:"0"|string_format:"%d"}
              </td>
            {/if}
            <td class="t_border" rowspan="{$production.rowspan}" style="vertical-align: middle;">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$production.id}" target="_blank">
                {$production.name|escape|default:"&nbsp;"}
              </a>
            </td>
            <td class="t_border" rowspan="{$production.rowspan}" style="vertical-align: middle;">
              {$production.status|escape|default:"&nbsp;"}
            </td>
            <td rowspan="{$production.rowspan}" colspan="10" style="vertical-align: middle; color: #9D9D9D;">
              <i>{#arise_no_client_orders#|escape}</i>
            </td>
          </tr>
        {/foreach}
      {/foreach}
    {/foreach}
  {foreachelse}
    <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}">
      <td class="error" colspan="20">{#no_items_found#|escape}</td>
    </tr>
  {/foreach}
  <tr class="row_blue">
    <td class="t_border hright" colspan="4"><strong>{#reports_total#}</strong></td>
    <td class="t_border hright"><strong>{$reports_additional_options.issued|string_format:"%d"|default:"0"}</strong></td>
    <td class="t_border hright"><strong>{$reports_additional_options.opened|string_format:"%d"|default:"0"}</strong></td>
    <td class="t_border hright"><strong>{$reports_additional_options.for_cut|string_format:"%d"|default:"0"}</strong></td>
    <td class="t_border hright"><strong>{$reports_additional_options.remain_cut|string_format:"%d"|default:"0"}</strong></td>
    <td class="t_border hright" colspan="6">&nbsp;</td>
    <td class="t_border hright"><strong>{$reports_additional_options.production_qty|string_format:"%d"|default:"0"}</strong></td>
    <td class="t_border hright"><strong>{$reports_additional_options.produced_qty|string_format:"%d"|default:"0"}</strong></td>
    <td class="t_border hright">&nbsp;</td>
    <td class="t_border hright"><strong>{$reports_additional_options.total|string_format:"%.4f"|default:"0.00"}</strong></td>
    <td class="t_border hright"><strong>{$reports_additional_options.qc_passed_quantity|string_format:"%d"|default:"0"}</strong></td>
    <td class="hright"><strong>{$reports_additional_options.qc_remaining_quantity|string_format:"%d"|default:"0"}</strong></td>
  </tr>

  <tr>
    <td class="t_footer" colspan="20"></td>
  </tr>
</table>
