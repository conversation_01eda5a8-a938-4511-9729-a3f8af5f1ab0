var reports_address_expenses_and_incomes_for_period_interval = '';
document.observe('dom:loaded', function () {
    /**
     * Export button
     */
    $('export_button').observe('click', function () {
        /**
         * Validate
         */
        if (!$('period_from').value || !$('period_to').value) {
            alert('Моля, попълнете задължителните филтри!');
            return false;
        }

        /**
         * Export
         */
        Cookie.set('report_byfar_products_sent', 1, 1);

        Effect.Center('loading');
        Effect.Appear('loading');

        report_byfar_products_sent = window.setInterval(
            function () {
                if (!Cookie.get('report_byfar_products_sent')) {
                    Effect.Fade('loading');
                    clearInterval(report_byfar_products_sent);
                }
            },
            1000
        );

        window.location = env.base_url + '?' + Form.serialize($('reports_generated'));
    });
});
