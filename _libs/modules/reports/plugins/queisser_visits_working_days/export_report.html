<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
    {literal}
      <style type="text/css">
        br {
          mso-data-placement: same-cell;
        }
        tr.row_red td {
          background-color:#CC1100;
          color: #FFFFFF;
        }
        tr.row_yellow td {
          background-color:#FFFDC0;
        }
        tr.row_green td {
          background-color:#BAFF8C;
        }
        tr.row_dark_green td {
          background-color:#DBEAD0;
        }
        tr.row_orange td {
          background-color:#FFCE37;
        }
        tr.row_blue td {
          background-color:#98BCFF;
        }
        tr.row_pink td {
          background-color:#F4878D;
        }
        tr.row_light_pink td {
          background-color:#EFB9B9;
        }
        tr.row_magenta td {
          background-color:#FECDCD;
        }
      </style>
    {/literal}
  </head>
  <body>
    {if is_array($reports_results) && $reports_results|@count}
      {if $reports_additional_options.result_table eq '1'}
        {if $reports_additional_options.export_type eq 'main'}
          <table border="1" cellpadding="0" cellspacing="0">
            <tr>
              <td style="vertical-align: middle; text-align:center;"><strong>{#reports_employee#|escape}</strong></td>
              <td style="vertical-align: middle; text-align:center;"><strong>{#reports_month#|escape}</strong></td>
              <td style="vertical-align: middle; text-align:center;"><strong>{#reports_working_days#|escape}</strong></td>
              <td style="vertical-align: middle; text-align:center;"><strong>{#reports_free_days#|escape}</strong></td>
              <td style="vertical-align: middle; text-align:center;"><strong>{#reports_compensations#|escape}</strong></td>
              <td style="vertical-align: middle; text-align:center;"><strong>{#reports_sick_days#|escape}</strong></td>
              <td style="vertical-align: middle; text-align:center;"><strong>{#reports_activities_in_working_days#|escape}</strong></td>
              <td style="vertical-align: middle; text-align:center;"><strong>{#reports_activities_in_weekend_days#|escape}</strong></td>
              <td style="vertical-align: middle; text-align:center;"><strong>{#reports_expected_completed_visits#|escape}</strong></td>
              <td style="vertical-align: middle; text-align:center;"><strong>{#reports_role_type_main#|escape}</strong></td>
              <td style="vertical-align: middle; text-align:center;"><strong>{#reports_role_type_additional#|escape}</strong></td>
              <td style="vertical-align: middle; text-align:center;"><strong>{#reports_completed_visits#|escape}</strong></td>
              <td style="vertical-align: middle; text-align:center;"><strong>{#reports_goal_percentage#|escape}</strong></td>
            </tr>
            {foreach from=$reports_results item='customer'}
              {foreach from=$customer.months item='month'}
                <tr>
                  <td style="mso-number-format:'\@';">
                    {$customer.name|escape|default:"&nbsp;"}
                  </td>
                  <td style="mso-number-format:'\@';">
                    {$month.name|escape|default:"&nbsp;"}
                  </td>
                  <td style="mso-number-format:'0';">
                    {$month.included_events.working_days|escape|default:"0;"}
                  </td>
                  <td style="mso-number-format:'0';">
                    {$month.included_events.free_days|escape|default:"0;"}
                  </td>
                  <td style="mso-number-format:'0';">
                    {$month.included_events.compensations|escape|default:"0;"}
                  </td>
                  <td style="mso-number-format:'0';">
                    {$month.included_events.sickness|escape|default:"0;"}
                  </td>
                  <td style="mso-number-format:'0';">
                    {$month.included_events.activities_in_working_days|escape|default:"0;"}
                  </td>
                  <td style="mso-number-format:'0';">
                    {$month.included_events.activities_in_weekend_days|escape|default:"0;"}
                  </td>
                  <td style="mso-number-format:'0';">
                    {$month.included_events.visits_possible|escape|default:"0;"}
                  </td>
                  <td style="mso-number-format:'0';">
                    {$month.included_events.main_participant|escape|default:"0"}
                  </td>
                  <td style="mso-number-format:'0';">
                    {$month.included_events.add_participant|escape|default:"0"}
                  </td>
                  <td style="mso-number-format:'0';">
                    {$month.included_events.visits_made|escape|default:"0;"}
                  </td>
                  <td style="text-align: right; mso-number-format:'0\.00 %';">
                    {$month.included_events.percentage_complete|escape|default:"%.2f"} %
                  </td>
                </tr>
              {/foreach}
            {/foreach}
          </table>
        {else}
          <table border="1" cellpadding="0" cellspacing="0">
            <tr>
              <td width="170"><strong>{#reports_employee#|escape}</strong></td>
              <td width="170"><strong>{#reports_activity_type#|escape}</strong></td>
              <td width="170"><strong>{#reports_activity_kind#|escape}</strong></td>
              <td width="170"><strong>{#reports_activity_customer_type#|escape}</strong></td>
              <td width="130"><strong>{#reports_activity_from#|escape}</strong></td>
              <td width="130"><strong>{#reports_activity_to#|escape}</strong></td>
              <td width="90"><strong>{#reports_activity_role#|escape}</strong></td>
              <td width="250"><strong>{#reports_activity_description#|escape}</strong></td>
            </tr>
            {foreach from=$reports_results item='activity'}
              {capture assign='current_row_class'}{$activity.class}{/capture}
              <tr class="{$current_row_class}">
                <td style="vertical-align: middle;">
                  {$activity.employee|escape|default:"&nbsp;"}
                </td>
                <td style="vertical-align: middle;">
                  {$activity.type|escape|default:"&nbsp;"}
                </td>
                <td style="vertical-align: middle;">
                  {$activity.kind|escape|default:"&nbsp;"}
                </td>
                <td style="vertical-align: middle;">
                  {$activity.c_type|escape|default:"&nbsp;"}
                </td>
                <td style="vertical-align: middle;">
                  {$activity.from|escape|default:"&nbsp;"}
                </td>
                <td style="vertical-align: middle;">
                  {$activity.to|escape|default:"&nbsp;"}
                </td>
                <td style="text-align: center; vertical-align: middle;">
                  {$activity.role|escape|default:"&nbsp;"}
                </td>
                <td style="vertical-align: middle;">
                  {$activity.descr|escape|nl2br|default:"&nbsp;"}
                </td>
              </tr>
            {foreachelse}
              <tr>
                <td colspan="7"><span style="color: red;">{#no_items_found#|escape}</span></td>
              </tr>
            {/foreach}
          </table>
        {/if}
      {else}
        <h1>{#reports_report_by_client#|escape}</h1>
        <table border="1" cellpadding="0" cellspacing="0">
          <tr>
            <td style="vertical-align: middle; text-align:center;"><strong>{#reports_employee#|escape}</strong></td>
            <td style="vertical-align: middle; text-align:center;"><strong>{#reports_expected_visits_per_period#|escape}</strong></td>
            <td style="vertical-align: middle; text-align:center;"><strong>{#reports_made_visits_per_period#|escape}</strong></td>
            <td style="vertical-align: middle; text-align:center;"><strong>{#reports_percent_completed_per_period#|escape}</strong></td>
          </tr>
          {foreach from=$reports_results item='customer'}
            <tr>
              <td style="mso-number-format:'\@';">
                {$customer.name|escape|default:"&nbsp;"}
              </td>
              <td style="mso-number-format:'0';">
                {$customer.visits_expected_total|escape|default:"0;"}
              </td>
              <td style="mso-number-format:'0';">
                {$customer.visits_made_total|escape|default:"0;"}
              </td>
              <td style="text-align: right; mso-number-format:'0\.00 %';">
                {$customer.percentage_complete|escape|default:"%.2f"} %
              </td>
            </tr>
          {/foreach}
          <tr>
            <td style="background-color: #98BCFF;"><strong>{#reports_total#|escape}</strong></td>
            <td style="background-color: #98BCFF; mso-number-format:'0';"><strong>{$reports_additional_options.totals.visits_expected|escape|default:"0;"}</strong></td>
            <td style="background-color: #98BCFF; mso-number-format:'0';"><strong>{$reports_additional_options.totals.visits_made|escape|default:"0;"}</strong></td>
            <td style="background-color: #98BCFF; text-align: right; mso-number-format:'0\.00 %';"><strong>{$reports_additional_options.totals.percents_completed|escape|default:"%.2f"} %</strong></td>
          </tr>
        </table>
      {/if}
    {/if}
  </body>
</html>