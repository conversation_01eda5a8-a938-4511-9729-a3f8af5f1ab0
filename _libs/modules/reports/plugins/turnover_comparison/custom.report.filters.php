<?php

class Custom_Report_Filters extends Report_Filters {

    /**
     * @var Registry
     */
    private static $registry;

    /**
     * Defining filters for the certain type of report
     *
     * @param Registry $registry - the main registry
     * @return array - filter definitions of report
     */
    function defineFilters(Registry &$registry) {
        self::$registry = &$registry;

        // $filters - array containing description of all filters
        $filters = array();

        /** @var Turnover_Comparison $report_class */
        $report_class = implode('_', array_map('ucfirst', explode('_', $this->reportName)));
        require_once PH_MODULES_DIR . 'reports/plugins/' . $this->reportName . '/custom.report.query.php';
        $settings = $report_class::getReportSettings($registry);

        // optional filters:
        foreach ($settings['enabled_filters'] as $name) {
            $filter = $dependent_filters = array();
            switch ($name) {
                case 'fir_types':
                    $filter = array(
                        'type' => 'radio',
                        'required' => in_array($name, $settings['required_filters']),
                        'hidden' => 0,
                        'options' => array(),
                        'options_align' => 'horizontal',
                    );
                    // prepare dependent filters and options of main filter
                    require_once PH_MODULES_DIR . 'finance/models/finance.dropdown.php';
                    $fir_types_options = Finance_Dropdown::getFinanceDocumentsTypes(array($registry, 'model' => 'Finance_Incomes_Reason'));
                    foreach ($settings['fir_types_groups'] as $types_group) {
                        $types_options = array_filter(
                            $fir_types_options,
                            function($a) use ($types_group, $settings) {
                                return !empty($settings["fir_types_{$types_group}"]) && in_array($a['option_value'], $settings["fir_types_{$types_group}"]);
                            }
                        );
                        if ($types_options) {
                            $filter['options'][$types_group] = array(
                                'option_value' => $types_group,
                                'label' => $this->i18n("reports_fir_types_{$types_group}"),
                            );
                            $dependent_filters[$types_group] = array(
                                'type' => 'checkbox_group',
                                'name' => $types_group,
                                'hidden' => !empty($dependent_filters),
                                'label' => ' ',
                                'help' => ' ',
                                'options' => $types_options,
                                'options_align' => 'horizontal',
                            );
                        }
                    }
                    if (count($filter['options']) >= 2) {
                        $filter['on_change'] = 'Turnover_Comparison.toggleTypesFilters(this);';
                    } else {
                        $filter['hidden'] = 1;
                        if (!empty($dependent_filters)) {
                            $filter_value = reset($filter['options']);
                            $filter_value = $filter_value['option_value'];
                            $dependent_filters[$filter_value]['label'] = $dependent_filters[$filter_value]['help'] = $this->i18n("reports_{$name}");
                        }
                    }
                    break;
                case 'company':
                    require_once  PH_MODULES_DIR . 'finance/models/finance.companies.factory.php';
                    $companies = Finance_Companies::search(
                        $registry,
                        array(
                            'sort' => array('fci18n.name ASC',),
                            'sanitize' => true,
                            'force' => false,
                        ));
                    array_walk($companies, function(&$a) {
                        $a = array(
                            'option_value' => $a->get('id'),
                            'active_option' => $a->get('active'),
                            'label' => $a->get('name'),
                        );
                    });
                    $filter = array(
                        'type' => 'checkbox_group',
                        'options' => $companies,
                    );
                    break;
                case 'office':
                    require_once PH_MODULES_DIR . 'finance/models/finance.dropdown.php';
                    $filter = array(
                        'type' => 'checkbox_group',
                        'options' => Finance_Dropdown::getCompanyOffices(array($registry)),
                    );
                    break;
                case 'department':
                    $filter = array(
                        'type' => 'dropdown',
                        'options' => Dropdown::getDepartments(array($registry)),
                    );
                    break;
                case 'article_category':
                    $this->loadDefaultFilter(
                        $registry,
                        $filter,
                        'dropdown',
                        array(
                            'filter_name' => $name,
                            'options' => Nomenclatures_Dropdown::getArticleCategories(array($registry)),
                        )
                    );
                    $filter = reset($filter);
                    $level_str = '&nbsp;&nbsp;';
                    array_walk($filter['options'], function(&$a) use ($level_str) {
                        if (preg_match('#^(' . $level_str . ')+#', $a['label'], $matches)) {
                            $a['label'] = preg_replace('#^' . $matches[0] . '#', str_repeat('---', floor(strlen($matches[0]) / strlen($level_str))), $a['label']);
                        }
                    });
                    break;
                case 'employee':
                case 'customer':
                case 'article':
                case 'trademark':
                    $params = array(
                        'autocomplete_type' => in_array($name, array('customer', 'employee')) ? 'customers' : 'nomenclatures',
                        'filter_name' => $name,
                        'autocomplete' => array(
                            'filters' => array(),
                        ),
                    );
                    if (!empty($settings["{$name}_types"])) {
                        $params['autocomplete']['filters']['<type>'] = strval($settings["{$name}_types"]);
                    }
                    if (!empty($settings["{$name}_include_inactive"])) {
                        $params['autocomplete']['filters']['<active>'] = '1,0';
                    }
                    if ($name == 'trademark') {
                        $params['autocomplete']['filters']['<type_keyword>'] = 'trademark';
                        $params['label'] = $this->i18n('trademark');
                    }
                    $this->loadDefaultFilter($registry, $filter, 'autocompleter', $params);
                    $filter = reset($filter);
                    break;
                case 'tag':
                    $tags_options_grouped = Dropdown::getTags(array(
                        $registry,
                        'model' => 'customers',
                        'model_types' => (!empty($settings['customer_types']) ? $settings['customer_types'] : null),
                        'active' => 1,
                    ));
                    if (!empty($tags_options_grouped)) {
                        $filter = array(
                            'type' => 'dropdown',
                            'options' => !empty($tags_options_grouped['contain_optgroups']) ? null : $tags_options_grouped,
                            'optgroups' => !empty($tags_options_grouped['contain_optgroups']) ? array_diff_key($tags_options_grouped, array('contain_optgroups' => '')) : null,
                        );
                    } else {
                        continue 2;
                    }
                    break;
                case 'currency':
                    // Remove currencies without rate
                    require_once PH_MODULES_DIR . 'finance/models/finance.dropdown.php';
                    $currencies = Finance_Dropdown::getCurrencies(array($registry));

                    //check if the rates have been fetched already
                    $date = General::strftime('%Y-%m-%d');
                    if (!Finance_Currencies::searchOne($registry, array('where' => array("fcur.date = '{$date}'")))) {
                        Finance_Currencies::updateRates($registry);
                    }

                    // Remove currencies without rate
                    foreach ($currencies as $key => $currency) {
                        // rates are always fetched in BGN
                        if ($currency['option_value'] == 'BGN') {
                            continue;
                        }
                        $currency = Finance_Currencies::searchOne(
                            $registry,
                            array(
                                'where' => array(
                                    "fcur.code = '{$currency['option_value']}'",
                                    "fcur.date <= '{$date}'",
                                ),
                                'sort' => array(
                                    'fcur.date DESC',
                                ),
                                'sanitize' => true,
                                )
                            );
                        if (!$currency || $currency->get('rate') == 0) {
                            unset($currencies[$key]);
                        }
                    }

                    $filter = array(
                        'type' => 'dropdown',
                        'required' => true,
                        'options' => $currencies,
                    );
                    break;
                default:
                    continue 2;
            }

            // process multiple filters:
            if (in_array($name, $settings['multiple_filters'])) {
                if ($filter['type'] == 'autocompleter') {
                    $filter['width'] = 244;
                    $filter['custom_buttons'] = 'search';
                }
                $filter['actual_type'] = $filter['type'];
                $filter['type'] = 'custom_filter';
                $filter['custom_template'] = PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html';
            }
            $filters[$name] = $filter + array(
                'custom_id' => $name,
                'name' => $name,
                'required' => false,
                'hidden' => false,
                'label' => $this->i18n("reports_{$name}"),
                'help' => $this->i18n("reports_{$name}"),
            );
            if ($dependent_filters) {
                $filters = $filters + $dependent_filters;
            }
        }

        // process grouping filters:
        $grouping_filters = array_intersect_key($filters, array_flip($settings['grouping_filters']));
        if ($grouping_filters) {
            $filters = array_diff_key($filters, $grouping_filters);

            $grouping_keys = array_keys($grouping_filters);
            foreach ($grouping_keys as $name) {
                $toggle_name = "report_based_on_{$name}";
                $toggle_filter = array(
                    $toggle_name => array(
                        'custom_id' => $toggle_name,
                        'name'      => $toggle_name,
                        'type'      => 'checkbox',
                        'label'     => $grouping_filters[$name]['label'],
                        'help'      => $grouping_filters[$name]['label'],
                        'onclick'   => "activateRow(this, '" . (in_array($name, $settings['multiple_filters']) ? 'table_' : '') . $name . "')",
                        'option_value' => "{$name}_checked",
                    )
                );
                $grouping_filters[$name]['hidden'] = 1;
                $grouping_filters[$name]['disabled'] = 1;
                General::injectInArray($toggle_filter, $grouping_filters, 'before', $name);
            }

            // define divider rows before and after grouping filters
            $grouping_filters =
                array(
                    '_devider' => array(
                        'custom_id' => '_devider',
                        'name'      => '_devider',
                        'type'      => 'devider',
                        'label'     => $this->i18n('reports_report_by')
                    ),
                ) +
                $grouping_filters +
                array(
                    '_devider2' => array(
                        'custom_id' => '_devider2',
                        'name'      => '_devider2',
                        'type'      => 'devider',
                        'label' => '',
                    ),
                );

            // add grouping filters back into filters array
            $min_key_pos = min(array_keys(array_intersect($settings['enabled_filters'], $grouping_keys)));

            General::injectInArray($grouping_filters, $filters, $min_key_pos == 0 ? 'first' : 'after', $min_key_pos == 0 ? '' : $settings['enabled_filters'][$min_key_pos - 1]);
        }

        // prepare required filters:
        $period_filters = array();

        // period 1
        $this->loadDefaultFilter(
            $registry,
            $period_filters,
            'period_from_to',
            array(
                'required' => in_array('period_from', $settings['required_filters']),
            )
        );
        $period_filters['period_from']['filter_type'] = $period_filters['period_to']['filter_type'] = 'dropdown';
        $period_filters['period_from']['width'] = $period_filters['period_to']['width'] = 100;
        $period_filters['period_from']['options'] = $period_filters['period_to']['options'] = array();
        $period_filters['period_from']['first_filter_label'] = $this->i18n('from');

        $start_year = date('Y');
        if (!empty($settings['period_start_year'])) {
            $start_year = $settings['period_start_year'];
        } elseif (!empty($settings['period_num_past_years'])) {
            $start_year -= $settings['period_num_past_years'];
        }
        $num_months = (date('Y') - $start_year + 1) * 12;
        $date = new DateTime("{$start_year}-01-01");
        while ($num_months > 0) {
            $label = General::strftime('%B %Y', $date->getTimestamp(), true);
            $period_filters['period_from']['options'][] = array(
                'label' => $label,
                'option_value' => $date->format('Y-m-01')
            );
            $period_filters['period_to']['options'][] = array(
                'label' => $label,
                'option_value' => $date->format('Y-m-t')
            );
            $date = $date->add(new DateInterval('P1M'));
            $num_months--;
        }

        // period 2
        $period_filters['period2_from'] = $period_filters['period_from'];
        $period_filters['period2_from']['name'] = $period_filters['period2_from']['custom_id'] = 'period2_from';
        $period_filters['period2_from']['additional_filter'] = 'period2_to';
        $period_filters['period2_to'] = $period_filters['period_to'];
        $period_filters['period2_to']['name'] = $period_filters['period2_to']['custom_id'] = 'period2_to';

        $period_filters['period_from']['label'] = $this->i18n('reports_default_filter_date_from_to_period_from') . ' 1';
        $period_filters['period2_from']['label'] = $this->i18n('reports_default_filter_date_from_to_period_from') . ' 2';

        // add onchange logic
        $period_filters['period_from']['onchange'] =
        $period_filters['period_to']['onchange'] =
        $period_filters['period2_from']['onchange'] =
        $period_filters['period2_to']['onchange'] = "Turnover_Comparison.updatePeriods(this, {$settings['period_default_num_months']})";

        $filters = $period_filters + $filters;

        return $filters;
    }

    /**
     * Process some filters that depend on the request or on each other
     *
     * @param array $filters - the report filters
     * @return array - report filters after processing
     */
    function processDependentFilters(array &$filters) {
        $registry = &self::$registry;

        // default group of types to search by - get first possible option
        // it will be invoices unless search by invoices is off and search by reasons in on
        $default_fir_types_group = 'invoices';
        if (!empty($filters['fir_types']) && !empty($filters['fir_types']['options'])) {
            $default_fir_types_group = reset($filters['fir_types']['options']);
            $default_fir_types_group = $default_fir_types_group['option_value'];
            // check that value is valid
            if (!empty($filters['fir_types']['value']) && !in_array($filters['fir_types']['value'], array_map(function($a) { return $a['option_value']; }, $filters['fir_types']['options']))) {
                unset($filters['fir_types']['value']);
            }
        }

        $unset_filters = array();
        foreach ($filters as $name => $filter) {
            // process and unset additional filters
            if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                $unset_filters[] = $filter['additional_filter'];
            }

            if (preg_match('#^report_based_on_(.*)$#', $name, $matches) && array_key_exists($matches[1], $filters) && $filter['option_value'] == $filter['value']) {
                // enable grouping filters when their toggles are on
                $filters[$matches[1]]['hidden'] = $filters[$matches[1]]['disabled'] = false;
            } elseif (in_array($name, array('invoices', 'reasons'))) {
                // toggle types filters
                $selected_fir_types_group = !empty($filters['fir_types']) && !empty($filters['fir_types']['value']) ? $filters['fir_types']['value'] : $default_fir_types_group;
                $filters[$name]['hidden'] = $filters[$name]['disabled'] = $name != $selected_fir_types_group;
            }

            // set default value
            if (empty($filter['value'])) {
                switch ($name) {
                    case 'fir_types':
                        $filters[$name]['value'] = $default_fir_types_group;
                        break;
                    case 'invoices':
                    case 'reasons':
                        $filters[$name]['value'] = array_map(function($a) { return $a['option_value']; }, $filter['options']);
                    case 'currency':
                        $filters[$name]['value'] = Finance_Currencies::getMain($registry);
                        break;
                    default:
                        break;
                }
            }
        }
        foreach ($unset_filters as $unset_fltr) {
            unset($filters[$unset_fltr]);
        }

        return $filters;
    }
}

?>
