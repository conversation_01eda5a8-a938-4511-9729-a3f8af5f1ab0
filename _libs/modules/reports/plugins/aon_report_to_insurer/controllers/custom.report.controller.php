<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {

    /*
     * Relation between gt2 rows and contracts ids
     */
    private $policy_rows_relations = array();

    /*
     * Result of the execution of the operation
     */
    protected $operation_result = array(
        'result'     => false,
        'messages'   => array(
            'success' => array(),
            'failed'  => array()
        ),
        'link'       => ''
    );

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
            case 'aon_report_to_insurer':
                $this->_createInsurerReport();
                break;
            case 'custom_export':
                $this->_customExport();
                break;
            default:
                parent::execute();
        }
    }

    /*
     * Function to create debit note in the selected invoice
     */
    public function _createInsurerReport() {
        set_time_limit(0);
        $registry = &$this->registry;

        $report = $this->getReportType();

        if (empty($report)) {
            $this->redirect($this->module, '', array('report_type' => ''), '');
        }
        $report = $report['name'];

        //load plugin i18n files
        $i18n_files = array();
        $i18n_files[] = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report,
            '/i18n/',
            $registry['lang'],
            '/reports.ini');

        // Load the documents main i18n file
        $i18n_files[] = sprintf('%s%s%s%s',
            PH_MODULES_DIR,
            'documents/i18n/',
            $registry['lang'],
            '/documents.ini');
        $registry['translater']->loadFile($i18n_files);

        Reports::getReportSettings($registry, $report);

        // get request and all post params
        $request = $registry['request'];

        // check the reoprt action
        if ($request->get('rep_action') == 'report') {
            // report have to be created
            $rows_data = $request->get('pay_ids');
            foreach ($rows_data as $key => $row) {
                $rows_data[$key] = unserialize(base64_decode($row));
            }

            // define how many documents have to be created
            $documents_data = array();
            $rows_info = array();
            $debit_notes_additional_vars = array();

            foreach ($rows_data as $row_dat) {
                foreach ($row_dat['debit_notes'] as $debit_note) {
                    foreach ($debit_note['insurers'] as $insurer) {
                        if (!isset($documents_data[$insurer['id']])) {
                            $documents_data[$insurer['id']] = array(
                                'custom_num'    => '',
                                'customer'      => $insurer['id'],
                                'customer_name' => $insurer['name'],
                                'debit_notes'   => array(),
                                'rows'          => array()
                            );
                        }

                        if (!in_array($debit_note['id'], $documents_data[$insurer['id']]['debit_notes'])) {
                            $documents_data[$insurer['id']]['debit_notes'][] = $debit_note['id'];
                        }

                        foreach ($insurer['gt2_rows'] as $row_key => $row_id) {
                            $documents_data[$insurer['id']]['rows'][$row_dat['id'] . '_' . $row_id] = array(
                                'article_second_code'    => $row_dat['num'],
                                'free_field5'            => $row_dat['id'],
                                'free_field4'            => sprintf('%.2f', round(floatval($row_dat['value']) - floatval($row_dat['not_distributed_value']), 2)),
                                'free_field2'            => sprintf('%.2f', (isset($insurer['paid_to_gt2_rows'][$row_key]) ? $insurer['paid_to_gt2_rows'][$row_key] : 0)),
                                'article_deliverer_name' => $row_dat['customer_name'],
                                'article_deliverer'      => $row_dat['customer'],
                                'free_text3'             => $debit_note['num'],
                                'article_delivery_code'  => $debit_note['id']
                            );
                            $rows_relations[$row_dat['id'] . '_' . $row_id] = $row_id;
                        }
                    }
                }
            }

            // define nums for the report
            if (!empty($documents_data)) {
                $sql = 'SELECT `customer`, COUNT(`id`) as current_count' . "\n" .
                       'FROM ' . DB_TABLE_DOCUMENTS . "\n" .
                       'WHERE `type`="' . DOCUMENT_REPORT_TYPE . '" AND `customer` IN ("' . implode('","', array_keys($documents_data)) . '") AND DATE_FORMAT(`added`, "%Y")="' . date('Y') . '"' . "\n" .
                       'GROUP BY `customer`';
                $doc_data = $registry['db']->getAssoc($sql);

                foreach ($documents_data as $insurer_id => $doc) {
                    if (isset($doc_data[$insurer_id])) {
                        $document_num = $doc_data[$insurer_id] + 1;
                    } else {
                        $document_num = 1;
                    }
                    $documents_data[$insurer_id]['custom_num'] = sprintf('%d/%d', $document_num, date('Y'));
                }
            }

            $full_rows_info = array();
            $discount_rows = array();
            if (!empty($rows_relations)) {
                // get the data for the rows
                $sql = 'SELECT gt2.id as idx, gt2.free_field3, gt2.article_id, gt2.article_barcode, gt2.subtotal_with_vat_with_discount as average_weighted_delivery_price, ' . "\n" .
                       '       c.date_sign as free_text5, pol_gt2_i18n.free_text1, pol_gt2.price, pol_gt2.quantity, pol_gt2.subtotal, gt2.free_field2 as article_volume, ' . "\n" .
                       '       pol_gt2.discount_surplus_field, pol_gt2_i18n.article_description as free_field1, pol_gt2.discount_percentage, pol_gt2.discount_value, pol_gt2.last_delivery_price, ' . "\n" .
                       '       pol_gt2.article_code, pol_gt2.article_width, pol_gt2.article_weight, pol_gt2_i18n.article_deliverer_name as article_height, pol_gt2.free_field4 as policy_type, ' . "\n" .
                       '       c.custom_num as article_name, pol_gt2_i18n.article_name as free_text2, pol_gt2_i18n.free_text4 as free_text4, pol_gt2.surplus_value, pol_gt2.surplus_percentage' . "\n" .
                       'FROM ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                       'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' as pol_gt2' . "\n" .
                       '  ON (pol_gt2.id=gt2.free_field2)' . "\n" .
                       'INNER JOIN ' . DB_TABLE_CONTRACTS . ' AS c' . "\n" .
                       '  ON (c.id=pol_gt2.model_id)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS pol_gt2_i18n' . "\n" .
                       '  ON (pol_gt2_i18n.parent_id=pol_gt2.id AND pol_gt2_i18n.lang="' . $registry['lang'] . '")' . "\n" .
                       'WHERE gt2.id IN ("' . implode('","', $rows_relations) . '")' . "\n";
                $full_rows_info = $registry['db']->getAssoc($sql);

                if (INCLUDE_DISCOUNT) {
                    $discount_policicies = array_column($full_rows_info, 'article_volume');
                    if (!empty($discount_policicies)) {
                        $sql = 'SELECT gt2_p.id as idx, gt2_rel.subtotal_discount_value as discount, gt2_p.average_weighted_delivery_price as bruto' . "\n" .
                               'FROM ' . DB_TABLE_GT2_DETAILS . ' AS gt2_p' . "\n" .
                               'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2_rel' . "\n" .
                               ' ON (gt2_p.id IN (' . implode(',', $discount_policicies) . ') AND (gt2_rel.free_field5=gt2_p.free_field5' .
                               ' OR gt2_p.free_field5 LIKE CONCAT(gt2_rel.free_field5, "_%")' . "\n" .
                               '     ) AND gt2_p.model_id=gt2_rel.model_id AND gt2_rel.model="Contract" AND gt2_rel.free_field4="' . DISCOUNT_POLICY_TYPE . '")' . "\n";
                        $discount_rows = $registry['db']->GetAssoc($sql);
                    }
                }
            }

            // get default options for this type
            $sql_default_options = 'SELECT dti18n.default_name, dt.default_department, dt.default_group, dt.default_media, dt.default_customer' . "\n" .
                                   'FROM ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                                   'LEFT JOIN  ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n' . "\n" .
                                   '  ON (dt.id=dti18n.parent_id AND dti18n.lang="' . $registry['lang'] . '")' . "\n" .
                                   'WHERE dt.id="' . DOCUMENT_REPORT_TYPE . '"';
            $default_document_data = $registry['db']->GetRow($sql_default_options);

            require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';
            foreach ($documents_data as $ins_id => $insurer_doc) {
                $included_payments = array();
                foreach ($insurer_doc['rows'] as $key => $row_data) {
                    $included_payments[$row_data['free_field5']] = $row_data['article_second_code'];
                    if (isset($rows_relations[$key]) && isset($full_rows_info[$rows_relations[$key]])) {
                        foreach ($full_rows_info[$rows_relations[$key]] as $key_gt2_var => $gt2_value) {
                            $documents_data[$ins_id]['rows'][$key][$key_gt2_var] = $gt2_value;
                        }
                    }
                }

                if (!empty($discount_rows)) {
                    foreach ($documents_data[$ins_id]['rows'] as $rw_idx => $rw) {
                        if (isset($discount_rows[$rw['article_volume']])) {
                            if ($documents_data[$ins_id]['rows'][$rw_idx]['policy_type'] == 3) {
                                $documents_data[$ins_id]['rows'][$rw_idx]['free_field4'] = '-';
                                continue;
                            }
                            $documents_data[$ins_id]['rows'][$rw_idx]['average_weighted_delivery_price'] -= $discount_rows[$rw['article_volume']]['discount'];
                            $documents_data[$ins_id]['rows'][$rw_idx]['article_height'] -= $discount_rows[$rw['article_volume']]['discount'];

                            // calculate the percent of the payment to be added
                            $discounted_bruto = $discount_rows[$rw['article_volume']]['bruto'] + $discount_rows[$rw['article_volume']]['discount'];
                            $percent_payment_increase = round(bcdiv($documents_data[$ins_id]['rows'][$rw_idx]['free_field2'], $discounted_bruto, 6), 6);
                            $added_sum_to_payment = round(bcsub(bcmul($discount_rows[$rw['article_volume']]['bruto'], $percent_payment_increase, 6), $documents_data[$ins_id]['rows'][$rw_idx]['free_field2'], 6), 2);
                            $documents_data[$ins_id]['rows'][$rw_idx]['free_field2'] += $added_sum_to_payment;
                            $documents_data[$ins_id]['rows'][$rw_idx]['free_field4'] += $added_sum_to_payment;
                        }
                    }
                }

                $registry['db']->StartTrans();
                $document = new Document($registry);
                $old_document = clone $document;

                $document->set('type', DOCUMENT_REPORT_TYPE, true);
                $document->set('custom_num', $insurer_doc['custom_num'], true);
                $document->set('name', $default_document_data['default_name'], true);
                $document->set('department', $default_document_data['default_department'], true);
                $document->set('group', $default_document_data['default_group'], true);
                $document->set('active', 1, true);

                $document->set('customer', $insurer_doc['customer'], true);
                $document->set('date', General::strftime('%Y-%m-%d'), true);

                // try to save the document
                $gt2 = $document->getGT2Vars();
                $gt2['values'] = array_values($documents_data[$ins_id]['rows']);

                if ($document->save()) {
                    $filters = array(
                        'where' => array('d.id = ' . $document->get('id'))
                    );
                    $new_document = Documents::searchOne($registry, $filters);

                    $new_document->getVars();
                    $new_document->set('grouping_table_2', $gt2, true);
                    $new_document->calculateGT2();
                    $new_document->set('table_values_are_set', true, true);

                    if ($new_document->saveGT2Vars()) {
                        Documents_History::saveData($registry,
                            array('action_type' => 'add',
                                  'new_model'   => $new_document,
                                  'old_model'   => $old_document
                            ));

                        // update the included debit notes
                        if (empty($debit_notes_additional_vars)) {
                            $debit_notes_additional_vars = array(DOCUMENT_DEBIT_NOTE_VAR_ORDER_DATE, DOCUMENT_DEBIT_NOTE_VAR_ORDER_NUM, DOCUMENT_DEBIT_NOTE_VAR_ORDER_ID);

                            $sql_for_dn_vars = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `name` IN ("' . implode('","', $debit_notes_additional_vars) . '") AND `model_type`="' . DOCUMENT_DEBIT_NOTE_TYPE . '"' . "\n";
                            $debit_notes_additional_vars = $registry['db']->GetAssoc($sql_for_dn_vars);
                        }

                        $sql = 'SELECT d.id, d_cstm_od.num, d_cstm_od.value as date, d_cstm_on.value as order_num, d_cstm_oid.value as order_id' . "\n" .
                               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_od' . "\n" .
                               '  ON (d_cstm_od.var_id="' . (isset($debit_notes_additional_vars[DOCUMENT_DEBIT_NOTE_VAR_ORDER_DATE]) ? $debit_notes_additional_vars[DOCUMENT_DEBIT_NOTE_VAR_ORDER_DATE] : '') . '" AND d_cstm_od.model_id=d.id)' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_on' . "\n" .
                               '  ON (d_cstm_on.var_id="' . (isset($debit_notes_additional_vars[DOCUMENT_DEBIT_NOTE_VAR_ORDER_NUM]) ? $debit_notes_additional_vars[DOCUMENT_DEBIT_NOTE_VAR_ORDER_NUM] : '') . '" AND d_cstm_on.model_id=d.id AND d_cstm_on.num=d_cstm_od.num)' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_oid' . "\n" .
                               '  ON (d_cstm_oid.var_id="' . (isset($debit_notes_additional_vars[DOCUMENT_DEBIT_NOTE_VAR_ORDER_ID]) ? $debit_notes_additional_vars[DOCUMENT_DEBIT_NOTE_VAR_ORDER_ID] : '') . '" AND d_cstm_oid.model_id=d.id AND d_cstm_oid.num=d_cstm_od.num)' . "\n" .
                               'WHERE d.id IN ("' . implode('","', $insurer_doc['debit_notes']) . '")' . "\n" .
                               'ORDER BY d_cstm_od.num ASC' . "\n";
                        $debit_notes_reports_results = $registry['db']->GetAll($sql);

                        $debit_notes_active_rows = array();
                        foreach ($debit_notes_reports_results as $dnrr) {
                            if (!isset($debit_notes_active_rows[$dnrr['id']])) {
                                $debit_notes_active_rows[$dnrr['id']] = array();
                            }
                            $debit_notes_active_rows[$dnrr['id']][$dnrr['num']] = array(
                                'date'      => $dnrr['date'],
                                'order_num' => $dnrr['order_num'],
                                'order_id'  => $dnrr['order_id']
                            );
                        }
                        $debit_notes_new_rows = array();
                        foreach ($insurer_doc['debit_notes'] as $dn) {
                            if (!isset($debit_notes_active_rows[$dn])) {
                                $debit_notes_active_rows[$dn] = 1;
                            } else {
                                foreach ($debit_notes_active_rows[$dn] as $row_num => $completed_rows) {
                                    if (empty($completed_rows['date'])) {
                                        $debit_notes_active_rows[$dn] = $row_num;
                                        break;
                                    }
                                }
                            }

                            if (is_array($debit_notes_active_rows[$dn])) {
                                $current_row_nums = array_keys($debit_notes_active_rows[$dn]);
                                $last_completed_row = end($current_row_nums);
                                $debit_notes_active_rows[$dn] = $last_completed_row + 1;
                            }
                        }

                        if (!empty($debit_notes_active_rows)) {
                            $filters = array(
                                'where' => array('d.id IN ("' . implode('","', array_keys($debit_notes_active_rows)) . '")')
                            );
                            $debit_notes = Documents::search($registry, $filters);

                            foreach ($debit_notes as $debit_note) {
                                $debit_note->getVars();
                                $old_debit_note = clone $debit_note;

                                $values = array();

                                // prepare update queries
                                $values[] = sprintf('("%s", NOW(), "%d", "%d", "%d", "%d", NOW(), "%d", "")',
                                    $new_document->get('date'),
                                    $registry['currentUser']->get('id'),
                                    $debit_note->get('id'),
                                    $debit_notes_additional_vars[DOCUMENT_DEBIT_NOTE_VAR_ORDER_DATE],
                                    $debit_notes_active_rows[$debit_note->get('id')],
                                    $registry['currentUser']->get('id')
                                );

                                $values[] = sprintf('("%s", NOW(), "%d", "%d", "%d", "%d", NOW(), "%d", "")',
                                    $new_document->get('id'),
                                    $registry['currentUser']->get('id'),
                                    $debit_note->get('id'),
                                    $debit_notes_additional_vars[DOCUMENT_DEBIT_NOTE_VAR_ORDER_ID],
                                    $debit_notes_active_rows[$debit_note->get('id')],
                                    $registry['currentUser']->get('id')
                                );

                                $values[] = sprintf('("%s", NOW(), "%d", "%d", "%d", "%d", NOW(), "%d", "")',
                                    $new_document->get('custom_num'),
                                    $registry['currentUser']->get('id'),
                                    $debit_note->get('id'),
                                    $debit_notes_additional_vars[DOCUMENT_DEBIT_NOTE_VAR_ORDER_NUM],
                                    $debit_notes_active_rows[$debit_note->get('id')],
                                    $registry['currentUser']->get('id')
                                );

                                $query = 'INSERT INTO `' . DB_TABLE_DOCUMENTS_CSTM . '` (`value`, `modified`, `modified_by`, `model_id`, `var_id`, `num`, `added`, `added_by`, `lang`)' . "\n" .
                                         'VALUES ' . implode(', ' . "\n", $values) . "\n" .
                                         'ON DUPLICATE KEY UPDATE `value` = VALUES(`value`), `modified` = VALUES(`modified`), `modified_by` = VALUES(`modified_by`)' . "\n";
                                $registry['db']->Execute($query);

                                $filters = array(
                                    'where' => array('d.id = ' . $debit_note->get('id'))
                                );
                                $new_debit_note = Documents::searchOne($registry, $filters);

                                $new_debit_note->getVars();
                                Documents_History::saveData($registry,
                                                            array('action_type' => 'edit',
                                                                  'new_model'   => $new_debit_note,
                                                                  'old_model'   => $old_debit_note
                                ));
                            }
                        }
                    } else {
                        $this->setOperationResult('error', sprintf($this->i18n('error_reports_gt2_report_save_failed'), $insurer_doc['customer_name'], implode(', ', $included_payments)));
                        $registry['db']->FailTrans();
                    }
                } else {
                    $this->setOperationResult('error', sprintf($this->i18n('error_reports_report_save_failed'), $insurer_doc['customer_name'], implode(', ', $included_payments)));
                    $registry['db']->FailTrans();
                }

                if (!$registry['db']->HasFailedTrans()) {
                    $filters = array(
                        'where' => array('d.id IN (' . implode(',', array_keys($included_payments)) . ')')
                    );
                    $payments = Documents::search($registry, $filters);

                    // update the tags of the payments
                    foreach ($payments as $payment) {
                        $old_payment = clone $payment;
                        $old_payment->getModelTagsForAudit();

                        $payment->unsanitize();
                        $payment->getTags();
                        $payment->set('tags', array_merge($payment->get('tags'), array(DOCUMENT_DEBIT_NOTE_PAYMENT_TAG_ISSUED_REPORT)), true);

                        if ($payment->updateTags(array('skip_permissions' => true))) {
                            // get the updated payment
                            $filters_pay = array(
                                'where' => array('d.id = ' . $payment->get('id'))
                            );
                            $new_payment = Documents::searchOne($registry, $filters_pay);
                            $new_payment->getModelTagsForAudit();
                            $new_payment->sanitize();

                            Documents_History::saveData($registry, array('model' => $new_payment, 'action_type' => 'tag', 'new_model' => $new_payment, 'old_model' => $old_payment));
                            $this->setOperationResult('result', true);
                            $this->setOperationResult('link', sprintf(
                                '%s?%s=%s&%s=%s',
                                $_SERVER['PHP_SELF'],
                                $registry->get('module_param'), 'reports',
                                'report_type', $report
                            ));
                            $this->setOperationResult(
                                'success',
                                sprintf($this->i18n('report_successfully_added_report'),
                                    sprintf('<a href="%s?%s=documents&documents=view&view=%d" target="_blank">%s</a>',
                                        $_SERVER['PHP_SELF'], $this->registry['module_param'], $new_document->get('id'),
                                        $new_document->get('custom_num')
                                    ),
                                    $new_document->get('customer_name')
                                ),
                                $new_document->get('id')
                            );
                        } else {
                            $this->setOperationResult('error', sprintf($this->i18n('error_reports_tag_payment_failed'), $payment->get('full_num')));
                            $registry['db']->FailTrans();
                        }
                    }
                }
                $registry['db']->CompleteTrans();
            }
        } else {
            // reports have to be confirmed
            // report have to be created
            $rows_data = $request->get('rep_ids');
            foreach ($rows_data as $key => $row) {
                $rows_data[$key] = unserialize(base64_decode($row));
                $rows_data[$key]['policy_rows'] = array_map('round', explode('|', $rows_data[$key]['policy_rows']));
                $this->policy_rows_relations = array_merge($this->policy_rows_relations, $rows_data[$key]['policy_rows']);
            }

            // get the ids of all the models for all rows
            $this->policy_rows_relations = array_unique($this->policy_rows_relations);
            $sql = 'SELECT `id`, `model_id` FROM ' . DB_TABLE_GT2_DETAILS . ' WHERE `id` IN ("' . implode('","', $this->policy_rows_relations) . '")';
            $this->policy_rows_relations = $registry['db']->GetAssoc($sql);

            require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
            require_once PH_MODULES_DIR . 'contracts/models/contracts.history.php';
            require_once PH_MODULES_DIR . 'contracts/models/contracts.audit.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';

            // get the name of the substatus
            $sql = 'SELECT `name` FROM ' . DB_TABLE_DOCUMENTS_STATUSES . ' WHERE `id`="' . preg_replace('#(.*)_(\d+)#', '$2', DOCUMENT_IGNORE_STATUSES) . '" AND `lang`="' . $registry['lang'] . '"';
            $substatus_report_name = $registry['db']->GetOne($sql);

            $registry->set('get_old_vars', true, true);
            $updated_contracts = $this->updateDocuments($rows_data, $substatus_report_name);

            if (!empty($updated_contracts)) {
                // start updating contracts by separating it to chunks
                $chunk_size = 10;
                $chunks = array_chunk($updated_contracts, $chunk_size, true);

                foreach ($chunks as $chunk) {
                    $this->processContractsHistory($chunk);
                }
            }
            $registry->set('get_old_vars', false, true);
        }

        if (empty($this->operation_result['messages']['success'])) {
            $this->setOperationResult('result', false);
            $this->operation_result['messages'] = $this->i18n('error_actions_not_completed') . "\n" . implode("\n", $this->operation_result['messages']['failed']);
        } else {
            $this->processMessages('success');
            $this->processMessages('error');
            $registry['messages']->insertInSession($this->registry);

            $this->setOperationResult('result', true);
            $this->setOperationResult(
                'link',
                sprintf(
                    '%s?%s=%s&%s=%s',
                    $_SERVER['PHP_SELF'],
                    $registry->get('module_param'), 'reports',
                    'report_type', $report
                )
            );
        }

        echo(json_encode($this->operation_result));
        exit;
    }

    /*
     * Function to make a custom export
     */
    public function _customExport() {
        // get registry
        $registry = $this->registry;

        // get the report
        $report = $this->getReportType();
        if (empty($report)) {
            $this->redirect($this->module, '', array('report_type' => ''), '');
        }

        //load plugin i18n files
        $i18n_files = array();
        $i18n_files[] = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report['name'],
            '/i18n/',
            $registry['lang'],
            '/reports.ini');

        Reports::getReportSettings($registry, $report['name']);
        if (!defined('EXPORT_FILENAME')) {
            list($report_data) = Reports::getReports($registry, array('name' => $report['name'], 'sanitize' => 1));

            //replace any bad characters from the filename
            $filename = preg_replace('#[\(\)\&]#', '', trim($report_data->get('name')));

            //replace spaces with _ and add the XLS extension
            $filename = preg_replace('#\s+#', '_', $filename) . '_' . ($registry['request']->get('export_table') == 'first' ? '1' : '2') . '.xls';
        } else {
            //check if the the export filename is not custom
            $filename = EXPORT_FILENAME . '_' . ($registry['request']->get('export_table') == 'first' ? '1' : '2');
        }

        // get the report filters from session
        $session_param = 'reports_' . $report['name'] . '_report';
        $report_filters = $registry['session']->get($session_param);

        require_once PH_MODULES_DIR . "reports/plugins/" . $report['name'] . "/custom.report.query.php";
        $report_name_elements = explode('_', $report['name']);
        foreach ($report_name_elements as $key => $element) {
            $report_name_elements[$key] = ucfirst($element);
        }
        $report_class_name = implode ('_', $report_name_elements);
        $report_filters['prepare_for_export'] = 1;
        $results = $report_class_name::buildQuery($registry, $report_filters);

        if ($registry['request']->get('export_table') == 'first') {
            if (isset($results['second_table'])) {
                unset($results['second_table']);
            }
        } else {
            if (isset($results['first_table'])) {
                unset($results['first_table']);
            }
        }

        require_once(PH_MODULES_DIR . 'reports/viewers/reports.export.viewer.php');
        $export_path = PH_MODULES_DIR . 'reports/plugins/' . $report['name'] . '/custom_export_report.html';
        $custom_export_viewer = new Reports_Export_Viewer($registry);
        $custom_export_viewer->loadReportLangFiles($report['name']);
        $custom_export_viewer->data['reports_results'] = $results;
        $custom_export_viewer->setTemplate($export_path);
        $custom_export_viewer->setFrameset('frameset_blank.html');

        //fetch content
        $content = $custom_export_viewer->fetch($export_path);

        //send it to the user
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header("Content-type: application/x-msexcel; charset=utf-8");
        header('Content-Transfer-Encoding: binary');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        print $content;
    }

    /*
     * Function to take the vars of the model
     *
     * @param object $model
     *
     * return void
     */
    private function getCustomVars(Model &$model) {
        $sanitized = $model->isSanitized();
        if ($sanitized) {
            $model->unsanitize();
        }
        $model->getAllVars();
        if ($sanitized) {
            $model->sanitize();
        }

        $records = $model->get('plain_vars');
        $group_records = $model->get('group_vars');
        $config_records = $model->get('config_vars');
        $table_records = $model->get('table_vars');
        $gt2 = $model->get('grouping_table_2');

        $records = array_merge($records, $group_records, $config_records, $table_records);
        if (!empty($gt2)) {
            $records = array_merge($records, array(is_array($gt2) ? $gt2 : array()));
        }
        $model->unsetProperty('group_vars');
        $model->unsetProperty('config_vars');
        $model->unsetProperty('table_vars');

        $model->set('vars', $records, true);

        return;
    }

    /*
     * Function to complete any type of execution information in the result data
     *
     * @param string $type - the type of the information that has to be set
     * @param mixed $data - the information to set
     *
     * return void
     */
    private function setOperationResult($type, $data, $idx = null) {
        if ($type == 'error' || $type == 'success') {
            $key = ($type == 'error') ? 'failed' : 'success';
            if ($idx !== null) {
                $this->operation_result['messages'][$key][$idx] = $data;
            } else {
                $this->operation_result['messages'][$key][] = $data;
            }
        } elseif ($type == 'result') {
            $this->operation_result['result'] = $data;
        } elseif ($type == 'link') {
            $this->operation_result['link'] = $data;
        }

        return;
    }

    /*
     * Function to update the related documents
     *
     * @param string $rows_data - the data for the documents which have to be updated
     * @param string $substatus_report_name - the name of the status which have to be set to each document
     * return array $updated_contracts - array with contracts and the data in them which was updated
     */
    private function updateDocuments($rows_data, $substatus_report_name) : array
    {
        $updated_contracts = array();
        foreach ($rows_data as $row_dat) {
            $this->registry['db']->StartTrans();
            $affected_rows = array();
            foreach ($row_dat['policy_rows'] as $policy_row) {
                $sql = 'SELECT `article_delivery_code`, `article_trademark` FROM ' . DB_TABLE_GT2_DETAILS . ' WHERE `id`="' . $policy_row . '" AND (`article_delivery_code`<"' . $row_dat['date'] . '" OR `article_trademark`!="' . $row_dat['id'] . '")' . "\n";
                $data_to_update = $this->registry['db']->GetRow($sql);
                if ($data_to_update) {
                    $sql = 'UPDATE ' . DB_TABLE_GT2_DETAILS . ' SET `article_delivery_code`="' . $row_dat['date'] . '", `article_trademark`="' . $row_dat['id'] . '" WHERE `id`="' . $policy_row . '" AND `article_delivery_code`<="' . $row_dat['date'] . '"' . "\n";
                    $this->registry['db']->Execute($sql);
                    $affected_rows[$policy_row] = $data_to_update;
                }
            }

            // try to change the status of the Report
            $filters = array('where'      => array('d.id = ' . $row_dat['id']),
                             'model_lang' => $row_dat['id']);
            $document_report = Documents::searchOne($this->registry, $filters);
            $old_report = clone $document_report;

            $document_report->set('status', preg_replace('#(.*)_(\d+)#', '$1', DOCUMENT_IGNORE_STATUSES), true);
            $document_report->set('substatus', DOCUMENT_IGNORE_STATUSES, true);
            $document_report->set('substatus_name', $substatus_report_name, true);

            if ($document_report->setStatus() && !$this->registry['db']->HasFailedTrans()) {
                $document_report->set('substatus', preg_replace('#.*_(\d+)#', '$1', $document_report->get('substatus')), true);
                Documents_History::saveData($this->registry, array('action_type' => 'status', 'new_model' => $document_report, 'old_model' => $old_report));
                $this->setOperationResult('success', sprintf($this->i18n('report_successfully_confirmed_report'), $row_dat['custom_num'], $row_dat['customer_name']));
            } else {
                $this->setOperationResult('error', sprintf($this->i18n('error_reports_report_confirmed_failed'), $row_dat['custom_num'], $row_dat['customer_name']));
                $this->registry['db']->FailTrans();
            }

            if (!$this->registry['db']->HasFailedTrans()) {
                foreach ($affected_rows as $k_af => $af_row) {
                    $contr_id = $this->policy_rows_relations[$k_af];
                    if (!isset($updated_contracts[$contr_id])) {
                        $updated_contracts[$contr_id] = array();
                    }
                    $updated_contracts[$contr_id][$k_af] = $af_row;
                }
            }

            $this->registry['db']->CompleteTrans();
        }

        return $updated_contracts;
    }


    /**
     * Function to set the messages in the registry
     *
     * @param string $type_messages - the type of the messages to be processed
     * return void
     */
    private function processMessages($type_messages) : void {
        $function_name = 'setMessage';
        $msg_key = 'success';
        if ($type_messages == 'error') {
            $function_name = 'setError';
            $msg_key = 'failed';
        }

        foreach ($this->operation_result['messages'][$msg_key] as $msg) {
            $this->registry['messages']->$function_name($msg);
        }

        return;
    }

    /*
     * Function to process just a chunk of data of the contracts
     *
     * @param array $contracts_data - the contracts data which have to processed
     *
     * return void
     */
    private function processContractsHistory($contracts_data) : void {
        // update the contracts
        // get the updated contract models
        $filters = array(
            'where' => array('co.id IN (' . implode(',', array_keys($contracts_data)) . ')')
        );
        $contracts = Contracts::search($this->registry, $filters);

        foreach ($contracts as $new_contract) {
            $this->getCustomVars($new_contract);
            $old_contract = clone $new_contract;

            // change the data in the old contract
            $gt2 = $old_contract->get('grouping_table_2');
            $vars = $old_contract->get('vars');
            $gt2 = array();
            $gt2_idx = null;
            foreach ($vars as $idx => $var) {
                if ($var['type'] == 'gt2') {
                    $gt2 = $var;
                    $gt2_idx = $idx;
                    break;
                }
            }

            if (empty($gt2)) {
                continue;
            }

            // fill the new model gt2 with the old data
            // so we will not need to get the old model from the DB
            foreach ($contracts_data[$old_contract->get('id')] as $row_k => $row_data) {
                if (empty($gt2['values'][$row_k])) {
                    continue;
                }

                foreach ($row_data as $var_name => $var_old_dat) {
                    $gt2['values'][$row_k][$var_name] = $var_old_dat;
                }
            }
            $vars[$gt2_idx] = $gt2;
            $old_contract->set('grouping_table_2', $gt2, true);
            $old_contract->set('vars', $vars, true);

            Contracts_History::saveData($this->registry, array('model' => $new_contract, 'action_type' => 'edittopic', 'new_model' => $new_contract, 'old_model' => $old_contract));
        }
        return;
    }
}

?>
