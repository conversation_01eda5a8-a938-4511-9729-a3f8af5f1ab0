<?php
Class Payments_Period_Report Extends Reports {
    private static $currencies = array();
    private static $gt2_values = array();
    private static $total_before = 0;
    private static $total_during = 0;
    private static $result = array();
    private static $articles = array();

    public static function buildQuery(&$registry, $filters = array()) {
        if (empty($filters['from_date']) || empty($filters['to_date'])) {
            return array(0, 0);
        }
        $registry->set('prepareModels', false, true);

        $types_array = array(PH_FINANCE_TYPE_INVOICE, PH_FINANCE_TYPE_CREDIT_NOTICE, PH_FINANCE_TYPE_DEBIT_NOTICE,
                             PH_FINANCE_TYPE_ANNULMENT);
        $additional_types = array_filter(preg_split('#\s*,\s*#', REPORTS_ADDITIONAL_TYPES));


        // check which of the types have to be included and which to exclude
        // We EXCLUDE documents which issue invoices
        if (!empty($additional_types)) {
            $sql = 'SELECT `id` FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . ' WHERE `id` IN (' . implode(',', $additional_types) . ') AND `add_invoice`=0';
            $additional_types = $registry['db']->GetCol($sql);
        }
        $types_array = array_merge($types_array, $additional_types);

        // find the start date
        $dete_begin_period = new DateTime($filters['from_date']);
        $dete_begin_period->sub(new DateInterval('P' . MONTHS_BEFORE_START_DATE . 'M'));

        // get the ids of the incomes
        $sql = 'SELECT `id` FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' WHERE `type` IN (' . implode(',', $types_array) . ') AND `issue_date` <= \'' . $filters['to_date'] . '\' AND `issue_date` >= \'' . $dete_begin_period->format('Y-m-01') . '\' AND ' . "\n" .
                                                                                '`active` = 1 AND `annulled_by` = 0 AND `status` = \'finished\' AND `total`!=0' . "\n" .
            //   'ORDER BY FIELD(`type`,' . implode(',', $types_array) . ')' .
            "\n";
        $iids = $registry['db']->GetCol($sql);

        self::$result = array('before' => array('claims' => 0, 'invoices' => '-', 'paid' => 0),
                              'during' => array('claims' => '-', 'invoices' => 0, 'paid' => 0),
                              'total' => array('claims' => 0, 'invoices' => 0, 'paid' => 0));

        if ($iids) {
            $query = 'SELECT model_id, article_id, article_name, subtotal_with_vat_with_discount' . "\n" .
                     'FROM ' . DB_TABLE_GT2_DETAILS . " AS t1\n" .
                     'LEFT JOIN ' . DB_TABLE_GT2_DETAILS_I18N . " AS t2\n" .
                     '  ON t1.id = t2.parent_id AND t2.lang=\'' . $registry['lang'] . '\'' .
                     'WHERE model=\'Finance_Incomes_Reason\' AND model_id IN(' . implode(', ', $iids) . ')';
            $values = $registry['db']->GetAll($query);
        } else {
            $values = array();
        }

        foreach ($values as $val) {
            self::$gt2_values[$val['model_id']][] = $val;
        }
        $prec = $registry['config']->getSectionParams('precision');

        // start updating contracts by separating it to chunks
        $chunk_size = 10;
        $chunks = array_chunk($iids, $chunk_size, true);

        foreach ($chunks as $chunk) {
            self::processIncomes($registry, $chunk, $filters);
        }

        self::$result['before']['rest'] = self::$result['before']['claims'] - self::$result['before']['paid'];
        if (self::$result['before']['claims'] == 0) {
            self::$result['before']['collected'] = 0;
            self::$result['before']['not_collected'] = 0;
        } else {
            self::$result['before']['collected'] = @round(self::$result['before']['paid'] / (self::$result['before']['claims'] + self::$result['before']['invoices']), $prec['gt2_total_with_vat'] + 2) * 100;
            self::$result['before']['not_collected'] = 100 - self::$result['before']['collected'];
        }
        self::$result['during']['rest'] =  self::$result['during']['invoices'] - self::$result['during']['paid'];
        if (self::$result['during']['invoices'] == 0) {
            self::$result['during']['collected'] = 0;
            self::$result['during']['not_collected'] = 0;
        } else {
            self::$result['during']['collected'] = @round(self::$result['during']['paid'] / (self::$result['during']['claims'] + self::$result['during']['invoices']), $prec['gt2_total_with_vat'] + 2) * 100;
            self::$result['during']['not_collected'] = 100 - self::$result['during']['collected'];
        }

        self::$result['total']['claims'] = self::$result['before']['claims'] + floatval(self::$result['during']['claims']);
        self::$result['total']['invoices'] = floatval(self::$result['before']['invoices']) + self::$result['during']['invoices'];
        self::$result['total']['paid'] = self::$result['before']['paid'] + self::$result['during']['paid'];
        self::$result['total']['rest'] = self::$result['before']['rest'] + self::$result['during']['rest'];

        foreach (self::$articles as $id => $article) {
            self::$articles[$id]['paid_old'] = (self::$total_before == 0) ? 0 : self::$articles[$id]['total_before'] * self::$result['before']['paid'] / self::$total_before;
            self::$articles[$id]['paid_new'] = (self::$total_during == 0) ? 0 : self::$articles[$id]['total_during'] * self::$result['during']['paid'] / self::$total_during;
            self::$articles[$id]['rest'] = self::$articles[$id]['total_before'] + self::$articles[$id]['total_during'] - self::$articles[$id]['paid_old'] - self::$articles[$id]['paid_new'];
        }

        $results = array('result' => self::$result, 'articles' => self::$articles, 'past_period_start' => $dete_begin_period->format('Y-m-01'));

         if (!empty($filters['paginate'])) {
            $results = array($results, 0);
        }

        return $results;
    }

    /*
     * Function to process the incomes by batches
     */
    public static function processIncomes(&$registry, $ids, $filters_rep) {
        $filts = array('where' => array('fir.id IN (' . implode(',', $ids) . ')'));

        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
        $incomes = Finance_Incomes_Reasons::search($registry, $filts);

        foreach ($incomes as $income) {
            $rate = 1;
            if ($income->get('currency') != $filters_rep['currency']) {
                if (empty(self::$currencies[$income->get('currency') . $filters_rep['currency']])) {
                    $rate = Finance_Currencies::getRate($registry,$income->get('currency'), $filters_rep['currency']);
                    self::$currencies[$income->get('currency') . $filters_rep['currency']] = $rate;
                } else {
                    $rate = self::$currencies[$income->get('currency') . $filters_rep['currency']];
                }
            }

            $date = $income->get('issue_date');
            $paid_before = $paid_after = 0;
            if ($date < $filters_rep['from_date']) {
                $params = array('added' => 'fr.added < \'' . $filters_rep['from_date'] . ' 00:00:00\'');
                $paid_before = $income->getInvoicePaid($params);
                self::$result['before']['claims'] += ($income->get('total_with_vat') - $paid_before) * $rate;
                $params = array('added' => 'fr.added >= \'' . $filters_rep['from_date'] . ' 00:00:00\'' . "\n" .
                                           'AND fr.added <= \'' . $filters_rep['to_date'] . ' 23:59:59\'');
                $paid_after = $income->getInvoicePaid($params);
                self::$result['before']['paid'] +=$paid_after * $rate;
            } else {
                $params = array('added' => 'fr.added <= \'' . $filters_rep['to_date'] . ' 23:59:59\'');
                $paid_after = $income->getInvoicePaid($params);

                self::$result['during']['invoices'] += $income->get('total_with_vat') * $rate;
                self::$result['during']['paid'] += $paid_after * $rate;
            }
            if (!empty($filters_rep['by_article'])) {
                foreach (self::$gt2_values[$income->get('id')] as $values) {
                    if (empty($values['article_id'])) {
                        continue;
                    }
                    if (!isset(self::$articles[$values['article_id']])) {
                        self::$articles[$values['article_id']] = array('name' => $values['article_name'],
                                                                 'total_before' => 0,
                                                                 'total_during' => 0,
                                                                 'invoiced' => 0);
                    }
                    if ($date < $filters_rep['from_date']) {
                        self::$articles[$values['article_id']]['total_before'] += ($values['subtotal_with_vat_with_discount'] - $values['subtotal_with_vat_with_discount'] / $income->get('total_with_vat') * $paid_before) * $rate;
                        self::$total_before += ($values['subtotal_with_vat_with_discount'] - $values['subtotal_with_vat_with_discount'] / $income->get('total_with_vat') * $paid_before) * $rate;
                    } else {
                        self::$articles[$values['article_id']]['total_during'] += $values['subtotal_with_vat_with_discount'] * $rate;
                        self::$total_during += $values['subtotal_with_vat_with_discount'] * $rate;
                    }

                }
            }
        }
    }
}
?>
