<tr>
  <td class="labelbox">
    <label for="{$filter_settings.custom_id|default:$filter_settings.name}" style="white-space: nowrap;">{help label_content=$filter_settings.label text_content=$filter_settings.help}</label>
  </td>
  <td{if $filter_settings.required} class="required">{#required#}{else} class="unrequired">&nbsp;{/if}</td>
  <td nowrap="nowrap" style="vertical-align: top;">
    <div style="float: left; padding-right: 10px;">
      {include file="input_date.html"
        standalone=true
        width=$filter_settings.width
        name=$filter_settings.name
        custom_id=$filter_settings.custom_id
        label=$filter_settings.first_filter_label
        help=$filter_settings.first_filter_label
        value=$filter_settings.value
        show_calendar_icon=true}
    </div>
    <div style="float: left; padding-right: 8px; font-size: 11px; color: #666666;">{$filter_settings.additional_filter.label}</div>
    <div style="float: left;">
      {include file="input_date.html"
        standalone=true
        width=$filter_settings.additional_filter.width
        name=$filter_settings.additional_filter.name
        custom_id=$filter_settings.additional_filter.custom_id
        label=$filter_settings.additional_filter.label
        help=$filter_settings.additional_filter.help
        value=$filter_settings.additional_filter.value
        show_calendar_icon=true}
    </div>
  </td>
</tr>