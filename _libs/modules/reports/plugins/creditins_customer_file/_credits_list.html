<h1 style="margin-top: 20px;">{#reports_credits#|escape}</h1>
<table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
  <tr class="reports_title_row">
    <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 130px;">{#reports_credits_credit_num#|escape}</div></td>
    <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 100px;">{#reports_credits_deadline#|escape}</div></td>
    <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 90px;">{#reports_credits_status#|escape}</div></td>
    <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 90px;">{#reports_credits_credit_amount#|escape}</div></td>
    <td style="text-align: center; vertical-align: middle;"><div style="width: 90px;">{#reports_credits_owed_until_now#|escape}</div></td>
  </tr>
  {foreach from=$credits_list item=cstm_credit}
    <tr class="{cycle name=credits_rower values='t_odd1 t_odd2,t_even1 t_even2'}">
      <td class="t_border" style="width: 90px;">
        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$cstm_credit.id}" target="_blank">{$cstm_credit.contract_num|escape|default:"&nbsp;"}</a>{if $cstm_credit.date|escape|default:"&nbsp;"} / {$cstm_credit.date|date_format:#date_short#|escape}{/if}
      </td>
      <td class="t_border" style="width: 100px;">
        {$cstm_credit.deadline|escape|default:"&nbsp;"}
      </td>
      <td class="t_border" style="width: 90px;">
        {$cstm_credit.status|escape|default:"&nbsp;"}
      </td>
      <td class="t_border hright" style="width: 90px;">
        {$cstm_credit.credit_value|string_format:"%.2f"|escape|default:"0.00"}
      </td>
      <td style="width: 90px;" class="hright">
        {if $cstm_credit.status_id eq $smarty.const.DOCUMENT_SUBSTATUS_ACTIVE}
          {$cstm_credit.owed_until_now|string_format:"%.2f"|escape|default:"0.00"}
        {else}
          -
        {/if}
      </td>
    </tr>
  {foreachelse}
    <tr class="{cycle name=credits_rower values='t_odd1 t_odd2,t_even1 t_even2'}">
      <td class="error" colspan="5">{#no_items_found#|escape}</td>
    </tr>
  {/foreach}
  <tr>
    <td class="t_footer" colspan="5"></td>
  </tr>
</table>