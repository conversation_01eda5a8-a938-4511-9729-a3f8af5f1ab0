reports_general_report_option = Generală
reports_financial_resources_option = Mijloace bănești
reports_transport_option = Autotransport
reports_materials_option = Materiale
reports_werehouse_option = Depozit/ZBUT/IST
reports_general_report = Raport general:
reports_werehouse = Depozit/ZBUT/IST
reports_transport = Autotransport:
reports_financial = Mijloace bănești:
reports_materials = Materiale:
reports_request_status = Status cerere
reports_from_date = De la dată
reports_to_date = Până la dată
reports_project = Obiectiv / Proiect
reports_pps_machine = PPS / Mașină
reports_requested_by = Solicitate de unitatea
reports_company = Firmă
reports_nomenclature = Nomenclatură
reports_expense_description = Descriere cheltuială
reports_deliverer = Va primi (Furnizor)
reports_employee = Va primi (Funcționar)
reports_werehouse_type_expense = Tip cheltuială
reports_transport_type_expense = Tip cheltuială
reports_type_pps = Tip PPS
reports_financial_type_expense = Tip cheltuială
reports_materials_type_expense = Tip cheltuială
reports_spec_materials = Materiale specializate
reports_undefined_type_expense = [cheltuieli nespecificate]
reports_all_statuses = toate cererile
reports_approved = aprobată
reports_approved_and_executed = aprobată și executată
reports_approved_and_partialy_executed = aprobată și parțial executată
reports_disapproved = neaprobată
reports_company_1 = Impuls Co
reports_company_2 = Impuls Project
reports_exp_invoice_prefix = F
reports_exp_proforma_prefix = FP
reports_werehouse_optlabel_werehouse = depozit
reports_werehouse_optlabel_zbut = ZBUT
reports_werehouse_optlabel_ist = IST
reports_request_num = Cerere №
reports_request_date = Dată
reports_request_type = Tip cerere
reports_receive_branch = Obiectiv de primit / TS
reports_expense_type = Tip cheltuială
reports_expense_description_lbl = Descriere
reports_quantity = Cant-te
reports_price = Preț
reports_value = Valoare
reports_total_value = Valoare totală
reports_total_value_with_vat = Valoare cu TVA
reports_total_value_invoice = Valoare F/FP
reports_difference = Diferență
reports_invoice_num_date = F/FP № (De la dată)
reports_note = Note
reports_received_by = Primit
reports_requested_by_unit = Solicitate de unitate
reports_chose_container = Casierie/Cont bancar
reports_create_expense_reason = Emite proformă/factură
reports_add_expense_reason = Creare document cheltuieli
reports_add_expense_reason_instructions = Selectați tipul documentului de cheltuieli care va fi creat:
message_reports_no_rows_to_issue_expense_reason = Nu sunt selectate rânduri pentru care să fie eliberat documentul de cheltuieli!
message_reports_no_deliverer_data = Nu există date despre furnizorii din rândurile selectate!
message_reports_select_only_one_deliverer = Vă rugăm să selectați articole de la un singur furnizor!
message_reports_one_or_more_rows_has_no_deliverer = Unul sau mai multe din rândurile nu au furnizor menționat!
reports_no_number = [nu există număr]
error_reports_complete_required_fields = Vă rugăm să completați filtrul pentru status cerere!
error_reports_complete_type_table_filter = Completați filtrul pentru tip raport (General / Mijloace bănești / Autotransport / Materiale / Depozit / ZBUT)

