<?php
    Class Optix_Products Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {
            $final_results = array();

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            if (!empty($filters['nomenclature'])) {
                // define the tyoe of the entered product
                $sql = 'SELECT `type`, `name` FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                       '  ON (ni18n.parent_id=n.id AND ni18n.lang="' . $registry['lang'] . '")' . "\n" .
                       'WHERE `id`="' . $filters['nomenclature'] . '"';
                $searched_nom = $registry['db']->GetRow($sql);

                if ($searched_nom['type'] == NOMENCLATURES_DEMO_PRODUCTS) {
                    // get the additional vars
                    $add_vars = array(NOMENCLATURES_DEMO_PRODUCT_SET, NOMENCLATURES_DEMO_FILES, NOMENCLATURES_DEMO_AVB_PERIOD);

                    //sql to take the ids of the needed additional vars
                    $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Nomenclature" AND `model_type`="' . $searched_nom['type'] . '" AND `name` IN ("' . implode('","', $add_vars) . '")';
                    $add_vars = $registry['db']->GetAssoc($sql);

                    // sql for the main data
                    $sql = 'SELECT n.id, ni18n.name, GROUP_CONCAT(ni18n_ps.name SEPARATOR "|") as product_set, GROUP_CONCAT(n_cstm_demo_file.value SEPARATOR "|") as files, ' . "\n" .
                           '       n_cstm_avb_period.value as availability_period ' . "\n" .
                           'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                           '  ON (ni18n.parent_id=n.id AND ni18n.lang="' . $model_lang . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_product_set' . "\n" .
                           '  ON (n_cstm_product_set.model_id=n.id AND n_cstm_product_set.var_id="' . (isset($add_vars[NOMENCLATURES_DEMO_PRODUCT_SET]) ? $add_vars[NOMENCLATURES_DEMO_PRODUCT_SET]: '') . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n_ps' . "\n" .
                           '  ON (ni18n_ps.parent_id=n_cstm_product_set.value AND ni18n_ps.lang="' . $model_lang . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_demo_file' . "\n" .
                           '  ON (n_cstm_demo_file.model_id=n.id AND n_cstm_demo_file.var_id="' . (isset($add_vars[NOMENCLATURES_DEMO_FILES]) ? $add_vars[NOMENCLATURES_DEMO_FILES]: '') . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_avb_period' . "\n" .
                           '  ON (n_cstm_avb_period.model_id=n.id AND n_cstm_avb_period.var_id="' . (isset($add_vars[NOMENCLATURES_DEMO_AVB_PERIOD]) ? $add_vars[NOMENCLATURES_DEMO_AVB_PERIOD]: '') . '")' . "\n" .
                           'WHERE n.id="' . $filters['nomenclature'] . '"' . "\n" .
                           'GROUP BY n.id' . "\n";
                    $demo_product_info = $registry['db']->GetRow($sql);

                    if (!empty($demo_product_info)) {
                        $demo_product_info['requests'] = array();

                        // get the requests data
                        // get the additional vars for requests
                        $add_vars_req = array(DOCUMENT_REQ_PRODUCT, DOCUMENT_REQ_USED_FROM, DOCUMENT_REQ_USED_TO, DOCUMENT_REQ_LOCATION, DOCUMENT_REQ_NOTES);

                        //sql to take the ids of the needed additional vars
                        $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `model_type`="' . DOCUMENT_PRODUCTS_REQUEST . '" AND `name` IN ("' . implode('","', $add_vars_req) . '")';
                        $add_vars_req = $registry['db']->GetAssoc($sql);

                        // sql for the request data
                        $sql = 'SELECT d.id as idx, d.id, d_cstm_used_from.value as request_from, d_cstm_used_to.value as request_to, d_cstm_location.value as location, d_cstm_notes.value as notes, CONCAT(ci18n.name, " ", ci18n.lastname) as in_charge' . "\n" .
                               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_product' . "\n" .
                               '  ON (d_cstm_product.model_id=d.id AND d_cstm_product.var_id="' . (isset($add_vars_req[DOCUMENT_REQ_PRODUCT]) ? $add_vars_req[DOCUMENT_REQ_PRODUCT]: '') . '" AND d_cstm_product.value="' . $filters['nomenclature'] . '")' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                               '  ON (ci18n.parent_id=d.customer AND ci18n.lang="' . $model_lang . '")' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_used_from' . "\n" .
                               '  ON (d_cstm_used_from.model_id=d.id AND d_cstm_used_from.var_id="' . (isset($add_vars_req[DOCUMENT_REQ_USED_FROM]) ? $add_vars_req[DOCUMENT_REQ_USED_FROM]: '') . '")' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_used_to' . "\n" .
                               '  ON (d_cstm_used_to.model_id=d.id AND d_cstm_used_to.var_id="' . (isset($add_vars_req[DOCUMENT_REQ_USED_TO]) ? $add_vars_req[DOCUMENT_REQ_USED_TO]: '') . '")' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_location' . "\n" .
                               '  ON (d_cstm_location.model_id=d.id AND d_cstm_location.var_id="' . (isset($add_vars_req[DOCUMENT_REQ_LOCATION]) ? $add_vars_req[DOCUMENT_REQ_LOCATION]: '') . '")' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_notes' . "\n" .
                               '  ON (d_cstm_notes.model_id=d.id AND d_cstm_notes.var_id="' . (isset($add_vars_req[DOCUMENT_REQ_NOTES]) ? $add_vars_req[DOCUMENT_REQ_NOTES]: '') . '")' . "\n" .
                               'WHERE d.type="' . DOCUMENT_PRODUCTS_REQUEST . '" AND d.active=1 AND d.deleted_by=0' . "\n" .
                               'ORDER BY d_cstm_used_from.value ASC' . "\n";
                        $requests_info = $registry['db']->GetAssoc($sql);
                        $demo_product_info['requests'] = $requests_info;

                        $files_list = explode('|', $demo_product_info['files']);
                        if (!empty($requests_info)) {
                            // get the files for the selected documents
                            $sql_files = 'SELECT `id` FROM ' . DB_TABLE_FILES . ' WHERE `model_id` IN ("' . implode('","', array_keys($requests_info)) . '") AND `model`="Document" AND `deleted_by`=0';
                            $files_list = array_merge($files_list, $registry['db']->GetCol($sql_files));
                        }
                        $files_list = array_values(array_filter(array_unique($files_list)));

                        $nom_files = array();
                        if (!empty($files_list)) {
                            require_once PH_MODULES_DIR . 'files/models/files.factory.php';
                            $filters_files = array(
                                'where'         => array('f.id IN ("' . implode('","', $files_list) . '")'),
                                'model_lang'    => $model_lang,
                                'sanitize'      => true
                            );
                           $files_models = Files::search($registry, $filters_files);
                           foreach ($files_models as $file_model) {
                               if ($file_model->get('model') == 'Document') {
                                   if (isset($demo_product_info['requests'][$file_model->get('model_id')])) {
                                       if (!isset($demo_product_info['requests'][$file_model->get('model_id')]['files'])) {
                                           $demo_product_info['requests'][$file_model->get('model_id')]['files'] = array();
                                       }
                                       $demo_product_info['requests'][$file_model->get('model_id')]['files'][] = $file_model;
                                   }
                               } elseif ($file_model->get('model') == 'Nomenclature') {
                                   $nom_files[] = $file_model;
                               }
                           }
                        }

                        $demo_product_info['rowspan'] = (count($demo_product_info['requests']) ? count($demo_product_info['requests']) : 1);
                        $demo_product_info['files'] = $nom_files;
                        $products_sets = explode('|', $demo_product_info['product_set']);
                        $demo_product_info['product_set'] = array_filter($products_sets);
                    }
                    $final_results['additional_options']['demo_product_info'] = $demo_product_info;
                    $final_results['additional_options']['demo_product_info']['rowspan'] = (!empty($demo_product_info['requests']) ? count($demo_product_info['requests']) : 1);
                    $final_results['additional_options']['report_view_type'] = 'demo';
                    $final_results['additional_options']['nom_name'] = $searched_nom['name'];
                } else {
                    $final_results['additional_options']['report_datatype'] = 'product';

                    // get the additional vars
                    $add_vars = array(
                        NOMENCLATURES_PRODUCTS_COST_PRICE_DATE, NOMENCLATURES_PRODUCTS_COST_PRICE_BGN, NOMENCLATURES_PRODUCTS_COST_PRICE_EUR, NOMENCLATURES_PRODUCTS_COST_PRICE_FILE, NOMENCLATURES_PRODUCTS_COMPONENT, NOMENCLATURES_PRODUCTS_REGULATIONS, NOMENCLATURES_PRODUCTS_PATENTS, NOMENCLATURES_PRODUCTS_TECHNOLOGIES, NOMENCLATURES_PRODUCTS_TECH_DOC_DATE, NOMENCLATURES_PRODUCTS_TECH_DOC_FILE, NOMENCLATURES_PRODUCTS_TECH_DOC_DESCRIPTION,
                        NOMENCLATURES_PRODUCTS_COMPETITIVE_NAME, NOMENCLATURES_PRODUCTS_COMPETITIVE_MANUFACTURER, NOMENCLATURES_PRODUCTS_COMPETITIVE_FILE, NOMENCLATURES_PRODUCTS_COMPETITIVE_NOTES, NOMENCLATURES_PRODUCTS_COST_PRICE_REMARKS
                    );

                    //sql to take the ids of the needed additional vars
                    $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Nomenclature" AND `model_type`="' . $searched_nom['type'] . '" AND `name` IN ("' . implode('","', $add_vars) . '")';
                    $add_vars = $registry['db']->GetAssoc($sql);

                    // sql for the main data
                    $sql = 'SELECT n.id, n_cstm_cost_price_date.value as cost_price_date, n_cstm_cost_price_bgn.value as cost_price_bgn, ' . "\n" .
                           '       n_cstm_cost_price_eur.value as cost_price_eur, n_cstm_cost_price_file.value as cost_price_file, n_cstm_regulations.value as regulations,' . "\n" .
                           '       n_cstm_patents.value as patents, n_cstm_tehnologies.value as tehnologies, n_cstm_remark.value as remarks ' . "\n" .
                           'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_cost_price_date' . "\n" .
                           '  ON (n_cstm_cost_price_date.model_id=n.id AND n_cstm_cost_price_date.var_id="' . (isset($add_vars[NOMENCLATURES_PRODUCTS_COST_PRICE_DATE]) ? $add_vars[NOMENCLATURES_PRODUCTS_COST_PRICE_DATE]: '') . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_cost_price_bgn' . "\n" .
                           '  ON (n_cstm_cost_price_bgn.model_id=n.id AND n_cstm_cost_price_bgn.var_id="' . (isset($add_vars[NOMENCLATURES_PRODUCTS_COST_PRICE_BGN]) ? $add_vars[NOMENCLATURES_PRODUCTS_COST_PRICE_BGN]: '') . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_cost_price_eur' . "\n" .
                           '  ON (n_cstm_cost_price_eur.model_id=n.id AND n_cstm_cost_price_eur.var_id="' . (isset($add_vars[NOMENCLATURES_PRODUCTS_COST_PRICE_EUR]) ? $add_vars[NOMENCLATURES_PRODUCTS_COST_PRICE_EUR]: '') . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_cost_price_file' . "\n" .
                           '  ON (n_cstm_cost_price_file.model_id=n.id AND n_cstm_cost_price_file.var_id="' . (isset($add_vars[NOMENCLATURES_PRODUCTS_COST_PRICE_FILE]) ? $add_vars[NOMENCLATURES_PRODUCTS_COST_PRICE_FILE]: '') . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_regulations' . "\n" .
                           '  ON (n_cstm_regulations.model_id=n.id AND n_cstm_regulations.var_id="' . (isset($add_vars[NOMENCLATURES_PRODUCTS_REGULATIONS]) ? $add_vars[NOMENCLATURES_PRODUCTS_REGULATIONS]: '') . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_patents' . "\n" .
                           '  ON (n_cstm_patents.model_id=n.id AND n_cstm_patents.var_id="' . (isset($add_vars[NOMENCLATURES_PRODUCTS_PATENTS]) ? $add_vars[NOMENCLATURES_PRODUCTS_PATENTS]: '') . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_tehnologies' . "\n" .
                           '  ON (n_cstm_tehnologies.model_id=n.id AND n_cstm_tehnologies.var_id="' . (isset($add_vars[NOMENCLATURES_PRODUCTS_TECHNOLOGIES]) ? $add_vars[NOMENCLATURES_PRODUCTS_TECHNOLOGIES]: '') . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_remark' . "\n" .
                           '  ON (n_cstm_remark.model_id=n.id AND n_cstm_remark.var_id="' . (isset($add_vars[NOMENCLATURES_PRODUCTS_COST_PRICE_REMARKS]) ? $add_vars[NOMENCLATURES_PRODUCTS_COST_PRICE_REMARKS]: '') . '")' . "\n" .
                           'WHERE n.id="' . $filters['nomenclature'] . '"' . "\n";
                    $product_info = $registry['db']->GetRow($sql);

                    // get the components
                    $sql = 'SELECT n_cstm_product.value as id, product_name.name as name' . "\n" .
                           'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_product' . "\n" .
                           '  ON (n_cstm_product.model_id=n.id AND n_cstm_product.var_id="' . (isset($add_vars[NOMENCLATURES_PRODUCTS_COMPONENT]) ? $add_vars[NOMENCLATURES_PRODUCTS_COMPONENT]: '') . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS product_name' . "\n" .
                           '  ON (product_name.parent_id=n_cstm_product.value AND product_name.lang="' . $model_lang . '")' . "\n" .
                           'WHERE n.id="' . $filters['nomenclature'] . '" AND n_cstm_product.value != ""' . "\n";
                    $product_info['components_table'] = $registry['db']->GetAll($sql);

                    // get the technical documentation
                    $sql = 'SELECT n_cstm_file_date.value as file_date, n_cstm_file.value as file, n_cstm_description.value as description' . "\n" .
                           'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_file_date' . "\n" .
                           '  ON (n_cstm_file_date.model_id=n.id AND n_cstm_file_date.var_id="' . (isset($add_vars[NOMENCLATURES_PRODUCTS_TECH_DOC_DATE]) ? $add_vars[NOMENCLATURES_PRODUCTS_TECH_DOC_DATE]: '') . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_file' . "\n" .
                           '  ON (n_cstm_file.model_id=n.id AND n_cstm_file.var_id="' . (isset($add_vars[NOMENCLATURES_PRODUCTS_TECH_DOC_FILE]) ? $add_vars[NOMENCLATURES_PRODUCTS_TECH_DOC_FILE]: '') . '") AND n_cstm_file.num=n_cstm_file_date.num' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_description' . "\n" .
                           '  ON (n_cstm_description.model_id=n.id AND n_cstm_description.var_id="' . (isset($add_vars[NOMENCLATURES_PRODUCTS_TECH_DOC_DESCRIPTION]) ? $add_vars[NOMENCLATURES_PRODUCTS_TECH_DOC_DESCRIPTION]: '') . '") AND n_cstm_description.num=n_cstm_file_date.num' . "\n" .
                           'WHERE n.id="' . $filters['nomenclature'] . '"' . "\n";
                    $technical_details = $registry['db']->GetAll($sql);

                    list($competitive_products, $competitive_products_pagination) = self::getProductCompetitiveProducts($registry, array('nomenclature_id' => $filters['nomenclature'], 'vars' => $add_vars));
                    list($tenders_data, $tenders_pagination) = self::getProductTenders($registry, array('nomenclature_id' => $filters['nomenclature']));
                    list($offers_data, $offers_pagination) = self::getProductOffers($registry, array('nomenclature_id' => $filters['nomenclature']));
                    list($contracts_data, $contracts_pagination) = self::getProductContracts($registry, array('nomenclature_id' => $filters['nomenclature']));

                    $included_files = array();
                    if (!empty($product_info['cost_price_file'])) {
                        $included_files[] = $product_info['cost_price_file'];
                    }
                    foreach ($technical_details as $td) {
                        $included_files[] = $td['file'];
                    }

                    $files = array();
                    $included_files = array_filter($included_files);
                    if (!empty($included_files)) {
                        // get the files
                        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
                        $filters_files = array(
                            'where'         => array('f.id IN ("' . implode('","', $included_files) . '")'),
                            'model_lang'    => $model_lang,
                            'sanitize'      => true
                        );
                        $product_info_files = Files::search($registry, $filters_files);

                        foreach ($product_info_files as $pif) {
                            $files[$pif->get('id')] = $pif;
                        }

                        // complete the files
                        if (!empty($product_info['cost_price_file']) && isset($files[$product_info['cost_price_file']])) {
                            $product_info['cost_price_file'] = $files[$product_info['cost_price_file']];
                        }
                        foreach ($technical_details as $td_idx => $td) {
                            if (!empty($td['file']) && isset($files[$td['file']])) {
                                $technical_details[$td_idx]['file'] = $files[$td['file']];
                            }
                        }
                    }

                    $final_results['additional_options']['product_info'] = $product_info;
                    $final_results['additional_options']['technical_details'] = $technical_details;
                    $final_results['additional_options']['competitive_products'] = $competitive_products;
                    $final_results['additional_options']['competitive_products_pagination'] = $competitive_products_pagination;
                    $final_results['additional_options']['tenders_data'] = $tenders_data;
                    $final_results['additional_options']['tenders_pagination'] = $tenders_pagination;
                    $final_results['additional_options']['offers_data'] = $offers_data;
                    $final_results['additional_options']['offers_pagination'] = $offers_pagination;
                    $final_results['additional_options']['contracts_data'] = $contracts_data;
                    $final_results['additional_options']['contracts_pagination'] = $contracts_pagination;
                    $final_results['additional_options']['product_id'] = $filters['nomenclature'];
                    $final_results['additional_options']['report_view_type'] = 'product';
                    $final_results['additional_options']['hide_cost_prices_panel'] = in_array($registry['currentUser']->get('id'), array_filter(preg_split('#\s*,\s*#', RESTRICTED_USERS_COST_PRICES_PANEL)));
                    $final_results['additional_options']['hide_tenders_panel'] = in_array($registry['currentUser']->get('id'), array_filter(preg_split('#\s*,\s*#', RESTRICTED_USERS_TENDERS_PANEL)));
                    $final_results['additional_options']['hide_offers_panel'] = in_array($registry['currentUser']->get('id'), array_filter(preg_split('#\s*,\s*#', RESTRICTED_USERS_OFFERS_PANEL)));
                    $final_results['additional_options']['hide_contracts_panel'] = in_array($registry['currentUser']->get('id'), array_filter(preg_split('#\s*,\s*#', RESTRICTED_USERS_CONTRACTS_PANEL)));
                    $final_results['additional_options']['nom_name'] = $searched_nom['name'];
                }
            } else {
                $registry['messages']->setError($registry['translater']->translate('error_complete_required_filters'));
            }

            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }

        /*
         * Get data for the tenders for the selected nomenclature
         */
        public static function getProductTenders(&$registry, $params = array(), $page = 1) {
            $tender_products = array();
            $pagination = array();

            // get the additional vars
            $add_vars = array(
                DOCUMENTS_TENDERS_PRODUCT, DOCUMENTS_TENDERS_REGION, DOCUMENTS_TENDERS_COUNTRY, DOCUMENTS_TENDERS_QUANTITY, DOCUMENTS_TENDERS_BUDGET_BGN, DOCUMENTS_TENDERS_PARTICIPATION_FORM,
                DOCUMENTS_TENDERS_BUDGET_EUR, DOCUMENTS_TENDERS_BUDGET_USD, DOCUMENTS_TENDERS_PERFORMER, DOCUMENTS_TENDERS_DOCUMENT_FILES, DOCUMENTS_TENDERS_CONTRACT, DOCUMENTS_TENDERS_NOTES,
                DOCUMENTS_TENDERS_COMPETITOR, DOCUMENTS_TENDERS_COMPETITOR_PRICE, DOCUMENTS_TENDERS_MAIN_CATEGORY, DOCUMENTS_TENDERS_END_USER, DOCUMENTS_TENDERS_COMPONENTS
            );

            //sql to take the ids of the needed additional vars
            $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `model_type`="' . DOCUMENTS_TENDERS_TYPE . '" AND `name` IN ("' . implode('","', $add_vars) . '")';
            $add_vars = $registry['db']->GetAssoc($sql);

            // get the tenders
            $sql = array();
            $sql['select'] = 'SELECT SQL_CALC_FOUND_ROWS'. "\n" .
                             '       d.id as idx, d.id, d.customer, CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name, di18n.name as subject, d.date as tender_date, ' . "\n" .
                             '       region_name.name as region, country_name.name as country, d_cstm_quantity.value as quantity, d_cstm_budget.value as budget_bgn, ' . "\n" .
                             '       d_cstm_budget_usd.value as budget_usd, d_cstm_budget_eur.value as budget_eur, CONCAT(end_user.name, " ", end_user.lastname) as end_user,' . "\n" .
                             '       fo_pf.label as participation_form, d_cstm_file.value as document_file, d.status, ds.name as substatus, ' . "\n" .
                             '       d_cstm_contract.value as contract_id, d_cstm_performer.value as performer, CONCAT(ci18n_perf.name, " ", ci18n_perf.lastname) as performer_name,' . "\n" .
                             '       con.num as contract_num, d_cstm_notes.value as description, "" as competitor_products, 1 as rowspan, "" as systems' . "\n";

            $sql['from'] =   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                             'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_prod' . "\n" .
                             '  ON (d_cstm_prod.model_id=d.id AND d_cstm_prod.var_id="' . (isset($add_vars[DOCUMENTS_TENDERS_PRODUCT]) ? $add_vars[DOCUMENTS_TENDERS_PRODUCT]: '') . '"' . (!empty($params['nomenclature_id']) ? ' AND d_cstm_prod.value="' . $params['nomenclature_id'] . '"' : '') . ')' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_STATUSES . ' AS ds' . "\n" .
                             '  ON (d.substatus=ds.id AND ds.lang="' . $registry['lang'] . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                             '  ON (ci18n.parent_id=d.customer AND ci18n.lang="' . $registry['lang'] . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
                             '  ON (di18n.parent_id=d.id AND di18n.lang="' . $registry['lang'] . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_region' . "\n" .
                             '  ON (d_cstm_region.model_id=d.id AND d_cstm_region.var_id="' . (isset($add_vars[DOCUMENTS_TENDERS_REGION]) ? $add_vars[DOCUMENTS_TENDERS_REGION]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS region_name' . "\n" .
                             '  ON (region_name.parent_id=d_cstm_region.value AND region_name.lang="' . $registry['lang'] . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_country' . "\n" .
                             '  ON (d_cstm_country.model_id=d.id AND d_cstm_country.var_id="' . (isset($add_vars[DOCUMENTS_TENDERS_COUNTRY]) ? $add_vars[DOCUMENTS_TENDERS_COUNTRY]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS country_name' . "\n" .
                             '  ON (d_cstm_country.value=country_name.parent_id AND country_name.lang="' . $registry['lang'] . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_quantity' . "\n" .
                             '  ON (d_cstm_quantity.model_id=d.id AND d_cstm_quantity.var_id="' . (isset($add_vars[DOCUMENTS_TENDERS_QUANTITY]) ? $add_vars[DOCUMENTS_TENDERS_QUANTITY]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_budget' . "\n" .
                             '  ON (d_cstm_budget.model_id=d.id AND d_cstm_budget.var_id="' . (isset($add_vars[DOCUMENTS_TENDERS_BUDGET_BGN]) ? $add_vars[DOCUMENTS_TENDERS_BUDGET_BGN]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_budget_eur' . "\n" .
                             '  ON (d_cstm_budget_eur.model_id=d.id AND d_cstm_budget_eur.var_id="' . (isset($add_vars[DOCUMENTS_TENDERS_BUDGET_EUR]) ? $add_vars[DOCUMENTS_TENDERS_BUDGET_EUR]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_budget_usd' . "\n" .
                             '  ON (d_cstm_budget_usd.model_id=d.id AND d_cstm_budget_usd.var_id="' . (isset($add_vars[DOCUMENTS_TENDERS_BUDGET_USD]) ? $add_vars[DOCUMENTS_TENDERS_BUDGET_USD]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_participation_form' . "\n" .
                             '  ON (d_cstm_participation_form.model_id=d.id AND d_cstm_participation_form.var_id="' . (isset($add_vars[DOCUMENTS_TENDERS_PARTICIPATION_FORM]) ? $add_vars[DOCUMENTS_TENDERS_PARTICIPATION_FORM]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS fo_pf' . "\n" .
                             '  ON (fo_pf.parent_name="' . DOCUMENTS_TENDERS_PARTICIPATION_FORM . '" AND fo_pf.option_value=d_cstm_participation_form.value AND fo_pf.lang="' . $registry['lang'] . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_performer' . "\n" .
                             '  ON (d_cstm_performer.model_id=d.id AND d_cstm_performer.var_id="' . (isset($add_vars[DOCUMENTS_TENDERS_PERFORMER]) ? $add_vars[DOCUMENTS_TENDERS_PERFORMER]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n_perf' . "\n" .
                             '  ON (ci18n_perf.parent_id=d_cstm_performer.value AND ci18n_perf.lang="' . $registry['lang'] . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_file' . "\n" .
                             '  ON (d_cstm_file.model_id=d.id AND d_cstm_file.var_id="' . (isset($add_vars[DOCUMENTS_TENDERS_DOCUMENT_FILES]) ? $add_vars[DOCUMENTS_TENDERS_DOCUMENT_FILES]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_contract' . "\n" .
                             '  ON (d_cstm_contract.model_id=d.id AND d_cstm_contract.var_id="' . (isset($add_vars[DOCUMENTS_TENDERS_CONTRACT]) ? $add_vars[DOCUMENTS_TENDERS_CONTRACT]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CONTRACTS . ' AS con' . "\n" .
                             '  ON (d_cstm_contract.value=con.id)' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_end_user' . "\n" .
                             '  ON (d_cstm_end_user.model_id=d.id AND d_cstm_end_user.var_id="' . (isset($add_vars[DOCUMENTS_TENDERS_END_USER]) ? $add_vars[DOCUMENTS_TENDERS_END_USER]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS end_user' . "\n" .
                             '  ON (end_user.parent_id=d_cstm_end_user.value AND end_user.lang="' . $registry['lang'] . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_notes' . "\n" .
                             '  ON (d_cstm_notes.model_id=d.id AND d_cstm_notes.var_id="' . (isset($add_vars[DOCUMENTS_TENDERS_NOTES]) ? $add_vars[DOCUMENTS_TENDERS_NOTES]: '') . '")' . "\n";
            if (!empty($params['competitor_id'])) {
                $sql['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_competitor_product' . "\n" .
                                '  ON (d_cstm_competitor_product.model_id=d.id AND d_cstm_competitor_product.var_id="' . (isset($add_vars[DOCUMENTS_TENDERS_COMPETITOR]) ? $add_vars[DOCUMENTS_TENDERS_COMPETITOR]: '') . '" AND d_cstm_competitor_product.value="' . $params['competitor_id'] . '")' . "\n";
            }
            $sql['where'] =  'WHERE d.type="' . DOCUMENTS_TENDERS_TYPE . '" AND d.active=1 AND d.deleted_by=0' . "\n";
            if (!empty($params['market_id'])) {
                $sql['where'] .= ' AND d_cstm_country.value="' . $params['market_id'] . '"';
            } elseif (!empty($params['region_id'])) {
                $sql['where'] .= ' AND d_cstm_region.value="' . $params['region_id'] . '"';
            }
            if (!empty($params['customer_id'])) {
                $sql['where'] .= ' AND d.customer="' . $params['customer_id'] . '"';
            }
            $sql['group'] =  'GROUP BY d.id' . "\n";
            $sql['order'] =  'ORDER BY d.added DESC'  . "\n";

            $records_per_page = 5;
            // prepare the pagination
            if ($page) {
                $sql['limit'] = 'LIMIT ' . (($page-1)*$records_per_page) . ',' . $records_per_page;
            }
            $tender_products = $registry['db']->getAssoc(implode("\n", $sql));

            if ($page) {
                $total = $registry['db']->getOne('SELECT FOUND_ROWS()');

                $pagination['start'] = (($page-1)*$records_per_page);
                $pagination['found'] = count($tender_products);
                $pagination['total'] = $total;
                $pagination['rpp'] = $records_per_page;
                $pagination['page'] = $page;
                $pagination['pages'] = ceil($total/$records_per_page);
            }

            $files_relations = array();
            foreach ($tender_products as $k_comm_p => $com_p) {
                $tender_products[$k_comm_p]['procedure_status'] = implode(' - ', array_filter(array($registry['translater']->translate('reports_status_' . $com_p['status']), $com_p['substatus'])));
                $tender_products[$k_comm_p]['competitor_products'] = array();
                $tender_products[$k_comm_p]['systems'] = array();
                $tender_products[$k_comm_p]['document_files'] = array();
            }

            // get the files
            require_once PH_MODULES_DIR . 'files/models/files.factory.php';
            $filters_files = array(
                'where'         => array(
                    'f.model="Document"',
                    'f.model_id IN ("' . implode('","', array_keys($tender_products)) . '")',
                    'f.origin="attached"'
                ),
                'model_lang'    => $registry['lang'],
                'sanitize'      => true
            );
            $files = Files::search($registry, $filters_files);

            foreach ($files as $f) {
                if (isset($tender_products[$f->get('model_id')])) {
                    $tender_products[$f->get('model_id')]['document_files'][$f->get('id')] = $f;
                }
            }

            if (!empty($tender_products)) {
                // get the systems
                $sql_sys = 'SELECT d.id, ni18n.name as product, nc_i18n.name as main_category, d_cstm_quantity.value as quantity, d_cstm_components.value as components' . "\n" .
                           'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_prod' . "\n" .
                           '  ON (d_cstm_prod.model_id=d.id AND d_cstm_prod.var_id="' . (isset($add_vars[DOCUMENTS_TENDERS_PRODUCT]) ? $add_vars[DOCUMENTS_TENDERS_PRODUCT]: '') . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                           '  ON (ni18n.parent_id=d_cstm_prod.value AND ni18n.lang="' . $registry['lang'] . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_mc' . "\n" .
                           '  ON (d_cstm_mc.model_id=d.id AND d_cstm_mc.var_id="' . (isset($add_vars[DOCUMENTS_TENDERS_MAIN_CATEGORY]) ? $add_vars[DOCUMENTS_TENDERS_MAIN_CATEGORY]: '') . '" AND d_cstm_prod.num=d_cstm_mc.num)' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_CATEGORIES_I18N . ' AS nc_i18n' . "\n" .
                           '  ON (nc_i18n.parent_id=d_cstm_mc.value AND nc_i18n.lang="' . $registry['lang'] . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_quantity' . "\n" .
                           '  ON (d_cstm_quantity.model_id=d.id AND d_cstm_quantity.var_id="' . (isset($add_vars[DOCUMENTS_TENDERS_QUANTITY]) ? $add_vars[DOCUMENTS_TENDERS_QUANTITY]: '') . '"  AND d_cstm_prod.num=d_cstm_quantity.num)' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_components' . "\n" .
                           '  ON (d_cstm_components.model_id=d.id AND d_cstm_components.var_id="' . (isset($add_vars[DOCUMENTS_TENDERS_COMPONENTS]) ? $add_vars[DOCUMENTS_TENDERS_COMPONENTS]: '') . '"  AND d_cstm_prod.num=d_cstm_components.num)' . "\n" .
                           'WHERE d.id IN ("' . implode('","', array_keys($tender_products)) . '")' . "\n" .
                           'ORDER BY d_cstm_prod.num' . "\n";
                $systems = $registry['db']->getAll($sql_sys);

                foreach ($systems as $system) {
                    if (!empty($system['product']) || !empty($system['main_category']) || !empty($system['quantity'])) {
                        $tender_products[$system['id']]['systems'][] = $system;
                        $tender_products[$system['id']]['rowspan'] = count($tender_products[$system['id']]['systems']);
                    }
                }
            }

            // get the competitor products
            $sql_comp = 'SELECT d.id, d_cstm_competitor_product.value as competitor_product, CONCAT(ci18n.name, " ", ci18n.lastname) as competitor_product_name, d_cstm_competitor_price.value as price' . "\n" .
                        'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_competitor_product' . "\n" .
                        '  ON (d_cstm_competitor_product.model_id=d.id AND d_cstm_competitor_product.var_id="' . (isset($add_vars[DOCUMENTS_TENDERS_COMPETITOR]) ? $add_vars[DOCUMENTS_TENDERS_COMPETITOR]: '') . '")' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                        '  ON (ci18n.parent_id=d_cstm_competitor_product.value AND ci18n.lang="' . $registry['lang'] . '")' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_competitor_price' . "\n" .
                        '  ON (d_cstm_competitor_price.model_id=d.id AND d_cstm_competitor_price.var_id="' . (isset($add_vars[DOCUMENTS_TENDERS_COMPETITOR_PRICE]) ? $add_vars[DOCUMENTS_TENDERS_COMPETITOR_PRICE]: '') . '" AND d_cstm_competitor_price.num=d_cstm_competitor_product.num)' . "\n" .
                        'WHERE d.id IN ("' . implode('","', array_keys($tender_products)) . '") AND d_cstm_competitor_product.value IS NOT NULL AND d_cstm_competitor_product.value!="" AND d_cstm_competitor_product.value!="0"';
            $competitive_products_list = $registry['db']->getAll($sql_comp);

            foreach ($competitive_products_list as $cpl) {
                if (isset($tender_products[$cpl['id']])) {
                    $tender_products[$cpl['id']]['competitor_products'][] = array(
                        'id'    => $cpl['competitor_product'],
                        'name'  => $cpl['competitor_product_name'],
                        'price' => $cpl['price']
                    );
                }
            }

            return array($tender_products, $pagination);
        }

        /*
         * Get data for the tenders for the selected nomenclature
         */
        public static function getProductOffers(&$registry, $params = array(), $page = 1) {
            $offers_products = array();
            $pagination = array();

            // get the additional vars
            $add_vars = array(
                DOCUMENTS_OFFER_BUDGET_BGN, DOCUMENTS_OFFER_BUDGET_EUR, DOCUMENTS_OFFER_BUDGET_USD, DOCUMENTS_OFFER_PRODUCT, DOCUMENTS_OFFER_COUNTRY, DOCUMENTS_OFFER_REGION,
                DOCUMENTS_OFFER_SALES_USER, DOCUMENTS_OFFER_DATE, DOCUMENTS_OFFER_NUM, DOCUMENTS_OFFER_DATE, DOCUMENTS_OFFER_MAIN_CATEGORY, DOCUMENTS_OFFER_DEPARTMENT,
                DOCUMENTS_OFFER_QUANTITY, DOCUMENTS_OFFER_OFFER_REASONS, DOCUMENTS_OFFER_ORDER_NUM, DOCUMENTS_OFFER_ORDER_DATE, DOCUMENTS_OFFER_ORDER_REASONS
            );

            //sql to take the ids of the needed additional vars
            $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `model_type`="' . DOCUMENTS_OFFER_TYPE . '" AND `name` IN ("' . implode('","', $add_vars) . '")';
            $add_vars = $registry['db']->GetAssoc($sql);

            $sql = array();
            $sql['select'] = 'SELECT SQL_CALC_FOUND_ROWS'. "\n" .
                             '       d.id as idx, d.id, d.customer, CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name, di18n.name as prospect_name, dm.name as media, ' . "\n" .
                             '       d.date as offer_date, "" as department, d_cstm_budget.value as budget_bgn, d_cstm_budget_usd.value as budget_usd, d_cstm_budget_eur.value as budget_eur, ' . "\n" .
                             '       country_name.name as market, CONCAT(ci18n_su.name, " ", ci18n_su.lastname) as sales_user, d_cstm_offer_num.value as full_num, d_cstm_offer_date.value as offers_date, ' . "\n" .
                             '       offer_reason.name as offer_reason, d_cstm_order_num.value as order_num, d_cstm_offer_date.value as order_date, offer_reason.name as order_reason, ' . "\n" .
                             '       di18n.notes as department_notes, GROUP_CONCAT(fo_dep.label SEPARATOR ", ") as department' . "\n";
;
            $sql['from'] =   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
                             '  ON (di18n.parent_id=d.id AND di18n.lang="' . $registry['lang'] . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_MEDIAS . ' AS dm' . "\n" .
                             '  ON (dm.id=d.media AND dm.lang="' . $registry['lang'] . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_budget' . "\n" .
                             '  ON (d_cstm_budget.model_id=d.id AND d_cstm_budget.var_id="' . (isset($add_vars[DOCUMENTS_OFFER_BUDGET_BGN]) ? $add_vars[DOCUMENTS_OFFER_BUDGET_BGN]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_budget_eur' . "\n" .
                             '  ON (d_cstm_budget_eur.model_id=d.id AND d_cstm_budget_eur.var_id="' . (isset($add_vars[DOCUMENTS_OFFER_BUDGET_EUR]) ? $add_vars[DOCUMENTS_OFFER_BUDGET_EUR]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_budget_usd' . "\n" .
                             '  ON (d_cstm_budget_usd.model_id=d.id AND d_cstm_budget_usd.var_id="' . (isset($add_vars[DOCUMENTS_OFFER_BUDGET_USD]) ? $add_vars[DOCUMENTS_OFFER_BUDGET_USD]: '') . '")' . "\n" .
                             'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_prod' . "\n" .
                             '  ON (d_cstm_prod.model_id=d.id AND d_cstm_prod.var_id="' . (isset($add_vars[DOCUMENTS_OFFER_PRODUCT]) ? $add_vars[DOCUMENTS_OFFER_PRODUCT]: '') . '"' . (!empty($params['nomenclature_id']) ? ' AND d_cstm_prod.value="' . $params['nomenclature_id'] . '"' : '') . ')' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS system_name' . "\n" .
                             '  ON (system_name.parent_id=d_cstm_prod.value AND system_name.lang="' . $registry['lang'] . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                             '  ON (ci18n.parent_id=d.customer AND ci18n.lang="' . $registry['lang'] . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_country' . "\n" .
                             '  ON (d_cstm_country.model_id=d.id AND d_cstm_country.var_id="' . (isset($add_vars[DOCUMENTS_OFFER_COUNTRY]) ? $add_vars[DOCUMENTS_OFFER_COUNTRY]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_region' . "\n" .
                             '  ON (d_cstm_region.model_id=d.id AND d_cstm_region.var_id="' . (isset($add_vars[DOCUMENTS_OFFER_REGION]) ? $add_vars[DOCUMENTS_OFFER_REGION]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS country_name' . "\n" .
                             '  ON (d_cstm_country.value=country_name.parent_id AND country_name.lang="' . $registry['lang'] . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_sales_user' . "\n" .
                             '  ON (d_cstm_sales_user.model_id=d.id AND d_cstm_sales_user.var_id="' . (isset($add_vars[DOCUMENTS_OFFER_SALES_USER]) ? $add_vars[DOCUMENTS_OFFER_SALES_USER]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n_su' . "\n" .
                             '  ON (ci18n_su.parent_id=d_cstm_sales_user.value AND ci18n_su.lang="' . $registry['lang'] . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_offer_num' . "\n" .
                             '  ON (d_cstm_offer_num.model_id=d.id AND d_cstm_offer_num.var_id="' . (isset($add_vars[DOCUMENTS_OFFER_NUM]) ? $add_vars[DOCUMENTS_OFFER_NUM]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_offer_date' . "\n" .
                             '  ON (d_cstm_offer_date.model_id=d.id AND d_cstm_offer_date.var_id="' . (isset($add_vars[DOCUMENTS_OFFER_DATE]) ? $add_vars[DOCUMENTS_OFFER_DATE]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_offer_reasons' . "\n" .
                             '  ON (d_cstm_offer_reasons.model_id=d.id AND d_cstm_offer_reasons.var_id="' . (isset($add_vars[DOCUMENTS_OFFER_OFFER_REASONS]) ? $add_vars[DOCUMENTS_OFFER_OFFER_REASONS]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS offer_reason' . "\n" .
                             '  ON (d_cstm_offer_reasons.value=offer_reason.parent_id AND offer_reason.lang="' . $registry['lang'] . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_order_num' . "\n" .
                             '  ON (d_cstm_order_num.model_id=d.id AND d_cstm_order_num.var_id="' . (isset($add_vars[DOCUMENTS_OFFER_ORDER_NUM]) ? $add_vars[DOCUMENTS_OFFER_ORDER_NUM]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_order_date' . "\n" .
                             '  ON (d_cstm_order_date.model_id=d.id AND d_cstm_order_date.var_id="' . (isset($add_vars[DOCUMENTS_OFFER_ORDER_DATE]) ? $add_vars[DOCUMENTS_OFFER_ORDER_DATE]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_order_reasons' . "\n" .
                             '  ON (d_cstm_order_reasons.model_id=d.id AND d_cstm_order_reasons.var_id="' . (isset($add_vars[DOCUMENTS_OFFER_ORDER_REASONS]) ? $add_vars[DOCUMENTS_OFFER_ORDER_REASONS]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS order_reason' . "\n" .
                             '  ON (d_cstm_order_reasons.value=order_reason.parent_id AND order_reason.lang="' . $registry['lang'] . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_department' . "\n" .
                             '  ON (d_cstm_department.model_id=d.id AND d_cstm_department.var_id="' . (isset($add_vars[DOCUMENTS_OFFER_DEPARTMENT]) ? $add_vars[DOCUMENTS_OFFER_DEPARTMENT]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS fo_dep' . "\n" .
                             '  ON (fo_dep.parent_name="' . DOCUMENTS_OFFER_DEPARTMENT . '" AND fo_dep.option_value=d_cstm_department.value AND fo_dep.lang="' . $registry['lang'] . '")' . "\n";
            ;

            $sql['where'] =  'WHERE d.type="' . DOCUMENTS_OFFER_TYPE . '" AND d.active=1 AND d.deleted_by=0' . "\n";
            if (!empty($params['market_id'])) {
                $sql['where'] .= ' AND d_cstm_country.value="' . $params['market_id'] . '"';
            } elseif (!empty($params['region_id'])) {
                $sql['where'] .= ' AND d_cstm_region.value="' . $params['region_id'] . '"';
            }
            if (!empty($params['customer_id'])) {
                $sql['where'] .= ' AND d.customer="' . $params['customer_id'] . '"';
            }
            $sql['group'] =  'GROUP BY d.id' . "\n";
            $sql['order'] =  'ORDER BY d.added DESC'  . "\n";

            $records_per_page = 5;
            // prepare the pagination
            if ($page) {
                $sql['limit'] = 'LIMIT ' . (($page-1)*$records_per_page) . ',' . $records_per_page;
            }
            $offers_products = $registry['db']->getAssoc(implode("\n", $sql));

            if ($page) {
                $total = $registry['db']->getOne('SELECT FOUND_ROWS()');

                $pagination['start'] = (($page-1)*$records_per_page);
                $pagination['found'] = count($offers_products);
                $pagination['total'] = $total;
                $pagination['rpp'] = $records_per_page;
                $pagination['page'] = $page;
                $pagination['pages'] = ceil($total/$records_per_page);
            }

            foreach ($offers_products as $k_comm_p => $com_p) {
                $offers_products[$k_comm_p]['systems'] = array();
                $notes = '';
                if (!empty($com_p['department_notes'])) {
                    $notes = sprintf('(%s)', trim($com_p['department_notes']));
                }
                $departments_included = array_filter(array($com_p['department'], $notes));
                $offers_products[$k_comm_p]['department'] = implode(' ', array_filter($departments_included));
            }

            if (!empty($offers_products)) {
                // get the systems
                $sql_sys = 'SELECT d.id, ni18n.name as product, nc_i18n.name as main_category, d_cstm_quantity.value as quantity' . "\n" .
                           'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_prod' . "\n" .
                           '  ON (d_cstm_prod.model_id=d.id AND d_cstm_prod.var_id="' . (isset($add_vars[DOCUMENTS_OFFER_PRODUCT]) ? $add_vars[DOCUMENTS_OFFER_PRODUCT]: '') . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                           '  ON (ni18n.parent_id=d_cstm_prod.value AND ni18n.lang="' . $registry['lang'] . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_mc' . "\n" .
                           '  ON (d_cstm_mc.model_id=d.id AND d_cstm_mc.var_id="' . (isset($add_vars[DOCUMENTS_OFFER_MAIN_CATEGORY]) ? $add_vars[DOCUMENTS_OFFER_MAIN_CATEGORY]: '') . '" AND d_cstm_prod.num=d_cstm_mc.num)' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_CATEGORIES_I18N . ' AS nc_i18n' . "\n" .
                           '  ON (nc_i18n.parent_id=d_cstm_mc.value AND nc_i18n.lang="' . $registry['lang'] . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_quantity' . "\n" .
                           '  ON (d_cstm_quantity.model_id=d.id AND d_cstm_quantity.var_id="' . (isset($add_vars[DOCUMENTS_OFFER_QUANTITY]) ? $add_vars[DOCUMENTS_OFFER_QUANTITY]: '') . '"  AND d_cstm_prod.num=d_cstm_quantity.num)' . "\n" .
                           'WHERE d.id IN ("' . implode('","', array_keys($offers_products)) . '")' . "\n" .
                           'ORDER BY d_cstm_prod.num' . "\n";
                $systems = $registry['db']->getAll($sql_sys);

                foreach ($systems as $system) {
                    if (!empty($system['product']) || !empty($system['main_category']) || !empty($system['quantity'])) {
                        $offers_products[$system['id']]['systems'][] = $system;
                        $offers_products[$system['id']]['rowspan'] = count($offers_products[$system['id']]['systems']);
                    }
                }
            }

            return array($offers_products, $pagination);
        }

        /*
         * Get data for the contracts related with the selected nomenclature
         */
        public static function getProductContracts(&$registry, $params = array(), $page = 1) {
            $contract_products = array();
            $pagination = array();

            // get the additional vars
            $add_vars = array(
                CONTRACT_PRODUCT, CONTRACT_REGION, CONTRACT_COUNTRY, CONTRACT_DOCUMENT_FILES, CONTRACT_QUANTITY,
                CONTRACT_COMPONENTS, CONTRACT_BUDGET_BGN, CONTRACT_BUDGET_EUR, CONTRACT_BUDGET_USD,
                CONTRACT_END_USER, CONTRACT_MAIN_CATEGORY, CONTRACT_DURATION_WARRANTY, CONTRACT_NOTES
            );

            //sql to take the ids of the needed additional vars
            $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Contract" AND `model_type`="' . CONTRACT_TYPE . '" AND `name` IN ("' . implode('","', $add_vars) . '")';
            $add_vars = $registry['db']->GetAssoc($sql);

            $sql = 'SELECT `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `model_type`="' . DOCUMENTS_WARRANTY_TYPE . '" AND `name`="' . DOCUMENT_WARRANTY_CONTRACT . '"';
            $warranty_contract = $registry['db']->GetOne($sql);

            $sql = 'SELECT `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `model_type`="' . DOCUMENTS_REFERENCE_TYPE . '" AND `name`="' . DOCUMENTS_REFERENCE_CONTRACT . '"';
            $reference_contract = $registry['db']->GetOne($sql);

            $sql = array();
            $sql['select'] = 'SELECT SQL_CALC_FOUND_ROWS'. "\n" .
                             '       c.id as idx, c.id, c.customer, CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name, c.date_start as date_start, c.date_validity as date_end, ' . "\n" .
                             '       c_cstm_prod.value as `system`,  system_name.name as system_name, region_name.name as region, country_name.name as country, c_cstm_quantity.value as quantity, ' . "\n" .
                             '       c_cstm_file.value as document_file, c_cstm_warranty.value as duration_warranty, CONCAT(end_user.name, " ", end_user.lastname) as end_user, ' . "\n" .
                             '       c_cstm_budget_bgn.value as budget_bgn, c_cstm_budget_eur.value as budget_eur, ' . "\n" .
                             '       c_cstm_budget_usd.value as budget_usd, c_cstm_notes.value as description, "" as systems, 1 as rowspan ' . "\n";
            $sql['from'] =   'FROM ' . DB_TABLE_CONTRACTS . ' AS c' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                             '  ON (ci18n.parent_id=c.customer AND ci18n.lang="' . $registry['lang'] . '")' . "\n" .
                             'INNER JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_prod' . "\n" .
                             '  ON (c_cstm_prod.model_id=c.id AND c_cstm_prod.var_id="' . (isset($add_vars[CONTRACT_PRODUCT]) ? $add_vars[CONTRACT_PRODUCT]: '') . '"' . (!empty($params['nomenclature_id']) ? ' AND c_cstm_prod.value="' . $params['nomenclature_id'] . '"' : '') . ')' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS system_name' . "\n" .
                             '  ON (system_name.parent_id=c_cstm_prod.value AND system_name.lang="' . $registry['lang'] . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_region' . "\n" .
                             '  ON (c_cstm_region.model_id=c.id AND c_cstm_region.var_id="' . (isset($add_vars[CONTRACT_REGION]) ? $add_vars[CONTRACT_REGION]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS region_name' . "\n" .
                             '  ON (region_name.parent_id=c_cstm_region.value AND region_name.lang="' . $registry['lang'] . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_country' . "\n" .
                             '  ON (c_cstm_country.model_id=c.id AND c_cstm_country.var_id="' . (isset($add_vars[CONTRACT_COUNTRY]) ? $add_vars[CONTRACT_COUNTRY]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS country_name' . "\n" .
                             '  ON (country_name.parent_id=c_cstm_country.value AND country_name.lang="' . $registry['lang'] . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_file' . "\n" .
                             '  ON (c_cstm_file.model_id=c.id AND c_cstm_file.var_id="' . (isset($add_vars[CONTRACT_DOCUMENT_FILES]) ? $add_vars[CONTRACT_DOCUMENT_FILES]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_budget_bgn' . "\n" .
                             '  ON (c_cstm_budget_bgn.model_id=c.id AND c_cstm_budget_bgn.var_id="' . (isset($add_vars[CONTRACT_BUDGET_BGN]) ? $add_vars[CONTRACT_BUDGET_BGN]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_budget_eur' . "\n" .
                             '  ON (c_cstm_budget_eur.model_id=c.id AND c_cstm_budget_eur.var_id="' . (isset($add_vars[CONTRACT_BUDGET_EUR]) ? $add_vars[CONTRACT_BUDGET_EUR]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_budget_usd' . "\n" .
                             '  ON (c_cstm_budget_usd.model_id=c.id AND c_cstm_budget_usd.var_id="' . (isset($add_vars[CONTRACT_BUDGET_USD]) ? $add_vars[CONTRACT_BUDGET_USD]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_quantity' . "\n" .
                             '  ON (c_cstm_quantity.model_id=c.id AND c_cstm_quantity.var_id="' . (isset($add_vars[CONTRACT_QUANTITY]) ? $add_vars[CONTRACT_QUANTITY]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_warranty' . "\n" .
                             '  ON (c_cstm_warranty.model_id=c.id AND c_cstm_warranty.var_id="' . (isset($add_vars[CONTRACT_DURATION_WARRANTY]) ? $add_vars[CONTRACT_DURATION_WARRANTY]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_end_user' . "\n" .
                             '  ON (c_cstm_end_user.model_id=c.id AND c_cstm_end_user.var_id="' . (isset($add_vars[CONTRACT_END_USER]) ? $add_vars[CONTRACT_END_USER]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS end_user' . "\n" .
                             '  ON (end_user.parent_id=c_cstm_end_user.value AND end_user.lang="' . $registry['lang'] . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_notes' . "\n" .
                             '  ON (c_cstm_notes.model_id=c.id AND c_cstm_notes.var_id="' . (isset($add_vars[CONTRACT_NOTES]) ? $add_vars[CONTRACT_NOTES]: '') . '")' . "\n";
            $sql['where'] =  'WHERE c.type="' . CONTRACT_TYPE . '" AND c.active=1 AND c.deleted_by=0 AND c.subtype="contract"' . "\n";
            if (!empty($params['market_id'])) {
                $sql['where'] .= ' AND c_cstm_country.value="' . $params['market_id'] . '"';
            } elseif (!empty($params['region_id'])) {
                $sql['where'] .= ' AND c_cstm_region.value="' . $params['region_id'] . '"';
            }
            if (!empty($params['customer_id'])) {
                $sql['where'] .= ' AND c.customer="' . $params['customer_id'] . '"';
            }
            $sql['group'] =  'GROUP BY c.id'  . "\n";
            $sql['order'] =  'ORDER BY c.added DESC'  . "\n";

            $records_per_page = 5;
            // prepare the pagination
            if ($page) {
                $sql['limit'] = 'LIMIT ' . (($page-1)*$records_per_page) . ',' . $records_per_page;
            }
            $contract_products = $registry['db']->getAssoc(implode("\n", $sql));

            if ($page) {
                $total = $registry['db']->getOne('SELECT FOUND_ROWS()');

                $pagination['start'] = (($page-1)*$records_per_page);
                $pagination['found'] = count($contract_products);
                $pagination['total'] = $total;
                $pagination['rpp'] = $records_per_page;
                $pagination['page'] = $page;
                $pagination['pages'] = ceil($total/$records_per_page);
            }

            foreach ($contract_products as $k_comm_p => $com_p) {
                $contract_products[$k_comm_p]['systems'] = array();
                $contract_products[$k_comm_p]['document_files'] = array();
                $contract_products[$k_comm_p]['warranty'] = array();
                $contract_products[$k_comm_p]['reference'] = array();
            }

            if (!empty($contract_products)) {
                // get the systems
                $sql_sys = 'SELECT c.id, ni18n.name as product, nc_i18n.name as main_category, c_cstm_quantity.value as quantity, c_cstm_components.value as components' . "\n" .
                           'FROM ' . DB_TABLE_CONTRACTS . ' AS c' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_prod' . "\n" .
                           '  ON (c_cstm_prod.model_id=c.id AND c_cstm_prod.var_id="' . (isset($add_vars[CONTRACT_PRODUCT]) ? $add_vars[CONTRACT_PRODUCT]: '') . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                           '  ON (ni18n.parent_id=c_cstm_prod.value AND ni18n.lang="' . $registry['lang'] . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_mc' . "\n" .
                           '  ON (c_cstm_mc.model_id=c.id AND c_cstm_mc.var_id="' . (isset($add_vars[CONTRACT_MAIN_CATEGORY]) ? $add_vars[CONTRACT_MAIN_CATEGORY]: '') . '" AND c_cstm_prod.num=c_cstm_mc.num)' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_CATEGORIES_I18N . ' AS nc_i18n' . "\n" .
                           '  ON (nc_i18n.parent_id=c_cstm_mc.value AND nc_i18n.lang="' . $registry['lang'] . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_quantity' . "\n" .
                           '  ON (c_cstm_quantity.model_id=c.id AND c_cstm_quantity.var_id="' . (isset($add_vars[CONTRACT_QUANTITY]) ? $add_vars[CONTRACT_QUANTITY]: '') . '"  AND c_cstm_prod.num=c_cstm_quantity.num)' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_components' . "\n" .
                           '  ON (c_cstm_components.model_id=c.id AND c_cstm_components.var_id="' . (isset($add_vars[CONTRACT_COMPONENTS]) ? $add_vars[CONTRACT_COMPONENTS]: '') . '"  AND c_cstm_prod.num=c_cstm_components.num)' . "\n" .
                           'WHERE c.id IN ("' . implode('","', array_keys($contract_products)) . '")' . "\n" .
                           'ORDER BY c_cstm_prod.num' . "\n";
                $systems = $registry['db']->getAll($sql_sys);

                foreach ($systems as $system) {
                    if (!empty($system['product']) || !empty($system['main_category']) || !empty($system['quantity'])) {
                        $contract_products[$system['id']]['systems'][] = $system;
                        $contract_products[$system['id']]['rowspan'] = count($contract_products[$system['id']]['systems']);
                    }
                }
            }

            // get the files
            require_once PH_MODULES_DIR . 'files/models/files.factory.php';
            $filters_files = array(
                'where'         => array(
                    'f.model="Contract"',
                    'f.model_id IN ("' . implode('","', array_keys($contract_products)) . '")',
                    'f.origin="attached"'
                ),
                'model_lang'    => $registry['lang'],
                'sanitize'      => true
            );
            $files = Files::search($registry, $filters_files);

            foreach ($files as $f) {
                if (isset($contract_products[$f->get('model_id')])) {
                    $contract_products[$f->get('model_id')]['document_files'][$f->get('id')] = $f;
                }
            }

            // get the warranties
            $sql_war = 'SELECT d.id, CONCAT(DATE_FORMAT(d.date, "%d.%m.%Y"), "/", d.full_num) as num, d_cstm_contr.value as contract' . "\n" .
                       'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                       'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_contr' . "\n" .
                       '  ON (d_cstm_contr.model_id=d.id AND d_cstm_contr.var_id="' . $warranty_contract . '" AND d_cstm_contr.value IN ("' . implode('","', array_keys($contract_products)) . '"))' . "\n" .
                       'WHERE d.type="' . DOCUMENTS_WARRANTY_TYPE . '" AND d.active=1 AND d.deleted_by=0 AND d_cstm_contr.value!=""' . "\n";
            $warranties = $registry['db']->getAll($sql_war);

            foreach ($warranties as $warranty) {
                $contract_products[$warranty['contract']]['warranty'][$warranty['id']] = $warranty;
            }

            // get the references
            $sql_war = 'SELECT d.id, CONCAT(DATE_FORMAT(d.date, "%d.%m.%Y"), "/", d.full_num) as num, d_cstm_contr.value as contract' . "\n" .
                       'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                       'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_contr' . "\n" .
                       '  ON (d_cstm_contr.model_id=d.id AND d_cstm_contr.var_id="' . $reference_contract . '" AND d_cstm_contr.value IN ("' . implode('","', array_keys($contract_products)) . '"))' . "\n" .
                       'WHERE d.type="' . DOCUMENTS_REFERENCE_TYPE . '" AND d.active=1 AND d.deleted_by=0 AND d_cstm_contr.value!=""' . "\n";
            $references = $registry['db']->getAll($sql_war);

            foreach ($references as $reference) {
                $contract_products[$reference['contract']]['reference'][$reference['id']] = $reference;
            }

            return array($contract_products, $pagination);
        }

        /*
         * Get data for competitive products for the selected nomenclature
         */
        public static function getProductCompetitiveProducts(&$registry, $params = array(), $page = 1) {
            $competitive_products = array();
            $pagination = array();

            if (empty($params['vars'])) {
                // define the tyoe of the entered product
                $sql = 'SELECT `type`, `name` FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                       '  ON (ni18n.parent_id=n.id AND ni18n.lang="' . $registry['lang'] . '")' . "\n" .
                       'WHERE `id`="' . $params['nomenclature_id'] . '"';
                $searched_nom = $registry['db']->GetRow($sql);

                // get the additional vars
                $add_vars = array(
                    NOMENCLATURES_PRODUCTS_COMPETITIVE_NAME, NOMENCLATURES_PRODUCTS_COMPETITIVE_MANUFACTURER, NOMENCLATURES_PRODUCTS_COMPETITIVE_FILE, NOMENCLATURES_PRODUCTS_COMPETITIVE_NOTES
                );

                //sql to take the ids of the needed additional vars
                $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Nomenclature" AND `model_type`="' . $searched_nom['type'] . '" AND `name` IN ("' . implode('","', $add_vars) . '")';
                $add_vars = $registry['db']->GetAssoc($sql);
            } else {
                $add_vars = $params['vars'];
            }

            // get the competitive products
            //sql to take the ids of the needed additional vars
            $sql = 'SELECT `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Nomenclature" AND `model_type`="' . NOMENCLATURES_COMPETITIVE_PRODUCTS . '" AND `name`="' . NOMENCLATURES_COMPETITIVE_PRODUCTS_FILE . '"';
            $competitve_product_file_var_id = $registry['db']->GetOne($sql);

            $sql = array();
            $sql['select'] = 'SELECT SQL_CALC_FOUND_ROWS' . "\n" .
                             '       n_cstm_comp_prod.value as product_id, comp_prod_name.name as porduct_name, ' . "\n" .
                             '       n_cstm_manufacturer.value as manufacturer, CONCAT(ci18n.name, " ", ci18n.lastname) as manufacturer_name, ' . "\n" .
                             '       n_cstm_file.value as file, n_cstm_description.value as description, n_cstm_comp_prod_file.value as comp_product_file' . "\n";
            $sql['from'] =   'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_comp_prod' . "\n" .
                             '  ON (n_cstm_comp_prod.model_id=n.id AND n_cstm_comp_prod.var_id="' . (isset($add_vars[NOMENCLATURES_PRODUCTS_COMPETITIVE_NAME]) ? $add_vars[NOMENCLATURES_PRODUCTS_COMPETITIVE_NAME]: '') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_comp_prod_file' . "\n" .
                             '  ON (n_cstm_comp_prod_file.model_id=n_cstm_comp_prod.value AND n_cstm_comp_prod_file.var_id="' . $competitve_product_file_var_id . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS comp_prod_name' . "\n" .
                             '  ON (comp_prod_name.parent_id=n_cstm_comp_prod.value AND comp_prod_name.lang="' . $registry['lang'] . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_manufacturer' . "\n" .
                             '  ON (n_cstm_manufacturer.model_id=n.id AND n_cstm_manufacturer.var_id="' . (isset($add_vars[NOMENCLATURES_PRODUCTS_COMPETITIVE_MANUFACTURER]) ? $add_vars[NOMENCLATURES_PRODUCTS_COMPETITIVE_MANUFACTURER]: '') . '") AND n_cstm_manufacturer.num=n_cstm_comp_prod.num' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                             '  ON (ci18n.parent_id=n_cstm_manufacturer.value AND ci18n.lang="' . $registry['lang'] . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_file' . "\n" .
                             '  ON (n_cstm_file.model_id=n.id AND n_cstm_file.var_id="' . (isset($add_vars[NOMENCLATURES_PRODUCTS_COMPETITIVE_FILE]) ? $add_vars[NOMENCLATURES_PRODUCTS_COMPETITIVE_FILE]: '') . '") AND n_cstm_file.num=n_cstm_comp_prod.num' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_description' . "\n" .
                             '  ON (n_cstm_description.model_id=n.id AND n_cstm_description.var_id="' . (isset($add_vars[NOMENCLATURES_PRODUCTS_COMPETITIVE_NOTES]) ? $add_vars[NOMENCLATURES_PRODUCTS_COMPETITIVE_NOTES]: '') . '") AND n_cstm_description.num=n_cstm_comp_prod.num' . "\n";
            $sql['where'] =  'WHERE n.id="' . $params['nomenclature_id'] . '"' . "\n";

            $records_per_page = 5;
            // prepare the pagination
            if ($page) {
                $sql['limit'] = 'LIMIT ' . (($page-1)*$records_per_page) . ',' . $records_per_page;
            }
            $competitive_products = $registry['db']->getAll(implode("\n", $sql));

            if ($page) {
                $total = $registry['db']->getOne('SELECT FOUND_ROWS()');

                $pagination['start'] = (($page-1)*$records_per_page);
                $pagination['found'] = count($competitive_products);
                $pagination['total'] = $total;
                $pagination['rpp'] = $records_per_page;
                $pagination['page'] = $page;
                $pagination['pages'] = ceil($total/$records_per_page);
            }

            $included_files = array();
            foreach ($competitive_products as $com_p => $comp_prod) {
                $included_files[] = $competitive_products[$com_p]['file'];
                $included_files[] = $competitive_products[$com_p]['comp_product_file'];
            }

            $included_files = array_filter($included_files);
            if (!empty($included_files)) {
                // get the files
                require_once PH_MODULES_DIR . 'files/models/files.factory.php';
                $filters_files = array(
                    'where'         => array('f.id IN ("' . implode('","', $included_files) . '")'),
                    'model_lang'    => $registry['lang'],
                    'sanitize'      => true
                );
                $product_info_files = Files::search($registry, $filters_files);

                foreach ($product_info_files as $pif) {
                    $files[$pif->get('id')] = $pif;
                }

                // complete the files
                foreach ($competitive_products as $cp_idx => $comp_prod) {
                    if (!empty($comp_prod['file']) && isset($files[$comp_prod['file']])) {
                        $competitive_products[$cp_idx]['file'] = $files[$comp_prod['file']];
                    }
                    if (!empty($comp_prod['comp_product_file']) && isset($files[$comp_prod['comp_product_file']])) {
                        $competitive_products[$cp_idx]['comp_product_file'] = $files[$comp_prod['comp_product_file']];
                    }
                }
            }

            return array($competitive_products, $pagination);
        }
    }
?>
