{include file="`$templatesDirPlugin``$report_type`/_grid_columns_templates.html"}

<div id="ej2_grid_container"></div>

<script>
  onReady().then(()=> {ldelim}
    let gridContainer = document.querySelector('#ej2_grid_container');
    let warehouses = {$reports_additional_options.warehouses|@json_encode};
    const report = new SelamoreCurrentAndUpcomingAvailabilities(gridContainer, warehouses, i18n);
    report.init();
  {rdelim});
</script>
