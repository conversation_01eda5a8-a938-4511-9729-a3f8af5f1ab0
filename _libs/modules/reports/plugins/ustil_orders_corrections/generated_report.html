<table class="reports_table" style="min-width: 50%;">
  <tr class="reports_title_row">
    <th colspan="10" class="hcenter">{#reports_results#|escape}{if $report_filters.period_from.value} {#from#|mb_lower} {$report_filters.period_from.value|date_format:#date_short#}{/if}{if $report_filters.period_from.additional_filter.value} {#to#|mb_lower} {$report_filters.period_from.additional_filter.value|date_format:#date_short#}{/if}</th>
  </tr>
  <tr class="reports_title_row" style="white-space: nowrap;">
    <th>{#reports_employee#|escape}</th>
    {assign var='total_corrections' value=$reports_additional_options.total_items|default:0}
    <th>
      {if $total_corrections}
        <div class="floatl hright">
          {include file="`$theme->templatesDir`_select_items.html"
                   pages=1
                   total=$total_corrections
                   action='filter'
                   selected_items=$reports_additional_options.selected_items
                   session_param=$session_param|default:$pagination.session_param
                   onclick='toggleCorrectionSelected(this);'
          }
          <span id="selectedItemsCount_1" class="selected_items_span{if $reports_additional_options.selected_items.ids|@count} green{/if}">{if is_array($reports_additional_options.selected_items.ids)}{$reports_additional_options.selected_items.ids|@count}{else}0{/if}</span>/{$total_corrections}
        </div>
      {/if}
      {#reports_correction_num#|escape}
    </th>
    <th>{#reports_order_num#|escape}</th>
    <th>{$reports_additional_options.labels[$smarty.const.CORRECTION2_KIND_MULTI_VAR]|escape}</th>
    <th>{$reports_additional_options.labels[$smarty.const.CORRECTION2_AMOUNT_MULTI_VAR]|escape}</th>
    <th>{$reports_additional_options.labels[$smarty.const.CORRECTION2_NOTE_MULTI_VAR]|escape}</th>
    <th>{$reports_additional_options.labels[$smarty.const.CORRECTION_EXPENSE_OF_VAR]|escape}</th>
    <th>{$reports_additional_options.labels[$smarty.const.CORRECTION2_DEBTOR_MULTI_VAR]|escape}</th>
    <th>{$reports_additional_options.labels[$smarty.const.CORRECTION2_PAYMENT_TYPE_MULTI_VAR]|escape}</th>
    <th>{$reports_additional_options.labels[$smarty.const.CORRECTION2_TERM_MULTI_VAR]|escape}</th>
  </tr>
  {foreach from=$reports_results item='result' key='rk' name='ri'}
    {capture assign='correction_selected'}{if $result.create_correction && @in_array($rk, $reports_additional_options.selected_items.ids)}1{else}0{/if}{/capture}
  <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{if $correction_selected} selected{/if}">
    <td>{$result.employee_name|escape|default:"&nbsp;"}</td>
    <td>
      {if $total_corrections}
      <div class="floatl" style="width: 30px;">
        {if $result.create_correction}
        <input 
            onclick="setCheckAllBox(params = {ldelim}
                the_element: this,
                module: '{$module}',
                controller: 'reports',
                action: 'filter',
                button_id: '{$module}_reports_filter_checkall_1'
               {rdelim}); toggleCorrectionSelected(this);"
            type="checkbox"
            name="items[]"
            id="items_{$rk}"
            value="{$rk}"
            class=""
            style="margin: 0 4px;"
            title="{#check_to_include#|escape}"
            {if $correction_selected}checked="checked"{/if}
        />
        {else}
          &nbsp;
        {/if}
      </div>
      {/if}
      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$result.id}" target="_blank">{$result.full_num|escape}/{$result.date|date_format:#date_short#}</a>
    </td>
    <td>{if $result.order_id}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$result.order_id}" target="_blank">{$result.custom_num|escape}</a>{else}{$result.custom_num|escape|default:"&nbsp;"}{/if}</td>
    <td>{$result.kind_label|escape}</td>
    <td class="hright">{$result.amount}</td>
    <td>{$result.note|mb_wordwrap:50:"\n"|escape|nl2br}</td>
    <td>{$result.expense_of_label|escape}</td>
    {if $result.create_correction}
    <td>
      {capture assign='field_disabled'}{if !$correction_selected}1{/if}{/capture}
      {capture assign='disabled_class_name'}{if !$correction_selected}input_inactive{/if}{/capture}
      {include file=input_hidden.html
               standalone=true
               name='correction_id'
               index=$rk
               eq_indexes=1
               disabled=$field_disabled
               value=$result.id|escape
      }
      {include file=input_hidden.html
               standalone=true
               name='custom_num'
               index=$rk
               eq_indexes=1
               disabled=$field_disabled
               value=$result.custom_num|escape
      }
      {include file=input_hidden.html
               standalone=true
               name=`$smarty.const.CORRECTION2_EMPLOYEE_MULTI_VAR`
               index=$rk
               eq_indexes=1
               disabled=$field_disabled
               value=$result.employee|escape
      }
      {include file=input_hidden.html
               standalone=true
               name=`$smarty.const.CORRECTION2_EMPLOYEE_NAME_MULTI_VAR`
               index=$rk
               eq_indexes=1
               disabled=$field_disabled
               value=$result.employee_name|escape
      }
      {include file=input_hidden.html
               standalone=true
               name=`$smarty.const.CORRECTION2_KIND_MULTI_VAR`
               index=$rk
               eq_indexes=1
               disabled=$field_disabled
               value=$result.kind|escape
      }
      {include file=input_hidden.html
               standalone=true
               name=`$smarty.const.CORRECTION2_AMOUNT_MULTI_VAR`
               index=$rk
               eq_indexes=1
               disabled=$field_disabled
               value=$result.amount|escape
      }
      {include file=input_hidden.html
               standalone=true
               name=`$smarty.const.CORRECTION2_NOTE_MULTI_VAR`
               index=$rk
               eq_indexes=1
               disabled=$field_disabled
               value=$result.note|escape
      }
      {assign var='var' value=$reports_additional_options.vars[$smarty.const.CORRECTION2_DEBTOR_MULTI_VAR]}
      {include file=input_dropdown.html
               standalone=true
               name=$var.name
               index=$rk
               eq_indexes=1
               disabled=$field_disabled
               required=1
               custom_class="`$disabled_class_name`"
               options=$var.options
               value=$var.value.$rk|default:''
               width=$var.width
               label=$var.label
      }
    </td>
    <td>
      {assign var='var' value=$reports_additional_options.vars[$smarty.const.CORRECTION2_PAYMENT_TYPE_MULTI_VAR]}
      {include file=input_dropdown.html
               standalone=true
               name=$var.name
               index=$rk
               eq_indexes=1
               disabled=$field_disabled
               required=1
               custom_class="`$disabled_class_name`"
               options=$var.options
               value=$var.value.$rk|default:''
               width=$var.width
               label=$var.label
      }
    </td>
    <td>
      {assign var='var' value=$reports_additional_options.vars[$smarty.const.CORRECTION2_TERM_MULTI_VAR]}
      {include file=input_`$var.type`.html
               standalone=true
               name=$var.name
               index=$rk
               eq_indexes=1
               disabled=$field_disabled
               value=$var.value.$rk|default:''
               width=$var.width
               custom_class="hright `$disabled_class_name`"
               text_align=$var.text_align
               restrict=$var.js_filter
               label=$var.label
               back_label=#months_short#
      }
    </td>
    {else}
    <td>{if $result.debtor}{foreach from=$reports_additional_options.vars[$smarty.const.CORRECTION2_DEBTOR_MULTI_VAR].options item='opt'}{if $opt.option_value eq $result.debtor}{$opt.label|escape}{/if}{/foreach}{else}&nbsp;{/if}</td>
    <td>{if $result.payment_type}{foreach from=$reports_additional_options.vars[$smarty.const.CORRECTION2_PAYMENT_TYPE_MULTI_VAR].options item='opt'}{if $opt.option_value eq $result.payment_type}{$opt.label|escape}{/if}{/foreach}{else}&nbsp;{/if}</td>
    <td class="hright">{if $result.term}{$result.term|escape} {#months_short#}{else}&nbsp;{/if}</td>
    {/if}
  </tr>
  {foreachelse}
  <tr class="t_odd1 t_odd2">
    <td class="error" colspan="10">{#no_items_found#|escape}</td>
  </tr>
  {/foreach}
</table>
<br />