<tr>
  <td colspan="3" nowrap="nowrap" style="vertical-align: top;">
    <table>
      <tr>
        <td><button type="submit" style="width: {$filter_settings.width}; height: {$filter_settings.height};" name="current_day" class="button" onclick="reportOrdersAnalysis.setPeriod(this, '{$filter_settings.current_day}', '{$filter_settings.current_day_formatted}', '{$filter_settings.current_day}', '{$filter_settings.current_day_formatted}');">{#reports_filter_period_current_day#}</button></td>
        <td><button type="submit" style="width: {$filter_settings.width}; height: {$filter_settings.height};" name="current_week" class="button" onclick="reportOrdersAnalysis.setPeriod(this, '{$filter_settings.current_week.start}', '{$filter_settings.current_week_formatted.start}', '{$filter_settings.current_week.end}', '{$filter_settings.current_week_formatted.end}');">{#reports_filter_period_current_week#}</button></td>
        <td><button type="submit" style="width: {$filter_settings.width}; height: {$filter_settings.height};" name="current_month" class="button" onclick="reportOrdersAnalysis.setPeriod(this, '{$filter_settings.current_month.start}', '{$filter_settings.current_month_formatted.start}', '{$filter_settings.current_month.end}', '{$filter_settings.current_month_formatted.end}');">{#reports_filter_period_current_month#}</button></td>
        <td><button type="submit" style="width: {$filter_settings.width}; height: {$filter_settings.height};" name="current_year" class="button" onclick="reportOrdersAnalysis.setPeriod(this, '{$filter_settings.current_year.start}', '{$filter_settings.current_year_formatted.start}', '{$filter_settings.current_year.end}', '{$filter_settings.current_year_formatted.end}');">{#reports_filter_period_current_year#}</button></td>
      </tr>
      <tr>
        <td><button type="submit" style="width: {$filter_settings.width}; height: {$filter_settings.height};" name="previous_day" class="button" onclick="reportOrdersAnalysis.setPeriod(this, '{$filter_settings.previous_day}', '{$filter_settings.previous_day_formatted}', '{$filter_settings.previous_day}', '{$filter_settings.previous_day_formatted}');">{#reports_filter_period_previous_day#}</button></td>
        <td><button type="submit" style="width: {$filter_settings.width}; height: {$filter_settings.height};" name="previous_week" class="button" onclick="reportOrdersAnalysis.setPeriod(this, '{$filter_settings.previous_week.start}', '{$filter_settings.previous_week_formatted.start}', '{$filter_settings.previous_week.end}', '{$filter_settings.previous_week_formatted.end}');">{#reports_filter_period_previous_week#}</button></td>
        <td><button type="submit" style="width: {$filter_settings.width}; height: {$filter_settings.height};" name="previous_month" class="button" onclick="reportOrdersAnalysis.setPeriod(this, '{$filter_settings.previous_month.start}', '{$filter_settings.previous_month_formatted.start}', '{$filter_settings.previous_month.end}', '{$filter_settings.previous_month_formatted.end}');">{#reports_filter_period_previous_month#}</button></td>
        <td><button type="submit" style="width: {$filter_settings.width}; height: {$filter_settings.height};" name="previous_year" class="button" onclick="reportOrdersAnalysis.setPeriod(this, '{$filter_settings.previous_year.start}', '{$filter_settings.previous_year_formatted.start}', '{$filter_settings.previous_year.end}', '{$filter_settings.previous_year_formatted.end}');">{#reports_filter_period_previous_year#}</button></td>
      </tr>
    </table>
  </td>
</tr>
<script type="text/javascript" src="{$filter_settings.src}?{$system_options.build}" defer="defer"></script>