<?php
// TODO: the report can be optimized using JavaScript when using the radio buttons of the filter "table"
//       (when a radio button is clicked, a JavaScript check can be made - if the report has already been executed
//       for the selected value of the "table" filter, then just show the result (the result can be hidden into a <div id="equipment_group">...</div> (for example)
// TODO: the pagination can be optimized too using JavaScript
Class Bushona_Orders_Analysis Extends Reports {
    public static function buildQuery(&$registry, $filters = array()) {
        // Prepare the array for the final results
        $final_results = array();

        $total = 0;
        $settings = self::getReportSettings($registry);

        // Check if the required filter "table" is empty
        if (empty($filters['table'])) {
            // Show error
            $registry['messages']->setError($registry['translater']->translate('error_reports_select_filter_table'), 'table');
        }

        // If no one from the non required filters is selected
        $atleast_one_required_filters = array(
            'order_statuses_group',
            'equipment_group',
            'equipment_category',
            'device',
            'brand',
            'warranty',
            'period_from',
            'period_to',
            'problem_client',
            'problem_parlor',
            'technician'
        );
        if ($filters['table'] == 'incomes_expenses') {
            $atleast_one_required_filters[] = 'incomes_expenses_period_from';
            $atleast_one_required_filters[] = 'incomes_expenses_period_to';
            $atleast_one_required_filters[] = 'income_expense_type';
        }
        if (isset($filters['brand'])) {
            $filters['brand'] = array_filter($filters['brand']);
        }
        if (isset($filters['brand_autocomplete'])) {
            $filters['brand_autocomplete'] = array_filter($filters['brand_autocomplete']);
        }
        if (count(array_filter(array_intersect_key($filters, array_flip($atleast_one_required_filters)))) == 0) {
            // Show error
            $registry['messages']->setError($registry['translater']->translate('error_reports_select_any_non_required_filter'));
        }
        // Limit period_added max length only for results: trademark
        if ($filters['table'] == 'trademark' && !empty($filters['period_added_from']) && !empty($filters['period_added_to'])) {
            $period_added_from = date_create($filters['period_added_from']);
            $period_added_to = date_create($filters['period_added_to']);
            $period_added_interval = $period_added_from->diff($period_added_to);
            if ($period_added_interval->days > $settings['filter_period_added_from_days_before']-1) {
                // Show error
                $registry['messages']->setError(sprintf($registry['translater']->translate('error_reports_filter_period_added_length'), $settings['filter_period_added_from_days_before']));
            }
        }

        // If there are any errors with the filters
        if ($registry['messages']->getErrors()) {
            return self::exitReport($registry, $filters, $final_results, $total);
        }

        // TODO: this peace of code can be moved somewhere up into the hierarchy (of the reports module) to not being copied
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            $model_lang = $registry['lang'];
        }

        $db = &$registry['db'];

        // TODO: the names of the statuses groups can be moved to one place and then being used everywhere in the code
        $osg = array();
        $osg['unfinished']             = preg_split('/\s*,\s*/', trim($settings['order_statuses_group_unfinished']));
        $osg['successfully_repaird']   = preg_split('/\s*,\s*/', trim($settings['order_statuses_group_successfully_repaird']));
        $osg['unsuccessfully_repaird'] = preg_split('/\s*,\s*/', trim($settings['order_statuses_group_unsuccessfully_repaird']));
        $osg['replaced']               = preg_split('/\s*,\s*/', trim($settings['order_statuses_group_replaced']));
        $osg['all'] = array_merge($osg['unfinished'], $osg['successfully_repaird'], $osg['unsuccessfully_repaird'], $osg['replaced']);

        $final_results['additional_options']['table'] = $filters['table'];

        $query = "
            SELECT `id`
              FROM `" . DB_TABLE_FIELDS_META . "`
              WHERE `model`      = 'Nomenclature'
                AND `model_type` = '{$settings['nom_type_brand']}'
                AND `name`       = '{$settings['field_nom_brand_mark_group']}'";
        $field_nom_brand_mark_group = $db->GetOne($query);
        if (empty($field_nom_brand_mark_group)) {
            $registry['messages']->setError($registry['translater']->translate('error_reports_missing_fields_from_settings'));
            return self::exitReport($registry, $filters, $final_results, $total);
        }

        $fields_doc_order_list = array(
            'field_doc_order_date_acceptance',
            'field_doc_order_product_type',
            'field_doc_order_tm_name',
            'field_doc_order_tm_id',
            'field_doc_order_product_name',
            'field_doc_order_why_repair_client',
            'field_doc_order_why_repair_priemna',
            'field_doc_order_type_repair',
            'field_doc_order_kind_tech_id',
            'field_doc_order_kind_tech_name',
            'field_doc_order_total_vat_rate',
            'field_doc_order_ex_free_text3',
            'field_doc_order_ex_subtotal_with_vat',
            'field_doc_order_ex_vat',
            'field_doc_order_ex_quantity',
            'field_doc_order_ex_price'
        );
        $query = "
            SELECT `name`, `id`
              FROM `" . DB_TABLE_FIELDS_META . "`
              WHERE `model`      = 'Document'
                AND `model_type` = '{$settings['doc_type_order']}'
                AND `name`       IN ('" . implode("', '", array_intersect_key($settings, array_flip($fields_doc_order_list))) . "')";
        $fields_doc_order = $db->GetAssoc($query);
        if (count($fields_doc_order) != count($fields_doc_order_list)) {
            $registry['messages']->setError($registry['translater']->translate('error_reports_missing_fields_from_settings'));
            return self::exitReport($registry, $filters, $final_results, $total);
        }

        $sql = array(
            'select_options' => array(),
            'select'         => array(),
            'from'           => '',
            'join'           => array(),
            'where'          => array(),
            'group_by'       => array(),
            'having'         => array(),
            'limit'          => ''
        );

        $sql['select']['order_id']           = "`d`.`id`                                                AS `order_id`";
        $sql['select']['order_num']          = "`d`.`full_num`                                          AS `order_num`";
        $sql['select']['group_name']         = "`fo7`.`label`                                           AS `group_name`";
        $sql['select']['date_acceptance']    = "`dc2`.`value`                                           AS `date_acceptance`";
        $sql['select']['substatus']          = "`d`.`substatus`                                         AS `substatus`";
        $sql['select']['substatus_name']     = "`ds1`.`name`                                            AS `substatus_name`";
        $sql['select']['substatus_modified'] = "MAX(IF(`dau`.`a_id` IS NOT NULL, `dh`.`h_date`, ''))    AS `substatus_modified`";
        $sql['select']['product_type_name']  = "`nti18n3`.`name`                                        AS `product_type_name`";
        $sql['select']['tm_id']              = "`dc7`.`value`                                           AS `tm_id`";
        $sql['select']['tm_name']            = "`dc4`.`value`                                           AS `tm_name`";
        $sql['select']['product_name']       = "`dc12`.`value`                                          AS `product_name`";
        $sql['select']['why_repair_client']  = "GROUP_CONCAT(DISTINCT `dc5`.`value` SEPARATOR '|')      AS `why_repair_client`";
        $sql['select']['why_repair_priemna'] = "GROUP_CONCAT(DISTINCT `dc8`.`value` SEPARATOR '|')      AS `why_repair_priemna`";
        $sql['select']['type_repair']        = "`dc6`.`value`                                           AS `type_repair`";
        $sql['select']['customer_name']      = "TRIM(CONCAT(`ci18n`.`name`, ' ', `ci18n`.`lastname`))   AS `customer_name`";
        $sql['select']['equipment_id']       = "`dc9`.`value`                                           AS `equipment_id`";
        $sql['select']['equipment_name']     = "`dc10`.`value`                                          AS `equipment_name`";
        $sql['select']['technicians_ids']    = "GROUP_CONCAT(DISTINCT `da`.`assigned_to` SEPARATOR '|') AS `technicians_ids`";

        $sql['from'] = "`" . DB_TABLE_DOCUMENTS . "` AS `d`";

        // TODO: optimization: some joins can be skipped depending on the filters
        $sql['join'][] = "  LEFT JOIN `" . DB_TABLE_DOCUMENTS_CSTM . "` AS `dc2`" . "\n" .
                         "    ON (`dc2`.`model_id` = `d`.`id`" . "\n" .
                         "      AND `dc2`.`var_id` = '{$fields_doc_order[$settings['field_doc_order_date_acceptance']]}')";
        $sql['join'][] = "  LEFT JOIN `" . DB_TABLE_DOCUMENTS_STATUSES . "` AS `ds1`" . "\n" .
                         "    ON (`ds1`.`doc_type`     = `d`.`type`" . "\n" .
                         "      AND `ds1`.`id`         = `d`.`substatus`" . "\n" .
                         "      AND `ds1`.`lang`       = '{$model_lang}'" . "\n" .
                         "      AND `ds1`.`deleted_by` = '0'" . "\n" .
                         "      AND `ds1`.`active`     = '1')";
        $sql['join'][] = "  LEFT JOIN `" . DB_TABLE_DOCUMENTS_CSTM . "` AS `dc3`" . "\n" .
                         "    ON (`dc3`.`model_id` = `d`.`id`" . "\n" .
                         "      AND `dc3`.`var_id` = '{$fields_doc_order[$settings['field_doc_order_product_type']]}')";
        $sql['join'][] = "  LEFT JOIN `" . DB_TABLE_NOMENCLATURES_TYPES_I18N . "` AS `nti18n3`" . "\n" .
                         "    ON (`nti18n3`.`parent_id` = `dc3`.`value`" . "\n" .
                         "      AND `nti18n3`.`lang`    = '{$model_lang}')";
        $sql['join'][] = "  LEFT JOIN `" . DB_TABLE_DOCUMENTS_CSTM . "` AS `dc7`" . "\n" .
                         "    ON (`dc7`.`model_id` = `d`.`id`" . "\n" .
                         "      AND `dc7`.`var_id` = '{$fields_doc_order[$settings['field_doc_order_tm_id']]}')";
        $sql['join'][] = "  LEFT JOIN `" . DB_TABLE_NOMENCLATURES_CSTM . "` AS `nc7`" . "\n" .
                         "    ON (`nc7`.`model_id` = `dc7`.`value`" . "\n" .
                         "      AND `nc7`.`var_id` = '{$field_nom_brand_mark_group}')";
        $sql['join'][] = "  LEFT JOIN `" . DB_TABLE_FIELDS_OPTIONS . "` AS `fo7`" . "\n" .
                         "    ON (`fo7`.`parent_name`     = '{$settings['field_nom_brand_mark_group']}'" . "\n" .
                         "      AND `fo7`.`option_value`  = `nc7`.`value`" . "\n" .
                         "      AND `fo7`.`active_option` = '1'" . "\n" .
                         "      AND `fo7`.`lang`          = '{$model_lang}')";
        $sql['join'][] = "  LEFT JOIN `" . DB_TABLE_DOCUMENTS_CSTM . "` AS `dc4`" . "\n" .
                         "    ON (`dc4`.`model_id` = `d`.`id`" . "\n" .
                         "      AND `dc4`.`var_id` = '{$fields_doc_order[$settings['field_doc_order_tm_name']]}')";
        $sql['join'][] = "  LEFT JOIN `" . DB_TABLE_DOCUMENTS_CSTM . "` AS `dc12`" . "\n" .
                         "    ON (`dc12`.`model_id` = `d`.`id`" . "\n" .
                         "      AND `dc12`.`var_id` = '{$fields_doc_order[$settings['field_doc_order_product_name']]}')";
        $sql['join'][] = "  LEFT JOIN `" . DB_TABLE_DOCUMENTS_CSTM . "` AS `dc5`" . "\n" .
                         "    ON (`dc5`.`model_id` = `d`.`id`" . "\n" .
                         "      AND `dc5`.`var_id` = '{$fields_doc_order[$settings['field_doc_order_why_repair_client']]}')";
        $sql['join'][] = "  LEFT JOIN `" . DB_TABLE_DOCUMENTS_CSTM . "` AS `dc8`" . "\n" .
                         "    ON (`dc8`.`model_id` = `d`.`id`" . "\n" .
                         "      AND `dc8`.`var_id` = '{$fields_doc_order[$settings['field_doc_order_why_repair_priemna']]}')";
        $sql['join'][] = "  LEFT JOIN `" . DB_TABLE_DOCUMENTS_CSTM . "` AS `dc6`" . "\n" .
                         "    ON (`dc6`.`model_id` = `d`.`id`" . "\n" .
                         "      AND `dc6`.`var_id` = '{$fields_doc_order[$settings['field_doc_order_type_repair']]}')";
        $sql['join'][] = "  LEFT JOIN `" . DB_TABLE_CUSTOMERS_I18N . "` AS `ci18n`" . "\n" .
                         "    ON (`ci18n`.`parent_id` = `d`.`customer`" . "\n" .
                         "      AND `ci18n`.`lang`    = '{$model_lang}')";
        $sql['join'][] = "  LEFT JOIN `" . DB_TABLE_DOCUMENTS_CSTM . "` AS `dc9`" . "\n" .
                         "    ON (`dc9`.`model_id` = `d`.`id`" . "\n" .
                         "      AND `dc9`.`var_id` = '{$fields_doc_order[$settings['field_doc_order_kind_tech_id']]}')";
        $sql['join'][] = "  LEFT JOIN `" . DB_TABLE_DOCUMENTS_CSTM . "` AS `dc10`" . "\n" .
                         "    ON (`dc10`.`model_id` = `d`.`id`" . "\n" .
                         "      AND `dc10`.`var_id` = '{$fields_doc_order[$settings['field_doc_order_kind_tech_name']]}')";
        $sql['join'][] = "  LEFT JOIN `" . DB_TABLE_DOCUMENTS_ASSIGNMENTS . "` AS `da`" . "\n" .
                         "    ON (`da`.`parent_id`          = `d`.`id`" . "\n" .
                         "      AND `da`.`assignments_type` = '{$settings['order_technician_assignment_type']}')";
        $sql['join'][] = "  LEFT JOIN `" . DB_TABLE_DOCUMENTS_HISTORY . "` AS `dh`" . "\n" .
                         "    ON (`dh`.`model`         = 'Document'" . "\n" .
                         "      AND `dh`.`model_id`    = `d`.`id`" . "\n" .
                         "      AND `dh`.`action_type` IN ('status', 'multistatus')" . "\n" .
                         "      AND `dh`.`lang`        = '{$model_lang}')";
        $sql['join'][] = "  JOIN `" . DB_TABLE_DOCUMENTS_AUDIT . "` AS `dau`" . "\n" .
                         "    ON (`dau`.`parent_id`     = `dh`.`h_id`" . "\n" .
                         "      AND `dau`.`field_name`  = 'substatus'" . "\n" .
                         "      AND `dau`.`field_value` = `d`.`substatus`)";

        $sql['where'][] = "`d`.`type`       = '{$settings['doc_type_order']}'";
        $sql['where'][] = "`d`.`deleted_by` = '0'";
        $sql['where'][] = "`d`.`active`     = '1'";
        // TODO: optimization: some of the filtering conditions can be moved to the joins
        if (!empty($filters['order_statuses_group']) && !empty($osg[$filters['order_statuses_group']])) {
            $sql['where'][] = "`d`.`substatus` IN ('" . implode("', '", $osg[$filters['order_statuses_group']]) . "')";
        } else if (!empty($filters['order_statuses_group'])) {
            $sql['where'][] = "`d`.`substatus` = '{$filters['order_statuses_group']}'";
        } else {
            $sql['where'][] = "`d`.`substatus` IN ('" . implode("', '", $osg['all']) . "')";
        }
        if (!empty($filters['equipment_group'])) {
            $sql['where'][] = "`nc7`.`value` = '{$filters['equipment_group']}'";
        }
        if (!empty($filters['equipment_category'])) {
            $sql['where'][] = "`dc3`.`value` = '{$filters['equipment_category']}'";
        }
        if (!empty($filters['device'])) {
            $sql['where'][] = "`dc9`.`value` = '{$filters['device']}'";
        }
        if (!empty($filters['brand'])) {
            $sql['where'][] = "`dc7`.`value` IN ('" . implode("', '", $filters['brand']) . "')";
        }
        if (!empty($filters['warranty'])) {
            $sql['where'][] = "`dc6`.`value` = '{$filters['warranty']}'";
        }
        if (!empty($filters['period_from'])) {
            $sql['where'][] = "'{$filters['period_from']}' <= DATE(`dc2`.`value`)";
        }
        if (!empty($filters['period_to'])) {
            // TODO: try to move this filter from HAVING to WHERE
            //       or automatically put the substatus_modified in the SELECT clause when building the query in buildSQLQuery()
            $sql['having'][] = "DATE(`substatus_modified`) <= '{$filters['period_to']}'";
        }
        if (!empty($filters['period_added_from'])) {
            $sql['where'][] = "'{$filters['period_added_from']}' <= DATE(`d`.`added`)";
        }
        if (!empty($filters['period_added_to'])) {
            $sql['where'][] = "DATE(`d`.`added`) <= '{$filters['period_added_to']}'";
        }
        if (!empty($filters['problem_client'])) {
            $sql['where'][] = "`dc5`.`value` IN ('" . implode("', '", $filters['problem_client']) . "')";
        }
        if (!empty($filters['problem_parlor'])) {
            $sql['where'][] = "`dc8`.`value` IN ('" . implode("', '", $filters['problem_parlor']) . "')";
        }
        if (!empty($filters['technician'])) {
            $sql['where'][] = "`da`.`assigned_to` = '{$filters['technician']}'";
        }

        $sql['group_by'][] = "`d`.`id`";

        if (!in_array($filters['table'], array('repairs', 'trademark', 'equipment', 'client')) && !empty($filters['limit'])) {
            $sql['limit'] = $filters['limit'];
        }

        if ($filters['table'] == 'trademark') {
            $sql_tmp = $sql;
            $sql_tmp['select'] = array_intersect_key($sql_tmp['select'], array_flip(array('tm_id', 'tm_name', 'product_name', 'equipment_id', 'equipment_name', 'substatus_modified')));
            $query = self::buildSQLQuery($sql_tmp);
            $query = "
                SELECT SQL_CALC_FOUND_ROWS `tm_id`, `tm_name`, `product_name`, `equipment_id`, `equipment_name`, COUNT(*) AS `orders_count`
                  FROM (
                    {$query}
                  ) AS `tmp`
                  GROUP BY `tm_id`, `product_name`, `equipment_id`
                  ORDER BY `orders_count` DESC" .
                  (!empty($filters['limit']) ? "
                  LIMIT {$filters['limit']}" : '');
            $final_results['trademark'] = $db->GetAll($query);

            if (empty($final_results['trademark'])) {
                return self::exitReport($registry, $filters, $final_results, $total, false, 'error_reports_no_results');
            }

            if (!empty($filters['limit'])) {
                $total = $registry['db']->GetOne('SELECT FOUND_ROWS()');
            } else {
                $total = count($final_results['trademark']);
            }

            /*
             * Charts
             */
            if (!empty($filters['period_added_from']) && !empty($filters['period_added_to'])) {
                $chart = new Chart($registry, 'line');
                $sql_tmp = $sql;
                $sql_tmp['select']['order_added'] = 'd.added AS order_added';
                unset($sql_tmp['limit']);
                $query = self::buildSQLQuery($sql_tmp);
                $query = "SELECT DATE(order_added), COUNT(*) AS `count` FROM ({$query}) AS tmp GROUP BY DATE(order_added)";
                $orders_by_day = $db->GetAssoc($query);
                $months = Dropdown::getMonthsNames();
                $months_names = array();
                $months_names_short = array();
                foreach ($months as $month) {
                    $months_names[] = $month['label'];
                    $month_name_short = mb_substr($month['label'], 0, 3, mb_detect_encoding($month['label']));
                    $months_names_short[] = $month_name_short . (mb_strlen($month_name_short, mb_detect_encoding($month_name_short)) != mb_strlen($month['label'], mb_detect_encoding($month['label'])) ? '.' : '');
                }
                $options = array(
                    'lang' => array(
                        'months' => $months_names,
                        'shortMonths' => $months_names_short
                    ),
                );
                $chart->prepareOptions($options);
                $chart_params = array(
                    'chart' => array(
//                         'type' => 'spline',
                        'width' => '1000',
                        'height' => '300',
                        'style' => array(
                            'marginTop' => '15px',
                            'marginBottom' => '15px'
                        ),
                        'spacingRight' => 50,
                    ),
                    'title' => array(
                        'text' => $registry['translater']->translate('reports_graphics_orders_for_a_day')
                    ),
                    'xAxis' => array(
//                         'type' => 'datetime',
    //                     'dateTimeLabelFormats' => array(
    //                         'day' => '%d.%m.%Y',
    //                         'month' => '%b %Y',
    //                         'year' => '%Y'
    //                     ),
                        'title' => array(
                            'text' => $registry['translater']->translate('reports_graphics_date')
                        ),
                        'labels' => array(
                            'formatter' => 'function() { return Highcharts.dateFormat(\'%d %b\', new Date(this.value)); }'
                        ),
                        'categories' => array_keys($orders_by_day)
                    ),
                    'yAxis' => array(
                        'title' => array(
                            'text' => $registry['translater']->translate('reports_graphics_count')
                        ),
                        'min' => 0,
        //                 'plotLines' => array(
        //                     'value' => 0,
        //                     'width' => 1,
        //                     'color' => '#808080'
        //                 ),
//                         'dateTimeLabelFormats' => array(
//                             'day' => '%d.%m.%Y',
//                             'month' => '%b %Y',
//                             'year' => '%Y'
//                         ),
                    ),
                    'tooltip' => array(
                        'formatter' => 'function() { return Highcharts.dateFormat(\'%d.%m.%Y\', new Date(this.x)) + \', \' + this.y; }',
//                         'formatter' => 'function() { return this.x + \', \' + this.y; }',
//                         'formatter' => 'function() { return Highcharts.dateFormat(\'%d.%m.%Y\', this.x) + \', \' + this.y; }',
    //                     'xDateFormat' => '%d.%m.%Y',
    //                     'valueSuffix' => 'бр.',
    //                     'pointFormat' => 'aaa'
                    ),
                    'legend' => array(
                        'enabled' => false
                    ),
                    'plotOptions' => array(
                        'line' => array(
                            'dataLabels' => array(
                                'enabled' => false
                            ),
//                             'tooltip' => array(
//                                 'formatter' => 'function () {return \'aaa\';}',
//     //                             'visible' => false,
//     //                             'dateTimeLabelFormats' => array(
//     //                                 'day' => '%d.%m.%Y',
//     //                                 'month' => '%b %Y',
//     //                                 'year' => '%Y'
//     //                             ),
//     //                             'pointFormat' => 'ddd',
//     //                             'headerFormat' => 'ddd',
//                             )
                        ),
//                         'series' => array(
//                             'pointStart' => strtotime($filters['period_added_from']) * 1000,
//                             'pointInterval' => 86400000// one day in milliseconds
//                         )
                    ),
                    'series' => array(
                        array(
                            'data' => array_values(array_map('intval', $orders_by_day)),
    //                         'pointStart' => strtotime($filters['period_added_from']) * 1000,
    //                         'pointInterval' => 86400000// one day in milliseconds
                        )
                    )
                );
//                 foreach ($orders_by_day as $date => $orders_count) {
//                     $chart_params['series'][0]['data'][] = array(strtotime($date)*1000, intval($orders_count));
//                 }
                if ($chart->prepareChart($chart_params)) {
                    $final_results['additional_options']['chart'] = $chart;
                }
            }
        } else if ($filters['table'] == 'equipment') {
            $sql_tmp = $sql;
            $sql_tmp['select'] = array_intersect_key($sql_tmp['select'], array_flip(array('equipment_id', 'equipment_name', 'substatus_modified')));
            $query = self::buildSQLQuery($sql_tmp);
            $query = "
                SELECT SQL_CALC_FOUND_ROWS `equipment_id`, `equipment_name`, COUNT(*) AS `orders_count`
                  FROM (
                    {$query}
                  ) AS `tmp`
                  GROUP BY `equipment_id`
                  ORDER BY `orders_count` DESC" .
                  (!empty($filters['limit']) ? "
                  LIMIT {$filters['limit']}" : '');
            $final_results['equipment'] = $db->GetAll($query);

            if (empty($final_results['equipment'])) {
                return self::exitReport($registry, $filters, $final_results, $total, false, 'error_reports_no_results');
            }

            if (!empty($filters['limit'])) {
                $total = $registry['db']->GetOne('SELECT FOUND_ROWS()');
            } else {
                $total = count($final_results['equipment']);
            }

            /*
             * Charts
             */
            if (!empty($filters['brand'])) {
                $chart = new Chart($registry, 'pie');
                $sql_tmp = $sql;
                unset($sql_tmp['limit']);
                $query = self::buildSQLQuery($sql_tmp);
                $query = "SELECT tm_id, tm_name, product_name, COUNT(*) AS `count` FROM ({$query}) AS tmp GROUP BY tm_id, product_name";
                $equipment_orders = $db->GetAll($query);
                $series = array(
                    0 => array(
                        'name' => $registry['translater']->translate('reports_graphics_trademark'),
                        'data' => array(),
                        'size' => '60%',
                        'showInLegend' => true,
                        'dataLabels' => array(
                            // display labels inside the pie
                            'distance' => 85,
                            'enabled'  => true,
                            // display labels only for slices larger than 5%
                            'formatter' => 'function() { return this.percentage >= 5 ? (this.point.name ? this.point.name + \', \' : \'\') + Highcharts.numberFormat(this.percentage, 2) + \'% (\' + this.point.y + \')\' : null; }',
                        )
                    ),
                    1 => array(
                        'name' => $registry['translater']->translate('reports_graphics_model'),
                        'data' => array(),
                        'innerSize' => '60%',
                        'dataLabels' => array(
                            'enabled'  => false
                        )
                    )
                );
                $colors = array('#AFD8F8', '#F6BD0F', '#8BBA00', '#FF8E46', '#008E8E', '#D64646', '#8E468E', '#588526', '#B3AA00', '#008ED6', '#9D080D', '#A186BE', '#CC6600', '#FDC689', '#ABA000', '#F26D7D', '#FFF200', '#0054A6', '#F7941C', '#CC3300', '#006600', '#663300', '#6DCFF6');
                foreach ($equipment_orders as $eo) {
                    if (!isset($series[0]['data'][$eo['tm_id']])) {
                        $color = array_shift($colors);
                        $series[0]['data'][$eo['tm_id']] = array(
                            'name' => $eo['tm_name'],
                            'y' => intval($eo['count']),
                            'color' => $color
                        );
                        $colors[] = $color;
                    } else {
                        $series[0]['data'][$eo['tm_id']]['y'] += $eo['count'];
                    }
                    $product_key = "{$eo['tm_id']}{$eo['product_name']}";
                    if (!isset($series[1]['data'][$product_key])) {
                        $rgb = array_map('hexdec', str_split(substr($color, 1), 2));
                        $a = 0.8;
                        $series[1]['data'][$product_key] = array(
                            'name' => $eo['product_name'],
                            'y' => intval($eo['count']),
                            'color' => sprintf('rgba(%d, %d, %d, %.2f)', $rgb[0], $rgb[1], $rgb[2], $a)
                        );
                    } else {//This should not happen, but it's done in any case
                        $series[1]['data'][$product_key]['y'] += $eo['count'];
                    }
                }
                $series[0]['data'] = array_values($series[0]['data']);
                $series[1]['data'] = array_values($series[1]['data']);
                $chart_params = array(
                    'chart' => array(
                        'width' => '700',
                        'height' => '500',
                    ),
                    'title' => array(
                        'text' => $registry['translater']->translate('reports_graphics_trademarks_and_models')
                    ),
                    'tooltip' => array(
                        'formatter' => 'function() { return this.point.series.name + \': \' + (this.point.name ? this.point.name + \', \' : \'\') + Highcharts.numberFormat(this.percentage, 2) + \'% (\' + this.point.y + \')\'; }'
                    ),
                    'series' => $series
                );
                if ($chart->prepareChart($chart_params)) {
                    $final_results['additional_options']['chart'] = $chart;
                }
            }
        } else if ($filters['table'] == 'client') {
            $sql_tmp = $sql;
            $sql_tmp['select'] = array_intersect_key($sql_tmp['select'], array_flip(array('customer_name', 'substatus_modified')));
            $sql_tmp['select']['customer_id'] = '`d`.`customer` AS `customer_id`';
            $query = self::buildSQLQuery($sql_tmp);
            $query = "
                SELECT SQL_CALC_FOUND_ROWS `customer_id`, `customer_name`, COUNT(*) AS `orders_count`
                  FROM (
                    {$query}
                  ) AS `tmp`
                  GROUP BY `customer_id`
                  ORDER BY `orders_count` DESC" .
                  (!empty($filters['limit']) ? "
                  LIMIT {$filters['limit']}" : '');
            $final_results['client'] = $db->GetAll($query);

            if (empty($final_results['client'])) {
                return self::exitReport($registry, $filters, $final_results, $total, false, 'error_reports_no_results');
            }

            if (!empty($filters['limit'])) {
                $total = $registry['db']->GetOne('SELECT FOUND_ROWS()');
            } else {
                $total = count($final_results['client']);
            }
        } else if ($filters['table'] == 'equipment_group') {
            // TODO: optimization: the count of rows can be done with the query for statistical information (this way it might be more optimal)
            // TODO: optimization: some of the parts of the query can be included/excluded depending on the filters
            // TODO: optimization: the building of the query can be separated to: one main building and 3 sub buildings
            // TODO: optimization: when a JOIN is made because of a filter, then the it can be just a JOIN, not a LEFT JOIN and the filtrating conditions can be into the ON clause
            $sql_tmp = $sql;
            $sql_tmp['select_options'][] = 'SQL_CALC_FOUND_ROWS';
            $query = self::buildSQLQuery($sql_tmp);
            $final_results['equipment_group'] = $db->GetAssoc($query);

            if (empty($final_results['equipment_group'])) {
                return self::exitReport($registry, $filters, $final_results, $total, false, 'error_reports_no_results');
            }

            if (!empty($filters['limit'])) {
                $total = $registry['db']->GetOne('SELECT FOUND_ROWS()');
            } else {
                $total = count($final_results['equipment_group']);
            }

            $sql_tmp = $sql;
            // TODO: optimization: the JOIN-s here could be only the onse used for the counting and the onse used for the filtering (i.e. the ones used only for showing data are redundant)
            $sql_tmp['select'] = array(
                $sql_tmp['select']['type_repair'],
                $sql_tmp['select']['substatus'],
                // This is required into the SELECT clause, when filtering by substatus_modified date
                $sql_tmp['select']['substatus_modified']
            );
            unset($sql_tmp['limit']);
            $query = self::buildSQLQuery($sql_tmp);
            $query = "SELECT SUM(`type_repair` = 'repair_garan_yes')                        AS `count_repair_garan_yes`," . "\n" .
                     "    SUM(`type_repair` = 'repair_garan_no')                            AS `count_repair_garan_no`," . "\n" .
                     "    SUM(`substatus` = '{$settings['order_statuses_group_replaced']}') AS `count_replaced`" . "\n" .
                     "  FROM (" . "\n" .
                     $query . "\n" .
                     "  ) AS `tmp`";
            $equipment_group_stat = $db->GetRow($query);

//             // Get the most common trademark
//             $sql_tmp = $sql;
//             // TODO: optimization: the JOIN-s here could be only the onse used for the counting and the onse used for the filtering (i.e. the ones used only for showing data are redundant)
//             $sql_tmp['select'] = array(
//                 $sql_tmp['select']['tm_id'],
//                 $sql_tmp['select']['tm_name'],
//                 // This is required into the SELECT clause, when filtering by substatus_modified date
//                 $sql_tmp['select']['substatus_modified']
//             );
//             unset($sql_tmp['limit']);
//             $query = self::buildSQLQuery($sql_tmp);
//             $query = "
//                 SELECT `tm_id` AS `id`,
//                     `tm_name`  AS `name`,
//                     COUNT(*)   AS `count`
//                   FROM (
//                     {$query}
//                   ) AS `tmp`
//                   GROUP BY `tm_id`
//                   ORDER BY `count` DESC";
//             $equipment_group_stat['most_common_tm'] = $db->GetAssoc($query);

            $final_results['additional_options']['equipment_group_stat'] = $equipment_group_stat;

            // TODO: this can be optimized depending on the selected value for the order_statuses_group filter
            $order_statuses_groups = array(
                'order_statuses_group_unfinished'             => $registry['translater']->translate('reports_order_statuses_group_unfinished'),
                'order_statuses_group_successfully_repaird'   => $registry['translater']->translate('reports_order_statuses_group_successfully_repaird'),
                'order_statuses_group_unsuccessfully_repaird' => $registry['translater']->translate('reports_order_statuses_group_unsuccessfully_repaird'),
                'order_statuses_group_replaced'               => $registry['translater']->translate('reports_order_statuses_group_replaced')
            );
            $final_results['additional_options']['order_statuses_groups'] = array();
            foreach ($order_statuses_groups as $os_group_key => $os_group_name) {
                $os_group_substatuses = preg_split('/\s*,\s*/', trim($settings[$os_group_key]));
                foreach ($os_group_substatuses as $os_group_substatus) {
                    $final_results['additional_options']['order_statuses_groups'][$os_group_substatus] = $os_group_name;
                }
            }

            // TODO: this can be done using: Report_Filters::getFieldOptions()
            $query = "
                SELECT `option_value`, `label`
                  FROM `" . DB_TABLE_FIELDS_OPTIONS . "`
                  WHERE `parent_name`   = '{$settings['field_doc_order_why_repair_client']}'
                    AND `active_option` = '1'
                    AND `lang`          = '{$model_lang}'";
            $final_results['additional_options']['why_repair_client'] = $db->GetAssoc($query);

            // TODO: this can be done using: Report_Filters::getFieldOptions()
            $query = "
                SELECT `option_value`, `label`
                  FROM `" . DB_TABLE_FIELDS_OPTIONS . "`
                  WHERE `parent_name`   = '{$settings['field_doc_order_why_repair_priemna']}'
                    AND `active_option` = '1'
                    AND `lang`          = '{$model_lang}'";
            $final_results['additional_options']['why_repair_priemna'] = $db->GetAssoc($query);
        } else if ($filters['table'] == 'incomes_expenses') {
            // Prepare default empty values for this results
            $final_results['additional_options']['incomes_expenses_totals'] = array(
                'incomes_no_vat'    => 0,
                'incomes_with_vat'  => 0,
                'expenses_no_vat'   => 0,
                'expenses_with_vat' => 0
            );
            $final_results['type_incomes_expenses'] = array(
                'nom_id'         => '',
                'nom_name'       => '',
                'total_quantity' => '',
                'total_subtotal' => '',
                'average_price'  => ''
            );

            // Get the filtered documents
            $sql_tmp = $sql;
            $sql_tmp['select'] = array(
                $sql_tmp['select']['order_id'],
                // This is required into the SELECT clause, when filtering by substatus_modified date
                $sql_tmp['select']['substatus_modified']
            );
            unset($sql_tmp['limit']);
            $query = self::buildSQLQuery($sql_tmp);
            $incomes_expenses_all_ids = $db->GetCol($query);
            if (empty($incomes_expenses_all_ids)) {
                return self::exitReport($registry, $filters, $final_results, $total, false, 'error_reports_no_results');
            }

            // If any documents are found
            if (!empty($incomes_expenses_all_ids)) {
                // Get the incomes and expenses by VAT
                $query = "
                    SELECT IF(
                          dc.value = 0,
                          'incomes_no_vat',
                          'incomes_with_vat')     AS vat,
                        SUM(gd.subtotal_with_vat) AS total
                      FROM " . DB_TABLE_GT2_DETAILS . " AS gd
                      JOIN " . DB_TABLE_GT2_DETAILS_I18N . " AS gdi
                        ON (gd.model                = 'Document'
                          AND gd.model_id           IN (" . implode(',', $incomes_expenses_all_ids) . ")
                          AND (gd.subtotal          != '0'
                            OR gd.subtotal_with_vat != '0')" .
                          (empty($filters['incomes_expenses_period_from']) ? '' : "
                          AND '{$filters['incomes_expenses_period_from']}' <= DATE(gd.added)") .
                          (empty($filters['incomes_expenses_period_to']) ? '' : "
                          AND DATE(gd.added) <= '{$filters['incomes_expenses_period_to']}'") . "
                          AND gdi.parent_id         = gd.id
                          AND gdi.lang              = '{$model_lang}')
                      JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc
                        ON (dc.var_id     = '{$fields_doc_order[$settings['field_doc_order_total_vat_rate']]}'
                          AND dc.model_id = gd.model_id
                          AND dc.num      = 1
                          AND dc.value    IN (0, 20))
                      JOIN " . DB_TABLE_NOMENCLATURES . " AS n
                        ON (n.id     = gdi.{$settings['field_doc_order_gt2_incomes_expenses']}
                          AND n.type = '{$settings['nom_type_income']}'" .
                          (empty($filters['income_expense_type']) ? '' : "
                          AND n.id   = '{$filters['income_expense_type']}'") . ")
                      GROUP BY dc.value";
                $incomes_expenses_totals = $db->GetAssoc($query);
                $query = "
                    SELECT IF(
                          dc3.value = 'null',
                          'expenses_no_vat',
                          'expenses_with_vat') AS vat,
                        SUM(dc2.value)         AS total
                      FROM " . DB_TABLE_DOCUMENTS_CSTM . " AS dc1
                      JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc2
                        ON (dc1.var_id        = '{$fields_doc_order[$settings['field_doc_order_ex_free_text3']]}'
                          AND dc1.model_id    IN (" . implode(',', $incomes_expenses_all_ids) . ")
                          AND dc1.value       != ''" .
                          (empty($filters['incomes_expenses_period_from']) ? '' : "
                          AND '{$filters['incomes_expenses_period_from']}' <= DATE(dc1.added)") .
                          (empty($filters['incomes_expenses_period_to']) ? '' : "
                          AND DATE(dc1.added) <= '{$filters['incomes_expenses_period_to']}'") . "
                          AND dc2.var_id      = '{$fields_doc_order[$settings['field_doc_order_ex_subtotal_with_vat']]}'
                          AND dc2.model_id    = dc1.model_id
                          AND dc2.num         = dc1.num)
                      JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc3
                        ON (dc3.var_id     = '{$fields_doc_order[$settings['field_doc_order_ex_vat']]}'
                          AND dc3.model_id = dc1.model_id
                          AND dc3.num      = dc1.num
                          AND dc3.value IN ('null', 20))
                      JOIN " . DB_TABLE_NOMENCLATURES . " AS n
                        ON (n.id     = dc1.value
                          AND n.type = '{$settings['nom_type_expense']}'" .
                          (empty($filters['income_expense_type']) ? '' : "
                          AND n.id   = '{$filters['income_expense_type']}'") . ")
                      GROUP BY dc3.value";
                $incomes_expenses_totals = array_merge($incomes_expenses_totals, $db->GetAssoc($query));
                foreach ($final_results['additional_options']['incomes_expenses_totals'] as $iet_key => $iet_value) {
                    if (isset($incomes_expenses_totals[$iet_key])) {
                        $final_results['additional_options']['incomes_expenses_totals'][$iet_key] = $incomes_expenses_totals[$iet_key];
                    }
                }

                // Get the incomes and expenses by type
                $query = "
                    SELECT
                        n.id                                AS nom_id,
                        ni.name                             AS nom_name,
                        SUM(gd.quantity)                    AS total_quantity,
                        SUM(gd.subtotal)                    AS total_subtotal,
                        SUM(gd.subtotal) / SUM(gd.quantity) AS average_price
                      FROM " . DB_TABLE_GT2_DETAILS . " AS gd
                      JOIN " . DB_TABLE_GT2_DETAILS_I18N . " AS gdi
                        ON (gd.model        = 'Document'
                          AND gd.model_id   IN (" . implode(',', $incomes_expenses_all_ids) . ")
                          AND gd.subtotal   != 0
                          AND gd.quantity   != 0" .
                          (empty($filters['incomes_expenses_period_from']) ? '' : "
                          AND '{$filters['incomes_expenses_period_from']}' <= DATE(gd.added)") .
                          (empty($filters['incomes_expenses_period_to']) ? '' : "
                          AND DATE(gd.added) <= '{$filters['incomes_expenses_period_to']}'") . "
                          AND gdi.parent_id = gd.id
                          AND gdi.lang      = '{$model_lang}')
                      JOIN " . DB_TABLE_NOMENCLATURES . " AS n
                        ON (n.id     = gdi.{$settings['field_doc_order_gt2_incomes_expenses']}
                          AND n.type = '{$settings['nom_type_income']}'" .
                          (empty($filters['income_expense_type']) ? '' : "
                          AND n.id   = '{$filters['income_expense_type']}'") . ")
                      JOIN " . DB_TABLE_NOMENCLATURES_I18N . " AS ni
                        ON (ni.parent_id = n.id
                          AND ni.lang    = '{$model_lang}')
                      GROUP BY n.id";
                $type_incomes_expenses = $db->GetAssoc($query);
                $query = "
                    SELECT
                        n.id                                        AS nom_id,
                        ni.name                                     AS nom_name,
                        SUM(dc2.value)                              AS total_quantity,
                        SUM(dc2.value * dc3.value)                  AS total_subtotal,
                        SUM(dc2.value * dc3.value) / SUM(dc2.value) AS average_price
                      FROM " . DB_TABLE_DOCUMENTS_CSTM . " AS dc1
                      JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc2
                        ON (dc1.var_id     = '{$fields_doc_order[$settings['field_doc_order_ex_free_text3']]}'
                          AND dc1.model_id IN (" . implode(',', $incomes_expenses_all_ids) . ")
                          AND dc1.value    != ''" .
                          (empty($filters['incomes_expenses_period_from']) ? '' : "
                          AND '{$filters['incomes_expenses_period_from']}' <= DATE(dc1.added)") .
                          (empty($filters['incomes_expenses_period_to']) ? '' : "
                          AND DATE(dc1.added) <= '{$filters['incomes_expenses_period_to']}'") . "
                          AND dc2.var_id   = '{$fields_doc_order[$settings['field_doc_order_ex_quantity']]}'
                          AND dc2.model_id = dc1.model_id
                          AND dc2.num      = dc1.num
                          AND dc2.value    != '')
                      JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc3
                        ON (dc3.var_id     = '{$fields_doc_order[$settings['field_doc_order_ex_price']]}'
                          AND dc3.model_id = dc1.model_id
                          AND dc3.num      = dc1.num
                          AND dc3.value    != '')
                      JOIN " . DB_TABLE_NOMENCLATURES . " AS n
                        ON (n.id     = dc1.value
                          AND n.type = '{$settings['nom_type_expense']}'" .
                          (empty($filters['income_expense_type']) ? '' : "
                          AND n.id   = '{$filters['income_expense_type']}'") . ")
                      JOIN " . DB_TABLE_NOMENCLATURES_I18N . " AS ni
                        ON (ni.parent_id = n.id
                          AND ni.lang    = '{$model_lang}')
                      GROUP BY n.id";
                $type_incomes_expenses = array_merge($type_incomes_expenses, $db->GetAssoc($query));
                $final_results['type_incomes_expenses'] = $type_incomes_expenses;
            }
        } else if ($filters['table'] == 'repairs') {
            $sql_tmp = $sql;
            $query = self::buildSQLQuery($sql_tmp);
            $final_results['repairs'] = $db->GetAssoc($query);

            if (empty($final_results['repairs'])) {
                return self::exitReport($registry, $filters, $final_results, $total, false, 'error_reports_no_results');
            }

            $repairs = array(
                'total_orders'                        => array(
                    'unfinished'             => 0,
                    'successfully_repaird'   => 0,
                    'unsuccessfully_repaird' => 0,
                    'replaced'               => 0
                ),
                'most' => array(
                    'shortest_successfully_repaird'   => array(
                        'order_id'    => '',
                        'days'        => '',
                        'technicians' => '',
                        'device'      => ''
                    ),
                    'longest_successfully_repaird'    => array(
                        'order_id'    => '',
                        'days'        => '',
                        'technicians' => '',
                        'device'      => ''
                    ),
                    'shortest_unsuccessfully_repaird' => array(
                        'order_id'    => '',
                        'days'        => '',
                        'technicians' => '',
                        'device'      => ''
                    ),
                    'longest_unsuccessfully_repaird'  => array(
                        'order_id'    => '',
                        'days'        => '',
                        'technicians' => '',
                        'device'      => ''
                    )
                ),
                'technicians' => array()
            );

            $technicians_orders = array(
                'unfinished'             => 0,
                'successfully_repaird'   => 0,
                'unsuccessfully_repaird' => 0
            );

            $query = "
                SELECT `u`.`id`, TRIM(CONCAT(`ui18n`.`firstname`, ' ', `ui18n`.`lastname`))
                  FROM `" . DB_TABLE_USERS . "` AS `u`
                  JOIN `" . DB_TABLE_USERS_I18N . "` AS `ui18n`
                    ON (`ui18n`.`parent_id` = `u`.`id`
                      AND `ui18n`.`lang`    = '{$model_lang}')
                  JOIN `" . DB_TABLE_USERS_GROUPS . "` AS `ug`
                    ON (`ug`.`parent_id`  = `u`.`id`
                      AND `ug`.`group_id` = '{$settings['users_group_technician']}')
                  WHERE `u`.`active`     = '1'
                    AND `u`.`deleted_by` = '0'
                    AND `u`.`hidden`     = '0'";
            $users = $db->GetAssoc($query);

            require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';

            $categories_data = array();
            $series_data = array();

            foreach ($final_results['repairs'] as $order_id => $record) {
                $orders_group = '';

                if (in_array($record['substatus'], $osg['unfinished'])) {
                    $orders_group = 'unfinished';
                } else if (in_array($record['substatus'], $osg['successfully_repaird'])) {
                    $orders_group = 'successfully_repaird';
                } else if (in_array($record['substatus'], $osg['unsuccessfully_repaird'])) {
                    $orders_group = 'unsuccessfully_repaird';
                } else if (in_array($record['substatus'], $osg['replaced'])) {
                    $orders_group = 'replaced';
                }

                if (!empty($orders_group)) {
                    $repairs['total_orders'][$orders_group]++;

                    if (!isset($categories_data[$orders_group])) {
                        $categories_data[$orders_group] = array(
                            'name' => $registry['translater']->translate("reports_technicians_{$orders_group}_chart"),
                            'count' => 0
                        );
                    }
                    $categories_data[$orders_group]['count']++;

                    $repair_period = '';

                    $technicians = explode('|', $record['technicians_ids']);
                    $technicians = array_intersect_key($users, array_flip($technicians));

                    if (in_array($orders_group, array('successfully_repaird', 'unsuccessfully_repaird'))) {
                        // TODO: check if no start or end date
                        // TODO: optimization: this can be done by a MySQL function
                        // TODO: optimization: too slow - should be optimized
                        // TODO: optimization: if this is not optimized using SQL query, it can be optimized by replacing the General::strftime() with something which will just cut the date part of the datetime string
                        $repair_period = Calendars_Calendar::getWorkingDays($registry, General::strftime('%Y-%m-%d', $record['date_acceptance']), General::strftime('%Y-%m-%d', $record['substatus_modified']));

                        if ($repairs['most']['shortest_' . $orders_group]['days'] == '' || $repair_period < $repairs['most']['shortest_' . $orders_group]['days']) {
                            $repairs['most']['shortest_' . $orders_group]['order_id']       = $order_id;
                            $repairs['most']['shortest_' . $orders_group]['days']           = $repair_period;
                            $repairs['most']['shortest_' . $orders_group]['technicians']    = $technicians;
                            $repairs['most']['shortest_' . $orders_group]['equipment_name'] = $record['equipment_name'];
                        }
                        if ($repairs['most']['longest_' . $orders_group]['days'] == '' || $repairs['most']['longest_' . $orders_group]['days'] < $repair_period) {
                            $repairs['most']['longest_' . $orders_group]['order_id']       = $order_id;
                            $repairs['most']['longest_' . $orders_group]['days']           = $repair_period;
                            $repairs['most']['longest_' . $orders_group]['technicians']    = $technicians;
                            $repairs['most']['longest_' . $orders_group]['equipment_name'] = $record['equipment_name'];
                        }
                    }

                    if (array_key_exists($orders_group, $technicians_orders)) {
                        foreach ($technicians as $technician_id => $technician_name) {
                            if (!isset($repairs['technicians'][$technician_id])) {
                                $repairs['technicians'][$technician_id] = $technicians_orders;
                                $repairs['technicians'][$technician_id]['name'] = $technician_name;
                                $repairs['technicians'][$technician_id][$orders_group . '_equipments'] = array();
                            }
                            $repairs['technicians'][$technician_id][$orders_group]++;
                            if (!isset($series_data[$technician_id])) {
                                $series_data[$technician_id] = array(
                                    'name' => $technician_name,
                                    'count' => array()
                                );
                            }
                            if (!isset($series_data[$technician_id]['count'][$orders_group])) {
                                $series_data[$technician_id]['count'][$orders_group] = 0;
                            }
                            $series_data[$technician_id]['count'][$orders_group]++;

                            if (!isset($repairs['technicians'][$technician_id][$orders_group . '_equipments'][$record['equipment_id']])) {
                                $repairs['technicians'][$technician_id][$orders_group . '_equipments'][$record['equipment_id']] = array(
                                    'name'         => $record['equipment_name'],
                                    'orders_count' => 0
                                );
                            }
                            $repairs['technicians'][$technician_id][$orders_group . '_equipments'][$record['equipment_id']]['orders_count']++;
                        }
                    }
                }
            }

            uasort(
                $repairs['technicians'],
                function ($user1, $user2) {
                    return strcmp($user1['name'], $user2['name']);
                }
            );

            // Sort the equipments for each technician
            $technicians_orders_names = array_keys($technicians_orders);
            foreach ($repairs['technicians'] as $t_id => $t) {
                foreach ($technicians_orders_names as $ton) {
                    if (isset($t[$ton . '_equipments'])) {
                        // Use usort because the keys should be reset (for the needs of the template)
                        usort(
                            $t[$ton . '_equipments'],
                            function ($a, $b) {
                                return ($a['orders_count'] < $b['orders_count']) ? 1 : -1;
                            }
                        );
                    }
                }
                $repairs['technicians'][$t_id] = $t;
            }
            $final_results['repairs'] = $repairs;

//             /*
//              * Charts
//              */
//             $chart = new Chart($registry, 'pie');
//             $series = array(
//                 0 => array(
//                     'data' => array(),
//                     'size' => '60%',
//                     'showInLegend' => true,
//                     'dataLabels' => array(
//                         // display labels inside the pie
//                         'distance' => 85,
//                         'enabled'  => true,
//                         // display labels only for slices larger than 5%
//                         'formatter' => 'function() { return this.percentage >= 5 ? (this.point.name ? this.point.name + \', \' : \'\') + Highcharts.numberFormat(this.percentage, 2) + \'% (\' + this.point.y + \')\' : null; }',
//                     )
//                 ),
//                 1 => array(
//                     'data' => array(),
//                     'innerSize' => '60%',
//                     'dataLabels' => array(
//                         'enabled'  => false
//                     )
//                 )
//             );
//             $colors = array('#AFD8F8', '#F6BD0F', '#8BBA00', '#FF8E46', '#008E8E', '#D64646', '#8E468E', '#588526', '#B3AA00', '#008ED6', '#9D080D', '#A186BE', '#CC6600', '#FDC689', '#ABA000', '#F26D7D', '#FFF200', '#0054A6', '#F7941C', '#CC3300', '#006600', '#663300', '#6DCFF6');
//             foreach ($chart_data as $group_key => $group) {
//                 $color = array_shift($colors);
//                 $series[0]['data'][] = array(
//                     'name' => $registry['translater']->translate("reports_technicians_{$group_key}_chart"),
//                     'y' => intval($group['count']),
//                     'color' => $color
//                 );
//                 $colors[] = $color;
//                 foreach ($group['technicians'] as $technician_id => $technician) {
//                     $rgb = array_map('hexdec', str_split(substr($color, 1), 2));
//                     $a = 0.8;
//                     $series[1]['data'][] = array(
//                         'name' => $technician['name'],
//                         'y' => $technician['count'],
//                         'color' => sprintf('rgba(%d, %d, %d, %.2f)', $rgb[0], $rgb[1], $rgb[2], $a)
//                     );
//                 }
//             }
//             $chart_params = array(
//                 'chart' => array(
//                     'width' => '810',
//                     'height' => '500',
//                     'style' => array(
//                         'marginTop' => '15px',
//                         'marginBottom' => '15px'
//                     ),
//                 ),
//                 'title' => array(
//                     'text' => 'По техник'
//                 ),
//                 'tooltip' => array(
//                     'formatter' => 'function() { return (this.point.name ? this.point.name + \': \' : \'\') + Highcharts.numberFormat(this.percentage, 2) + \'% (\' + this.point.y + \')\'; }'
//                 ),
//                 'series' => $series
//             );
//             if ($chart->prepareChart($chart_params)) {
//                 $final_results['additional_options']['chart'] = $chart;
//             }

            /*
             * Charts
             */
            $chart = new Chart($registry, 'column');
            $colors = array('#AFD8F8', '#F6BD0F', '#8BBA00', '#FF8E46', '#008E8E', '#D64646', '#8E468E', '#588526', '#B3AA00', '#008ED6', '#9D080D', '#A186BE', '#CC6600', '#FDC689', '#ABA000', '#F26D7D', '#FFF200', '#0054A6', '#F7941C', '#CC3300', '#006600', '#663300', '#6DCFF6');
            $series = array();
            $categories = array();
            $categories_keys = array_keys($technicians_orders);
            foreach ($categories_keys as $group_key) {
                $categories[] = $registry['translater']->translate("reports_technicians_{$group_key}_chart") . ' (' . (isset($categories_data[$group_key]['count']) ? $categories_data[$group_key]['count'] : 0) . ')';
            }
            foreach ($series_data as $technician_id => $technician) {
                $serie = array(
                    'name' => $technician['name'],
                    'data' => array(),
                );
                // rotate colors
                $colors[] = $serie['color'] = array_shift($colors);
                foreach (array_keys($technicians_orders) as $group_key) {
                    $serie['data'][] = (isset($technician['count'][$group_key]) ? $technician['count'][$group_key] : 0);
                }
                $series[] = $serie;
            }
            $chart_params = array(
                'colors' => array(''),
                'chart' => array(
                    'width' => '810',
                    'height' => '500',
                    'style' => array(
                        'marginTop' => '15px',
                        'marginBottom' => '15px'
                    ),
                ),
                'title' => array(
                    'text' => $registry['translater']->translate('reports_graphics_by_technician')
                ),
                'tooltip' => array(
                    'formatter' => 'function() { return this.series.name + ": " + this.point.y; }'
                ),
                'xAxis' => array(
                    'categories' => $categories
                ),
                'yAxis' => array(
                    'title' => array(
                        'text' => $registry['translater']->translate('reports_graphics_orders')
                    )
                ),
                'plotOptions' => array(
                    'column' => array(
                        'pointWidth' => 20
                    )
                ),
                'series' => $series
            );
            if ($chart->prepareChart($chart_params)) {
                $final_results['additional_options']['chart'] = $chart;
            }
        }

        return self::exitReport($registry, $filters, $final_results, $total, true);
    }

// TODO: the $sql['select'] contains elements such:
//         $sql['select']['order_id']           = "`d`.`id`                                                AS `order_id`";
//       but it might be better if it contains elements such:
//         $sql['select']['order_id']           = "`d`.`id`";
//       and the "AS" clause can be added later using the key of the element
    private static function buildSQLQuery($sql) {
        $query = '';
        if (!empty($sql['select']) && !empty($sql['from'])) {
            $query .= "SELECT ";
            if (!empty($sql['select_options'])) {
                $query .= implode(' ', $sql['select_options']);
            }
            $query .= "\n    " . implode(",\n    ", $sql['select']) . "\n" .
                      "  FROM {$sql['from']}";
            if (!empty($sql['join'])) {
                $query .= "\n" . implode("\n", $sql['join']);
            }
            if (!empty($sql['where'])) {
                $query .= "\n  WHERE " . implode("\n    AND ", $sql['where']);
            }
            if (!empty($sql['group_by'])) {
                $query .= "\n  GROUP BY " . implode(", ", $sql['group_by']);
            }
            if (!empty($sql['having'])) {
                $query .= "\n  HAVING " . implode("\n    AND ", $sql['having']);
            }
            if (!empty($sql['limit'])) {
                $query .= "\n  LIMIT {$sql['limit']}";
            }
        }

// TODO: everywhere the function is called whe should check if the function returns an empty string
        return $query;
    }

    private static function exitReport(&$registry, $filters, $final_results, $total, $success = false, $error = '') {
        if (!$success) {
            $final_results['additional_options']['failed']                  = true;
            $final_results['additional_options']['dont_show_export_button'] = true;
            if (!empty($error)) {
                $final_results['additional_options']['error'] = $registry['translater']->translate($error);
            }
        }

        if (!empty($filters['paginate'])) {
            $results = array($final_results, $total);
        } else {
            $results = $final_results;
        }

        return $results;
    }
}
?>