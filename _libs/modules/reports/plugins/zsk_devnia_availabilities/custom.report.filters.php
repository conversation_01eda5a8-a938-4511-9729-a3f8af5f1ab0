<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE ARTICLE FILTER
            $options_articles = array(
                array(
                    'label'         => $this->i18n('reports_concrete_articles'),
                    'option_value'  => 'concrete_1'
                ),
                array(
                    'label'         => $this->i18n('reports_aggregates_articles'),
                    'option_value'  => 'aggregates_5'
                ),
            );

            //prepare filters
            $filter = array (
                'custom_id' => 'articles',
                'name'      => 'articles',
                'type'      => 'dropdown',
                'required'  => 1,
                'label'     => $this->i18n('reports_articles'),
                'help'      => $this->i18n('reports_articles'),
                'options'   => $options_articles,
            );
            $filters['articles'] = $filter;

            //DEFINE OPERATION FILTER
            $options_operation = array(
                array(
                    'label'         => $this->i18n('reports_operation_production'),
                    'option_value'  => 'production'
                ),
                array(
                    'label'         => $this->i18n('reports_operation_sales'),
                    'option_value'  => 'sales'
                ),
                array(
                    'label'         => $this->i18n('reports_operation_availability'),
                    'option_value'  => 'availability'
                ),
            );

            //prepare filters
            $filter = array (
                'custom_id' => 'operation',
                'name'      => 'operation',
                'type'      => 'radio',
                'required'  => 1,
                'label'     => $this->i18n('reports_operation'),
                'help'      => $this->i18n('reports_operation'),
                'options'   => $options_operation,
            );
            $filters['operation'] = $filter;

            //DEFINE DATE FILTER FROM
            $filter = array (
                'custom_id' => 'from_date',
                'name' => 'from_date',
                'type' => 'date',
                'label' => $this->i18n('reports_from_date'),
                'help' => $this->i18n('reports_from_date')
            );
            $filters['from_date'] = $filter;

            //DEFINE DATE FILTER TO
            $filter = array (
                'custom_id' => 'to_date',
                'name' => 'to_date',
                'type' => 'date',
                'label' => $this->i18n('reports_to_date'),
                'help' => $this->i18n('reports_to_date')
            );
            $filters['to_date'] = $filter;

            //DEFINE NOMENCLATURES' FILTER
            $filter = array (
                'custom_id'         => 'nomenclatures',
                'name'              => 'nomenclatures',
                'type'              => 'autocompleter',
                'autocomplete_type' => 'nomenclatures',
                'autocomplete_buttons' => 'clear',
                'autocomplete'      => array(
                    'type'          => 'nomenclatures',
                    'url'           => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures'),
                    'suggestions'   => '<name>/<a__size>/<a__color>',
                    'fill_options'  => array(
                        '$nomenclatures_autocomplete => <name>/<a__size>/<a__color>',
                        '$nomenclatures_oldvalue => <name>/<a__size>/<a__color>',
                        '$nomenclatures => <id>'
                    ),
                    'filters' => array(
                        '<type>' => '1,2,3,5'
                    ),
                    'clear' => 1
                ),
                'label'             => $this->i18n('reports_nomenclatures'),
                'help'              => $this->i18n('reports_nomenclatures'),
                'value'             => ''
            );
            $filters['nomenclatures'] = $filter;

            return $filters;
        }
    }
?>