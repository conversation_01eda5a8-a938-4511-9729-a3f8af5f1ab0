<?php

class Custom_Report_Filters extends Report_Filters {

    /**
     * @var Registry
     */
    private static $registry = array();

    /**
     * Defining filters for the certain type of report
     *
     * @param Registry $registry - the main registry
     * @return array - filter definitions of report
     */
    function defineFilters(Registry &$registry) {

        self::$registry = &$registry;

        // $filters - array containing description of all filters
        $filters = array();

        $settings = Reports::getReportSettings($registry, $this->reportName);

        $d = new Nomenclature($registry, array('type' => $settings['nom_type_vehicle']));
        $assoc_vars = $d->getAssocVars();

        $filters['assignor'] = array_replace_recursive(
            $assoc_vars[$settings['vehicle_assignor_var']],
            array(
                'name' => 'assignor',
                'label' => $this->i18n('reports_assignor'),
                'help' => $this->i18n('reports_search_by'),
                'show_placeholder' => 'help',
                'autocomplete' => array(
                    'fill_options' => array(
                        '$assignor => <id>',
                        '$assignor_autocomplete => <name> <lastname>',
                    ),
                    'add' => '0',
                ),
                'readonly' => '0',
                'required' => '0',
            ));

        $filters['user'] = array_replace_recursive(
            $assoc_vars[$settings['vehicle_user_var']],
            array(
                'name' => 'user',
                'label' => $this->i18n('reports_user'),
                'help' => $this->i18n('reports_search_by'),
                'show_placeholder' => 'help',
                'autocomplete' => array(
                    'fill_options' => array(
                        '$user => <id>',
                        '$user_autocomplete => <name> <lastname>',
                    ),
                    'add' => '0',
                ),
                'readonly' => '0',
                'required' => '0',
            ));

        $filters['make'] = array_replace_recursive(
            $assoc_vars[$settings['vehicle_make_var']],
            array(
                'name' => 'make',
                'label' => $this->i18n('reports_make'),
                'show_placeholder' => $this->i18n('all'),
                'autocomplete' => array(
                    'fill_options' => array(
                        '$make => <id>',
                        '$make_autocomplete => <name>',
                        '$model => ',
                        '$model_autocomplete => ',
                    ),
                    'add' => '0',
                    'combobox' => '1',
                    'combobox_mode' => 'empty',
                ),
                'readonly' => '0',
                'required' => '0',
            ));

        $filters['model'] = array_replace_recursive(
            $assoc_vars[$settings['vehicle_model_var']],
            array(
                'name' => 'model',
                'label' => $this->i18n('reports_model'),
                'show_placeholder' => $this->i18n('all'),
                'autocomplete' => array(
                    'fill_options' => array(
                        '$model => <id>',
                        '$model_autocomplete => <name>',
                    ),
                    'optional_filters' => array(
                        "<a__{$settings['vehicle_make_id_var']}>" => '$make',
                    ),
                    'add' => '0',
                    'combobox' => '1',
                    'combobox_mode' => 'empty',
                ),
                'readonly' => '0',
                'required' => '0',
            ));

        $filters['reg_num'] = array_replace_recursive(
            $assoc_vars[$settings['vehicle_reg_num_var']],
            array(
                'name' => 'reg_num',
                'label' => $this->i18n('reports_reg_num'),
                'help' => $this->i18n('reports_search_by'),
                'show_placeholder' => 'help',
                'readonly' => '0',
                'required' => '0',
            ));

        $filters['chassis_num'] = array(
            'name' => 'chassis_num',
            'label' => $this->i18n('reports_chassis_num'),
        ) + $filters['reg_num'];

        foreach ($filters as $k => $v) {
            if (!empty($v['help']) && strpos($v['help'], '[param]') !== false) {
                $filters[$k]['help'] = str_replace('[param]', $registry['lang'] != 'de' ? mb_strtolower($v['label'], mb_detect_encoding($v['label'])) : $v['label'], $v['help']);
            }
        }

        $filters['date_from'] = array(
            'name' => 'date_from',
            'type' => 'custom_filter',
            'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
            'label' => $this->i18n('reports_reg_date'),
            'first_filter_label' => mb_convert_case($this->i18n('from'), MB_CASE_LOWER),
            'additional_filter' => array(
                'name' => 'date_to',
            ),
        );

        $filters['date_to'] = array();

        // filter to hold vehicle id when displaying detailed data for selected vehicle
        $filters['vehicle'] = array(
            'name' => 'vehicle',
            'type' => 'autocompleter',
            'label' => $this->i18n('reports_vehicle'),
            'readonly' => '1',
            'hidden' => '1',
            'custom_class' => 'viewmode doubled strong',
        );

        return $filters;
    }

    /**
     * Process some filters that depend on the request or on each other
     *
     * @param array $filters - the report filters
     * @return array - report filters after processing
     */
    function processDependentFilters(array &$filters) {
        $registry = &self::$registry;

        if (array_key_exists('date_to', $filters)) {
            if (!empty($filters['date_from']['additional_filter'])) {
                $filters['date_from']['additional_filter'] = $filters['date_to'] + $filters['date_from']['additional_filter'];
            }
            unset ($filters['date_to']);
        }

        if (!empty($filters['vehicle']['value'])) {
            if (empty($filters['vehicle']['value_autocomplete'])) {
                $filters['vehicle']['value_autocomplete'] = $registry['db']->GetOne(
                    "SELECT name FROM " . DB_TABLE_NOMENCLATURES_I18N .
                    " WHERE parent_id = '{$filters['vehicle']['value']}' AND lang = '{$registry['lang']}'");
            }
            $filters['vehicle']['hidden'] = '0';
        }

        return $filters;
    }
}
