<html>
  <body>
    <table border="1" cellpadding="0" cellspacing="0">
      <tr>
        <td style="text-align: center; vertical-align: middle;"><strong>{#reports_insured#|escape}</strong></td>
        <td style="text-align: center; vertical-align: middle;"><strong>{#reports_insurance_type#|escape}</strong></td>
        <td style="text-align: center; vertical-align: middle;"><strong>{#reports_department#|escape}</strong></td>
        <td style="text-align: center; vertical-align: middle;"><strong>{#reports_premium_bgn#|escape}</strong></td>
        <td style="text-align: center; vertical-align: middle;"><strong>{#reports_commission_bgn#|escape}</strong></td>
      </tr>
      {foreach from=$reports_results item=result name=results}
        {foreach from=$result.insurances item=insurance name=ins}
          {foreach from=$insurance.departments item=department name=dep}
            <tr>
              <td style="mso-number-format:'\@';">
                {$result.name|escape|default:"&nbsp;"}
              </td>
              <td style="mso-number-format:'\@';">
                {$insurance.name|escape|default:"&nbsp;"}
              </td>
              <td style="mso-number-format:'\@';">
                {$department.name|escape|default:"&nbsp;"}
              </td>
              <td align="right" style="mso-number-format:'0\.00'">
                {$department.bonus|escape|default:"0.00"}
              </td>
              <td align="right" style="mso-number-format:'0\.00'">
                {$department.commission|escape|default:"0.00"}
              </td>
            </tr>
          {/foreach}
        {/foreach}
      {foreachelse}
        <tr>
          <td colspan="5"><span color="red">{#no_items_found#|escape}</span></td>
        </tr>
      {/foreach}
    </table>
  </body>
</html>
