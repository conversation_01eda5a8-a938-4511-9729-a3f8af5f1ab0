<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tr>
    <td>
      {if $reports_additional_options.document_id}
        <label for="calculated_payment" class="labelbox" style="padding-right: 5px;">{#reports_payments_sum#|escape}:</label><input type="text" name="calculated_payment" id="calculated_payment" readonly="readonly" value="{$reports_additional_options.payments_sum|default:'0'|string_format:'%.2f'}" class="txtbox small readonly" style="margin-right: 15px; text-align: right;" />
        <input type="hidden" value="{$reports_additional_options.document_id}" name="document_id" id="document_id" />
        <button type="submit" name="add_policies" style="margin-left: 20px;" class="button" onclick="return aonCreateDebitNoteProcessRecords(this, 'create_debit_note');">{#reports_create_debit_note#|escape}</button>
        <button type="button" name="cancel" class="button" onclick="window.location.href='index.php?launch=documents&documents=edit&edit={$reports_additional_options.document_id}'">{#cancel#|escape}</button>
        <br />
        <br />
      {/if}
      {assign var=total_colspan value=8}
      {if $reports_additional_options.document_id}
        {math equation='1+x' x=$total_colspan assign='total_colspan'}
      {/if}
      {if $reports_additional_options.include_discount}
        {math equation='2+x' x=$total_colspan assign='total_colspan'}
      {/if}
      <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row hcenter">
          {if $reports_additional_options.document_id}
            <td class="t_border" style="vertical-align: middle;">
              <input type="checkbox" name="all_policies" id="all_policies" value="1" onclick="selectAllPolicies(this, 'row_')"{if $reports_additional_options.main_checkbox_checked} checked="checked"{/if} />
            </td>
          {/if}
          <td class="t_border" style="vertical-align: middle;"><div>{#reports_account_manager#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div>{#reports_insurer#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div>{#reports_contract#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div>{#reports_insurance_type#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div>{#reports_deadline#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div>{#reports_payment_num#|escape}</div></td>
          {if $reports_additional_options.include_discount}
            <td class="t_border" style="vertical-align: middle;">
              {#reports_discounted_value#|escape}
            </td>
            <td class="t_border" style="vertical-align: middle;">
              {#reports_discount#|escape}
            </td>
          {/if}
          <td class="t_border" style="vertical-align: middle;"><div>{#reports_subtotal#|escape}</div></td>
          <td style="vertical-align: middle;"><div>{#reports_bruto_total#|escape}</div></td>

        </tr>
        {foreach from=$reports_results item=result name=results}
          <tr class="{cycle values='t_odd,t_even'}">
            {if $reports_additional_options.document_id}
              <td class="t_border">
                <input type="checkbox" name="included_gt2_rows[]" id="row_{$result.gt2_id}" value="{$result.encoded_data|escape}"{if $reports_additional_options.document_id} onclick="calculateSelectedRows(this, '{$reports_additional_options.precision|default:'0'}')"{/if}{if $result.checked} checked="checked"{/if} />
                <input type="hidden" name="payment_value_{$result.gt2_id}" id="payment_value_{$result.gt2_id}" value="{$result.subtotal|escape|default:'0.00'|string_format:'%.2f'}" disabled="disabled" />
              </td>
            {/if}
            <td class="t_border">
              {$result.administrative_name|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.insurer_name|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=contracts&amp;contracts=viewtopic&amp;viewtopic={$result.policy_id}" target="_blank">{if $result.policy_num}{$result.policy_num|escape|default:"&nbsp;"}{else}<i>[{#reports_no_number#|escape}]</i>{/if}</a>
            </td>
            <td class="t_border">
              {$result.insurance_type_name|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.deadline|date_format:#date_short#|escape|default:"&nbsp;"}
            </td>
            <td class="t_border hright">
              {$result.payment_num|escape|default:"-"}
            </td>
            {if $reports_additional_options.include_discount}
              <td class="hright t_border">
                {$result.subtotal_no_discount|escape|default:"0.00"|string_format:"%.2f"}
              </td>
              <td class="hright t_border">
                {$result.discount_value|escape|default:"0.00"|string_format:"%.2f"}
              </td>
            {/if}
            <td class="t_border hright">
              {$result.subtotal|escape|default:"0.00"|string_format:"%.2f"}
            </td>
            <td class="hright">
              {$result.subtotal_with_vat_with_discount|escape|default:"0.00"|string_format:"%.2f"}
            </td>

          </tr>
        {foreachelse}
          <tr class="{cycle values='t_odd,t_even'}">
            <td class="error" colspan="{$total_colspan}">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="{$total_colspan}"></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
