<?php
    Class Suppliers_Clients_Obligations Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $final_results = array();
            if (!empty($filters['from']) && !empty($filters['to'])) {
                // currencies
                require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
                $currency_multipliers = array();
                $currency_multipliers[$filters['currency'] . '->' . $filters['currency']] = 1;

                $incomes_reasons_included = array_filter(preg_split('#\s*,\s*#', INCOMES_REASONS_INCLUDED));
                $owned_companies = preg_split('#\s*,\s*#', OWNED_COMPANIES);
                $tm_additional_filter = array();
                if (!empty($filters['type_client'])) {
                    // get the var id for client id
                    $sql = 'SELECT fm.id FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Nomenclature" AND fm.model_type="' . NOMENCLATURES_CLIENTS_GROUP . '" AND fm.name="' . NOMENCLATURES_CLIENTS_GROUP_VAR_CLIENT_ID . '"';
                    $var_id = $registry['db']->GetOne($sql);

                    $sql = 'SELECT cstm_client.value' . "\n" .
                           'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                           'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS cstm_client' . "\n" .
                           '  ON (cstm_client.model_id=n.id AND cstm_client.var_id="' . $var_id . '" AND cstm_client.value IS NOT NULL AND cstm_client.value!="" AND cstm_client.value!="0")' . "\n" .
                           'WHERE n.id="' . $filters['type_client'] . '"' . "\n";
                    $tm_additional_filter = $var_id = $registry['db']->GetCol($sql);
                }

                if (empty($filters['show_according_to']) || ($filters['show_according_to'] == 'client')) {
                    // FIRST TABLE CALCULATIONS

                    // includes the factory to retrieve all the documents which match the criteria
                    require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';

                    $filters_incomes_reasons = array('model_lang'   => $model_lang,
                                                     'sanitize'     => true,
                                                     'sort'         => array('fir.issue_date DESC'),
                                                     'where'        => array(
                                                        'fir.annulled_by = 0',
                                                        'fir.status = "finished"',
                                                        'fir.active = 1',
                                                        'fir.type IN ("' . implode('","', $incomes_reasons_included) . '")',
                                                        'fir.issue_date >= "' . $filters['from'] . '"',
                                                        'fir.issue_date <= "' . $filters['to'] . '"'
                                                     )
                    );

                    // process tags
                    $exclude_tags = array_filter(preg_split('#\s*,\s*#', EXCLUDE_FINANCE_INCOMES_TAGS));
                    if (!empty($exclude_tags)) {
                        foreach ($exclude_tags as $ex_tags) {
                            $filters_incomes_reasons['where'][] = 'tags.tag_id != \'' . $ex_tags . '\'';
                        }
                    }
                    if (!empty($filters['client'])) {
                        $filters_incomes_reasons['where'][] = 'fir.customer = "' . $filters['client'] . '"';
                    }
                    if (!empty($filters['type_client'])) {
                        $filters_incomes_reasons['where'][] = 'fir.customer IN ("' . implode('","', $tm_additional_filter) . '")';
                    }
                    if (!empty($filters['company'])) {
                        $filters_incomes_reasons['where'][] = 'fir.company IN (' . implode(',', $filters['company']) . ')';
                    }
                    if (!empty($filters['income_num'])) {
                        $filters_incomes_reasons['where'][] = 'fir.num="' . $filters['income_num'] . '"';
                    }
                    if (!empty($filters['created_by'])) {
                        $filters_incomes_reasons['where'][] = 'fir.added_by="' . $filters['created_by'] . '"';
                    }

                    $incomes_reasons_list = Finance_Incomes_Reasons::search($registry, $filters_incomes_reasons);

                    $incomes_reasons_models = array();
                    foreach ($incomes_reasons_list as $key_irl => $income_reason) {
                        $incomes_reasons_models[$income_reason->get('id')] = $income_reason;
                    }

                    $incomes_reasons = array();

                    // search for relations with expense request
                    if (!empty($incomes_reasons_models)) {
                        $query_relatives = 'SELECT frr.parent_id FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                                           'INNER JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                           ' ON (frr.link_to_model_name="Document" AND frr.link_to=d.id AND d.type="' . EXPENSE_REQUEST . '" AND d.active=1 AND d.deleted_by=0)' . "\n" .
                                           'WHERE frr.parent_model_name="Finance_Incomes_Reason" AND frr.link_to_model_name="Document" AND frr.parent_id IN (' . implode(',', array_keys($incomes_reasons_models)) . ')' . "\n";
                        $related_documents = $registry['db']->getCol($query_relatives);

                        foreach ($incomes_reasons_models as $key_ir => $income_reason) {
                            if (in_array($income_reason->get('id'), $related_documents)) {
                                $income_reason->set('payment_status', 'paid', true);
                                $income_reason->set('transformed_from_expense_request', true, true);
                            } else {
                                $income_reason->set('transformed_from_expense_request', false, true);
                            }

                            // check the payment status
                            if (!empty($filters['payment_status'])) {
                                if ($income_reason->get('payment_status') != $filters['payment_status']) {
                                    continue;
                                }
                            }

                            $incomes_reasons[$key_ir] = array(
                                'id'                               => $income_reason->get('id'),
                                'num'                              => $income_reason->get('num'),
                                'issue_date'                       => $income_reason->get('issue_date'),
                                'customer'                         => $income_reason->get('customer'),
                                'customer_name'                    => $income_reason->get('customer_name'),
                                'source_currency'                  => $income_reason->get('currency'),
                                'currency'                         => $filters['currency'],
                                'payment_status'                   => $income_reason->get('payment_status'),
                                'employee'                         => $income_reason->get('employee'),
                                'employee_name'                    => $income_reason->get('employee_name'),
                                'substatus_name'                   => $income_reason->get('substatus_name'),
                                'transformed_from_expense_request' => $income_reason->get('transformed_from_expense_request'),
                                // the rows from the income
                                'gt2_income'                       => array(),
                                // the rows which have to be invoiced according to checkAddingInvoice
                                'gt2_to_be_invoiced'               => array(),
                                // the rows which will be shown in the report
                                'gt2_rows'                         => array(),
                                'rowspan'                          => 0
                            );
                        }
                    }

                    // GET REQUIRED GT2 ROWS
                    // directly get the gt2 rows for the required models
                    $gt2_rows_info = array();
                    if (!empty($incomes_reasons)) {
                        $query = 'SELECT gt2_det.*, gt2_det_i18n.*' . "\n" .
                                 'FROM ' . DB_TABLE_GT2_DETAILS . ' AS gt2_det' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS gt2_det_i18n' . "\n" .
                                 ' ON (gt2_det.id=gt2_det_i18n.parent_id AND gt2_det_i18n.lang="' . $model_lang . '")' . "\n" .
                                 'WHERE gt2_det.model="Finance_Incomes_Reason" AND gt2_det.model_id IN (' . implode(',', array_keys($incomes_reasons)) . ')';

                        if (!empty($filters['deliverer'])) {
                            $query .= ' AND gt2_det.article_deliverer="' . $filters['deliverer'] . '"';
                        }
                        if (empty($filters['include_owned_companies'])) {
                            $query .= ' AND gt2_det.article_deliverer NOT IN ("' . implode('","', $owned_companies) . '")';
                        }
                        if (!empty($filters['article_description'])) {
                            $query .= ' AND gt2_det_i18n.article_description LIKE "%' . $filters['article_description'] . '%"';
                        }
                        if (!empty($filters['type_order'])) {
                            $query .= ' AND gt2_det.free_field1="' . $filters['type_order'] . '"';
                        }

                        $gt2_rows_info = $registry['db']->getAll($query);
                    }

                    // set the rows to the belonging model
                    foreach ($gt2_rows_info as $gt2_row) {
                        $incomes_reasons[$gt2_row['model_id']]['gt2_income'][$gt2_row['id']] = $gt2_row;
                    }

                    foreach ($incomes_reasons as $key_ir => $incomes_reason) {
                        if (!count($incomes_reason['gt2_income'])) {
                            unset($incomes_reasons[$key_ir]);
                            continue;
                        }
                        $income_reason = $incomes_reasons_models[$key_ir];

                        $income_reason->checkAddingInvoice(true);
                        $grouping_table_2 = $income_reason->get('grouping_table_2');

                        // reduce the data for the gt2 table
                        $income_reason->unsetProperty('grouping_table_2', true);
                        if (!empty($grouping_table_2)) {
                            $incomes_reasons[$key_ir]['gt2_to_be_invoiced'] = $grouping_table_2['values'];
                        }
                    }

                    // unset the models to reduce the data flow
                    unset($incomes_reasons_models);
                    unset($incomes_reasons_list);

                    // if the deliverer filter is selected than the rows
                    if (!empty($filters['deliverer'])) {
                        // clears the rows that have to be invoiced but are not for the selected deliverer
                        foreach ($incomes_reasons as $key_ir => $incomes_reason) {
                            // diff the arrays
                            $rows_to_clear = array_diff_key($incomes_reasons[$gt2_row['model_id']]['gt2_to_be_invoiced'], $incomes_reasons[$gt2_row['model_id']]['gt2_income']);

                            // clears the rows which we don't need
                            foreach (array_keys($rows_to_clear) as $row_clr) {
                                unset($incomes_reasons[$gt2_row['model_id']]['gt2_to_be_invoiced'][$row_clr]);
                            }
                        }

                        foreach ($incomes_reasons as $key_ir => $incomes_reason) {
                            if (!count($incomes_reason['gt2_income'])) {
                                unset($incomes_reasons[$key_ir]);
                            }
                        }
                    }

                    // list with active gt2 rows
                    $gt2_rows_list = array();

                    // match the results depending on the invoiced status
                    if (!empty($filters['invoiced_status'])) {
                        foreach ($incomes_reasons as $key_ir => $incomes_reason) {
                            $rows_to_show = array();

                            $rows_to_be_invoiced = $incomes_reason['gt2_to_be_invoiced'];
                            $original_to_be_invoiced = $incomes_reason['gt2_income'];

                            // convert currencies
                            $currency_key = $incomes_reason['source_currency'] . '->' . $incomes_reason['currency'];
                            if (!isset($currency_multipliers[$currency_key])) {
                                $currency_multipliers[$currency_key] = Finance_Currencies::getRate($registry, $incomes_reason['source_currency'], $incomes_reason['currency']);
                            }
                            $current_multiplier = $currency_multipliers[$currency_key];

                            if (!empty($rows_to_be_invoiced) || $incomes_reason['transformed_from_expense_request']) {
                                // empty means that everything is paid in full. In this case we don't need this record
                                unset($incomes_reasons[$key_ir]);
                                continue;
                            } else {
                                if ($filters['invoiced_status'] == 'fully_invoiced' || $filters['invoiced_status'] == 'partial_invoiced_not_invoiced') {
                                    if ($filters['invoiced_status'] == 'fully_invoiced') {
                                        $rows_to_go_through = $original_to_be_invoiced;
                                    } else {
                                        $rows_to_go_through = $rows_to_be_invoiced;
                                    }
                                    foreach ($rows_to_go_through as $key_row) {
                                        $rows_to_show[$key_row['id']] = array(
                                            'row_id'               => $key_row['id'],
                                            'article'              => $key_row['article_id'],
                                            'article_name'         => $key_row['article_name'],
                                            'deliverer'            => $key_row['article_deliverer'],
                                            'deliverer_name'       => $key_row['article_deliverer_name'],
                                            'description'          => $key_row['article_description'],
                                            'full_quantity'        => $key_row['quantity'],
                                            'price'                => $key_row['price_with_discount']*$current_multiplier,
                                            'total'                => $key_row['subtotal_with_discount']*$current_multiplier,
                                            'not_invoiced'         => (($filters['invoiced_status'] == 'fully_invoiced') ? 0 : $key_row['quantity']),
                                            'not_ordered'          => $key_row['quantity'],
                                            'issue_expense_reason' => true,
                                            'difference'           => 0,
                                            'expenses_reasons'     => array()
                                        );
                                    }
                                    $incomes_reasons[$key_ir]['gt2_rows'] = $rows_to_show;
                                    unset($incomes_reasons[$key_ir]['gt2_to_be_invoiced']);
                                    unset($incomes_reasons[$key_ir]['gt2_income']);

                                    $gt2_rows_list = array_merge($gt2_rows_list, array_keys($rows_to_show));
                                } elseif ($filters['invoiced_status'] == 'not_invoiced') {
                                    foreach ($original_to_be_invoiced as $row_id => $otbi) {
                                        if (isset($rows_to_be_invoiced[$row_id])) {
                                            $remaining_quantity_to_be_paid = $otbi['quantity'] - $rows_to_be_invoiced[$row_id]['quantity'];
                                            if ($remaining_quantity_to_be_paid > 0) {
                                                unset($original_to_be_invoiced[$row_id]);
                                            } else {
                                                $rows_to_show[$otbi['id']] = array(
                                                    'row_id'               => $otbi['id'],
                                                    'article'              => $otbi['article_id'],
                                                    'article_name'         => $otbi['article_name'],
                                                    'deliverer'            => $otbi['article_deliverer'],
                                                    'deliverer_name'       => $otbi['article_deliverer_name'],
                                                    'description'          => $otbi['article_description'],
                                                    'full_quantity'        => $otbi['quantity'],
                                                    'price'                => $otbi['price_with_discount']*$current_multiplier,
                                                    'total'                => $otbi['subtotal_with_discount']*$current_multiplier,
                                                    'not_invoiced'         => $remaining_quantity_to_be_paid,
                                                    'not_ordered'          => $otbi['quantity'],
                                                    'issue_expense_reason' => true,
                                                    'expenses_reasons'     => array()
                                                );
                                            }
                                        } else {
                                            unset($original_to_be_invoiced[$row_id]);
                                        }
                                    }
                                    if (!empty($rows_to_show)) {
                                        $incomes_reasons[$key_ir]['gt2_rows'] = $rows_to_show;
                                        unset($incomes_reasons[$key_ir]['gt2_to_be_invoiced']);
                                        unset($incomes_reasons[$key_ir]['gt2_income']);

                                        $gt2_rows_list = array_merge($gt2_rows_list, array_keys($rows_to_show));
                                    } else {
                                        unset($incomes_reasons[$key_ir]);
                                    }
                                }
                            }
                        }
                    } else {
                        // if there is no such filter defined then both have to be shown -
                        // the invoiced amount and the amount that is not invoiced
                        // Because of this the calculations have to be made by matching the quantities
                        foreach ($incomes_reasons as $key_ir => $incomes_reason) {
                            $rows_to_be_invoiced = $incomes_reason['gt2_to_be_invoiced'];
                            $original_to_be_invoiced = $incomes_reason['gt2_income'];
                            $rows_to_show = array();

                            // convert currencies
                            $currency_key = $incomes_reason['source_currency'] . '->' . $incomes_reason['currency'];
                            if (!isset($currency_multipliers[$currency_key])) {
                                $currency_multipliers[$currency_key] = Finance_Currencies::getRate($registry, $incomes_reason['source_currency'], $incomes_reason['currency']);
                            }
                            $current_multiplier = $currency_multipliers[$currency_key];

                            foreach ($original_to_be_invoiced as $row_id => $otbi) {
                                if (isset($rows_to_be_invoiced[$row_id])) {
                                    $remaining_quantity_to_be_paid = $rows_to_be_invoiced[$row_id]['quantity'];
                                } else {
                                    $remaining_quantity_to_be_paid = 0;
                                }
                                $gt2_rows_list[] = $row_id;

                                $rows_to_show[$row_id] = array(
                                    'row_id'               => $otbi['id'],
                                    'article'              => $otbi['article_id'],
                                    'article_name'         => $otbi['article_name'],
                                    'deliverer'            => $otbi['article_deliverer'],
                                    'deliverer_name'       => $otbi['article_deliverer_name'],
                                    'description'          => $otbi['article_description'],
                                    'full_quantity'        => $otbi['quantity'],
                                    'price'                => $otbi['price_with_discount']*$current_multiplier,
                                    'total'                => $otbi['subtotal_with_discount']*$current_multiplier,
                                    'not_invoiced'         => $remaining_quantity_to_be_paid,
                                    'not_ordered'          => $otbi['quantity'],
                                    'issue_expense_reason' => true,
                                    'expenses_reasons'     => array()
                                );
                            }

                            // set the rows which have to be included in the report
                            $incomes_reasons[$key_ir]['gt2_rows'] = $rows_to_show;
                            unset($incomes_reasons[$key_ir]['gt2_to_be_invoiced']);
                            unset($incomes_reasons[$key_ir]['gt2_income']);
                        }
                    }

                    // define how many rows have to be ordered
                    $first_table_total_rows_to_be_ordered = 0;

                    // get the related expenses reasons to the current found rows
                    $gt2_rows_list = array_unique($gt2_rows_list);
                    if (!empty($gt2_rows_list) && !empty($incomes_reasons)) {
                        $query_relatives = 'SELECT frr.rows_links, frr.parent_id, frr.link_to ' . "\n" .
                                           'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                                           'INNER JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                                           ' ON (frr.parent_id=fer.id AND fer.type IN (' . FINANCE_EXPENSE . ') AND fer.active=1 AND fer.annulled_by=0)' . "\n" .
                                           'WHERE frr.parent_model_name="Finance_Expenses_Reason" AND frr.link_to_model_name="Finance_Incomes_Reason" AND frr.link_to IN (' . implode(',', array_keys($incomes_reasons)) . ')' . "\n";
                        $related_expenses_reasons = $registry['db']->getAll($query_relatives);

                        $rows_relations = array();
                        $expenses_gt2_rows = array();
                        $expenses_reasons_ids = array();
                        $relations_tree = array();

                        // process the rows to check what relations rows in the incomes reason have with the rows in expenses reasons
                        foreach ($related_expenses_reasons as $rer) {
                            $rows_links = preg_split('#\n|\r|\r\n#', $rer['rows_links']);
                            foreach ($rows_links as $row) {
                                $row_rels = preg_split('#\s*\=\>\s*#', $row);
                                if (!empty($row_rels[1]) && in_array($row_rels[1], $gt2_rows_list)) {
                                    $rows_relations[] = array('from_row' => $row_rels[1], 'from_model' => $rer['link_to'], 'to_row' => $row_rels[0], 'to_model' => $rer['parent_id']);
                                    $expenses_gt2_rows[] = $row_rels[0];
                                    $expenses_reasons_ids[] = $rer['parent_id'];
                                }
                            }
                        }

                        // check the reversed relation
                        $query_relatives_2 = 'SELECT frr.rows_links, frr.parent_id, frr.link_to' . "\n" .
                                             'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                                             'INNER JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                                             ' ON (frr.link_to=fer.id AND fer.type IN (' . FINANCE_EXPENSE . ') AND fer.active=1 AND fer.annulled_by=0)' . "\n" .
                                             'WHERE frr.link_to_model_name="Finance_Expenses_Reason" AND frr.parent_model_name="Finance_Incomes_Reason" AND frr.parent_id IN (' . implode(',', array_keys($incomes_reasons)) . ')' . "\n";
                        $related_expenses_reasons_2 = $registry['db']->getAll($query_relatives_2);

                        // process the rows to check what relations rows in the incomes reason have with the rows in expenses reasons
                        foreach ($related_expenses_reasons_2 as $rer) {
                            $rows_links = preg_split('#\n|\r|\r\n#', $rer['rows_links']);
                            foreach ($rows_links as $row) {
                                $row_rels = preg_split('#\s*\=\>\s*#', $row);
                                if (!empty($row_rels[0]) && in_array($row_rels[0], $gt2_rows_list)) {
                                    $rows_relations[] = array('from_row' => $row_rels[0], 'from_model' => $rer['parent_id'], 'to_row' => $row_rels[1], 'to_model' => $rer['link_to']);
                                    $expenses_gt2_rows[] = $row_rels[1];
                                    $expenses_reasons_ids[] = $rer['link_to'];
                                }
                            }
                        }

                        // find out the relations tree
                        $relations_tree = array();
                        $related_debit_credit_notices = array();
                        $changed_quantities_by_articles = array();
                        if (!empty($expenses_reasons_ids)) {
                            // relate 4 times to fin_expenses_reasons to retrieve all possible relations
                            $relations_list = 'SELECT frr.link_to as main_id, frr.parent_id as first_level_id, frr1.parent_id as second_level_id, frr2.parent_id as third_level_id' . "\n" .
                                              'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                                              'LEFT JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr1' . "\n" .
                                              ' ON (frr.parent_id=frr1.link_to AND frr1.parent_model_name="Finance_Expenses_Reason" AND frr1.link_to_model_name="Finance_Expenses_Reason")' . "\n" .
                                              'LEFT JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr2' . "\n" .
                                              ' ON (frr1.parent_id=frr2.link_to AND frr2.parent_model_name="Finance_Expenses_Reason" AND frr2.link_to_model_name="Finance_Expenses_Reason")' . "\n" .
                                              'WHERE frr.link_to_model_name="Finance_Expenses_Reason" AND frr.link_to IN (' . implode(',', array_unique($expenses_reasons_ids)) . ') AND frr.parent_model_name="Finance_Expenses_Reason"' . "\n";
                            $plain_relations_tree = $registry['db']->getAll($relations_list);

                            // get all the related rows in a list
                            $related_models_info = array();
                            foreach ($plain_relations_tree as $prl) {
                                if (!empty($prl['first_level_id']) && !array_key_exists($prl['first_level_id'], $related_models_info)) {
                                    $related_models_info[$prl['first_level_id']] = array();
                                }
                                if (!empty($prl['second_level_id']) && !array_key_exists($prl['second_level_id'], $related_models_info)) {
                                    $related_models_info[$prl['second_level_id']] = array();
                                }
                                if (!empty($prl['third_level_id']) && !array_key_exists($prl['third_level_id'], $related_models_info)) {
                                    $related_models_info[$prl['third_level_id']] = array();
                                }
                            }

                            if (!empty($plain_relations_tree)) {
                                // get the data for the related_models
                                $related_models_info_sql = 'SELECT fer.id as idx, fer.id, fer.type, fer.invoice_num as num, fdi18n.name as type_name, fer.status, fer.issue_date' . "\n" .
                                                           'FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                                                           'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' AS fdi18n' . "\n" .
                                                           ' ON (fdi18n.parent_id=fer.type AND fdi18n.lang="' . $model_lang . '")' . "\n" .
                                                           'WHERE fer.id IN (' . implode(',', array_keys($related_models_info)) . ') AND fer.type IN (' . FINANCE_EXPENSE_RELATED_TYPES . ') AND annulled_by=0 AND active=1' . "\n";
                                $related_models_info = $registry['db']->getAssoc($related_models_info_sql);

                                // work out the relations
                                foreach ($plain_relations_tree as $prt) {
                                    if (!isset($relations_tree[$prt['main_id']])) {
                                        $relations_tree[$prt['main_id']] = array();
                                    }
                                    if (!isset($relations_tree[$prt['main_id']][$prt['first_level_id']]) && isset($related_models_info[$prt['first_level_id']])) {
                                        $relations_tree[$prt['main_id']][$prt['first_level_id']] = $related_models_info[$prt['first_level_id']];
                                        $relations_tree[$prt['main_id']][$prt['first_level_id']]['level'] = 1;
                                        $relations_tree[$prt['main_id']][$prt['first_level_id']]['relations'] = array();
                                        if ($related_models_info[$prt['first_level_id']]['type'] == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE || $related_models_info[$prt['first_level_id']]['type'] == PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE) {
                                            $related_debit_credit_notices[$related_models_info[$prt['first_level_id']]['id']] = $prt['main_id'];
                                        }
                                    }
                                    if (isset($relations_tree[$prt['main_id']][$prt['first_level_id']]) && isset($related_models_info[$prt['second_level_id']]) && !isset($relations_tree[$prt['main_id']][$prt['first_level_id']]['relations'][$prt['second_level_id']])) {
                                        $relations_tree[$prt['main_id']][$prt['first_level_id']]['relations'][$prt['second_level_id']] = $related_models_info[$prt['second_level_id']];
                                        $relations_tree[$prt['main_id']][$prt['first_level_id']]['relations'][$prt['second_level_id']]['level'] = 2;
                                        $relations_tree[$prt['main_id']][$prt['first_level_id']]['relations'][$prt['second_level_id']]['relations'] = array();
                                        if ($related_models_info[$prt['second_level_id']]['type'] == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE || $related_models_info[$prt['second_level_id']]['type'] == PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE) {
                                            $related_debit_credit_notices[$related_models_info[$prt['second_level_id']]['id']] = $prt['main_id'];
                                        }
                                    }
                                    if (isset($relations_tree[$prt['main_id']][$prt['first_level_id']]['relations'][$prt['second_level_id']]) && isset($related_models_info[$prt['third_level_id']]) && !isset($relations_tree[$prt['main_id']][$prt['first_level_id']]['relations'][$prt['second_level_id']]['relations'][$prt['third_level_id']])) {
                                        $relations_tree[$prt['main_id']][$prt['first_level_id']]['relations'][$prt['second_level_id']]['relations'][$prt['third_level_id']] = $related_models_info[$prt['third_level_id']];
                                        $relations_tree[$prt['main_id']][$prt['first_level_id']]['relations'][$prt['second_level_id']]['relations'][$prt['third_level_id']]['level'] = 3;
                                        if ($related_models_info[$prt['third_level_id']]['type'] == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE || $related_models_info[$prt['third_level_id']]['type'] == PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE) {
                                            $related_debit_credit_notices[$related_models_info[$prt['third_level_id']]['id']] = $prt['main_id'];
                                        }
                                    }
                                }
                            }

                            unset($plain_relations_tree);
                        }

                        if (!empty($related_debit_credit_notices)) {
                            // get the gt2 rows of the debit and credit notices
                            $query_debit_credit_info = 'SELECT fer.id, fer.type, gt2_det.quantity, gt2_det.article_id, gt2_det.article_deliverer ' . "\n" .
                                                       'FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer'  . "\n" .
                                                       'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2_det' . "\n" .
                                                       ' ON (gt2_det.model_id IN (' . implode(',', array_keys($related_debit_credit_notices)) . ') AND fer.id=gt2_det.model_id AND gt2_det.model="Finance_Expenses_Reason")' . "\n" .
                                                       'WHERE fer.id IN (' . implode(',', array_keys($related_debit_credit_notices)) . ')' . "\n";
                            $debit_credit_gt2_info = $registry['db']->getAll($query_debit_credit_info);

                            foreach ($debit_credit_gt2_info as $dc_inf) {
                                // $changed_quantities_by_articles
                                if (!isset($related_debit_credit_notices[$dc_inf['id']])) {
                                    continue;
                                }

                                $exp_reason_key = $related_debit_credit_notices[$dc_inf['id']];
                                if (!isset($changed_quantities_by_articles[$exp_reason_key])) {
                                    $changed_quantities_by_articles[$exp_reason_key] = array();
                                }
                                if (!isset($changed_quantities_by_articles[$exp_reason_key][$dc_inf['article_deliverer']])) {
                                    $changed_quantities_by_articles[$exp_reason_key][$dc_inf['article_deliverer']] = array();
                                }
                                if (!isset($changed_quantities_by_articles[$exp_reason_key][$dc_inf['article_deliverer']][$dc_inf['article_id']])) {
                                    $changed_quantities_by_articles[$exp_reason_key][$dc_inf['article_deliverer']][$dc_inf['article_id']] = 0;
                                }
                                $multiplier = 0;
                                $current_article_quantity = abs($dc_inf['quantity']);
                                if ($dc_inf['type'] == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE) {
                                    $multiplier = -1;
                                } else {
                                    $multiplier = 1;
                                }
                                $changed_quantities_by_articles[$exp_reason_key][$dc_inf['article_deliverer']][$dc_inf['article_id']] += ($current_article_quantity * $multiplier);
                                unset($multiplier);
                                unset($current_article_quantity);
                            }
                        }
                        unset($related_debit_credit_notices);

                        // get the gt2 rows of the expenses reasons to retrieve
                        //what quantity of the selected row has been ordered
                        if (!empty($expenses_gt2_rows)) {
                            $query_gt2_info = 'SELECT gt2_det.id as idx, fer.id, fer.invoice_num as num, fer.issue_date, fer.payment_status, fer.status, fdi18n.name as type_name, 0 as level, ' . "\n" .
                                              '  fer.currency as source_currency, gt2_det.id as gt2_id, gt2_det.article_deliverer, gt2_det.price_with_discount, gt2_det.subtotal, gt2_det.quantity, gt2_det.article_id' . "\n" .
                                              'FROM ' . DB_TABLE_GT2_DETAILS . ' AS gt2_det'  . "\n" .
                                              'LEFT JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                                              ' ON (fer.id=gt2_det.model_id)' . "\n" .
                                              'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' AS fdi18n' . "\n" .
                                              ' ON (fdi18n.parent_id=fer.type AND fdi18n.lang="' . $model_lang . '")' . "\n" .
                                              'WHERE gt2_det.id IN (' . implode(',', $expenses_gt2_rows) . ') AND gt2_det.model="Finance_Expenses_Reason"' . "\n";
                            $expenses_gt2_rows = $registry['db']->getAssoc($query_gt2_info);
                        }

                        // goes through all of the related rows and retrieves what quantity
                        // was not transfered to the expense reason
                        foreach ($rows_relations as $row_relation) {
                            if (isset($incomes_reasons[$row_relation['from_model']])) {
                                $rows_to_show = $incomes_reasons[$row_relation['from_model']]['gt2_rows'];
                                if (isset($rows_to_show[$row_relation['from_row']]) && isset($expenses_gt2_rows[$row_relation['to_row']])) {
                                    //check if there is changed quantities of the selected article, made by credit or debit notice
                                    if (!empty($changed_quantities_by_articles[$row_relation['to_model']][$expenses_gt2_rows[$row_relation['to_row']]['article_deliverer']][$expenses_gt2_rows[$row_relation['to_row']]['article_id']])) {
                                        $expenses_gt2_rows[$row_relation['to_row']]['quantity'] += $changed_quantities_by_articles[$row_relation['to_model']][$expenses_gt2_rows[$row_relation['to_row']]['article_deliverer']][$expenses_gt2_rows[$row_relation['to_row']]['article_id']];
                                    }

                                    $rows_to_show[$row_relation['from_row']]['not_ordered'] = $rows_to_show[$row_relation['from_row']]['not_ordered'] - $expenses_gt2_rows[$row_relation['to_row']]['quantity'];
                                    $rows_to_show[$row_relation['from_row']]['expenses_reasons'][$row_relation['to_row']] = $expenses_gt2_rows[$row_relation['to_row']];
                                    $rows_to_show[$row_relation['from_row']]['expenses_reasons'][$row_relation['to_row']]['relations'] = (isset($relations_tree[$expenses_gt2_rows[$row_relation['to_row']]['id']])) ? $relations_tree[$expenses_gt2_rows[$row_relation['to_row']]['id']] : array();

                                    // convert currencies
                                    $currency_key = $expenses_gt2_rows[$row_relation['to_row']]['source_currency'] . '->' . $filters['currency'];
                                    if (!isset($currency_multipliers[$currency_key])) {
                                        $currency_multipliers[$currency_key] = Finance_Currencies::getRate($registry, $expenses_gt2_rows[$row_relation['to_row']]['source_currency'], $filters['currency']);
                                    }
                                    $current_multiplier = $currency_multipliers[$currency_key];
                                    $rows_to_show[$row_relation['from_row']]['expenses_reasons'][$row_relation['to_row']]['price_with_discount'] = $expenses_gt2_rows[$row_relation['to_row']]['price_with_discount']*$current_multiplier;
                                    $rows_to_show[$row_relation['from_row']]['expenses_reasons'][$row_relation['to_row']]['subtotal'] = $expenses_gt2_rows[$row_relation['to_row']]['subtotal']*$current_multiplier;

                                    if ($rows_to_show[$row_relation['from_row']]['not_ordered']<=0) {
                                        $rows_to_show[$row_relation['from_row']]['issue_expense_reason'] = false;
                                        $rows_to_show[$row_relation['from_row']]['not_ordered'] = 0;
                                    } else {
                                        $first_table_total_rows_to_be_ordered++;
                                    }
                                    $incomes_reasons[$row_relation['from_model']]['gt2_rows'] = $rows_to_show;
                                }
                            }
                        }
                        unset($changed_quantities_by_articles);
                    }

                    // calculates the row spans for the first table
                    foreach ($incomes_reasons as $key_ir => $incomes_reason) {
                        foreach ($incomes_reason['gt2_rows'] as $gt2_row_id => $gt2_row) {
                            $current_gt2_row_rowspan = count($gt2_row['expenses_reasons']);
                            if (!$current_gt2_row_rowspan) {
                                $current_gt2_row_rowspan = 1;
                            }
                            $incomes_reasons[$key_ir]['gt2_rows'][$gt2_row_id]['rowspan'] = $current_gt2_row_rowspan;
                            $incomes_reasons[$key_ir]['gt2_rows'][$gt2_row_id]['difference'] = floatval($gt2_row['total']) - array_sum(array_column($gt2_row['expenses_reasons'], 'subtotal'));;
                            $incomes_reasons[$key_ir]['rowspan'] += $current_gt2_row_rowspan;

                            // prepare the summary table
                            if (!empty($filters['show_summary_table'])) {
                                if (!isset($final_results['additional_options']['summary_table'])) {
                                    $final_results['additional_options']['summary_table'] = array(
                                        'total_value'      => 0,
                                        'total_sell_price' => 0,
                                        'difference'       => 0
                                    );
                                }

                                $expenses_value = array_sum(array_column($gt2_row['expenses_reasons'], 'subtotal'));
                                $final_results['additional_options']['summary_table']['total_value'] += $expenses_value;
                                $final_results['additional_options']['summary_table']['total_sell_price'] += floatval($gt2_row['total']);
                                $final_results['additional_options']['summary_table']['difference'] += (floatval($gt2_row['total']) - $expenses_value);
                            }
                        }

                        if (!$incomes_reasons[$key_ir]['rowspan']) {
                            $incomes_reasons[$key_ir]['rowspan'] = 1;
                        }
                    }

                    $final_results['additional_options']['first_table'] = $incomes_reasons;
                    $final_results['additional_options']['first_table_selected_rows'] = ($registry['session']->get('reports_bgservice_noname_report_first_table', 'selected_items') ? $registry['session']->get('reports_bgservice_noname_report_first_table', 'selected_items') : array());
                    $final_results['additional_options']['first_table_total_rows_to_be_ordered'] = $first_table_total_rows_to_be_ordered;
                }



                if (empty($filters['show_according_to']) || ($filters['show_according_to'] == 'deliverer')) {
                    // SECOND TABLE CALCULATIONS
                    $where = array(
                        'fer.annulled_by = 0',
                        'fer.status = "finished"',
                        'fer.active = 1',
                        'fer.type IN (' . FINANCE_EXPENSE . ')',
                        'fer.issue_date >= "' . $filters['from'] . '"',
                        'fer.issue_date <= "' . $filters['to'] . '"'
                    );
                    if (!empty($filters['deliverer'])) {
                        $where[] = 'fer.customer = "' . $filters['deliverer'] . '"';
                    }
                    if (!empty($filters['expense_num'])) {
                        $where[] = 'fer.invoice_num = "' . $filters['expense_num'] . '"';
                    }
                    if (!empty($filters['company'])) {
                        $where[] = 'fer.company IN (' . implode(',', $filters['company']) . ')';
                    }
                    if (!empty($filters['payment_status'])) {
                        $where[] = 'fer.payment_status = "' . $filters['payment_status'] . '"';
                    } else {
                        $where[] = 'fer.payment_status != "nopay"';
                    }

                    // process tags
                    $additional_join = '';
                    $exclude_tags = array_filter(preg_split('#\s*,\s*#', EXCLUDE_FINANCE_EXPENSES_TAGS));
                    if (!empty($exclude_tags)) {
                        $additional_join = 'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS fer_tags' . "\n" .
                                           '  ON (fer_tags.model_id=fer.id AND fer_tags.model="Finance_Expenses_Reason" AND fer_tags.tag_id IN ("' . implode('","', $exclude_tags) . '"))' . "\n";
                        $where[] = 'fer_tags.tag_id IS NULL';
                    }

                    $query = 'SELECT DISTINCT(fer.id), fer.*, fer.id AS order_idx, ' . "\n" .
                             '       TRIM(CONCAT(ci18n.name, " ", ci18n.lastname)) as customer_name, "bg" as model_lang, ' . "\n" .
                             '       fds.name AS substatus_name, fdti18n.name as type_name, ' . "\n" .
                             '       fer.employee AS employee1, CONCAT(ci18n2.name, " ", ci18n2.lastname) AS employee_name' . "\n" .
                             'FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_STATUSES . ' AS fds' . "\n" .
                             '  ON (fds.id=fer.substatus AND fds.lang="bg")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' AS fdti18n' . "\n" .
                             '  ON (fer.type=fdti18n.parent_id AND fdti18n.lang="bg")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                             '  ON (fer.customer=ci18n.parent_id AND ci18n.lang="bg")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n2' . "\n" .
                             '  ON (fer.employee=ci18n2.parent_id AND ci18n2.lang="bg")' . "\n" .
                             ($additional_join ? $additional_join : "") .
                             'WHERE ' . implode(' AND ', $where) . "\n" .
                             'GROUP BY fer.id' . "\n" .
                             'ORDER BY fer.issue_date DESC';
                    $expenses_reasons_list = $registry['db']->GetAll($query);

                    // put the expenses reasons in a new array with id as index
                    $expenses_reasons = array();
                    foreach ($expenses_reasons_list as $key_erl => $expense_reason) {
                        $expenses_reasons[$expense_reason['id']] = array(
                            'id'                               => $expense_reason['id'],
                            'num'                              => $expense_reason['invoice_num'],
                            'issue_date'                       => $expense_reason['issue_date'],
                            'customer'                         => $expense_reason['customer'],
                            'customer_name'                    => $expense_reason['customer_name'],
                            'source_currency'                  => $expense_reason['currency'],
                            'type_name'                        => $expense_reason['type_name'],
                            'currency'                         => $filters['currency'],
                            'payment_status'                   => $expense_reason['payment_status'],
                            'status'                           => $expense_reason['status'],
                            'employee'                         => $expense_reason['employee'],
                            'employee_name'                    => $expense_reason['employee_name'],
                            'substatus_name'                   => $expense_reason['substatus_name'],
                            'relations'                        => array(),
                            'level'                            => 0,
                            // the rows which will be shown in the report
                            'gt2_rows'                         => array(),
                            'rowspan'                          => 0
                        );
                    }

                    // unset the models to reduce the data flow
                    unset($expenses_reasons_list);

                    // list with active gt2 rows
                    $gt2_rows_list = array();

                    // find out the relations tree
                    $relations_tree = array();
                    if (!empty($expenses_reasons)) {
                        // relate 4 times to fin_expenses_reasons to retrieve all possible relations
                        $relations_list = 'SELECT frr.link_to as main_id, frr.parent_id as first_level_id, frr1.parent_id as second_level_id, frr2.parent_id as third_level_id' . "\n" .
                                          'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                                          'LEFT JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr1' . "\n" .
                                          ' ON (frr.parent_id=frr1.link_to AND frr1.parent_model_name="Finance_Expenses_Reason" AND frr1.link_to_model_name="Finance_Expenses_Reason")' . "\n" .
                                          'LEFT JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr2' . "\n" .
                                          ' ON (frr1.parent_id=frr2.link_to AND frr2.parent_model_name="Finance_Expenses_Reason" AND frr2.link_to_model_name="Finance_Expenses_Reason")' . "\n" .
                                          'WHERE frr.link_to_model_name="Finance_Expenses_Reason" AND frr.link_to IN (' . implode(',', array_keys($expenses_reasons)) . ') AND frr.parent_model_name="Finance_Expenses_Reason"' . "\n";
                        $plain_relations_tree = $registry['db']->getAll($relations_list);

                        // get all the related rows in a list
                        $related_models_info = array();
                        $models_needed_info = array();
                        foreach ($plain_relations_tree as $prl) {
                            if (!empty($prl['first_level_id']) && !array_key_exists($prl['first_level_id'], $related_models_info)) {
                                if (isset($expenses_reasons[$prl['first_level_id']])) {
                                    $related_models_info[$prl['first_level_id']] = array(
                                        'id'         => $expenses_reasons[$prl['first_level_id']]['id'],
                                        'num'        => $expenses_reasons[$prl['first_level_id']]['num'],
                                        'type_name'  => $expenses_reasons[$prl['first_level_id']]['type_name'],
                                        'status'     => $expenses_reasons[$prl['first_level_id']]['status'],
                                        'issue_date' => $expenses_reasons[$prl['first_level_id']]['issue_date']
                                    );
                                    unset($expenses_reasons[$prl['first_level_id']]);
                                } else {
                                    $models_needed_info[] = $prl['first_level_id'];
                                }
                            }
                            if (!empty($prl['second_level_id']) && !array_key_exists($prl['second_level_id'], $related_models_info)) {
                                if (isset($expenses_reasons[$prl['second_level_id']])) {
                                    $related_models_info[$prl['second_level_id']] = array(
                                        'id'         => $expenses_reasons[$prl['second_level_id']]['id'],
                                        'num'        => $expenses_reasons[$prl['second_level_id']]['num'],
                                        'type_name'  => $expenses_reasons[$prl['second_level_id']]['type_name'],
                                        'status'     => $expenses_reasons[$prl['second_level_id']]['status'],
                                        'issue_date' => $expenses_reasons[$prl['second_level_id']]['issue_date']
                                    );
                                    unset($expenses_reasons[$prl['second_level_id']]);
                                } else {
                                    $models_needed_info[] = $prl['second_level_id'];
                                }
                            }
                            if (!empty($prl['third_level_id']) && !array_key_exists($prl['third_level_id'], $related_models_info)) {
                                if (isset($expenses_reasons[$prl['third_level_id']])) {
                                    $related_models_info[$prl['third_level_id']] = array(
                                        'id'         => $expenses_reasons[$prl['third_level_id']]['id'],
                                        'num'        => $expenses_reasons[$prl['third_level_id']]['num'],
                                        'type_name'  => $expenses_reasons[$prl['third_level_id']]['type_name'],
                                        'status'     => $expenses_reasons[$prl['third_level_id']]['status'],
                                        'issue_date' => $expenses_reasons[$prl['third_level_id']]['issue_date']
                                    );
                                    unset($expenses_reasons[$prl['third_level_id']]);
                                } else {
                                    $models_needed_info[] = $prl['third_level_id'];
                                }
                            }
                        }

                        if (!empty($models_needed_info)) {
                            // get the data for the related_models
                            $related_models_info_sql = 'SELECT fer.id as idx, fer.id, fer.invoice_num as num, fdi18n.name as type_name, fer.status, fer.issue_date' . "\n" .
                                                       'FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                                                       'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' AS fdi18n' . "\n" .
                                                       ' ON (fdi18n.parent_id=fer.type AND fdi18n.lang="' . $model_lang . '")' . "\n" .
                                                       'WHERE fer.id IN (' . implode(',', array_unique($models_needed_info)) . ') AND fer.type IN (' . FINANCE_EXPENSE_RELATED_TYPES . ') AND annulled_by=0 AND active=1' . "\n";
                            $models_needed_info = $registry['db']->getAssoc($related_models_info_sql);
                            foreach ($models_needed_info as $key_mni => $mni) {
                                $related_models_info[$key_mni] = $mni;
                            }
                        }
                        unset($models_needed_info);

                        if (!empty($plain_relations_tree)) {
                            // work out the relations
                            foreach ($plain_relations_tree as $prt) {
                                if (!isset($expenses_reasons[$prt['main_id']])) {
                                    continue;
                                }
                                if (!isset($expenses_reasons[$prt['main_id']]['relations'][$prt['first_level_id']]) && isset($related_models_info[$prt['first_level_id']])) {
                                    $expenses_reasons[$prt['main_id']]['relations'][$prt['first_level_id']] = $related_models_info[$prt['first_level_id']];
                                    $expenses_reasons[$prt['main_id']]['relations'][$prt['first_level_id']]['level'] = 1;
                                    $expenses_reasons[$prt['main_id']]['relations'][$prt['first_level_id']]['relations'] = array();
                                }
                                if (isset($expenses_reasons[$prt['main_id']]['relations'][$prt['first_level_id']]) && isset($related_models_info[$prt['second_level_id']]) && !isset($expenses_reasons[$prt['main_id']]['relations'][$prt['first_level_id']]['relations'][$prt['second_level_id']])) {
                                    $expenses_reasons[$prt['main_id']]['relations'][$prt['first_level_id']]['relations'][$prt['second_level_id']] = $related_models_info[$prt['second_level_id']];
                                    $expenses_reasons[$prt['main_id']]['relations'][$prt['first_level_id']]['relations'][$prt['second_level_id']]['level'] = 2;
                                    $expenses_reasons[$prt['main_id']]['relations'][$prt['first_level_id']]['relations'][$prt['second_level_id']]['relations'] = array();
                                }
                                if (isset($expenses_reasons[$prt['main_id']]['relations'][$prt['first_level_id']]['relations'][$prt['second_level_id']]) && isset($related_models_info[$prt['third_level_id']]) && !isset($expenses_reasons[$prt['main_id']]['relations'][$prt['first_level_id']]['relations'][$prt['second_level_id']]['relations'][$prt['third_level_id']])) {
                                    $expenses_reasons[$prt['main_id']]['relations'][$prt['first_level_id']]['relations'][$prt['second_level_id']]['relations'][$prt['third_level_id']] = $related_models_info[$prt['third_level_id']];
                                    $expenses_reasons[$prt['main_id']]['relations'][$prt['first_level_id']]['relations'][$prt['second_level_id']]['relations'][$prt['third_level_id']]['level'] = 3;
                                }
                            }
                        }
                        unset($plain_relations_tree);
                    }

                    // GET REQUIRED GT2 ROWS
                    // directly get the gt2 rows for the required models
                    $gt2_rows_info = array();
                    if (!empty($expenses_reasons)) {
                        $query = 'SELECT gt2_det.*, gt2_det_i18n.*' . "\n" .
                                 'FROM ' . DB_TABLE_GT2_DETAILS . ' AS gt2_det' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS gt2_det_i18n' . "\n" .
                                 ' ON (gt2_det.id=gt2_det_i18n.parent_id AND gt2_det_i18n.lang="' . $model_lang . '")' . "\n" .
                                 'WHERE gt2_det.model="Finance_Expenses_Reason" AND gt2_det.model_id IN (' . implode(',', array_keys($expenses_reasons)) . ') AND gt2_det.article_id NOT IN (' . NOMENCLATURES_NOT_SHOWN . ')';

                        if (!empty($filters['client'])) {
                            $query .= ' AND gt2_det.article_deliverer="' . $filters['client'] . '"';
                        }
                        if (!empty($filters['type_client'])) {
                            $query .= ' AND gt2_det.article_deliverer IN ("' . implode('","', $tm_additional_filter) . '")';
                        }
                        if (empty($filters['include_owned_companies'])) {
                            $query .= ' AND gt2_det.article_deliverer NOT IN ("' . implode('","', $owned_companies) . '")';
                        }
                        if (!empty($filters['article_description'])) {
                            $query .= ' AND gt2_det_i18n.article_description LIKE "%' . $filters['article_description'] . '%"';
                        }

                        $gt2_rows_info = $registry['db']->getAll($query);
                    }

                    // set the rows to the belonging model
                    foreach ($gt2_rows_info as $gt2_row) {
                        // convert currencies
                        $currency_key = $expenses_reasons[$gt2_row['model_id']]['source_currency'] . '->' . $expenses_reasons[$gt2_row['model_id']]['currency'];
                        if (!isset($currency_multipliers[$currency_key])) {
                            $currency_multipliers[$currency_key] = Finance_Currencies::getRate($registry, $expenses_reasons[$gt2_row['model_id']]['source_currency'], $expenses_reasons[$gt2_row['model_id']]['currency']);
                        }
                        $current_multiplier = $currency_multipliers[$currency_key];

                        $gt2_rows_list[] = $gt2_row['id'];

                        $expenses_reasons[$gt2_row['model_id']]['gt2_rows'][$gt2_row['id']] = array(
                            'row_id'               => $gt2_row['id'],
                            'article'              => $gt2_row['article_id'],
                            'article_name'         => $gt2_row['article_name'],
                            'deliverer'            => $gt2_row['article_deliverer'],
                            'deliverer_name'       => $gt2_row['article_deliverer_name'],
                            'description'          => $gt2_row['article_description'],
                            'full_quantity'        => $gt2_row['quantity'],
                            'price'                => $gt2_row['price_with_discount']*$current_multiplier,
                            'total'                => $gt2_row['subtotal_with_discount']*$current_multiplier,
                            'not_client_invoiced'  => $gt2_row['quantity'],
                            'issue_income_reason'  => true,
                            'incomes_reasons'      => array()
                        );
                    }

                    // clears the rows which don't have gt2 rows because of the selected client or the selected article
                    foreach ($expenses_reasons as $key_exp_reason => $expense_reason) {
                        if (empty($expense_reason['gt2_rows'])) {
                            unset($expenses_reasons[$key_exp_reason]);
                        }
                    }

                    // define how many rows have to be invoiced
                    $second_table_total_rows_to_be_invoiced = 0;

                    // get the connected incomes reasons to the current found rows
                    $gt2_rows_list = array_unique($gt2_rows_list);
                    if (!empty($gt2_rows_list) && !empty($expenses_reasons)) {
                        $query_relatives = 'SELECT frr.* FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                                           'INNER JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                                           ' ON (frr.parent_id=fir.id AND fir.type IN ("' . implode('","', $incomes_reasons_included) . '") AND fir.active=1 AND fir.annulled_by=0)' . "\n" .
                                           'WHERE frr.parent_model_name="Finance_Incomes_Reason" AND frr.link_to_model_name="Finance_Expenses_Reason" AND frr.link_to IN (' . implode(',', array_keys($expenses_reasons)) . ')' . "\n";
                        $related_expenses_reasons = $registry['db']->getAll($query_relatives);

                        $rows_relations = array();
                        $incomes_gt2_rows = array();

                        // process the rows to check what relations rows in the incomes reason have with the rows in expenses reasons
                        foreach ($related_expenses_reasons as $rer) {
                            $rows_links = preg_split('#\n|\r|\r\n#', $rer['rows_links']);
                            foreach ($rows_links as $row) {
                                $row_rels = preg_split('#\s*\=\>\s*#', $row);
                                if (!empty($row_rels[1]) && in_array($row_rels[1], $gt2_rows_list)) {
                                    $rows_relations[] = array('from_row' => $row_rels[1], 'from_model' => $rer['link_to'], 'to_row' => $row_rels[0], 'to_model' => $rer['parent_id']);
                                    $incomes_gt2_rows[] = $row_rels[0];
                                }
                            }
                        }

                        // check the reversed relation
                        $query_relatives_2 = 'SELECT frr.* FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                                             'INNER JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                                             ' ON (frr.link_to=fir.id AND fir.type IN ("' . implode('","', $incomes_reasons_included) . '") AND fir.active=1 AND fir.annulled_by=0)' . "\n" .
                                             'WHERE frr.link_to_model_name="Finance_Incomes_Reason" AND frr.parent_model_name="Finance_Expenses_Reason" AND frr.parent_id IN (' . implode(',', array_keys($expenses_reasons)) . ')' . "\n";
                        $related_expenses_reasons_2 = $registry['db']->getAll($query_relatives_2);

                        // process the rows to check what relations rows in the incomes reason have with the rows in expenses reasons
                        foreach ($related_expenses_reasons_2 as $rer) {
                            $rows_links = preg_split('#\n|\r|\r\n#', $rer['rows_links']);
                            foreach ($rows_links as $row) {
                                $row_rels = preg_split('#\s*\=\>\s*#', $row);
                                if (!empty($row_rels[0]) && in_array($row_rels[0], $gt2_rows_list)) {
                                    $rows_relations[] = array('from_row' => $row_rels[0], 'from_model' => $rer['parent_id'], 'to_row' => $row_rels[1], 'to_model' => $rer['link_to']);
                                    $incomes_gt2_rows[] = $row_rels[1];
                                }
                            }
                        }

                        // get the gt2 rows of the incomes reasons to retrieve
                        //what quantity of the selected row has been ordered
                        if (!empty($incomes_gt2_rows)) {
                            $query_gt2_info = 'SELECT gt2_det.id as idx, fir.id, fir.num, fir.issue_date, fir.payment_status, fir.status, fir.currency as source_currency, ' . "\n" .
                                              '  gt2_det.id as gt2_id, gt2_det.price_with_discount, gt2_det.subtotal, gt2_det.quantity' . "\n" .
                                              'FROM ' . DB_TABLE_GT2_DETAILS . ' AS gt2_det'  . "\n" .
                                              'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                                              ' ON (fir.id=gt2_det.model_id)' . "\n" .
                                              'WHERE gt2_det.id IN (' . implode(',', $incomes_gt2_rows) . ') AND gt2_det.model="Finance_Incomes_Reason"' . "\n";
                            $incomes_gt2_rows = $registry['db']->getAssoc($query_gt2_info);
                        }

                        // goes through all of the related rows and retrieves what quantity
                        // was not transfered to the expense reason
                        foreach ($rows_relations as $row_relation) {
                            if (isset($expenses_reasons[$row_relation['from_model']])) {
                                $rows_to_show = $expenses_reasons[$row_relation['from_model']]['gt2_rows'];
                                if (isset($rows_to_show[$row_relation['from_row']]) && isset($incomes_gt2_rows[$row_relation['to_row']])) {
                                    $rows_to_show[$row_relation['from_row']]['not_client_invoiced'] = $rows_to_show[$row_relation['from_row']]['not_client_invoiced'] - $incomes_gt2_rows[$row_relation['to_row']]['quantity'];
                                    $rows_to_show[$row_relation['from_row']]['incomes_reasons'][$row_relation['to_row']] = $incomes_gt2_rows[$row_relation['to_row']];

                                    // convert currencies
                                    $currency_key = $incomes_gt2_rows[$row_relation['to_row']]['source_currency'] . '->' . $filters['currency'];
                                    if (!isset($currency_multipliers[$currency_key])) {
                                        $currency_multipliers[$currency_key] = Finance_Currencies::getRate($registry, $incomes_gt2_rows[$row_relation['to_row']]['source_currency'], $filters['currency']);
                                    }
                                    $current_multiplier = $currency_multipliers[$currency_key];
                                    $rows_to_show[$row_relation['from_row']]['incomes_reasons'][$row_relation['to_row']]['price_with_discount'] = $incomes_gt2_rows[$row_relation['to_row']]['price_with_discount']*$current_multiplier;
                                    $rows_to_show[$row_relation['from_row']]['incomes_reasons'][$row_relation['to_row']]['subtotal'] = $incomes_gt2_rows[$row_relation['to_row']]['subtotal']*$current_multiplier;

                                    if ($rows_to_show[$row_relation['from_row']]['not_client_invoiced']<=0) {
                                        $rows_to_show[$row_relation['from_row']]['issue_income_reason'] = false;
                                        $rows_to_show[$row_relation['from_row']]['not_client_invoiced'] = 0;
                                    } else {
                                        $second_table_total_rows_to_be_invoiced++;
                                    }
                                    $expenses_reasons[$row_relation['from_model']]['gt2_rows'] = $rows_to_show;
                                }
                            }
                        }
                    }


                    // calculates the row spans for the first table
                    foreach ($expenses_reasons as $key_ir => $incomes_reason) {
                        foreach ($incomes_reason['gt2_rows'] as $gt2_row_id => $gt2_row) {
                            $current_gt2_row_rowspan = count($gt2_row['incomes_reasons']);
                            if (!$current_gt2_row_rowspan) {
                                $current_gt2_row_rowspan = 1;
                            }
                            $expenses_reasons[$key_ir]['gt2_rows'][$gt2_row_id]['rowspan'] = $current_gt2_row_rowspan;
                            $expenses_reasons[$key_ir]['rowspan'] += $current_gt2_row_rowspan;
                        }

                        if (!$expenses_reasons[$key_ir]['rowspan']) {
                            $expenses_reasons[$key_ir]['rowspan'] = 1;
                        }
                    }

                    $final_results['additional_options']['second_table'] = $expenses_reasons;
                    $final_results['additional_options']['second_table_selected_rows'] = ($registry['session']->get('reports_bgservice_noname_report_second_table', 'selected_items') ? $registry['session']->get('reports_bgservice_noname_report_second_table', 'selected_items') : array());
                    $final_results['additional_options']['second_table_total_rows_to_be_invoiced'] = $second_table_total_rows_to_be_invoiced;
                }

                $final_results['additional_options']['exclude_outter_form'] = true;

            } else {
                $registry['messages']->setError($registry['translater']->translate('error_complete_required_filters'));
            }

            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }
    }
?>
