<?php

class Ustil_Installations_Schedule extends Reports {

    /**
     * Gets the settings from the reports table and applies report-specific processing
     *
     * @param Registry $registry - the main registry
     * @param string $report_name - if not specified, report name is taken from class name
     * @return array - parsed and processed report settings
     */
    public static function getReportSettings(&$registry, $report_name = '') {

        $settings = parent::getReportSettings($registry, $report_name ?: strtolower(get_class()));

        $default_settings = array(
            'order_types' => '7,10,13,14,15,16,23,24,25,27,30',
            'max_num_results' => 1000,
        );

        $order_types = isset($settings['order_types']) ? $settings['order_types'] : $default_settings['order_types'];
        $settings['order_types'] = "'" . implode("', '", General::slashesEscape(preg_split('#\s*,\s*#', $order_types))) . "'";

        // get ids of variables from orders which have the same name in all types
        $orders_var_ids = array(
            'time_instalation',
            'to_pay__total',
            'deposit_customer__total',
            'link_pay_sum',
            'delivery',
            'instalation',
            'name_order',
            'num_total',
            'accessories_services_id',
            'accessories_services_name',
            'accessories_services_quantity',
            'accessories_services_measure',
        );
        // sanitize setting before further use
        $orders_var_ids = $registry['db']->GetAssoc("
            SELECT name, GROUP_CONCAT(id)
            FROM " . DB_TABLE_FIELDS_META . "
            WHERE model = 'Document'
              AND model_type IN ({$settings['order_types']})
              AND name IN ('" . implode("', '", $orders_var_ids) . "')
            GROUP BY name
        ") + array_fill_keys($orders_var_ids, '0');
        foreach ($orders_var_ids as $var_name => $ids) {
            $default_settings["{$var_name}_var_ids"] = $ids;
        }

        $settings = $settings + $default_settings;

        return $settings;
    }

    /**
     * Performs query with specified filter values and returns found results
     *
     * @param Registry $registry - the main registry
     * @param array $filters - values of report filters to search with
     * @return mixed[]|[][] - found results with/without pagination
     */
    public static function buildQuery(&$registry, $filters = array()) {
        // Prepare the array for the final results
        $final_results = array();

        // Get the report settings
        $settings = self::getReportSettings($registry);
        $final_results['additional_options']['settings'] = $settings;

        $filters['for_date_from'] = $registry['request']->get('fordate') ? : $filters['for_date_from'];
        $filters['for_date_to'] = $registry['request']->get('fordate') ? : $filters['for_date_to'];
        $filters['employee'] = array_filter($filters['employee']);
        $filters['undistributed_installations'] = !empty($filters['undistributed_installations']) ? array_filter($filters['undistributed_installations']) : array();
        $filters['unconfirmed'] = !empty($filters['unconfirmed']) ? array_filter($filters['unconfirmed']) : array();

        // validate filters only when definitions are available
        $required_filters = $filters;
        $filter_definitions = $registry['report_filters'];
        if ($filter_definitions && is_array($filter_definitions)) {
            foreach ($filter_definitions as $filter_def) {
                if (array_key_exists('additional_filter', $filter_def) && array_key_exists('name', $filter_def['additional_filter'])) {
                    $filter_definitions[$filter_def['additional_filter']['name']] = array();
                }
            }
            $required_filters = array_filter(array_intersect_key($filters, $filter_definitions));
            if (
                array_key_exists('request_status', $required_filters) &&
                isset($filter_definitions['request_status']['options'][0]['option_value']) &&
                $required_filters['request_status'] == $filter_definitions['request_status']['options'][0]['option_value']
            ) {
                unset($required_filters['request_status']);
            }
        }

        if ($filter_definitions && !$required_filters) {
            $registry['messages']->setError($registry['translater']->translate('error_report_select_filter'));
            $final_results['requests'] = array();
        } else {
            // Get requests with teams
            $query = "
                SELECT d.id                                      AS id,
                    GROUP_CONCAT(dc.value ORDER BY dc.value ASC) AS team_key
                  FROM documents AS d
                  JOIN documents_cstm AS dc
                    ON (d.deleted_by = 0
                      AND d.active = 1
                      AND d.type = 20
                      AND dc.model_id = d.id
                      AND dc.var_id = 2001
                      AND dc.value != ''
                      AND dc.lang IN ('', 'bg'))
                  GROUP BY d.id";
            $requests_with_teams = $registry['db']->GetAssoc($query, false, true);
            $requests_with_teams_list = "'" . implode("', '", array_keys($requests_with_teams)) . "'";

            $query = "
                SELECT d.id      AS id,
                    d.full_num   AS num,
                    d.status     AS status,
                    d2.id        AS order_id,
                    d.custom_num AS order_num,
                    dc18.value   AS meters_req,
                    dc17.value   AS num_req,
                    d.date       AS order_date,
                    dc21.value   AS production_date,
                    d2.deadline  AS contract_deadline,
                    dc1.value    AS installation_date,
                    dc2.value    AS installation_time,
                    dc20.value   AS installation_minutes_real,
                    dc14.value   AS installation_duration,
                    dc3.value    AS confirmation_client,
                    FIND_IN_SET(dc3.value, ',2,1') AS confirmation_client_order,
                    d.customer   AS client_distributor_id,
                    TRIM(CONCAT(ci.name, ' ', ci.lastname)) AS client_distributor_name,
                    dc4.value    AS village_name,
                    dc5.value    AS neighborhood_name,
                    CONCAT_WS(', ',
                      IF (dc6.value  != '', CONCAT('ул. ', dc6.value),  NULL),
                      IF (dc7.value  != '', CONCAT('бл. ', dc7.value),  NULL),
                      IF (dc8.value  != '', CONCAT('вх. ', dc8.value),  NULL),
                      IF (dc9.value  != '', CONCAT('ет. ', dc9.value),  NULL),
                      IF (dc10.value != '', CONCAT('ап. ', dc10.value), NULL)
                    )            AS address,
                    dc11.value   AS object_phone,
                    dc12.value   AS object_mobile,
                    dc13.value   AS to_pay_total,
                    dc22.value   AS deposit,
                    dc16.value   AS request_notes,
                    ''           AS team_key" .
                    ($registry->get('action') == 'export' ? ",
                    dc6.value    AS street,
                    dc7.value    AS block,
                    dc8.value    AS entrance,
                    dc9.value    AS floor,
                    dc10.value   AS flat" : '') .
                    (!empty($settings['display_order_notes']) ? ",
                    d2i.notes    AS order_notes" : '') .
                    (!empty($settings['display_order_custom_num']) ? ",
                    d2.custom_num AS order_custom_num" : '') . "
                  FROM documents AS d
                  JOIN customers_i18n AS ci
                    ON (ci.parent_id = d.customer
                      AND ci.lang = 'bg')
                  JOIN documents_relatives AS dr
                    ON (d.deleted_by = 0
                      AND d.active = 1
                      AND d.`type` = 20" .
                      ($filters['request_status'] == 'finished' ? "
                      AND d.status = 'closed'" : '') .
                      ($filters['request_status'] == 'non_finished' || $registry->get('action') == 'export' ? "
                      AND d.status != 'closed'" : '') .
                      (!empty($filters['order_num']) ? "
                      AND d.custom_num LIKE '%{$filters['order_num']}%'" : '') .
                      (!empty($filters['order_date_from']) ? "
                      AND '{$filters['order_date_from']}' <= d.date" : '') .
                      (!empty($filters['order_date_to']) ? "
                      AND d.date <= '{$filters['order_date_to']}'" : '') .
                      (!empty($filters['customer']) ? "
                      AND d.customer = '{$filters['customer']}'" : '') .
                      (!empty($filters['trademark']) ? "
                      AND d.trademark = '{$filters['trademark']}'" : '') .
                      (!empty($filters['offices']) ? "
                      AND d.office IN (" . implode(', ', $filters['offices']) . ")" : '') .
                      (!empty($filters['undistributed_installations']) && !empty($requests_with_teams) ? "
                      AND d.id NOT IN ({$requests_with_teams_list})" : '') . "
                      AND dr.parent_model_name = 'Document'
                      AND dr.parent_id = d.id
                      AND dr.link_to_model_name = 'Document')
                  JOIN documents AS d2
                    ON (d2.deleted_by = 0
                      AND d2.active = 1
                      AND d2.`type` IN ({$settings['order_types']})
                      AND d2.id = dr.link_to)
                  JOIN documents_cstm AS dc1
                    ON (dc1.model_id = d.id
                      AND dc1.var_id = 2011# for_date
                      AND dc1.num = 1" .
                      (!empty($filters['for_date_from']) ? "
                      AND '{$filters['for_date_from']}' <= dc1.value" : '') .
                      (!empty($filters['for_date_to']) ? "
                      AND dc1.value <= '{$filters['for_date_to']}'" : '') . "
                      AND dc1.lang = '')
                  JOIN documents_cstm AS dc2
                    ON (dc2.model_id = d.id
                      AND dc2.var_id = 2012# for_time
                      AND dc2.num = 1
                      AND dc2.lang = '')
                  JOIN documents_cstm AS dc20
                    ON (dc20.model_id = d.id
                      AND dc20.var_id = 2131# for_time_min
                      AND dc20.num = 1
                      AND dc20.lang = '')
                  JOIN documents_cstm AS dc14
                    ON (dc14.model_id = d2.id
                      AND dc14.var_id IN ({$settings['time_instalation_var_ids']})# time_instalation
                      AND dc14.num = 1
                      AND dc14.lang = '')
                  JOIN documents_cstm AS dc3
                    ON (dc3.model_id = d.id
                      AND dc3.var_id = 2005# client_alert
                      AND dc3.num = 1
                      AND dc3.lang = ''" .
                      (!empty($filters['unconfirmed']) ? "
                      AND dc3.value != '1'" : '') .")
                  JOIN documents_cstm AS dc4
                    ON (dc4.model_id = d.id
                      AND dc4.var_id = 2020# village_name
                      AND dc4.num = 1
                      AND dc4.lang = '')
                  JOIN documents_cstm AS dc5
                    ON (dc5.model_id = d.id
                      AND dc5.var_id = 2016# neighborhood_name
                      AND dc5.num = 1
                      AND dc5.lang = '')
                  JOIN documents_cstm AS dc6
                    ON (dc6.model_id = d.id
                      AND dc6.var_id = 2022# street_num
                      AND dc6.num = 1
                      AND dc6.lang = '')
                  JOIN documents_cstm AS dc7
                    ON (dc7.model_id = d.id
                      AND dc7.var_id = 2023# object_block
                      AND dc7.num = 1
                      AND dc7.lang = '')
                  JOIN documents_cstm AS dc8
                    ON (dc8.model_id = d.id
                      AND dc8.var_id = 2024# object_entrance
                      AND dc8.num = 1
                      AND dc8.lang = '')
                  JOIN documents_cstm AS dc9
                    ON (dc9.model_id = d.id
                      AND dc9.var_id = 2025# object_floor
                      AND dc9.num = 1
                      AND dc9.lang = '')
                  JOIN documents_cstm AS dc10
                    ON (dc10.model_id = d.id
                      AND dc10.var_id = 2026# object_flat
                      AND dc10.num = 1
                      AND dc10.lang = '')
                  JOIN documents_cstm AS dc11
                    ON (dc11.model_id = d.id
                      AND dc11.var_id = 2028# object_phone
                      AND dc11.num = 1
                      AND dc11.lang = '')
                  JOIN documents_cstm AS dc12
                    ON (dc12.model_id = d.id
                      AND dc12.var_id = 2029# object_mobile
                      AND dc12.num = 1
                      AND dc12.lang = '')
                  JOIN documents_cstm AS dc16
                    ON (dc16.model_id = d.id
                      AND dc16.var_id = '2013'# notes_montage
                      AND dc16.num = 1
                      AND dc16.lang = '')
                  JOIN documents_cstm AS dc17
                    ON (dc17.model_id = d.id
                      AND dc17.var_id = 2009
                      AND dc17.num = 1
                      AND dc17.lang = '')
                  JOIN documents_cstm AS dc18
                    ON (dc18.model_id = d.id
                      AND dc18.var_id = 2010
                      AND dc18.num = 1
                      AND dc18.lang = '')
                  JOIN documents_cstm AS dc21
                    ON (dc21.model_id = d.id
                      AND dc21.var_id = 2014
                      AND dc21.num = 1
                      AND dc21.lang = '')
                  JOIN documents_cstm AS dc13
                    ON (dc13.model_id = d2.id
                      AND dc13.var_id IN ({$settings['to_pay__total_var_ids']})# to_pay__total
                      AND dc13.num = 1
                      AND dc13.lang = '')
                  JOIN documents_cstm AS dc22
                    ON (dc22.model_id = d2.id
                      AND dc22.var_id IN ({$settings['deposit_customer__total_var_ids']})# deposit_customer__total
                      AND dc22.num = 1
                      AND dc22.lang = '')" .
                  (!empty($filters['production_of']) ? "
                  JOIN documents_cstm AS dc15
                    ON (dc15.model_id = d.id
                      AND dc15.var_id = 2008
                      AND dc15.num = 1
                      AND dc15.value = '{$filters['production_of']}'
                      AND dc15.lang = '')" : '') .
                  (!empty($filters['village']) ? "
                  JOIN documents_cstm AS dc19
                    ON (dc19.model_id = d.id
                      AND dc19.var_id = 2019
                      AND dc19.num = 1
                      AND dc19.value = '{$filters['village']}'
                      AND dc19.lang = '')" : '') .
                  (!empty($settings['display_order_notes']) ? "
                  LEFT JOIN " . DB_TABLE_DOCUMENTS_I18N . " AS d2i
                    ON d2i.parent_id = d2.id
                      AND d2i.lang = '{$registry['lang']}'" : '');
            $final_results['requests'] = $registry['db']->GetAssoc($query);

            if ($final_results['requests'] && !empty($settings['max_num_results']) && $settings['max_num_results'] < count($final_results['requests'])) {
                $registry['messages']->setError($registry['translater']->translate('error_reports_too_many_results'));
                $final_results['requests'] = array();
            }
        }

        if (!empty($final_results['requests'])) {
            // Get the installation men
            $query = "
                SELECT d.id   AS request_id,
                    dc1.value AS id,
                    dc2.value AS name,
                    dc3.value AS role,
                    dc4.value AS consumable
                  FROM documents AS d
                  JOIN documents_cstm AS dc1
                    ON (d.id IN (" . implode(',', array_keys($final_results['requests'])) . ")
                      AND dc1.model_id = d.id
                      AND dc1.var_id = 2001
                      AND dc1.lang IN ('', 'bg')
                      AND dc1.value != '')
                  JOIN documents_cstm AS dc2
                    ON (dc2.model_id = d.id
                      AND dc2.var_id = 2002
                      AND dc2.num = dc1.num
                      AND dc2.lang IN ('', 'bg'))
                  JOIN documents_cstm AS dc3
                    ON (dc3.model_id = d.id
                      AND dc3.var_id = 2003
                      AND dc3.num = dc1.num
                      AND dc3.lang IN ('', 'bg'))
                  JOIN documents_cstm AS dc4
                    ON (dc4.model_id = d.id
                      AND dc4.var_id = 2007
                      AND dc4.num = dc1.num
                      AND dc4.lang IN ('', 'bg'))
                  GROUP BY d.id, dc1.value
                  ORDER BY d.id, dc1.num";
            $installation_mans = $registry['db']->GetAll($query);
            $query = "
                SELECT nc.value AS man_id,
                    n.id        AS team_id
                  FROM " . DB_TABLE_NOMENCLATURES . " AS n
                  JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc
                    ON (n.deleted_by = 0
                      AND n.active = 1
                      AND n.`type` = 72
                      AND nc.model_id = n.id
                      AND nc.var_id = 17101
                      AND nc.value != ''
                      AND nc.lang IN ('', 'bg'))";
            $men_teams = $registry['db']->GetAssoc($query);
            if (!empty($installation_mans)) {
                foreach ($installation_mans as $man) {
                    $man['team_id'] = (isset($men_teams[$man['id']]) ? $men_teams[$man['id']] : '');
                    $final_results['additional_options']['installation_mans'][$man['request_id']][$man['id']] = $man;
                }
            }

            $query = "
                SELECT n.id,
                    nc.value AS color
                  FROM nom AS n
                  JOIN nom_cstm AS nc
                    ON (n.deleted_by = 0
                      AND n.active = 1
                      AND n.`type` = 72
                      AND nc.model_id = n.id
                      AND nc.var_id = 17106
                      AND nc.num = 1
                      AND nc.lang IN ('', 'bg')
                      AND nc.value != '')";
            $final_results['additional_options']['teams_colors'] = $registry['db']->GetAssoc($query);

            // Filter requests by team
            if (!empty($filters['employee'])) {
                if (!empty($final_results['additional_options']['installation_mans'])) {
                    $requests_filtered_by_team = array();
                    foreach ($final_results['additional_options']['installation_mans'] as $request_id => $request_men) {
                        if (array_intersect(array_keys($request_men), $filters['employee'])) {
                            $requests_filtered_by_team[] = $request_id;
                        }
                    }
                    $final_results['additional_options']['installation_mans'] = array_intersect_key($final_results['additional_options']['installation_mans'], array_flip($requests_filtered_by_team));
                    $final_results['requests'] = array_intersect_key($final_results['requests'], $final_results['additional_options']['installation_mans']);
                } else {
                    $final_results['requests'] = array();
                }
            }

            if (!empty($final_results['requests'])) {
                // Set the team keys for the requests
                if (!empty($requests_with_teams)) {
                    $requests_with_teams = array_intersect_key($requests_with_teams, $final_results['requests']);
                    $final_results['requests'] = array_replace_recursive($final_results['requests'], $requests_with_teams);
                }

                // Collect orders ids
                $orders_ids = array();

                $final_results['additional_options']['printable_installation_dates'] = array();

                // Sort results
                $sort_installation_date         = array();
                $sort_team_key                  = array();
                $sort_confirmation_client_order = array();
                $sort_installation_time         = array();
                $sort_client_distributor_name   = array();
                $sort_order_id                  = array();
                $sort_request_id                = array();
                foreach ($final_results['requests'] as $r_id => $r) {
                    $orders_ids[] = $r['order_id'];

                    // For a given date, if there is at least one non-finished request, then show the printers for this date
                    if ($r['status'] != 'closed') {
                        $final_results['additional_options']['printable_installation_dates'][$r['installation_date']] = true;
                    }

                    $sort_installation_date[$r_id]         = $r['installation_date'];
                    $sort_team_key[$r_id]                  = $r['team_key'];
                    $sort_confirmation_client_order[$r_id] = $r['confirmation_client_order'];
                    $sort_installation_time[$r_id]         = $r['installation_time'];
                    $sort_client_distributor_name[$r_id]   = $r['client_distributor_name'];
                    $sort_order_id[$r_id]                  = $r['order_id'];
                    $sort_request_id[$r_id]                = $r_id;
                }
                if ($registry->get('action') == 'export') {
                    array_multisort($sort_team_key, $sort_installation_time, $sort_order_id, $sort_request_id);
                } else {
                    array_multisort($sort_installation_date, $sort_team_key, $sort_confirmation_client_order, $sort_installation_time, $sort_client_distributor_name, $sort_order_id, $sort_request_id);
                }
                $sort_request_id = array_flip($sort_request_id);
                $final_results['requests'] = array_replace($sort_request_id, $final_results['requests']);

                $orders_ids = implode(',', $orders_ids);
                $query = "
                    SELECT model_id AS order_id,
                        SUM(CAST(value AS DECIMAL(10, 2))) AS paid_sum
                      FROM " . DB_TABLE_DOCUMENTS_CSTM . "
                      WHERE var_id IN ({$settings['link_pay_sum_var_ids']})# link_pay_sum
                        AND model_id IN ({$orders_ids})
                      GROUP BY model_id";
                $final_results['orders_paid_sums'] = $registry['db']->GetAssoc($query);

                $query = "
                    SELECT d.id   AS order_id,
                        dc1.value AS related_order_id,
                        dc2.value AS related_order_name
                      FROM documents AS d
                      JOIN _fields_meta AS fm1
                        ON (d.deleted_by = 0
                          AND d.active = 1
                          AND d.id IN ({$orders_ids})
                          AND fm1.model = 'Document'
                          AND fm1.model_type = d.`type`
                          AND fm1.name = 'related_order_id')
                      JOIN documents_cstm AS dc1
                        ON (dc1.model_id = d.id
                          AND dc1.var_id = fm1.id
                          AND dc1.value != ''
                          AND dc1.lang IN ('', 'bg'))
                      JOIN _fields_meta AS fm2
                        ON (fm2.model = 'Document'
                          AND fm2.model_type = d.`type`
                          AND fm2.name = 'related_order_name')
                      JOIN documents_cstm AS dc2
                        ON (dc2.model_id = d.id
                          AND dc2.var_id = fm2.id
                          AND dc2.num = dc1.num
                          AND dc2.value != ''
                          AND dc2.lang IN ('', 'bg'))";
                $related_orders = $registry['db']->GetAll($query);
                if (!empty($related_orders)) {
                    $related_orders_ids = array();
                    foreach ($related_orders as $ro) {
                        $final_results['related_orders'][$ro['order_id']][$ro['related_order_id']] = array(
                            'id' => $ro['related_order_id'],
                            'name' => $ro['related_order_name']
                        );
                        $related_orders_ids[$ro['order_id']][] = $ro['related_order_id'];
                    }
                    $final_results['additional_options']['related_orders_json'] = base64_encode(json_encode($related_orders_ids));
                }

//                 $query = "
//                     SELECT dr.link_to AS order_id,
//                         GROUP_CONCAT(
//                           DATE_FORMAT(
//                             IF (d.substatus = 77,
//                               dc.value,
//                               d.deadline
//                             ),
//                             '%d.%m.%Y'
//                           )
//                           SEPARATOR
//                           '\r\n'
//                         ) AS production_dates
//                       FROM documents AS d
//                       JOIN documents_relatives AS dr
//                         ON (d.deleted_by = 0
//                           AND d.active = 1
//                           AND d.type = 26
//                           AND d.substatus != 15
//                           AND dr.parent_model_name = 'Document'
//                           AND dr.parent_id = d.id
//                           AND dr.origin = 'inherited'
//                           AND dr.link_to_model_name = 'Document'
//                           AND dr.link_to IN ({$orders_ids}))
//                       JOIN documents_cstm AS dc
//                         ON (dc.model_id = d.id
//                           AND dc.var_id = 2608
//                           AND dc.num = 1
//                           AND dc.lang IN ('', 'bg'))
//                       GROUP BY dr.link_to";
//                 $final_results['orders_production_dates'] = $registry['db']->GetAssoc($query);

                $final_results['additional_options']['fields']['confirmation_client']['options'] = array(
                    array(
                        'label' => $registry['translater']->translate('yes'),
                        'option_value' => 1
                    ),
                    array(
                        'label' => $registry['translater']->translate('no'),
                        'option_value' => 2
                    )
                );
                $doc = new Document($registry, array('type' => 20));
                $doc->getVars();
                $vars = $doc->get('vars');
                $fields = array(
                    'inastallation_man' => null,
                    'rol_man' => null,
                    'inst_consumable' => null
                );
                foreach ($vars as $var) {
                    if (array_key_exists($var['name'], $fields)) {
                        $final_results['additional_options']['fields'][$var['name']] = $var;
                        if (isset($var['options'])) {
                            $final_results['additional_options']['fields'][$var['name']]['options_assoc'] = array();
                            foreach ($var['options'] as $option) {
                                $final_results['additional_options']['fields'][$var['name']]['options_assoc'][$option['option_value']] = $option['label'];
                            }
                        }
                        unset($fields[$var['name']]);
                        if (count($fields) < 1) {
                            break;
                        }
                    }
                }
                if (array_key_exists('inastallation_man', $final_results['additional_options']['fields'])) {
                    $final_results['additional_options']['fields']['inastallation_man']['autocomplete']['execute_after'] = 'reportUstilInstallationsSchedule.addTeam';
                }

                $query = "
                    SELECT d.id AS request_id,
                        d2.id,
                        d2.full_num
                      FROM documents AS d
                      JOIN documents_relatives AS dr
                        ON (d.id IN (" . implode(',', array_keys($final_results['requests'])) . ")
                          AND dr.link_to_model_name = 'Document'
                          AND dr.link_to = d.id
                          AND dr.parent_model_name = 'Document'
                          AND dr.origin = 'inherited')
                      JOIN documents AS d2
                        ON (d2.deleted_by = 0
                          AND d2.type = '9'
                          AND d2.id = dr.parent_id" .
                          (!empty($settings['protocol_substatus_failed_installation']) ? "
                          AND d2.substatus != '{$settings['protocol_substatus_failed_installation']}'" : '') . ")";
                $requests_protocols = $registry['db']->GetAll($query);
                foreach ($requests_protocols as $rp) {
                    $final_results['requests_protocols'][array_shift($rp)][] = $rp;
                }

    //             $final_results['additional_options']['fields']['inastallation_man'] = Report_Filters::getAdditionalField($registry, 'Document', 20, 'inastallation_man');
    //             $final_results['additional_options']['fields']['rol_man'] = Report_Filters::getAdditionalField($registry, 'Document', 20, 'rol_man');
    //             $final_results['additional_options']['fields']['inst_consumable'] = Report_Filters::getAdditionalField($registry, 'Document', 20, 'inst_consumable');
                if ($registry->get('action') == 'export') {
                    $query = "
                        SELECT d.id      AS id,
                            dc6.value    AS street,
                            dc7.value    AS block,
                            dc8.value    AS entrance,
                            dc9.value    AS floor,
                            dc10.value   AS flat,
                            dc18.value   AS township_id,
                            dc19.value   AS township_name,
                            dc20.value   AS village_id,
                            dc21.value   AS neighborhood_id
                          FROM documents AS d
                          JOIN documents_cstm AS dc6
                            ON (d.id IN (" . implode(',', array_keys($final_results['requests'])) . ")
                              AND dc6.model_id = d.id
                              AND dc6.var_id = 2022 # street_num
                              AND dc6.num = 1
                              AND dc6.lang = '')
                          JOIN documents_cstm AS dc7
                            ON (dc7.model_id = d.id
                              AND dc7.var_id = 2023 # object_block
                              AND dc7.num = 1
                              AND dc7.lang = '')
                          JOIN documents_cstm AS dc8
                            ON (dc8.model_id = d.id
                              AND dc8.var_id = 2024 # object_entrance
                              AND dc8.num = 1
                              AND dc8.lang = '')
                          JOIN documents_cstm AS dc9
                            ON (dc9.model_id = d.id
                              AND dc9.var_id = 2025 # object_floor
                              AND dc9.num = 1
                              AND dc9.lang = '')
                          JOIN documents_cstm AS dc10
                            ON (dc10.model_id = d.id
                              AND dc10.var_id = 2026 # object_flat
                              AND dc10.num = 1
                              AND dc10.lang = '')
                          JOIN documents_cstm AS dc18
                            ON (dc18.model_id = d.id
                              AND dc18.var_id = 2017 # township_id
                              AND dc18.num = 1
                              AND dc18.lang = '')
                          JOIN documents_cstm AS dc19
                            ON (dc19.model_id = d.id
                              AND dc19.var_id = 2018 # township_name
                              AND dc19.num = 1
                              AND dc19.lang = '')
                          JOIN documents_cstm AS dc20
                            ON (dc20.model_id = d.id
                              AND dc20.var_id = 2019 # village_id
                              AND dc20.num = 1
                              AND dc20.lang = '')
                          JOIN documents_cstm AS dc21
                            ON (dc21.model_id = d.id
                              AND dc21.var_id = 2015 # neighborhood_id
                              AND dc21.num = 1
                              AND dc21.lang = '')";
                    $final_results['requests_additional_export_data'] = $registry['db']->GetAssoc($query);

                    $query = "
                        SELECT d2.id     AS order_id,
                            d2.type      AS order_type,
                            d2.trademark AS order_object_id,
                            d2.customer  AS order_customer_id,
                            dc16.value   AS order_delivery,
                            dc17.value   AS order_installation,
                            d2.full_num  AS order_full_num,
                            dc22.value   AS order_name_order,
                            dc23.value   AS order_num_total,
                            di2.name     AS order_name
                          FROM documents AS d2
                          JOIN documents_cstm AS dc16
                            ON (d2.id IN ({$orders_ids})
                              AND dc16.model_id = d2.id
                              AND dc16.var_id IN ({$settings['delivery_var_ids']})# delivery
                              AND dc16.num = 1
                              AND dc16.lang IN ('', 'bg'))
                          JOIN documents_cstm AS dc17
                            ON (dc17.model_id = d2.id
                              AND dc17.var_id IN ({$settings['instalation_var_ids']})# instalation
                              AND dc17.num = 1
                              AND dc17.lang IN ('', 'bg'))
                          JOIN documents_cstm AS dc22
                            ON (dc22.model_id = d2.id
                              AND dc22.var_id IN ({$settings['name_order_var_ids']})# name_order
                              AND dc22.num = 1
                              AND dc22.lang IN ('', 'bg'))
                          JOIN documents_cstm AS dc23
                            ON (dc23.model_id = d2.id
                              AND dc23.var_id IN ({$settings['num_total_var_ids']})# num_total
                              AND dc23.num = 1
                              AND dc23.lang IN ('', 'bg'))
                          JOIN documents_i18n AS di2
                            ON (di2.parent_id = d2.id
                              AND di2.lang = 'bg')";
                    $final_results['orders_additional_export_data'] = $registry['db']->GetAssoc($query);

                    $query = "
                        SELECT d.id, dc.value
                          FROM documents AS d
                          JOIN documents_cstm AS dc
                            ON (d.id IN ({$orders_ids})
                              AND d.type = 10
                              AND dc.model_id = d.id
                              AND dc.var_id = 1047# vertical_cornice
                              AND dc.num = 1
                              AND dc.lang IN ('', 'bg'))";
                    $final_results['orders_vertical_blinds_cornice'] = $registry['db']->GetAssoc($query);

                    // If some of the orders are of type "Order for construction services" (id 16)
                    $orders_construction = $registry['db']->GetCol("SELECT id FROM documents WHERE type = 16 AND id IN ({$orders_ids})");
                    $final_results['orders_construction_additional_export_data'] = array();
                    if ($orders_construction) {
                        // Get the table for construction services from it, to be copied into the protocols
                        $query = "
                            SELECT name, id
                              FROM " . DB_TABLE_FIELDS_META . "
                              WHERE model = 'Document'
                                AND model_type = 16
                                AND `grouping` = 55
                                AND type != 'group'";
                        $orders_construction_fields = $registry['db']->GetAll($query);
                        if (!empty($orders_construction_fields)) {
                            $query = array(
                                'select' => array("
                                    SELECT d.id AS id"),
                                'from' => array("
                                    FROM documents AS d")
                            );
                            $i = 1;
                            foreach ($orders_construction_fields as $orders_construction_field) {
                                $query['select'][] = "dc{$i}.value AS {$orders_construction_field['name']}";
                                $query['from'][] = "
                                    JOIN documents_cstm AS dc{$i}
                                      ON (dc{$i}.model_id = d.id
                                        AND dc{$i}.var_id = {$orders_construction_field['id']}
                                        AND dc{$i}.lang = ''" .
                                        ($i == 1 ? "
                                        AND d.id IN (" . implode(", ", $orders_construction) . ")" : "
                                        AND dc{$i}.num = dc1.num") . ")";
                                $i++;
                            }
                            $query['select'] = implode(",
                                    ", $query['select']);
                            $query['from'] = implode("
                                  ", $query['from']);
                            $query = implode("
                                  ", $query);
                            $orders_construction = $registry['db']->GetAll($query);
                            foreach ($orders_construction as $oc) {
                                foreach ($oc as $k => $v) {
                                    if ($k == 'id') {
                                        continue;
                                    }
                                    $final_results['orders_construction_additional_export_data'][$oc['id']][$k][] = $v;
                                }
                            }
                        }
                    }

                    $query = "
                        SELECT d.id   AS order_id,
                            dc1.value AS accessories_services_id,
                            dc2.value AS accessories_services_name,
                            dc3.value AS accessories_services_quantity,
                            dc4.value AS accessories_services_measure
                          FROM documents AS d
                          JOIN documents_cstm AS dc1
                            ON (d.id IN ({$orders_ids})
                              AND dc1.model_id = d.id
                              AND dc1.var_id IN ({$settings['accessories_services_id_var_ids']})# accessories_services_id
                              AND dc1.value != ''
                              AND dc1.lang IN ('', 'bg'))
                          JOIN documents_cstm AS dc2
                            ON (dc2.model_id = d.id
                              AND dc2.var_id IN ({$settings['accessories_services_name_var_ids']})# accessories_services_name
                              AND dc2.value != ''
                              AND dc2.num = dc1.num
                              AND dc2.lang IN ('', 'bg'))
                          JOIN documents_cstm AS dc3
                            ON (dc3.model_id = d.id
                              AND dc3.var_id IN ({$settings['accessories_services_quantity_var_ids']})# accessories_services_quantity
                              AND dc3.value != ''
                              AND dc3.num = dc1.num
                              AND dc3.lang IN ('', 'bg'))
                          JOIN documents_cstm AS dc4
                            ON (dc4.model_id = d.id
                              AND dc4.var_id IN ({$settings['accessories_services_measure_var_ids']})# accessories_services_measure
                              AND dc4.value != ''
                              AND dc4.num = dc1.num
                              AND dc4.lang IN ('', 'bg'))";
                    $orders_accessories = $registry['db']->GetAll($query);
                    foreach ($orders_accessories as $a) {
                        $final_results['orders_accessories'][array_shift($a)][] = $a;
                    }

                    $query = "
                        SELECT dc.model_id AS id,
                            dc.value AS num,
                            dc1.value AS size,
                            dc2.value AS `count`
                          FROM documents_cstm AS dc
                          JOIN documents_cstm AS dc1
                            ON (dc.model_id IN ({$orders_ids})
                              AND dc.var_id = 201633
                              AND dc.num = 1
                              AND dc.lang IN ('', 'bg')
                              AND dc.value != ''
                              AND dc1.var_id = 201626
                              AND dc1.model_id = dc.model_id
                              AND dc1.num = dc.num
                              AND dc1.lang = dc.lang)
                          JOIN documents_cstm AS dc2
                            ON (dc2.var_id = 301538
                              AND dc2.model_id = dc.model_id
                              AND dc2.num = dc.num
                              AND dc2.lang = dc.lang)";
                    $final_results['orders_additional_orders'] = $registry['db']->GetAssoc($query);

                    $final_results['teams'] = array();
                    foreach ($final_results['requests'] as $request_id => $request) {
                        $final_results['additional_options']['requests_orders'][$request_id] = $request['order_id'];

                        $team = '';
                        if (isset($final_results['additional_options']['installation_mans'][$request_id])) {
                            $team = array_keys($final_results['additional_options']['installation_mans'][$request_id]);
                            sort($team);
                            $team = implode($team);
                            $final_results['teams'][$team]['men'] = $final_results['additional_options']['installation_mans'][$request_id];
                        }
                        $final_results['teams'][$team]['requests'][$request_id] = $request;
                    }
                    $final_results['additional_options']['placeholders']['installations_schedule'] = array('name' => 'installations_schedule');
                }
            }
        }

        $final_results['additional_options']['dont_show_export_button'] = true;

        if (!empty($filters['paginate'])) {
            $results = array($final_results, 0);
        } else {
            $results = $final_results;
        }

        return $results;
    }
}

?>
