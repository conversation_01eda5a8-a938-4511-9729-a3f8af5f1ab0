{if !$prepare_placeholder}
  <html>
    <head>
      <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
    </head>
    <body>
{/if}
  {if !$prepare_placeholder || $prepare_placeholder eq 'available_tsii_list_tables'}
    <table border="0" cellpadding="0" cellspacing="0">
      <tr>
        <td style="text-align: center; font-weight: bold; vertical-align: middle;">{#reports_t1_name#|escape}</td>
      </tr>
      <tr>
        <td>
          <table width="998" border="1" cellpadding="2" cellspacing="0">
            <tr>
              <td style="text-align: center; font-weight: bold; vertical-align: middle; background-color: #DFDFDF;" width="293"><div style="width: 293px">{#reports_t1_h1#|escape}</div></td>
              <td style="text-align: center; font-weight: bold; vertical-align: middle; background-color: #DFDFDF;" width="144"><div style="width: 144px">{#reports_t1_h2#|escape}</div></td>
              <td style="text-align: center; font-weight: bold; vertical-align: middle; background-color: #DFDFDF;" width="144"><div style="width: 144px">{#reports_t1_h3#|escape}</div></td>
              <td style="text-align: center; font-weight: bold; vertical-align: middle; background-color: #DFDFDF;" width="194"><div style="width: 194px">{#reports_t1_h4#|escape}</div></td>
              <td style="text-align: center; font-weight: bold; vertical-align: middle; background-color: #DFDFDF;" width="193"><div style="width: 193px">{#reports_t1_h5#|escape}</div></td>
            </tr>
            {foreach from=$reports_results.tsii_kalibs item=tsii_kalib key=nomenclature_id}
              <tr>
                <td style="text-align: left">{$tsii_kalib.name|escape|default:"--"}, {$tsii_kalib.tech_ident_num|escape|default:"--"}, {$tsii_kalib.tech_producer|escape|default:"--"}</td>
                <td style="text-align: left">{$tsii_kalib.tech_range|escape|default:"&nbsp;"}</td>
                <td style="text-align: left">{$tsii_kalib.tech_borders|escape|default:"&nbsp;"}</td>
                <td style="text-align: left">{$tsii_kalib.document_full_num|escape|default:"--"} / {$tsii_kalib.document_date|escape|default:"--"}, <br />{$tsii_kalib.document_customer|escape|default:"--"}</td>
                <td style="text-align: left">{$tsii_kalib.tech_charact|escape|default:"&nbsp;"}</td>
              </tr>
            {foreachelse}
              <tr>
                <td colspan="5" style="text-align: center;">--</td>
              </tr>
            {/foreach}
          </table>
        </td>
      </tr>
    </table>
    <br />
    <table border="0" cellpadding="0" cellspacing="0">
      <tr>
        <td style="text-align: center; font-weight: bold; vertical-align: middle;">{#reports_t2_name#|escape}</td>
      </tr>
      <tr>
        <td>
          <table width="998" border="1" cellpadding="2" cellspacing="0">
            <tr>
              <td style="text-align: center; font-weight: bold; vertical-align: middle; background-color: #DFDFDF;" width="443"><div style="width: 443px">{#reports_t2_h1#|escape}</div></td>
              <td style="text-align: center; font-weight: bold; vertical-align: middle; background-color: #DFDFDF;" width="244"><div style="width: 244px">{#reports_t2_h2#|escape}</div></td>
              <td style="text-align: center; font-weight: bold; vertical-align: middle; background-color: #DFDFDF;" width="94" ><div style="width:  94px">{#reports_t2_h3#|escape}</div></td>
              <td style="text-align: center; font-weight: bold; vertical-align: middle; background-color: #DFDFDF;" width="193"><div style="width: 193px">{#reports_t2_h4#|escape}</div></td>
            </tr>
            {foreach from=$reports_results.tsii_no_kalibs item=tsii_no_kalib key=nomenclature_id}
              <tr>
                <td style="text-align: left">{$tsii_no_kalib.name|escape|default:"--"}, {$tsii_no_kalib.tech_ident_num|escape|default:"--"}, {$tsii_no_kalib.tech_producer|escape|default:"--"}</td>
                <td style="text-align: left">{$tsii_no_kalib.document_full_num|escape|default:"--"} / {$tsii_no_kalib.document_date|escape|default:"--"}</td>
                <td style="text-align: left">{$tsii_no_kalib.tech_interval|escape|default:"&nbsp;"}</td>
                <td style="text-align: left">{$tsii_no_kalib.tech_charact|escape|default:"&nbsp;"}</td>
              </tr>
            {foreachelse}
              <tr>
                <td colspan="4" style="text-align: center;">--</td>
              </tr>
            {/foreach}
          </table>
        </td>
      </tr>
    </table>
    <br />
    <table border="0" cellpadding="0" cellspacing="0">
      <tr>
        <td style="text-align: center; font-weight: bold; vertical-align: middle;">{#reports_t3_name#|escape}</td>
      </tr>
      <tr>
        <td>
          <table width="998" border="1" cellpadding="2" cellspacing="0">
            <tr>
              <td style="text-align: center; font-weight: bold; vertical-align: middle; background-color: #DFDFDF;" width="493"><div style="width: 493px">{#reports_t3_h1#|escape}</div></td>
              <td style="text-align: center; font-weight: bold; vertical-align: middle; background-color: #DFDFDF;" width="294"><div style="width: 294px">{#reports_t3_h2#|escape}</div></td>
              <td style="text-align: center; font-weight: bold; vertical-align: middle; background-color: #DFDFDF;" width="193"><div style="width: 193px">{#reports_t3_h3#|escape}</div></td>
            </tr>
            {foreach from=$reports_results.equipments item=equipment key=nomenclature_id}
              <tr>
                <td style="text-align: left">{$equipment.name|escape|default:"--"}, {$equipment.tech_ident_num|escape|default:"--"}, {$equipment.tech_producer|escape|default:"--"}</td>
                <td style="text-align: left">{$equipment.document_full_num|escape|default:"--"} / {$equipment.document_date|escape|default:"--"}</td>
                <td style="text-align: left">{$equipment.tech_charact|escape|default:"&nbsp;"}</td>
              </tr>
            {foreachelse}
              <tr>
                <td colspan="3" style="text-align: center;">--</td>
              </tr>
            {/foreach}
          </table>
        </td>
      </tr>
    </table>
  {/if}
{if !$prepare_placeholder}
    </body>
  </html>
{/if}
