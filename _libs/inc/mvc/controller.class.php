<?php

use <PERSON>zo<PERSON>\Mvc\ControllerInterface\ActionsInterface;
use Nzoom\Mvc\ControllerInterface\GlobalValsInterface;
use Nzoom\Mvc\ControllerInterface\LockedRecordsInterface;
use Nzoom\Mvc\ControllerInterface\ManageModelInterface;
use Nzoom\Mvc\ControllerInterface\ManageViewerInterface;
use Nzoom\Mvc\ControllerTrait\CheckAccessTrait;
use Nzoom\Mvc\ControllerTrait\I18nTrait;
use Nzoom\Mvc\ControllerTrait\RedirectTrait;

Abstract Class Controller implements LockedRecordsInterface, ActionsInterface, GlobalValsInterface,
                                     ManageModelInterface, ManageViewerInterface
{
    use CheckAccessTrait;
    use I18nTrait;
    use RedirectTrait;

    /**
     * Module name
     */
    public $module;

    /**
     * Controller name, usually it matches the module name
     */
    public $controller;

    /**
     * Action name one of the add, edit, delete, list, etc.
     */
    public $action;

    /**
     * Action name one of the add, edit, delete, list, etc.
     */
    public $defaultAction = 'list';

    /**
     * Automation user
     */
    public $automationUser = '';

    /**
     * Original user
     */
    public $originalUser = '';

    /**
     * Default action definitions (add, edit, delete, list, etc.)
     */
    public $actionDefinitions = array(
        'list', 'search', 'add', 'multiadd',
        'view', 'edit', 'translate'
    );

    /**
     * Default after action definitions (add, edit, delete, list, etc.)
     */
    public $afterActionDefinitions = array(
        'list', 'search', 'add', 'multiadd',
        'view', 'edit', 'translate'
    );

    /**
     * Default action filters
     */
    public $actionFilterDefinitions = array(
        'myassigned', 'myresponsible', 'myobserver', 'myrecords'
    );

    /**
     * Available actions (add, edit, delete, list, etc.)
     */
    public $actions;

    /**
     * Sets default action to execute after success
     */
    public $afterAction;

    /**
     * Available after actions
     */
    public $afterActions;

    /**
     * Defines whether the action has been finished or not
     */
    public $actionCompleted = false;

    /**
     * Defines whether the action has been canceled or not
     */
    public $actionCanceled = false;

    /**
     * Default definitions for actions with multiple records
     */
    public $multipleSelectionDefinitions = array(
        'delete', 'restore', 'activate', 'deactivate', 'export',
        'multiedit', 'multistatus', 'multitag', 'multiremovetag', 'multiprint', 'printlist', 'multiarchive',
        'approve_send', 'approve', 'disapprove', 'issue', 'issue_send',
        'multiaddinvoice', 'ajax_multiaddinvoice', 'change_templates_observer'
    );

    /**
     * System modules where export action is specific and is not applied to multiple records
     */
    public $systemExportModules = array(
        'translations', 'cookbooks'
    );

    /**
     * Name of the main model to be controlled
     */
    public $modelName;

    /**
     * Name of the model factory
     */
    public $modelFactoryName;

    /**
     * The main model object used by the controller
     */
    public $model;

    /**
     * Path to directory of the models and their factories
     */
    public $modelsDir;

    /**
     * Path to directory of the module viewers
     */
    public $viewersDir;

    /**
     * Path to directory of the this module controller
     */
    public $controllersDir;

    /**
     * Regular expression that defines action that requires model
     * Define regular expression to recognize the actions that require model_id
     */
    const REGEX_DEFINING_MODEL = '#^(view|edit|translate|profile|phases|assign|addsubstage|stageactivities|clone|transform|multitransform|generate(?!_report)|attachments|observer|articles|services|print(settings|form)?$|relatives|payments|balance|dependencies|finish|history|comments|timesheets|branches|contactpersons|trademarks|permissions|revision|menu|transfer|tag|(companiesand)?offices|cachboxesandbankaccounts|displaysettings|addinvoice|addcorrect|addcreditdebit|addproformainvoice|email|addhandover|commodities_reservation|release_reservation|addannex|annulmentsubtype|statistics|listinvoices|distribute|enter|control|communications|remind|parties|archive$|extract|addcredit|adddebit|addpayment|annul$|setstatus|receive_date|listhandovers|(proforma)?advances)|allocate(_costs)?|create#';

    /**
     * Regular expression that defines actions that require lock of the model
     */
    const REGEX_DEFINING_LOCK = '#(edit|translate|phases|allocate)#';

    /**
     * Constructor sets action
     *
     * @param Registry $registry - the main registry
     * @throws Exception
     */
    public function __construct(&$registry) {
        $this->registry = &$registry;

        //set checkpoint for controller start
        $this->registry['timer']->setCheckpoint('controller', 'Controller execution started');

        // the router has already setup the module, controller and the action
        $this->module     = $this->registry['module'];
        $this->controller = $this->registry['controller'];
        if ($this->registry['action']) {
            $this->action = $this->registry['action'];
        } else {
            $this->action = $this->defaultAction;
            $this->registry->set('action', $this->defaultAction, true);
        }

        //set model name
        if (empty($this->modelName)) {
            $this->modelName = preg_replace('#s_Controller#', '', get_class($this));
        }
        //set model factory name
        if (empty($this->modelFactoryName)) {
            $this->modelFactoryName = preg_replace('#_Controller#', '', get_class($this));
        }

        //check for returning link
        if ($this->registry['request']->get('save_url') && isset($_SERVER['HTTP_REFERER'])) {
            $this->registry['session']->set('return_link', $_SERVER['HTTP_REFERER']);
        }

        //check access to module and action
        $this->checkAccessModule();

        if ($this->registry['locking_records']) {
            //unlock records
            $this->unlockRecords();
        }

        //set paths to MVC
        $this->setPaths();

        //include the MVC files
        $this->includeLibs();

        if (isset($this::$searchAdditionalVarsSwitch)) {
            $this->registry->set('searchAdditionalVarsSwitch', $this::$searchAdditionalVarsSwitch, true);
        }

        //manage multiple selection checkboxes
        $this->manageMultipleSelectionCheckboxes();
    }

    /**
     * sets paths within the module
     */
    public function setPaths() {
        $this->modelsDir      = PH_MODULES_DIR . $this->module . '/models/';
        $this->viewersDir     = PH_MODULES_DIR . $this->module . '/viewers/';
        $this->controllersDir = PH_MODULES_DIR . $this->module . '/controllers/';
        $this->templatesDir   = PH_MODULES_DIR . $this->module . '/templates/';
        $this->i18nDir        = PH_MODULES_DIR . $this->module . '/i18n/';

        return true;
    }

    /**
     * try to include before hand the most essential libs - models, viewers, etc.
     */
    public function includeLibs() {
        $modelNamePlural = (isset($this->modelFactoryName)) ? $this->modelFactoryName : $this->modelName . 's';
        $modelNamePlural = preg_replace('#_#', '.', $modelNamePlural);

        //include model file
        $modelLib = $this->modelsDir . strtolower($modelNamePlural) . '.model.php';
        if (file_exists($modelLib)) {
            include_once $modelLib;
        }

        //include model factory file
        $modelFactoryLib = $this->modelsDir . strtolower($modelNamePlural) . '.factory.php';
        if (is_readable($modelFactoryLib)) {
            include_once $modelFactoryLib;
        }

        //include viewer file
        $viewerName = strtolower($this->module);
        if ($this->controller != $this->module) {
            $viewerName .= '.' . strtolower($this->controller);
        }

        $viewerLib = $this->viewersDir . $viewerName . '.' . $this->action . '.viewer.php';
        if (is_readable($viewerLib)) {
            include_once $viewerLib;
        }

        return true;
    }

    /**
     * Sets explicitly the requested action into model and in registry
     *
     * @param string $action - explicitly requested action
     * @return string - current action name
     */
    public function setAction($action) {
        $this->action = $action;
        //force setting of action
        $this->registry->set('action', $action, true);

        return $this->action;
    }

    /**
     * Gets viewer object according to current module and controller and
     * specified name / current action
     *
     * @param string $viewerName - explicitly specified viewer name
     * @return Viewer - Viewer object or false on error
     */
    public function getViewer($viewerName = '') {
        if (empty($viewerName)) {
            switch ($this->action) {
            case 'home':
            case 'index':
            case 'dashboard':
                if ($this->module == 'index') {
                    $viewerName  = 'frontend';
                } else {
                    $viewerName  = 'index';
                }
                break;
            case 'list':
            case 'restore':
            case 'delete':
            case 'purge':
            case 'activate':
            case 'deactivate':
            case 'approve':
            case 'approve_send':
            case 'issue':
            case 'issue_send':
            case 'disapprove':
                $viewerName  = 'list';
                break;
            default:
                $viewerName  = $this->action;
            }
        }

        //prepare viewer file name and class name
        if (preg_match('#_#', $viewerName)) {
            //replace underscore (_) with dot (.)
            $viewerFileName = strtolower(preg_replace('#_#', '.', $viewerName));

            //capitalize all words of the class name
            $viewerClassName = ucwords(strtolower(preg_replace('#_#', ' ', $viewerName)));
            $viewerClassName = preg_replace('# #', '_', $viewerClassName);
        } else {
            $viewerFileName = strtolower($viewerName);
            $viewerClassName = ucfirst(strtolower($viewerName));
        }

        if ($this->module != 'reports') {
            if ($this->controller != $this->module) {
                $viewerFileName = strtolower($this->controller) . '.' . $viewerFileName;
                $viewerClassName = ucfirst(strtolower($this->controller)) . '_' . $viewerClassName;
            }

            //load the viewer file
            $viewerFile = $this->viewersDir . $this->module . '.' . $viewerFileName . '.viewer.php';
        } else {
            $viewerFileName = strtolower(preg_replace('#_#', '.', $this->controller)) . '.' . $viewerName;
            $viewerClassName = ucfirst(strtolower($this->controller)) . '_' . $viewerClassName;
            //load the viewer file
            $report_type = $this->getReportType();
            @$viewerFile = PH_MODULES_DIR . 'reports/plugins/' . $report_type['name'] .
                          '/viewers/' . $viewerFileName . '.viewer.php';
            if (!is_readable($viewerFile)) {
                $viewerClassName = $viewerName;
                $viewerFileName = $viewerName;
                $viewerFile = $this->viewersDir . $this->module . '.' . $viewerFileName . '.viewer.php';
            }
        }

        if (is_readable($viewerFile)) {
            include_once $viewerFile;
            $viewerClass = ucfirst(strtolower($this->module)) . '_' . $viewerClassName . '_Viewer';

            //the "true" argument defines that the viewer is MAIN
            $is_main = preg_match('#^(dashlet|subpanel|filter|addquick)$#', $viewerName) ? false : true;
            return $this->viewer = new $viewerClass($this->registry, $is_main, [ 'templateDir' => $this->templatesDir ]);
        }

        return false;
    }

    /**
     * generic execute method
     * this is the method for internal controller dispatching
     */
    abstract function execute();

    /**
     * Loads custom additional i18n files,
     * All files that reside in the module's i18n folder
     *
     * @param string|array - path to file or files
     */
    public function loadI18NFiles($files) {
        if (!$this->registry) {
            $translater = &$GLOBALS['translater'];
        } else {
            $translater = &$this->registry['translater'];
        }

        $translater->loadFile($files, 'custom');
    }

    /**
     * Gets actions available for the current user
     *
     * @param array $action_defs - custom action definitions
     * @return array - all available actions with their details
     */
    public function getActions($action_defs = array()) {
        if (empty($action_defs)) {
            //set default action definitions
            $action_defs = $this->actionDefinitions;
        }

        //get model
        $this->getModel();
        if ($this->model) {
            $this->model->unsanitize();

            //prepare the model to be displayed
            $this->model->prepare();

            $this->model->sanitize();
        }

        if ($this->model) {
            if (!in_array($this->module, $this->systemExportModules) && in_array('export', $action_defs)) {
                unset($action_defs[array_search('export', $action_defs)]);
            }
        } else {
            foreach ($action_defs as $idx => $def) {
                if ($this->isModelRequired($def)) {
                    unset($action_defs[$idx]);
                }
            }
        }

        //get permissions of the currently logged user
        $this->getUserPermissions();

        //check for secondary controller
        $secondary_controller = '';
        $module_check = $this->module;
        if ($this->module != $this->controller) {
            $secondary_controller = $this->registry['controller'];
            $module_check .= '_' . $this->controller;
        }

        // initialize variable with a value of expected type - array
        $this->actions = array();

        $theme = $this->registry['theme'];

        //prepare the actions
        foreach ($action_defs as $name) {

            //check permissions
            if (!$this->checkActionPermissions($module_check, $name)) {
                //no permissions for this action
                //skip this action
                continue;
            } elseif ($this->model && !$this->model->checkPermissions($name)) {
                //the action is permitted but the ownership does not match
                //skip this action
                continue;
            }

            //define links
            if ($this->isModelRequired($name)) {
                //skip the actions which require model id when the model id is not defined
                if (!$this->model->get('id')) {
                    continue;
                }

                $model_lang = $this->model->get('model_lang');
                $lang = $this->model->get('translations') && is_array($this->model->get('translations')) && in_array($model_lang, $this->model->get('translations')) ? $model_lang : $this->registry['lang'];
                $url = sprintf('%s?%s=%s%s&amp;%s=%s&amp;%s=%s&amp;model_lang=%s%s',
                                $_SERVER['PHP_SELF'],
                                $this->registry['module_param'], $this->module,
                                ($secondary_controller) ?
                                    '&amp;' . $this->registry['controller_param'] . '=' . $secondary_controller : '',
                                $this->registry['action_param'], $name,
                                $name, $this->model->get('id'), $lang,
                                ($this->registry['model_locked_by_me']) ? '&amp;unlock=' . $this->registry['model_locked_by_me'] : '');
            } else {
                $url = sprintf('%s?%s=%s%s&amp;%s=%s%s',
                                $_SERVER['PHP_SELF'],
                                $this->registry['module_param'], $this->module,
                                ($secondary_controller) ?
                                    '&amp;' . $this->registry['controller_param'] . '=' . $secondary_controller : '',
                                $this->registry['action_param'], $name,
                                ($this->registry['model_locked_by_me']) ? '&amp;unlock=' . $this->registry['model_locked_by_me'] : '');
            }

            //define options
            $options = (isset(${$name . '_options'})) ? ${$name . '_options'} : array();
            if ($name == 'search' || $name == 'filter') {
                $options = 1;
            }

            //define when the action tab is expanded
            $expanded = false;
            // in filter action tab is expanded
            if ($name == 'filter') {
                $expanded = true;
            }

            //create actions array
            $this->actions[$name] = array(
                'module_param'     => $this->registry['module_param'],
                'module'           => $this->module,
                'controller_param' => ($this->controller != $this->module) ? $this->registry['controller_param'] : '',
                'controller'       => ($this->controller != $this->module) ? $this->controller : '',
                'action_param'     => $this->registry['action_param'],
                'action'           => $name,
                'model_id'         => ($this->isModelRequired($name) && $this->model) ? $this->model->get('id') : 0,
                'model_lang'       => ($this->isModelRequired($name) && $this->model && $name != 'translate') ? $this->model->get('model_lang') : '',
                'name'             => $name,
                'label'            => $this->i18n($name),
                'selected'         => ($this->action == $name) ? true : false,
                'expanded'         => $expanded,
                'url'              => $url,
                'options'          => (!empty($options)) ? $options : false
            );
            if (is_callable([$theme,'getIconForAction'])) {
                $this->actions[$name]['icon'] = $theme->getIconForAction($name);
            }
            if ($name == 'search' || $name == 'filter') {
                $session_param = strtolower($name . '_' . $this->modelName);
                if ($this->registry['request']->get('model_lang')) {
                    $this->actions[$name]['model_lang'] = $this->registry['request']->get('model_lang');
                }
                $this->actions[$name]['session_param'] = $session_param;
            }

        }

        if (!$this->registry['include_calendar']) {
            $this->registry['include_calendar'] = true;
        }

        //prepare options for export
        if (isset($this->actions['export']) && $this->checkActionPermissions($module_check, 'export')) {
            if (!in_array($module_check, $this->systemExportModules)) {
                $this->actions['export']['options'] = $this->prepareExportOptions();
                $this->actions['export']['ajax_no'] = 1;
                $this->actions['export']['disable_items_before_execute'] = 'diselectItemsBeforeMultiAction(this);';
            }
        } else {
            unset($this->actions['export']);
        }

        // Prepare options for clone
        if (isset($this->actions['clone']) && in_array('relatives', $this->actionDefinitions)) {
            $this->actions['clone']['options'] = array(
                array(
                    'name' => 'skip_relatives',
                    'type' => 'checkbox_group',
                    'options' => array(
                        array(
                            'label' => '',
                            'option_value' => 1
                        )
                    ),
                    'value' => array(1),
                    'label' => $this->i18n('skip_relatives'),
                    'help' => $this->i18n('help_skip_relatives')
                )
            );
        }

        if (!$this->model && $this->registry['validLogin'] && $this->registry['currentUser']) {
            $saved_filters = Filters::search(
                $this->registry,
                array(
                    'where' => array(
                        "f.module = '{$this->module}'",
                        "f.controller = '{$this->controller}'",
                        "f.user_defined = 1",
                        "f.action = 1",
                        "f.added_by = '{$this->registry['currentUser']->get('id')}'",
                    ),
                    'sort' => array(
                        'f.id ASC',
                    ),
                    'sanitize' => true,
                )
            );
            if ($saved_filters) {
                $filter_actions = array();
                // the main action
                $action = 'search';
                // add saved filters as actions
                foreach ($saved_filters as $filter) {
                    $name = $action . $filter->get('id');
                    $url = sprintf(
                        '%s?%s=%s%s&amp;%s=%s&filters_action=loadfilter&filter_name=%d%s',
                        $_SERVER['PHP_SELF'],
                        $this->registry['module_param'],
                        $this->module,
                        ($secondary_controller) ?
                        '&amp;' . $this->registry['controller_param'] . '=' . $secondary_controller : '',
                        $this->registry['action_param'],
                        $action,
                        $filter->get('id'),
                        ($this->registry['model_locked_by_me']) ? '&amp;unlock=' . $this->registry['model_locked_by_me'] : ''
                    );
                    $filter_actions[$name] = array(
                        'module_param'     => $this->registry['module_param'],
                        'controller_param' => ($this->controller != $this->module) ? $this->registry['controller_param'] : '',
                        'action_param'     => $this->registry['action_param'],
                        'action' => $action,
                        'model_id' => '0',
                        'name' => $name,
                        'label' => $filter->get('name'),
                        'selected' => $this->action == $action && $this->registry['request']->get('filters_action') == 'loadfilter' && $this->registry['request']->get('filter_name') == $filter->get('id'),
                        'expanded' => false,
                        'url' => $url,
                        'img' => 'menu/' . $this->module . ($this->controller != $this->module ? '_' . $this->controller : ''),
                    ) + $filter->getAll();

                    if (is_callable([$theme,'getIconForRecord'])) {
                        $filter_actions[$name]['icon'] = $theme->getIconForRecord($this->module . ($this->controller != $this->module ? '_' . $this->controller : ''));
                    }
                }
                $this->registry->set('available_actions_filter', $filter_actions, true);
            }
        }

        return $this->actions;
    }

    /**
     * Reformat the actions, adding dividers
     *
     * @param array $actions - actions to reformat or null when actions property should be processed
     * @return array - reformatted actions
     */
    public function reformatActions(array &$actions = null) {

        /** @var [] $actions - a reference to the array that is processed */
        if ($actions === null) {
            $actions = &$this->actions;
        }
        if (!is_array($actions)) {
            $actions = array();
        }
        //clear all the dividers
        if (array_key_exists('divider1', $actions)) {
            foreach ($actions as $def => $action) {
                if (preg_match('#^divider#', $def)) {
                    //remove all dividers
                    unset($actions[$def]);
                }
            }
        }

        // do not reformat when there are no labels
        if ($this->registry['currentUser'] && !$this->registry['currentUser']->getPersonalSettings('interface', 'action_labels')) {
            return $actions;
        }

        //add dividers for more compatible view
        $actions_count = count($actions);
        //allow only X tabs per row
        $tabs_per_row = 8;
        if ($actions_count > $tabs_per_row) {
            $offset = -$tabs_per_row;
            $div_count = floor(($actions_count - 1) / $tabs_per_row);
            while ($offset > -$actions_count) {
                $keys = array_keys($actions);
                $actions = array_values($actions);
                array_splice($keys, $offset, 0, array('divider'.$div_count => 'divider'.$div_count));
                array_splice($actions, $offset, 0, array('divider'.$div_count => array('name' => '|')));
                $actions = array_combine($keys, $actions);
                $div_count--;
                $offset -= ($tabs_per_row + 1);
                $actions_count = count($actions);
            }
        }

        return $actions;
    }

    /**
     * Gets after actions available for the current user
     * These actions are performed after successful execution of the previous action
     *
     * @param array $action_defs - custom action definitions
     * @return array - all available actions with their details
     */
    public function getAfterActions($action_defs = array()) {
        if (empty($action_defs)) {
            //set default after action definitions
            $action_defs = $this->afterActionDefinitions;
        }

        //get model
        $this->getModel();

        if ($this->model) {

            //prepare lang options
            $langs = preg_split('#\s*,\s*#', $this->registry['config']->getParam('i18n', 'model_langs'));

            $lang_options = array();
            foreach ($langs as $lang) {
                if ($this->model->get('translations') && is_array($this->model->get('translations')) && in_array($lang, $this->model->get('translations')) || $lang == $this->model->get('model_lang')) {
                    continue;
                }
                $lang_options[] = array(
                    'label' => $this->i18n('lang_' . $lang),
                    'option_value' => $lang);
            }

            if (!empty($lang_options)) {
                //prepare translate options
                //all of the options in the after actions box begin with aa_
                //this is for naming compatibility with the other options in the form
                $translate_options = array (
                    array (
                        'custom_id' => 'aa_model_lang',
                        'name' => 'aa_model_lang',
                        'type' => 'dropdown',
                        'required' => 1,
                        'label' => $this->i18n('language'),
                        'help' => $this->i18n('help_language_model'),
                        'options' => $lang_options
                    )
                );
            } elseif (in_array('translate', $action_defs)) {
                unset($action_defs[array_search('translate', $action_defs)]);
            }

            // if model can be edited in more than one language, prepare options
            if ($this->model->get('translations') && is_array($this->model->get('translations')) && array_diff($this->model->get('translations'), array($this->model->get('model_lang')))) {
                $lang_options = $this->model->get('translations');
                if ($this->action == 'translate' && !in_array($this->model->get('model_lang'), $this->model->get('translations'))) {
                    $lang_options[] = $this->model->get('model_lang');
                }
                foreach ($lang_options as $idx => $lang) {
                    $lang_options[$idx] = array(
                        'label' => $this->i18n('lang_' . $lang),
                        'option_value' => $lang,
                    );
                }
                $edit_options = array(
                    array (
                        'custom_id' => 'aa2_model_lang',
                        'name' => 'aa2_model_lang',
                        'type' => 'dropdown',
                        'required' => 1,
                        'label' => $this->i18n('language'),
                        'help' => $this->i18n('help_language_model'),
                        'options' => $lang_options,
                        'value' => $this->model->get('model_lang'),
                    )
                );
                $i = 2;
                foreach (array('edittopic', 'editclause') as $edit_action) {
                    if (in_array($edit_action, $action_defs)) {
                        $i++;
                        // create a variable with a name based on the action
                        $edit_action .= '_options';
                        ${$edit_action} = $edit_options;
                        ${$edit_action}[0]['custom_id'] = ${$edit_action}[0]['name'] = "aa{$i}_model_lang";
                    }
                }
            }
        } else {
            foreach ($action_defs as $idx => $def) {
                if ($this->isModelRequired($def)) {
                    unset($action_defs[$idx]);
                }
            }
        }

        //get selected after action
        $selected_after_action = $this->defineAfterAction();

        //check for secondary controller
        $module_check = $this->module;
        if ($this->module != $this->controller) {
            $module_check .= '_' . $this->controller;
        }

        // reassign the afteraction to view or to the current action if the selected is not in the list
        if (! in_array($selected_after_action, $action_defs)) {
            if (in_array('view', $action_defs) && $this->checkActionPermissions($module_check, 'view')) {
                $selected_after_action = 'view';
            } else {
                $selected_after_action = $this->action;
            }
        }

        //get permissions of the currently logged user
        $this->getUserPermissions();

        //prepare the actions
        foreach ($action_defs as $name) {

            //check permissions
            if (!$this->checkActionPermissions($module_check, $name)) {
                //no permissions for this action
                //skip this action
                continue;
            } elseif ($this->model && $this->model->get('added_by') && !$this->model->checkPermissions($name)) {
                //the action is permitted but the ownership does not match
                //skip this action
                continue;
            }

            //define options
            $options = (isset(${$name . '_options'})) ? ${$name . '_options'} : array();

            //create actions array
            $this->afterActions[$name] = array(
                'action'       => $name,
                'name'         => $name,
                'label'        => $this->i18n($name),
                'selected'     => ($selected_after_action == $name) ? true : false,
                'options'      => (!empty($options)) ? $options : false);
        }

        return $this->afterActions;
    }

    /**
     * Gets after action from request, session, defaultly set after action or try to guess it
     * This method also stores the after action in the session for the current module and action
     *
     * @return string - selected after action string
     */
    public function defineAfterAction() {
        //get model
        $this->getModel();

        //check for secondary controller
        $module_check = $this->module;
        if ($this->module != $this->controller) {
            $module_check .= '_' . $this->controller;
        }

        //define the after action
        $selected_after_action = '';
        if ($requested_action = $this->registry['request']->get('after_action')) {
            //get after action from the request
            $selected_after_action = $requested_action;

            //check if the after action is manually specified in the code (in the GET portion of the request)
            if ($this->registry['request']->get('after_action', 'get') == $selected_after_action) {
                $custom_after_action = true;
            }
        } elseif ($this->registry['session']->get($module_check . '_' . $this->action, 'after_action') && !in_array($this->registry['session']->get($module_check . '_' . $this->action, 'after_action'), array('transformations'))) {
            //get after action from the session
            $selected_after_action = $this->registry['session']->get($module_check . '_' . $this->action, 'after_action');
        } elseif (isset($this->afterAction)) {
            //get the after action customize in the action method
            $selected_after_action = $this->afterAction;
        } else {
            //try to guess standard default after action by analyzing the current action
            switch ($this->action) {
                case 'translate':
                case 'commodities_reservation':
                case 'release_reservation':
                    $selected_after_action = 'view';
                    break;
                case 'edit':
                case 'add':
                case 'email':
                case 'transformations':
                    $selected_after_action = 'view';
                    break;
                case 'addannex':
                    $selected_after_action = 'viewtopic';
                    break;
                case 'delete':
                case 'purge':
                case 'restore':
                case 'activate':
                case 'deactivate':
                case 'multiadd':
                case 'multiedit':
                case 'multitransform':
                case 'multitag':
                case 'multiremovetag':
                case 'unlock':
                case 'export':
                case 'printlist':
                case 'approve':
                case 'approve_send':
                case 'issue':
                case 'issue_send':
                case 'disapprove':
                case 'change_templates_observer':
                    $selected_after_action = 'list';
                    break;
                case 'attachments':
                case 'generate':
                    $selected_after_action = 'attachments';
                    break;
                case 'relatives':
                    $selected_after_action = 'relatives';
                    break;
                case 'revision':
                    $selected_after_action = 'phases';
                    break;
                case 'dependencies':
                    $selected_after_action = 'dependencies';
                    break;
                default:
                    $selected_after_action = $this->action;
            }
        }

        if ($this->action == 'addquick') {
            //IMPORTANT: no after action upon successful add in a popup (addquick)
            return '';
        }

        //if the selected after action is not defined
        //change it with the current action
        if (!in_array($selected_after_action, $this->afterActionDefinitions) && empty($custom_after_action)) {
            $selected_after_action = $this->action;
        }

        if (preg_match("#add#", $this->action) && $this->registry['request']->isRequested('cancel_action')) {
            if (preg_match("#^add(handover|correct|payment|creditdebit)$#", $this->action)) {
                $selected_after_action = 'view';
            } else {
                $selected_after_action = 'list';
            }
        }

        //get permissions of the currently logged user
        $this->getUserPermissions();

        //check permissions
        if (!$this->checkActionPermissions($module_check, $selected_after_action)) {
            //no permissions for the selected after action, set after action as same as the current action
            $selected_after_action = $this->action;
        } elseif (!$this->registry['request']->isPost() && $this->model && $this->model->get('id') && !$this->model->checkPermissions($selected_after_action)) {
            //check if the selected after action is permitted
            $selected_after_action = $this->action;
        }

        //skip_after_action_save is a flag that skip saving of after action in the session
        if ($selected_after_action && !$this->registry['request']->isRequested('skip_after_action_save')) {
            //store the after action in the session for the current module and action
            $this->registry['session']->set($module_check . '_' . $this->action, $selected_after_action, 'after_action', true);
        } elseif ($this->registry['request']->isRequested('skip_after_action_save') && $this->registry['request']->get('prev_after_action')) {
            //store the previously stored after action in the session for the current module and action
            $this->registry['session']->set($module_check . '_' . $this->action, $this->registry['request']->get('prev_after_action'), 'after_action', true);
        }

        return $selected_after_action;
    }

    /**
     * Standard method to execute the current after action
     */
    public function executeAfter() {

        //get selected after action
        $selected_after_action = $this->defineAfterAction();

        //get model
        $this->getModel();

        //additional _GET options, transposed from the post array of after action options
        $options = array();
        $postedOptions = $this->registry['request']->getAll();
        foreach ($postedOptions as $index => $value) {
            //all of the options in the after actions box begin with aa_
            //this is for naming compatibility with the other options in the form
            if (preg_match('#^aa[0-9]?_#', $index) && $value != '') {
                $idx = preg_replace('#^aa[0-9]?_#', '', $index);
                $options[$idx] = $value;
            }
        }

        if ($selected_after_action) {
            //set boolean param defining whether to redirect or not
            $redirect = false;

            switch ($selected_after_action) {
            case 'add':
            case 'multiadd':
            case 'list':
            case 'search':
            case 'filter':
            case 'profile':
            case 'password':
            case 'multitransform':
            case 'unlock':
            case 'calendars':
            case 'listincome':
            case 'listexpense':
            case 'import':
            case 'rights':
            case 'index':
            case 'unsubscribed':
                $redirect = true;
                break;
            case 'day':
            case 'week':
            case 'month':
            case 'year':
                if ($this->registry['request']->get('date')) {
                    $options = array_merge(array('date' => $this->registry['request']->get('date')), $options);
                }
                $redirect = true;
                break;
            case 'view':
            case 'edit':
            case 'viewtopic':
            case 'viewclause':
            case 'edittopic':
            case 'editclause':
            case 'translate':
            case 'phases':
            case 'assign':
            case 'generate':
            case 'attachments':
            case 'relatives':
            case 'balance':
            case 'payments':
            case 'dependencies':
            case 'timesheets':
            case 'assignments':
            case 'history':
            case 'statistics':
            case 'comments':
            case 'communications':
            case 'remind':
            case 'menu':
            case 'companiesandoffices':
            case 'cachboxesandbankaccounts':
            case 'addinvoice';
            case 'distribute':
            case 'allocate_costs':
            case 'enter':
            case 'control':
            case 'printform':
            case 'parties':
            case 'displaysettings':
            case 'printsettings':
            case 'emailsettings':
            case 'transformations':
            case 'trademarks':
            case 'allocate':
            case 'offices':
                $redirect = true;
                $getOptions = array();
                if ($this->model) {
                    $getOptions = array(
                        $selected_after_action => $this->model->get('id'),
                        'model_lang' => $this->getModelLang());
                }
                $options = array_merge($getOptions, $options);
                break;
            case 'mynzoom':
                //if selected after action is mynzoom the controller has to go to the requested section(layout)
                $redirect = true;
                $options = array(
                    'mynzoom'   => $this->registry['request']->get('id') ?: $this->registry['currentUser']->get('id'),
                    'layout'    => $this->registry['request']->get('layout')
                );
                break;
            case 'viewfile':
                //redirect to generated pdf file
                $redirect = true;
                $getOptions = array();
                if ($this->model) {
                    $getOptions = array(
                        $selected_after_action => $this->model->get('id'),
                        'file' => $this->model->get('file_id'),
                        'model_lang' => $this->getModelLang());
                }
                $options = array_merge($getOptions, $options);
                break;
            }

            if ($this->registry['locking_records'] && $this->model && $this->model->isLockedByMe()) {
                $this->model->unlock();
            }
            if ($this->registry['session']->isRequested('return_link')) {
                $redirect_link = 'Location: ' . $this->registry['session']->get('return_link');
                $this->registry['session']->remove('return_link');
                header($redirect_link);
                exit;
            } elseif ($redirect) {
                if ($selected_after_action == 'add' && $this->model && $this->model->get('is_company')) {
                    $options['is_company'] = 1;
                }
                //redirect to the desired action
                $this->redirect($this->module, $selected_after_action, $options);
            }
        }

        return true;
    }

    /**
     * Gets search options
     *
     * @param array $session_filters - session filters
     * @return array - search options
     */
    public function getSearchOptions(&$session_filters = array()) {

        $registry = &$this->registry;
        $request = &$registry['request'];
        $action_name = $request->get($this->action);

        require_once(PH_MODULES_DIR . 'filters/models/filters.factory.php');

        if ($action_name == 'filters_ajax_load') {
            //load search definitions from DB in the session
            $filter_id = $request->get($action_name);
            [$noneeded, $session_filters] = Filters::loadSearchDefinitions($registry, $filter_id);
        }

        // extract values of model types and names of all other search fields from session filters
        $params = Filters::extractSearchFields($session_filters);

        //prepare simple search definitions
        $simple_search = Filters::getSimpleSearchDefinitions($registry, true);
        $params['simple_search_defs'] = $simple_search;

        //prepare advanced search definitions
        [$advanced_search, $system_fields, $saved_filters, $simple_search] = Filters::getAdvancedSearchDefinitions($registry, $params);

        //prepare the basic sort definitions for optgroup
        if (!empty($system_fields['sort'])) {
            $system_fields['sort'] = array('basic_vars' => $system_fields['sort']);
        } else {
            $system_fields['sort'] = array();
        }

        //get additional variables if session filters contain model types and module has additional vars
        $additional_search = false;
        if (!empty($params['model_types']) && isset($this::$searchAdditionalVarsSwitch)) {
            $request->set('model_types', $params['model_types'], 'all', true);

            $additional_search = Filters::getAdditionalSearchDefs($registry);

            $additional_sortables = Filters::getAdditionalSortDefs($registry);
            if ($additional_sortables) {
                $system_fields['sort']['additional_vars'] = $additional_sortables;
            }

            $request->remove('model_types');
        }

        if (!empty($system_fields['sort']['basic_vars'])) {
            uasort($system_fields['sort']['basic_vars'], array('Filters', 'searchSort'));
        }
        if (!empty($system_fields['sort']['additional_vars'])) {
            uasort($system_fields['sort']['additional_vars'], array('Filters', 'searchSort'));
        }

        //include js for zapatec calendar
        $this->registry->set('include_calendar', true, true);
        return array($advanced_search, $system_fields, $saved_filters, $simple_search, $additional_search);
    }

    /**
     * Sets search options values from request or session
     *
     * @param string $session_param - index in session array where the search options are saved
     * @return array - search options with their values
     */
    public function setSearchOptionsValues($session_param) {
        if (empty($this->searchOptions)) {
            return false;
        }

        $clear_flag = $this->registry['request']->get('clear_flag');
        if (!$clear_flag) {
            //define the search options values from request or session
            foreach ($this->searchOptions as $k => $var) {
                if (empty($var)) {
                    continue;
                }
                if ($this->registry['request']->isRequested($var['name'])) {
                    $this->searchOptions[$k]['value'] = $this->registry['request']->get($var['name']);
                } elseif ($this->registry['session']->isRequested($var['name'], $session_param)) {
                    $this->searchOptions[$k]['value'] = $this->registry['session']->get($var['name'], $session_param);
                    //these are custom settings for the values from searching customers as autocompleters
                    if (($var['name'] == 'search_customer' || $var['name'] == 'customer') && $this->searchOptions[$k]['value']) {
                        $this->searchOptions[$k]['value1'] = $this->registry['session']->get('customer_code', $session_param);
                        $this->searchOptions[$k]['value2'] = $this->registry['session']->get('customer_name', $session_param);
                    } elseif ($var['name'] == 'search_deliverer' && $this->searchOptions[$k]['value']) {
                        $this->searchOptions[$k]['value1'] = $this->registry['session']->get('search_deliverer_code', $session_param);
                        $this->searchOptions[$k]['value2'] = $this->registry['session']->get('search_deliverer_name', $session_param);
                    }
                }
            }
        } else {
            $this->registry['session']->remove('', $session_param);
        }

        return $this->searchOptions;
    }

    /**
     * Get currently defined model
     *
     * @return mixed - model object if found or false
     */
    public function getModel() {
        if ($this->model) {
            if (!$this->modelName) {
                $this->modelName = get_class($this->model);
            }
            return $this->model;
        }

        //get model if defined in registry
        if (!empty($this->modelName)) {
            $model_name = strtolower($this->modelName);
        } else {
            $model_name = strtolower(preg_replace('#_Controller#', '', get_class($this)));
            $model_name = General::plural2singular($model_name);
        }

        //try to get model
        $this->model = $this->registry[$model_name];

        if (is_object($this->model) && is_subclass_of($this->model, 'Model')) {
            if (!$this->modelName) {
                $this->modelName = get_class($this->model);
            }
            return $this->model;
        }

        return false;
    }

    /**
     * Gets model directly from the DB, not from the registry
     *
     * @param mixed $id - id of model
     * @return object|bool - model
     */
    public function getModelFromDB($id = '') {
        $registry = &$this->registry;

        //get the requested model ID
        if (!$id) {
            $id = $this->registry['request']->get('id');
        }

        $className = $this->modelFactoryName;

        //try to guess the table alias for the filters
        try {
            $alias = $className::getAlias($this->module, $this->controller);
        } catch (\Error | \Exception $e) {
            return false;
        }

        $filters['where'][] = $alias . '.id = "' . $id . '"';
        $model = $className::searchOne($registry, $filters);

        if (!is_object($model) || !is_subclass_of($model, 'Model')) {
            throw new Exception('Expected variable subclass of Model. Found: ' . (is_object($model) ? get_class($model) : gettype($model)));
        }

        return $model;
    }

    /**
     * Get model lang of the currently defined model
     *
     * @return string - model lang or currently defined interface lang
     */
    public function getModelLang() {
        $lang = '';

        if ($this->getModel()) {
            $lang = $this->model->get('model_lang');
        } else {
            $lang = $this->registry['lang'];
        }

        return $lang;
    }

    /**
     * Check access and ownership of the current user to a certain model object
     * If no access redirect to the default action of the module
     *
     * @param object $model - model object
     * @param bool $redirect - defines whether to redirect after check or not
     * @param string $action - action to check permissions for
     * @return bool - result of the check, true - accessible, false - inaccessible
     */
    public function checkAccessOwnership($model, $redirect = true, $action = '') {
        if (!$this->registry['validLogin']) {
            return true;
        }

        if (!is_object($model) || !is_subclass_of($model, 'Model')) {
            return true;
        }

        //check if the model has not been deleted before
        if ($model->isDeleted()) {
            return false;
        }

        $request = $this->registry['request'];

        if (empty($action)) {
            $action = $this->action;
        }

        //check the permissions and ownership of the model
        if (!$model->checkPermissions($action)) {

            //set error message
            $this->registry['messages']->setError($this->i18n('error_no_access_to_model'));

            if (!$redirect) {
                return false;
            }

            // store error message
            $this->registry['messages']->insertInSession($this->registry);

            //get user permissions of the current user
            $this->getUserPermissions();

            //check for secondary controller
            $module_check = $this->module;
            if ($this->module != $this->controller) {
                $module_check .= '_' . $this->controller;
            }

            if ($action != 'view' && $this->checkActionPermissions($module_check, 'view') && $model->checkPermissions('view')) {
                //redirect to module's view action
                $this->redirect($this->module, 'view', 'view=' . $model->get('id'));
            } elseif ($this->checkActionPermissions($module_check, $this->defaultAction)) {
                //redirect to module's default action
                $this->redirect($this->module, $this->defaultAction);
            } else {
                //redirect to the index
                $this->redirect('index');
            }

        } elseif ($this->registry['locking_records'] && $model && $model->isLockedByOther() &&
        preg_match(self::REGEX_DEFINING_LOCK, $action) &&
        // allocate action is forbidden for a locked model only when current user can edit data
        !($action == 'allocate' && !$model->checkPermissions('edit_allocate'))) {

            //set error message
            $this->registry['messages']->setError($this->i18n('error_no_access_model_locked'));

            if (!$redirect) {
                return false;
            }

            //store error message
            $this->registry['messages']->insertInSession($this->registry);

            //get user permissions of the current user
            $this->getUserPermissions();

            //check for secondary controller
            $module_check = $this->module;
            if ($this->module != $this->controller) {
                $module_check .= '_' . $this->controller;
            }

            //try to redirect to the default action of the module (defaultAction - usually list)
            if ($this->checkActionPermissions($module_check, 'view') && $action != 'view') {
                //redirect to module's default action
                $this->redirect($this->module, 'view', 'view=' . $model->get('id'));
            } elseif ($this->checkActionPermissions($module_check, $this->defaultAction)) {
                //redirect to module's default action
                $this->redirect($this->module, $this->defaultAction);
            } else {
                //redirect to the index
                $this->redirect('index');
            }

        } elseif ($this->registry['locking_records'] && !$request->isPost() && !$this->registry['request']->isRequested('ajax_checklock')) {
            //lock record only for certain operations
            if (preg_match(self::REGEX_DEFINING_LOCK, $action) &&
            // allocate action locks model only when current user can edit data
            !($action == 'allocate' && !$model->checkPermissions('edit_allocate'))) {
                $model->lock();
            }
        }

        if ($this->registry['locking_records'] && $model && $model->isLockedByMe() &&
        preg_match(self::REGEX_DEFINING_LOCK, $action) &&
        // allocate action locks model only when current user can edit data
        !($action == 'allocate' && !$model->checkPermissions('edit_allocate'))) {
            //define if the model is locked by the current user
            $this->registry->set('model_locked_by_me', $model->lockedInfo['id'], true);
        }

        return true;
    }

    /**
     * Check access and ownership of the current user to a multple list of models
     * If just one model is not accessible return false
     *
     * @param array $ids - list of model ids
     * @param string $action - action to be checked
     * @param string $factoryClass - factory class to search the models
     * @param bool $redirect - defines whether to redirect after check or not
     * @return bool - result of the check, true - accessible, false - inaccessible
     */
    public function checkAccessOwnershipMultiple($ids, $action = '', $factoryClass = '', $redirect = true) {
        if (!$this->registry['validLogin']) {
            return true;
        }

        if (empty($ids)) {
            return true;
        }

        if (empty($action)) {
            $action = $this->action;
        }

        if (empty($factoryClass)) {
            $factoryClass = $this->modelFactoryName;
        }

        $access = $factoryClass::checkPermissions($this->registry, $ids, $action, $factoryClass);

        //check the permissions and ownership of the models
        if (!$access) {
            if (!$redirect) {
                return false;
            }

            //show error message
            $this->registry['messages']->setError($this->i18n('error_no_access_to_models'));
            $this->registry['messages']->insertInSession($this->registry);

            //get user permissions of the current user
            $this->getUserPermissions();

            //check for secondary controller
            $module_check = $this->module;
            if ($this->module != $this->controller) {
                $module_check .= '_' . $this->controller;
            }

            //try to redirect to the default action of the module (defaultAction - usually list)
            if ($this->checkActionPermissions($module_check, $this->defaultAction)) {
                //redirect to module's default action
                $this->redirect($this->module, $this->defaultAction);
            } else {
                //redirect to the index
                $this->redirect('index');
            }
        }

        return true;
    }

    /**
     * Stores checkboxes items into the session or restores them from the session
     *
     * @return bool - result from the operations
     */
    public function manageMultipleSelectionCheckboxes() {

        // this cannot possibly be performed for the automations controller
        // when it is created for some multi-action
        if (get_class($this) == 'Automations_Controller' || is_subclass_of($this, 'Automations_Controller')) {
            return true;
        }

        $module = $this->module;
        $factory = $this->modelFactoryName;

        $session = &$this->registry['session'];
        $request = &$this->registry['request'];

        if (!$session_param = $request->get('session_param')) {
            if ($this->modelName == 'Report') {
                if ($request->get('report_type')) {
                    $session_param = 'reports_' . $request->get('report_type') . '_report';
                } else {
                    $session_param = 'reports_' . $session->get('report_type') . '_report';
                }
            } else {
                $session_param = $this->action . '_' . strtolower($this->modelName);
            }
        }

        //check if all items are selected (on all the pages)
        $all_selected_items = $session->get('selected_items');
        $skipItemsCheck = false;
        if ($this->action == 'export') {
            $exportWhat = $request->get('export_what');
            if ($request->get('export_what') == 'all') {
                $export_session = isset($all_selected_items[$session_param]) ? $all_selected_items[$session_param] : array('ids' => array());
                $new_param = array('select_all' => true,
                                   'ignore_ids' => array());
                $all_selected_items[$session_param] = $new_param;
            } elseif ($exportWhat == 'empty') {
                $skipItemsCheck = true;
            }
        }

        if ($this->action == 'multiarchive' && $request->get('archive_what') == 'all') {
            $archive_session = isset($all_selected_items[$session_param]) ? $all_selected_items[$session_param] : array('ids' => array());
            $new_param = array('select_all' => true,
                               'ignore_ids' => array());
            $all_selected_items[$session_param] = $new_param;
        }
        $selected_items = isset($all_selected_items[$session_param]) ? $all_selected_items[$session_param] : array('ids' => array());
        $select_all = !empty($selected_items['select_all']) ? true : false;

        //if all items are selected prepare to put their IDs in the session
        if ($select_all && in_array($this->action, $this->multipleSelectionDefinitions)) {

            //get filters for current session param
            $filters = $session[$session_param];
            $controller = '';
            if ($this->controller != $module) {
                $controller = '.' . $this->controller;
            }

            //get all the IDs for the specified module
            require_once PH_MODULES_DIR . $module . '/models/' . $module . $controller . '.factory.php';

            if (preg_match('#^(contact_persons|branches)_ajax_customers_\d+_$#', $session_param)) {
                // for branches and contact persons use the whole session param as session prefix
                $session_prefix = $session_param;
            } else {
                $session_prefix = explode('_', $session_param, 2);
                $session_prefix = $session_prefix[0] . '_';
            }
            $filters = $factory::saveSearchParams($this->registry, $filters, $session_prefix);

            //if we will extract items just make archive tables to be used
            if ($this->action == 'multiarchive' && $request->get('archive_action') == 'extracting') {
                foreach ($filters['where'] as $k => $v) {
                    if (preg_match('#([a-zA-Z0-9]+)\.archive#', $v, $match)) {
                        unset($filters['where'][$k]);
                    }
                }
                $filters['where'][] = $match[1] . '.archive = \'archive\'';
            }
            if ($factory == 'Contracts') {
                // If there is a subtype filter set to "all"
                if (!empty($filters['where']) && is_array($filters['where'])) {
                    foreach ($filters['where'] as $where_key => $where) {
                        // If there is a subtype filter
                        if (preg_match('/^co\.subtype.+$/', $where)) {
                            // Set that there is a subtype filter
                            $have_subtype_filter = true;
                        }
                        if (preg_match('/^co\.subtype.+all.*$/', $where)) {
                            // Remove the subtype filter
                            unset($filters['where'][$where_key]);
                        }
                    }
                }
                // If there is NO subtype filter
                if (empty($have_subtype_filter)) {
                    // Export only contracts with subtype: contract
                    $filters['where'][] = 'co.subtype = "contract"';
                }
            }
            $items = $factory::getIds($this->registry, $filters);

            // check for ignored items and remove them from the array
            $ignored_items = $selected_items['ignore_ids'];
            $selected_items = array_values(array_diff($items, $ignored_items));

            $request->set('items', $selected_items, 'post', true);

        } elseif ($request->isRequested('session_param') && !$request->isRequested('skip_session_ids')) {

            if (!isset($selected_items['ids'])) {
                $selected_items['ids'] = array();
            }
            if ($request->isRequested('items')) {
                $selected_items['ids'] = array_unique(array_merge($selected_items['ids'], $request->get('items')));
            }
            $selected_items['ids'] = array_values($selected_items['ids']);
            $request->set('items', $selected_items['ids'], 'post', true);
            $this->registry->set('selected_items', $selected_items, true);

        } elseif ($session->isRequested('selected_items')) {
            $this->registry->set('selected_items', $selected_items, true);
        }
        if (isset($export_session)) {
            $session->set($session_param, $export_session, 'selected_items', true);
        }
        if (isset($archive_session)) {
            $session->set($session_param, $archive_session, 'selected_items', true);
        }
exit;
        if (in_array($this->action, $this->multipleSelectionDefinitions) && (!$skipItemsCheck && !$request->get('items')) &&
        !in_array($this->module, array_merge(array('reports'), $this->systemExportModules)) && !$request->get($this->action)) {
            $this->registry['messages']->setError($this->i18n('no_selected_records'), '');
            $this->registry['messages']->insertInSession($this->registry);
            if ($this->action == 'printlist' || $this->action == 'export') {
                $action = preg_match('#^(search|list)_.*#', $session_param) ? preg_replace('#^(search|list)_.*#', '$1', $session_param) : 'list';
            } else {
                $action = 'list';
                if (!empty($_SERVER['HTTP_REFERER'])) {
                    preg_match('/&' . $this->controller . '=([^&]*)(&.+)?$/', $_SERVER['HTTP_REFERER'], $matches);
                    if (isset($matches[1]) && !in_array($matches[1], $this->multipleSelectionDefinitions)) {
                        $action = $matches[1];
                    }
                }
            }
            $this->redirect($this->module, $action);
        }

        return true;
    }

    /**
     * Stores checkboxes items into the session
     *
     * @return int - number of checked items
     */
    public function _insertIds() {
        /*$module_check = $this->module;
        if ($this->module != $this->controller) {
            $module_check .= '_' . $this->controller;
        }
        if ($this->registry['request']->get('action')) {
            $module_check .= '_' . $this->registry['request']->get('action');
        }*/

        $session = &$this->registry['session'];
        $request = &$this->registry['request'];
        //$session->set('selected_items',array(),'', true);
        $session_param_check = $request->get('session_param', 'post');

        $all_selected_items = $session->get('selected_items');
        if (isset($all_selected_items[$session_param_check])) {
            $selected_items = $all_selected_items[$session_param_check];
        } else {
            $selected_items = array('ids' => array(),
                                    'select_all' => '',
                                    'ignore_ids' => array());
        }

        // initialize variable
        $return_string = 0;

        if ($request->isGet('delids')) {
            if ($request->isPost('ids')) {
                $ids = $request->get('ids', 'post');

                //parameter shows that there are checkboxes dependent to selected one
                //and they should be unchecked as well (their ids are in POST)
                if (!$request->isPost('delete_dependent')) {
                    $ids = array($ids[0]);
                }

                foreach ($ids as $id) {
                    if (@$selected_items['select_all'] == 1) {
                        $selected_items['ignore_ids'][] = $id;
                        $return_string = intval($request->get('total', 'post')) - count($selected_items['ignore_ids']);
                    } else {
                        if (($key = array_search($id, $selected_items['ids'])) !== false) {
                           unset($selected_items['ids'][$key]);
                        }

                        $return_string = count($selected_items['ids']);
                    }
                }
            } else {
                $return_string = 0;
            }
        } elseif ($request->isPost('ids')) {
            $new_selected_items = $request->get('ids', 'post');
            if (@in_array('all', $new_selected_items)) {
                $selected_items = array ('ids' => array(),
                                         'select_all' => 1,
                                         'ignore_ids' => array());
                $return_string = intval($request->get('total', 'post'));
            } elseif (@$selected_items['select_all'] == 1 && count($new_selected_items) == 1) {
                $key = array_search($new_selected_items[0],$selected_items['ignore_ids']);
                if ($key !== false) {
                    unset($selected_items['ignore_ids'][$key]);
                }
                $return_string = intval($request->get('total', 'post')) - count($selected_items['ignore_ids']);
            } else {
                $selected_items['select_all'] = '';
                $selected_items['ignore_ids'] = array();
                $selected_items['ids'] = array_values(array_unique(array_merge($selected_items['ids'], $new_selected_items)));
                $return_string = count($selected_items['ids']);
            }
        }
        if ($return_string == 0) {
            unset($all_selected_items[$session_param_check]);
        } else {
            $all_selected_items[$session_param_check] = $selected_items;
        }
        $session->set('selected_items', $all_selected_items, '', true);

        echo $return_string;
        exit;
    }

    /**
     * Calculate
     */
    protected function _calculate() {
        print Calculator::calculate($this->registry);
        exit;
    }

    /**
     * Display audit of model
     */
    protected function _audit() {
        //all the actions are within the viewer
        return true;
    }

    /**
     * Dashlet display
     *
     * @return boolean
     */
    protected function _dashlet() {
        //all the actions are within the viewer
        return true;
    }

    /**
     * Subpanel display
     *
     * @return boolean
     */
    protected function _subpanel() {
        //all the actions are within the viewer
        return true;
    }

    /**
     * Side panel display
     */
    protected function _sidePanel() {
        $module = $this->registry['module'];
        $controller = $this->registry['controller'];

        $model_name = General::plural2singular($module . ($module != $controller ? '_' . $controller : ''));
        $chunks = explode('_', $model_name);
        $model_name = '';
        foreach ($chunks as $idx => $chunk) {
            $chunks[$idx] = ucwords($chunk);
        }
        $model_name = implode('_', $chunks);

        $request = &$this->registry['request'];

        $panel = $request->get('panel');

        $assignments_flag = ($panel == 'assignments' || $panel == 'timesheets' ||
                            (in_array($model_name, array('Document', 'Project')) && $panel == 'related_records') ||
                            $model_name == 'Event' && $panel == 'last_records') ?
                            true : false;
        $this->registry->set('getAssignments', $assignments_flag, true);
        $this->registry->set('getAssignmentsResponsible', $assignments_flag, true);
        $this->registry->set('getAssignmentsDecision', $assignments_flag, true);
        $this->registry->set('getAssignmentsObserver', $assignments_flag, true);

        $model_class = PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.model.php';
        require_once ($model_class);

        $params = array('id' => $request->get('model_id'),
                        'model_lang' => $request->get('model_lang'));
        // customer might be changed during edit
        if ($request->isRequested('customer')) {
            $params['customer'] = $request->get('customer');
        }
        $archive = $request->get('archive');
        if ($archive) {
            // flag for getting assignments of archived records
            $params['archived_by'] = true;
        }
        /** @var Model $model */
        $model = new $model_name($this->registry, $params);

        //prepare data for the template
        $this->viewer = new Viewer($this->registry);
        $this->viewer->data['side_panel'] = $panel;
        if ($archive) {
            $this->viewer->data['archive'] = 1;
        }

        $model->prepareSidePanelData($this->viewer->data);

        $template = '';
        $i18n_files = array();
        switch ($panel) {
            case 'customers_info':
                $template = '_' . $panel . '_side_panel.html';
                $i18n_files[] = PH_MODULES_DIR . 'customers/i18n/' . $this->registry['lang'] . '/customers.ini';
                $this->viewer->loadCustomI18NFiles($i18n_files);
                break;
            case 'last_records':
            case 'assignments':
            $templatesDir = PH_MODULES_DIR . $this->module;
            $templatesDir .= $this->registry['theme']->isModern() && is_dir($templatesDir . '/view')
                ? '/view/templates/'
                : '/templates/';
            $template = $templatesDir . '_' . $panel . '_side_panel.html';
                break;
            case 'emails':
            case 'comments':
                $template = '_communications_side_panel.html';
                $i18n_files[] = PH_MODULES_DIR . 'communications/i18n/' . $this->registry['lang'] . '/communications.ini';
                $this->viewer->loadCustomI18NFiles($i18n_files);
                break;
            case 'timesheets':
                $template = '_' . $panel . '_side_panel.html';
                $i18n_files[] = PH_MODULES_DIR . 'tasks/i18n/' . $this->registry['lang'] . '/tasks.ini';
                $this->viewer->loadCustomI18NFiles($i18n_files);
                break;
            case 'related_records':
            case 'history':
            case 'attachments':
                $template = '_' . $panel . '_side_panel.html';
                break;
        }

        if ($template) {
            $this->viewer->setFrameset($template);
            $this->viewer->display();
        }

        exit;
    }

    /**
     * Get action options
     */
    protected function _getOptions() {

        $registry = &$this->registry;
        $request = &$registry['request'];
        $session = &$registry['session'];

        //get some info we will need after
        $action_name = $request->get($this->action);
        $factory_name = $this->modelFactoryName;
        $model_name = strtolower($this->modelName);

        if ($request->isRequested('module_name')) {
            if ($this->module != $request->get('module_name')) {
                // load i18n files if the search from this module is for another module
                $i18n_module_dir = sprintf('%s%s/i18n/%s/', PH_MODULES_DIR, $request->get('module_name'), $this->registry['lang']);
                $i18n_files_list = FilesLib::readDir($i18n_module_dir, false, '', 'ini', true);

                $this->loadI18NFiles($i18n_files_list);
            }
        }

        //if model type is requested prepare additional vars and main vars for search and exit
        //because the user is making search now and just choosing model types
        if ($request->isRequested('model_types')) {

            $params = array();
            if ($request->isRequested('module_name')) {
                $params['module'] = $request->get('module_name');
                $params['controller'] = $request->get('controller_name');
            }
            if ($request->get('model_types')) {
                // clear blank values - passed in order to reload search defs when last type filter is removed
                $params['model_types'] = array_filter($request->get('model_types'));
            } else {
                $params['model_types'] = array();
            }

            require_once(PH_MODULES_DIR . 'filters/models/filters.factory.php');
            [$advanced_search, $system_fields] = Filters::getAdvancedSearchDefinitions($registry, $params);

            if (!empty($params['model_types'])) {
                // check if module has additional vars
                if (!empty($params['module'])) {
                    // searching from another module
                    $controller_name = $params['module'] . ($params['controller'] && $params['controller'] != $params['module'] ? '_' . $params['controller'] : '');
                    $controller_name = implode('_', array_map('ucfirst', explode('_', $controller_name))) . '_Controller';
                    // use reflection to get static property of class
                    require_once PH_MODULES_DIR . $params['module'] . '/controllers/' . $params['module'] . ($params['controller'] && $params['controller'] != $params['module'] ? '.' . $params['controller'] : '') . '.controller.php';
                    $reflection_class = new ReflectionClass($controller_name);
                    $prop = 'searchAdditionalVarsSwitch';
                    $static_props = $reflection_class->getStaticProperties();
                    $switch_additional = array_key_exists($prop, $static_props) !== false ? $static_props[$prop] : '';
                    unset($reflection_class);

                    // prepare additional columns for dashlet
                    if ($switch_additional && $registry['module'] == 'dashlets') {
                        $additional_columns = Dashlets::getAdditionalColumns($registry, General::plural2singular(preg_replace('#_Controller$#', '', $controller_name)), $params['model_types'], true);
                    }
                } else {
                    $switch_additional = isset($this::$searchAdditionalVarsSwitch) ? $this::$searchAdditionalVarsSwitch : '';
                }

                if ($switch_additional) {
                    $additional_search = Filters::getAdditionalSearchDefs($registry, $params);
                    $additional_sortables = Filters::getAdditionalSortDefs($registry);
                }
            }

            if (!empty($system_fields['sort'])) {
                $system_fields['sort'] = array('basic_vars' => $system_fields['sort']);
            } else {
                $system_fields['sort'] = array();
            }
            if (!empty($additional_sortables)) {
                $system_fields['sort']['additional_vars'] = $additional_sortables;
            }
            if (!empty($system_fields['sort']['basic_vars'])) {
                uasort($system_fields['sort']['basic_vars'], array('Filters', 'searchSort'));
            }
            if (!empty($system_fields['sort']['additional_vars'])) {
                uasort($system_fields['sort']['additional_vars'], array('Filters', 'searchSort'));
            }

            if (empty($additional_search)) {
                $additional_search = false;
            }

            $result = sprintf('basic_search_obj = %s; additional_search_obj = %s; sortables=%s;', json_encode($advanced_search), json_encode($additional_search), json_encode($system_fields['sort']));
            // prepare additional columns for dashlet
            if ($registry['module'] == 'dashlets' && !empty($params['module'])) {
                $result .= sprintf(' var additional_columns = %s;', json_encode(!empty($additional_columns) ? $additional_columns : false));
            }
            exit($result);
        }

        //try to guess table alias
        $alias_words = explode('_', $factory_name, 2);
        $alias = $factory_name::getAlias($alias_words[0], (isset($alias_words[1]) ? $alias_words[1] : $alias_words[0]));

        if ($request->get('model_id')) {
            $filters = array();
            if (!preg_match('/layout/i', $model_name)) {
                $filters['where'] = array($alias . '.id = "' . $request->get('model_id') . '"');
            } else {
                $filters['where'] = array($alias . '.layout_id = "' . $request->get('model_id') . '"');
            }
            $filters['model_lang'] = $request->get('model_lang');
            $model = $factory_name::searchOne($registry, $filters);
            if (!empty($model)) {
                //register the model,
                //so that it could be used further by the viewer
                if (!$this->registry->isRegistered($model_name)) {
                    $this->registry->set($model_name, $model->sanitize());
                }
            }
        }

        //Prepare definitions for search template
        $action_arr = $this->getActions(array($action_name));

        if ($action_name == 'search' || $action_name == 'filter' || $action_name == 'filters_ajax_load') {

            //if we have call from 1 module to another(example: from dashlets we load search definitions for documents)
            //we have to have params 'real_module' & 'real_controller'
            $params = array();
            if ($request->get('real_module')) {
                $params['real_module'] = $request->get('real_module');
                if ($request->get('real_controller')) {
                    $params['real_controller'] = $request->get('real_controller');
                } else {
                    $params['real_controller'] = $request->get('real_module');
                }
            }
            if ($request->get('session_param')) {
                $session_param = $request->get('session_param');
            } else {
                //try to guess session param
                $session_param = strtolower($action_name . '_' . $this->modelName);
            }

            //clear session_param if requested
            if ($request->isRequested('clear_flag')) {
                // takes the current session filters
                $saved_session_filters = $session->get($session_param);

                //counter used for types/sections/categories
                $type_sections_counter = 0;

                if (isset($saved_session_filters['search_fields'])) {
                    // go through all filters to find the filters for types/sections/categories
                    foreach ($saved_session_filters['search_fields'] as $key => $search_field_value) {
                        if (preg_match('#^[^\.]*\.(type_section|type|category)#', $search_field_value)) {

                            //set additional simple search filters
                            $filter = preg_replace('#^[^\.]*\.(type_section|type|category)#', '$1', $saved_session_filters['search_fields'][$key]);
                            $additional_hidden_filters[0] = array('name' => 'hidden_' . $filter,
                                                                  'value' => $saved_session_filters ['values'][$key]);

                            // increments counter if the current filter matches the criteria
                            $type_sections_counter++;
                            if ($type_sections_counter > 1) {
                                break;
                            }
                        } else {
                            // clears all the elements of this filter if it doesn't match the criteria
                            unset($saved_session_filters['search_fields'][$key]);
                            unset($saved_session_filters['compare_options'][$key]);
                            unset($saved_session_filters['values'][$key]);
                            unset($saved_session_filters['date_period'][$key]);
                            unset($saved_session_filters['logical_operator'][$key]);
                        }
                    }
                }

                if (isset($saved_session_filters['key'])) {
                    foreach ($saved_session_filters as $search_key => $search_value) {
                        //unset only simple search criteria, different from hidden type/section/category
                        if (!in_array($search_key, array('hidden_type', 'hidden_type_section', 'hidden_category')) && !is_array($search_value)) {
                            unset($saved_session_filters[$search_key]);
                        }
                    }
                }

                // if there is exactly one type/section/category filter, all the other filters are removed
                // otherwise all the filters will be cleared
                if ($type_sections_counter == 1) {
                    $session_filters = $saved_session_filters;
                    $session->set($session_param, $session_filters, '', true);
                } else {
                    $session_filters = array();
                    $session->remove($session_param);
                }
            } else {
                $session_filters = $session->get($session_param);

                //if we are searching in a module which has filters also for list action
                //we check if any filters are used there and transfer what we need to search session filters
                if ($action_name == 'search' && !preg_match('#(' . $this->module . '|' . $this->controller . ')=search#', $_SERVER['HTTP_REFERER'])) {
                    $session_param = preg_replace('#^search_#', 'list_', $session_param);

                    //get session param for list action
                    $list_filters = $session->get($session_param);

                    if (!empty($list_filters['search_fields'])) {
                        //check filters
                        $found = array();
                        $matches_found = array();
                        foreach ($list_filters['search_fields'] as $key => $value) {
                            //check if type/section/category is required
                            if (preg_match('#^[^\.]*\.(type_section|type|category)#', $value, $matches)) {
                                $found[] = $key;
                                $matches_found = $matches;
                            }
                        }
                        if (count($found) == 1) {
                            //if only ONE filter is found assign it to the search session param
                            $session_filters['search_fields'] = array($list_filters['search_fields'][$found[0]]);
                            $session_filters['compare_options'] = array($list_filters['compare_options'][$found[0]]);
                            $session_filters['values'] = array($list_filters['values'][$found[0]]);
                            $session_filters['logical_operator'] = array(@$list_filters['logical_operator'][$found[0]]);
                            $session_filters['hidden_' . $matches_found[1]] = $list_filters['values'][$found[0]];

                            //set additional simple search filters
                            $filter = preg_replace('#^[^\.]*\.(type_section|type|category)#', '$1', $list_filters['search_fields'][$found[0]]);
                            $additional_hidden_filters[0] = array('name' => 'hidden_' . $filter,
                                                                  'value' => $list_filters['values'][$found[0]]);
                        }
                    }
                } elseif (preg_match_all('#hidden_(type_section|type|category)=\d+(&|$)#', $_SERVER['HTTP_REFERER'], $matches)) {
                    //set additional search filters
                    $additional_hidden_filters = array();
                    foreach ($matches[0] as $match) {
                        $match = explode('=', $match);
                        $additional_hidden_filters[] = array('name' => $match[0],
                                                             'value' => str_replace('&', '', $match[1]));
                    }
                } elseif (!empty($session_filters['hidden_type']) || !empty($session_filters['hidden_type_section']) || !empty($session_filters['hidden_category'])) {
                    if (!empty($session_filters['hidden_type'])) {
                        $simple_param = 'hidden_type';
                    } elseif (!empty($session_filters['hidden_type_section'])) {
                        $simple_param = 'hidden_type_section';
                    } elseif (!empty($session_filters['hidden_category'])) {
                        $simple_param = 'hidden_category';
                    }
                    $additional_hidden_filters[] = array('name' => $simple_param,
                                                         'value' => $session_filters[$simple_param]);
                }
            }

            //get search definitions from the DB
            [$advanced_search, $system_fields, $saved_filters, $simple_search, $additional_search] = $this->getSearchOptions($session_filters);

            // when loading a saved filter from dashlet set-up screen, additional columns should be prepared as well
            if ($request->get('real_module') == 'dashlets') {
                $extracted = Filters::extractSearchFields($session_filters);
                $additional_columns = Dashlets::getAdditionalColumns($registry, $this->modelName, $extracted['model_types'], true) ?: false;
            }

            //prepare data for the template
            $this->viewer = new Viewer($registry);

            // on initial opening of filter window from autocompleter
            // start processing AC filters by additional variables ACs
            if ($action_name == 'filter' && $request->get('autocomplete_filter') == strtolower($factory_name) &&
            !empty($session_filters['search_fields']) && !empty($additional_search)) {
                $update_session_filters = false;
                foreach ($session_filters['search_fields'] as $idx => $field) {
                    // if searching by additional variable
                    if (preg_match('#^a__#', $field) &&
                    isset($additional_search[$field]) &&
                    isset($session_filters['values'][$idx])) {
                        // get first option of first optgroup from compare options
                        $compare_option = reset($additional_search[$field]['compare_options']);
                        $compare_option = reset($compare_option);
                        // check if field is searchable as autocompleter
                        if ($compare_option['itype'] == 'autocompleter') {
                            // get table name from the type of the autocompleter
                            $ac_type = $compare_option['options']['type'];
                            $ac_table = 'DB_TABLE_' . strtoupper($ac_type);
                            $ac_table_i18n = $ac_table . '_I18N';
                            $autocomplete_values = array();
                            // if autocompleter has value
                            if (defined($ac_table) && defined($ac_table_i18n) && !empty($session_filters['values'][$idx])) {
                                // specify which fields to use as code and name
                                $code_fld = $ac_type == 'documents' ? 't.full_num' :
                                            ($ac_type == 'contracts' ? 't.num' : 't.code');
                                $name_fld = $ac_type == 'customers' ?
                                            'TRIM(CONCAT(ti18n.name, \' \', ti18n.lastname))' :
                                            ($ac_type == 'users' ?
                                            'TRIM(CONCAT(ti18n.firstname, \' \', ti18n.lastname))' :
                                            'ti18n.name');
                                // get code and name values
                                $query = 'SELECT ' . $code_fld . ' AS code, ' . $name_fld . ' AS name' . "\n" .
                                         'FROM ' . constant($ac_table) . ' AS t' . "\n" .
                                         'LEFT JOIN ' . constant($ac_table_i18n) . ' AS ti18n' . "\n" .
                                         '  ON t.id=ti18n.parent_id AND ti18n.lang="' . $this->registry['lang'] . '"' . "\n" .
                                         'WHERE t.id="' . $session_filters['values'][$idx] . '"';
                                $autocomplete_values = $this->registry['db']->GetRow($query);
                            }
                            // set autocomplete value in session filters
                            $session_filters['values_autocomplete'][$idx] =
                                $autocomplete_values ?
                                sprintf('[%s] %s', $autocomplete_values['code'], $autocomplete_values['name']) :
                                '';

                            $update_session_filters = true;
                        }
                    }
                }
                if ($update_session_filters) {
                    $session->set($session_param, $session_filters, '', true);
                }
            }
            // end processing AC filters by additional variables ACs

            if ($params) {
                $this->viewer->data['params'] = $params;
                if (!empty($params['real_module'])) {
                    $this->viewer->data['inner_search'] = 1;
                }
            }
            $search_fields = array('basic_vars' => $advanced_search, 'additional_vars' => $additional_search);
            $this->viewer->data['available_action'] = $action_arr[$action_name];
            $this->viewer->data['session_filters'] = $session_filters;
            $this->viewer->data['saved_filters'] = $saved_filters;
            $this->viewer->data['simple_search_defs'] = $simple_search;
            $this->viewer->data['search_fields'] = $search_fields;
            $this->viewer->data['advanced_search_options'] = json_encode($advanced_search);
            $this->viewer->data['additional_search_options'] = json_encode($additional_search) . (isset($additional_columns) ? ', additional_columns = ' .json_encode($additional_columns) : '');
            $this->viewer->data['switch_additional'] = isset($this::$searchAdditionalVarsSwitch) ? $this::$searchAdditionalVarsSwitch : false;
            $this->viewer->data['alias'] = $alias;

            $this->viewer->data['additional_hidden_filters'] = !empty($additional_hidden_filters) ? $additional_hidden_filters : array();
            $this->viewer->data['system_fields'] = $system_fields;
            $this->viewer->setFrameset('_action_search_options.html');
        } else {
            if (method_exists($this, 'getActionOptions')) {
                $action_arr = $this->getActionOptions($action_name);
            }
            $action_arr = @$action_arr[$action_name];
            $this->viewer = new Viewer($registry);
            $this->viewer->data['available_action'] = $action_arr;
            $this->viewer->setFrameset('_action_common_options.html');
        }

        $this->viewer->display();
        exit;
    }

    /**
     * Get files for model (latest accessible revisions)
     */
    protected function _getFiles() {

        $registry = &$this->registry;
        $request = &$registry['request'];

        if ($request->isRequested('model_id') && $request->isRequested('real_module') && $request->isRequested('real_controller')) {

            $db = $registry['db'];
            $lang = ($request->get('model_lang')) ? $request->get('model_lang') : $registry['lang'];

            $module = $request->get('real_module');
            $controller = $request->get('real_controller');
            $model_id = $request->get('model_id');

            // set in the registry certain searched file IDs if specified
            if ($request->get('searched_files_ids')) {
                $registry->set('searched_files_ids', $request->get('searched_files_ids'), true);
            }

            // prepare factory name and model name
            if ($module != $controller) {
                $factory_name = implode('_', array_map('ucfirst', explode('_', $module . '_' . $controller)));
            } else {
                $factory_name = ucfirst($module);
            }

            $model_name = General::plural2singular($factory_name);

            $factory = PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.factory.php';
            require_once ($factory);

            // prepare alias
            $alias = $factory_name::getAlias($module, $controller);

            $filters = array('where' => array($alias . '.id = ' . $model_id));
            if ($module != 'finance') {
                $filters['where'][] = $alias . '.deleted IS NOT NULL';
            }
            if ($module == 'announcements') {
                $filters['where'][] = $alias . '.search_archive = \'all\'';
            }
            if ($module == 'finance' && $controller == 'payments') {
                $filters['where'][] = $alias . '.annulled IS NOT NULL';
            }

            if ($request->get('archive') == 1) {
                $filters['where'][] = $alias . '.archive = \'archive\'';
            }
            if ($factory_name == 'Imports') {
                $model = new ImportLog($registry, array('id' => $model_id));
            } else {
                $model = $factory_name::searchOne($registry, $filters);
            }

            $files = array();
            if ($model) {
                $files = $model->getFiles();
            }

            $files_display_count = 10;
            $files_total_count = 0;
            if (!empty($files['generated'])) {
                $files_total_count += count($files['generated']);
            }
            if (!empty($files['attachments'])) {
                $files_total_count += count($files['attachments']);
            }
            if ($files_total_count > $files_display_count) {
                if (count($files['attachments']) > $files_display_count) {
                    $files['attachments'] = array_slice($files['attachments'], 0, $files_display_count);
                    $files['generated'] = array();
                } else {
                    $files['generated'] = array_slice($files['attachments'], 0, $files_display_count - count($files['attachments']));
                }
            }
            $files_more = ($files_total_count > $files_display_count) ? $files_total_count - $files_display_count : 0;

            if (!empty($files)) {
                //prepare data for the template
                $this->viewer = new Viewer($registry);
                $this->viewer->data['files'] = $files;
                $this->viewer->data['files_more'] = $files_more;
                $this->viewer->data['model_name'] = strtolower($model_name);
                $this->viewer->data['model_id'] = $model_id;
                $this->viewer->data['archive'] = $model->get('archived_by');
                $this->viewer->data['fileAction'] = $factory_name === 'Imports' ? 'getfile' : 'viewfile';
                $this->viewer->setFrameset('_attachments_info.html');

                $this->viewer->display();
                exit;
            }
        }
        return true;
    }

    /**
     * Unlock locked records and redirects to an URL not containing unlock in the query string
     *
     * @return bool - result from the operations
     */
    public function unlockRecords() {
        $request = &$this->registry['request'];
        $unlock = $request->get('unlock') ? $request->get('unlock') : $request->get('ajax_unlock');

        if ($this->action != 'unlock' && $unlock && $this->registry['currentUser']) {
            $this->registry['currentUser']->unlockLockedRecords($unlock);

            if ($request->isRequested('ajax_unlock')) {
                exit('true');
            }
            $url = preg_replace('#(&|\?)unlock=?[a-zA-Z0-9]*#', '', $_SERVER['REQUEST_URI']);
            //$this->registry['messages']->insertInSession($this->registry);
            header('Location: ' . $url);
            exit;
        }

        return true;
    }

    /**
     * Checks the status of the locked records
     * Check if the current record is still being locked
     *
     * @return bool - result from the operations
     */
    public function checkLockedRecords() {
        $request = &$this->registry['request'];

        //user wants to continue the lock of the model
        if ($this->registry['validLogin'] && $this->registry['currentUser']) {
            //if user is still logged in get the locked records
            $locked_records = $this->registry['currentUser']->getLockedRecords();

            $locked = false;
            //$expired_records = array();
            foreach ($locked_records as $idx => $lr) {
                //prepare the url of the record as we have to compare it with the URL in the current window
                $url  = preg_replace('#\.#', '\.', $_SERVER['PHP_SELF']) . '\?' . Router::MODULE_PARAM . '=' . $lr['module'];
                if ($lr['module'] != $lr['controller']) {
                    $url .= '&' . Router::CONTROLLER_PARAM . '=' . $lr['controller'];
                }
                $url .= '&' . $lr['controller'] . '=' . $lr['action'] . '&' . $lr['action'] . '=' . $lr['model_id'];

                if (preg_match('#' . $url . '(\&|$)#', $_SERVER['REQUEST_URI']) && preg_match(self::REGEX_DEFINING_LOCK, $this->action)) {
                    //if the url of the record and the url of the window are equal
                    //check the status of the record and take an action in function of the status
                    if (preg_match('#locked#', $lr['status']) && $lr['lock_by'] != $this->registry['currentUser']->get('id')) {
                        //record is still locked
                        $locked = true;
                    } elseif ($lr['status'] == 'locked_expire' && $lr['lock_by'] == $this->registry['currentUser']->get('id')) {
                        //record is locked but soon will expire so return this to the JS function which manages the AJAX
                        //the user will be asked if he/she want to continue the lock
                        echo 'locked_expire';
                        break;
                    } elseif ($lr['status'] == 'modified') {
                        //record is expired and already changed by other user
                        //the message will be displayed that the record is modified and the user will be redirected
                        //in view mode of the record
                        exit('modified_' . $lr['id']);
                    } elseif ($lr['status'] == 'expired' && $lr['lock_by'] == $this->registry['currentUser']->get('id')) {
                        if (!$locked && !$request->isRequested('ajax_continue_lock')) {
                            //record is expired but not modified
                            //the user will be asked if he/she want to continue the lock
                            exit('expired_' . $lr['id']);
                        } elseif ($locked) {
                            //record is expired and already is locked by other user
                            //the message will be displayed that the record is locked and the user will be redirected
                            //in view mode of the record
                            exit('locked_by_other_' . $lr['id']);
                        }
                    }
                }
            }

            //set the viewer whick will display the locked records info
            include_once PH_MODULES_DIR . 'ajax/viewers/ajax.checklock.viewer.php';
            $this->viewer = new Ajax_Checklock_Viewer($this->registry);

            if ($this->isModelRequired($this->action)) {
                //if the action requires model we get this model from the DB and assign it to the viewer
                //the record is still locked by the current user
               try {
                   $model = $this->getModelFromDB($request->get($this->action));
                   $request->remove('id');
                   if ($model) {
                       if ($request->isRequested('ajax_continue_lock')) {
                           $model->lock();
                       }
                       $model->isLocked(true);
                       $this->viewer->data['model'] = $model;
                   }
               } catch (\Exception $e) {

               }
            }

            $this->viewer->prepare();
            $this->viewer->display();
        }
        exit;
    }

    /**
     * Checks the transition table records
     * Check if there is a method
     *
     * @param Model $model - model to check transition conditions for
     * @param bool $redirect - if true, redirects to refer on failure of check
     * @return bool - result from the operations
     */
    public function checkTransition(Model $model, $redirect = false) {

        if ($model->isTransition()) {
            return true;
        } elseif ($redirect) {
            $this->registry['messages']->insertInSession($this->registry);
            if (isset($_SERVER['HTTP_REFERER'])) {
                header('Location: ' . $_SERVER['HTTP_REFERER']);
            } else {
                header('Location: ' . $_SERVER['PHP_SELF']);
            }
            exit;
        }
        return false;
    }

    /**
     * Cancels current action, gets current model and unlocks it
     * if the action is working with one model only
     * Sets flag for canceling action
     */
    public function cancel()
    {
        if ($this->isModelRequired($this->action)) {
            try {
                $model = $this->getModelFromDB();
                if ($model) {
                    $this->model = $model;
                    if ($this->registry['locking_records']) {
                        $this->model->unlock();
                    }
                }
            } catch (Exception $e) {

            }

        }

        $this->actionCanceled = true;
    }

    /**
     * Checks if action requires model
     *
     * @return bool - result from the operations
     */
    public function isModelRequired($action, $module = '', $controller = '') {
        $result = false;

        if (!$module) {
            $module = $this->module;
        }
        if (!$controller) {
            $controller = $this->controller;
        }

        if (preg_match(self::REGEX_DEFINING_MODEL, $action)) {
            //special occasions would be defined here with if statements
            $result = true;
        }

        return $result;
    }

    public function _select($autocomplete) {
        $request = &$this->registry['request'];

        if ($request->isRequested('get_fill_options_only')) {
            $options = array();
            foreach ($autocomplete['fill_options'] as $option) {
                $field = preg_replace('/(\$[^= ]*).*/', "$1", $option);
                $options[$field] = '';
            }
            exit(json_encode($options));
        }

        // flag means that current action is 'filter' and script is executed in a popup window
        if (!$request->isRequested('autocomplete_filter')) {
            //get last thread_id for autocomplete from session
            $last_db_thread_id = $this->registry['session']->get('last_db_thread_id');
            if ($last_db_thread_id && !$request->get('dont_kill_concurent_queries')) {
                //stop previous autocomplete search query
                @$this->registry['db']->Execute('KILL QUERY ' . $last_db_thread_id);
            }
            //set this mysql thread_id in session
            $this->registry['session']->set('last_db_thread_id', $this->registry['db']->_connectionID->thread_id, '', true);
            // if ajax_filter flag and uniqid are specified, then filter window
            // is being closed and autocomplete params with that key should be
            // removed from session
            if ($request->isRequested('ajax_filter') && $request['uniqid']) {
                // remove item from session if it is present
                if ($this->registry['session']->get($request['uniqid'], 'autocomplete_params')) {
                    // if there is a locking record (autocomplete action was 'edit'), remove it
                    $autocomplete_params = $this->registry['session']->get($request['uniqid'], 'autocomplete_params');
                    if ($this->registry['currentUser'] && !empty($autocomplete_params['unlock'])) {
                        $this->registry['currentUser']->unlockLockedRecords($autocomplete_params['unlock']);
                    }
                    $this->registry['session']->remove($request['uniqid'], 'autocomplete_params');
                }
                // if flag is set, this is a clean up request
                if ($request['exit_after']) {
                    exit;
                }
            }
            //end the current session and allow to execute another script without waiting the end of this script
            $this->registry['session']->end();
        }

        //get fields type
        if ($request->isRequested('fill_types')) {
            $autocomplete['fill_types'] = $request->get('fill_types');
        }

        //fields that should not be empty so we set them to 0(zero)
        $mandatory_num = array('price', 'sell_price', 'delivery_price', 'alternative_deliverer', 'deliverer');
        //prepare default filters
        $filters = array('sanitize' => true,
                         'where' => array(),
                         'sort' => array()
                   );

        if ($request->isRequested('model_lang')) {
            $filters['model_lang'] = $request->get('model_lang');
        }

        if ($request->isRequested('ajax_filter') && $request->get($request->get('field'))) {
            // number of models to search by specified ids (from filter or refresh)
            $filters['display'] = count(preg_split('#\s*,\s*#', $request->get($request->get('field'))));
        } elseif (!$request->isRequested('autocomplete_filter')) {
            // max. number of suggestions
            $filters['display'] = PH_MAX_AUTOCOMPLETER_OPTIONS;
        }

        // Set flag to check the permissions of the current user
        if ($request->get('check_user_permissions')) {
            $filters['check_user_permissions'] = true;
        }

        //name of the input field that request the autocompleter
        $source_field = $request->get('field');

        //key is the search word
        if ($request->isRequested('autocomplete_filter')) {
            $key = $request->get('search_value');
        } else {
            $key = $request->get($source_field);
        }

        //row is the group table row index (starting with 1 instead of 0)
        $row = $request->get('row');

        //the key might be an array derived from a group multi index table
        if (is_array($key)) {
            if (isset($key[-$row])) {
                //new row in GT2
                $key = $key[-$row];
            } else {
                $key = $key[$row-1];
            }
        }

        if ((@!$suggestions_format = $autocomplete['suggestions_format']) && !$request->isRequested('autocomplete_filter')) {
            exit('<ul><li><span class="red">SUGGESTIONS ERROR</span></li></ul>');
        }
        if ((@!$fill_options = $autocomplete['fill_options']) && !$request->isRequested('autocomplete_filter')) {
            exit('<ul><li><span class="red">FILL ERROR</span></li></ul>');
        }
        if (!$request->get('ajax_filter')) {
            //check if $key match fill options for the field
            //and split the $key in to words
            foreach ($fill_options as $idx => $option) {
                // fill option must match the EXACT source field
                if (preg_match('#\$' . $source_field . '\s*=>#', $option)) {
                    $symbols = preg_replace('#(<[^<>]*>)|(\s+)|(\$' . $source_field . ')|(=>)#u', '', $option);
                    // fill options might include non-ASCII characters
                    $symbols = preg_replace_callback('#(.)#u', function($matches) {return preg_quote($matches[0], '#') . '|';}, $symbols);
                    $symbols = substr($symbols, 0, -1);
                    if (!empty($symbols)) {
                        $replaced_key = preg_replace('#' . $symbols . '#ui', ' ', $key);
                        // if there is at least one other character left
                        if (trim($replaced_key)) {
                            $key = $replaced_key;
                        }
                    }
                    $key = preg_split('#\s+#u', $key);
                    break;
                }
            }
        }

        if (!is_array($key)) {
            $key = array($key);
        }
        //clear white spaces
        foreach ($key as $idx => $k) {
            $key[$idx] = trim($k);
            if ($key[$idx] === '') {
                unset($key[$idx]);
            }
        }
        $key = array_values($key);
        //regular expression is prepared so that the keywords are later bolded
        $regex_key = '#(';
        foreach ($key as $idx => $k) {
            if ($idx == (count($key)-1)) {
                $regex_key .= preg_quote($k, '#');
            } else {
                $regex_key .= preg_quote($k, '#') . '|';
            }
        }
        $regex_key .= ')#iu';

        // escape entered values for the SQL search query
        $key = General::slashesEscape($key);

        $search_ids = $all_data = array();
        if (!$request->isRequested('autocomplete_filter')) {
            //sets search values
            foreach ($key as $k) {
                $subfilters = array();
                foreach ($autocomplete['search'] as $i => $field) {
                    if ($k != '' && !preg_match('#\.id(?!\w)#', $field)) {
                        $subfilters[] = $field . ' LIKE \'%' . $k . '%\'';
                        if (preg_match('#ci18n\.name#', $field)) {
                            $subfilters[] = 'ci18n.lastname LIKE \'%' . $k . '%\'';
                        } elseif (preg_match('#ui18n\.firstname#', $field) && !in_array('ui18n.lastname', $autocomplete['search'])) {
                            $subfilters[] = 'ui18n.lastname LIKE \'%' . $k . '%\'';
                        }
                    } elseif ($k && preg_match('#\.id#', $field)) {
                        $search_ids = $k;
                        $subfilters[] = $field . ' IN (' . $k . ')';
                    }
                }
                // add search conditions to $filters['where'] as if they come from the "Search" form
                $num_sf = count($subfilters);
                for ($sf = 0; $sf < $num_sf; $sf++) {
                    $filters['where'][] = $subfilters[$sf] . ' ' . ($sf < $num_sf - 1 ? 'OR' : 'AND');
                }
            }
        } else {
            //when opening the filter popup window for search from autocompleter
            $search_fields = $autocomplete['search'];

            //sets search values
            foreach ($key as $k) {
                foreach ($search_fields as $field) {
                    if ($k != '' && !preg_match('#\.id(?!\w)#', $field)) {
                        if (preg_match('#ci18n\.name#', $field)) {
                            $filters['where'][] = 'CONCAT(ci18n.name, \' \', ci18n.lastname) LIKE \'%' . $k . '%\' OR';
                        } elseif (preg_match('#ui18n\.firstname#', $field) && !in_array('ui18n.lastname', $search_fields)) {
                            $filters['where'][] = 'ui18n.firstname LIKE \'%' . $k . '%\' OR';
                            $filters['where'][] = 'ui18n.lastname LIKE \'%' . $k . '%\' OR';
                        } else {
                            $filters['where'][] = $field . ' LIKE \'%' . $k . '%\' OR';
                        }
                    } elseif ($k && preg_match('#\.id#', $field)) {
                        $search_ids = $k;
                        $filters['where'][] = $field . ' IN (' . $k . ') OR';
                    }
                }
                if (count($filters['where']) > 0) {
                    $filters['where'][count($filters['where']) - 1] = preg_replace('#\s+OR\s*$#', ' AND',
                                                                      $filters['where'][count($filters['where']) - 1]);
                }
            }
        }

        // get model factory name from autocomplete type
        $factory = implode('_', array_map('ucfirst', explode('_', $autocomplete['type'])));

        $active_flag = false;
        //try to guess table alias
        $alias = $factory::getAlias($this->module, $this->controller);
        if (!empty($autocomplete['additional_where'])) {
            foreach ($autocomplete['additional_where'] as $filter) {
                $filters['where'][] = $filter;
                if (preg_match('#^' . $alias . '\.active#', trim($filter))) {
                    $active_flag = true;
                }
            }
        }
        if (!$active_flag) {
            $filters['where'][] = $alias . '.active = \'1\' AND';
        }

        //prepare sort
        foreach ($autocomplete['sort'] as $sort) {
            $filters['sort'][] = $sort;
        }

        //return the filters as we will need them in the FILTER VIEWER
        if ($request->isRequested('autocomplete_filter')) {
            $autocomplete['select_multiple'] = $request->get('autocomplete_select_multiple');
            $autocomplete['field'] = $request->get('field');
            $autocomplete['scope'] = $request->get('scope');
            $autocomplete['uniqid'] = $request->get('uniqid');
            $autocomplete['row'] = $row;
            if ($request->get('table')) {
                $autocomplete['table'] = $request->get('table');
            }
            if ($request->get('id_var')) {
                $autocomplete['id_var'] = $request->get('id_var');
            }
            if ($request->get('var_type')) {
                $autocomplete['var_type'] = $request->get('var_type');
            }
            if ($request->get('autocomplete_execute_after')) {
                $autocomplete['execute_after'] = $request->get('autocomplete_execute_after');
            }
            if ($request->get('autocomplete_execute_after_params')) {
                $autocomplete['execute_after_params'] = $request->get('autocomplete_execute_after_params');
            }
            if ($request->get('autocomplete_plugin')) {
                $autocomplete['plugin'] = $request->get('autocomplete_plugin');
                if ($request->get('autocomplete_plugin_params')) {
                    $autocomplete['plugin_params'] = json_decode($request->get('autocomplete_plugin_params'));
                }
                if ($request->get('autocomplete_on_select')) {
                    $autocomplete['on_select'] = $request->get('autocomplete_on_select');
                }
            }
            //check if unique column content is requested
            if ($request->get('autocomplete_unique')) {
                $autocomplete['unique'] = $request->get('autocomplete_unique');
            }
            //check if stop_customer_details is requested
            if ($request->get('stop_customer_details')) {
                $autocomplete['stop_customer_details'] = $request->get('stop_customer_details');
            }
            $autocomplete['filters'] = $request->get('filters');
            // store autocomplete params as a sub-element in section with uniqid as key
            $this->registry['session']->set($request->get('uniqid'), $autocomplete, 'autocomplete_params', true);

            // check for specific 'additional_where' filters which are flags
            $master_keys_array = array('customer_trademark' => 1, 'contactpersons' => 1);
            if (isset($autocomplete['additional_where']) && array_intersect_key($master_keys_array, $autocomplete['additional_where'])) {
                foreach ($filters['where'] as $idx => $filter) {
                    if ($filter == '1') {
                        unset($filters['where'][$idx]);
                    }
                }
                $filters['where'] = array_values($filters['where']);
            }
            if (isset($autocomplete['additional_where'])) {
                foreach ($autocomplete['additional_where'] as $aw) {
                    if (preg_match('/get_available_quantities/', $aw)) {
                        foreach ($filters['where'] as $idx => $filter) {
                            if (preg_match('/get_available_quantities/', $filter)) {
                                unset($filters['where'][$idx]);
                                break;
                            }
                        }
                        $filters['where'] = array_values($filters['where']);
                        break;
                    }
                    if (preg_match('/currency/', $aw)) {
                        foreach ($filters['where'] as $idx => $filter) {
                            if (preg_match('/currency/', $filter)) {
                                unset($filters['where'][$idx]);
                                break;
                            }
                        }
                        $filters['where'] = array_values($filters['where']);
                        break;
                    }
                }
            }
            return $filters;
        }

        //get properties names for the suggestions format
        preg_match_all('#<[^<>]*>#', $suggestions_format, $sugg_matches);
        if (empty($sugg_matches[0])) {
            exit('<ul><li><span class="red">SUGGESTIONS ERROR</span></li></ul>');
        } else {
            $sugg_matches = $sugg_matches[0];
        }

        if (isset($autocomplete['get_fields'])) {
            $filters['get_fields'] = $autocomplete['get_fields'];
        }
        if (isset($autocomplete['ignore_fields'])) {
            $filters['ignore_fields'] = $autocomplete['ignore_fields'];
        }
        // check for specific 'additional_where' filters which are flags
        $master_keys_array = array('customer_trademark' => 1, 'contactpersons' => 1);
        if (isset($autocomplete['additional_where']) && $match_array = array_intersect_key($master_keys_array, $autocomplete['additional_where'])) {
            foreach ($filters['where'] as $idx => $filter) {
                if ($filter == '1') {
                    unset($filters['where'][$idx]);
                }
            }
            $filters['where'] = array_values($filters['where']);

            $ac_flag = array_keys($match_array);
            $ac_flag = reset($ac_flag);
            if ($ac_flag == 'contactpersons') {
                require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
                $factory .= '_Contactpersons';
                [$models, $pagination] = $factory::pagedSearch($this->registry, $filters);
            } else {
                $this->registry->set('trademarks_search_requested', true, true);
                require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
                [$models, $pagination] = $factory::paginatedSearch($this->registry, $filters, 'Customers', 'getTrademarks');
            }
        } elseif (!empty($autocomplete['plugin']['method'])) {
            // define plugin file
            if ($autocomplete['plugin']['name']) {
                $plugin_file = PH_MODULES_DIR . sprintf('autocompleters/plugins/%s/models/%s.autocompleters.factory.php', $autocomplete['plugin']['name'], $autocomplete['plugin']['name']);
                // define lang file
                $lang_file = PH_MODULES_DIR . sprintf('autocompleters/plugins/%s/i18n/%s/autocompleters.ini', $autocomplete['plugin']['name'], $this->registry['lang']);
            } else {
                // include general autocompleters factory
                $plugin_file = PH_MODULES_DIR . 'autocompleters/models/autocompleters.factory.php';
                $lang_file = '';
            }

            if (!file_exists($plugin_file)) {
                $models = array();
            } else {
                require_once($plugin_file);
                if (file_exists($lang_file)) {
                    // load lang file
                    $this->loadI18NFiles($lang_file);
                }
                $plugin_class = $autocomplete['plugin']['class'];
                $plugin_method = $autocomplete['plugin']['method'];
                if (!method_exists($plugin_class, $plugin_method)) {
                    $models = array();
                } else {
                    @list($models, $pagination) = $plugin_class::$plugin_method($this->registry, $autocomplete);
                    if (empty($pagination)) {
                        $pagination = array(
                            'total' => count($models),
                            'found' => count($models)
                        );
                    }
                }
            }
        } else {
            [$models, $pagination] = $factory::pagedSearch($this->registry, $filters);
        }
        if (!$request->get('ajax_filter')) {
            //guarantee UTF8 output
            if (!headers_sent()) {
                header('Content-Type: text/html; charset=utf-8');
            }
            printf('<ul>');

            // additional suggestions containing placeholders, such as currentUser etc.
            if (($autocomplete['type'] == 'users' || $autocomplete['type'] == 'departments') &&
            $request->get('field') == 'values_autocomplete') {
                $ac_additional_suggestions = array();
                foreach ($autocomplete as $ac_key => $ac_value) {
                    if ($autocomplete['type'] == 'users') {
                        switch ($ac_key) {
                        case 'current_user':
                            $ac_additional_suggestions[] = array('value' => 'currentUser',
                                                                 'label' => sprintf('<< %s >>', $this->i18n('current_user')));
                            break;
                        case 'include_nobody':
                            $ac_additional_suggestions[] = array('value' => '0',
                                                                 'label' => sprintf('<< %s >>', $this->i18n('nobody')));
                            break;
                        case 'include_not_read':
                            $ac_additional_suggestions[] = array('value' => 'not_read_by_anyone',
                                                                 'label' => sprintf('<< %s >>', $this->i18n('not_read_by_anyone')));
                            break;
                        case 'include_not_assigned':
                            $ac_additional_suggestions[] = array('value' => 'no_user_assigned',
                                                                 'label' => sprintf('<< %s >>', $this->i18n('no_user_assigned')));
                            break;
                        default:
                            break;
                        }
                    } elseif ($autocomplete['type'] == 'departments') {
                        switch ($ac_key) {
                        case 'current_user_departments':
                            $ac_additional_suggestions[] = array('value' => 'currUserDepartments',
                                                                 'label' => sprintf('<< %s >>', $this->i18n('current_user_departments')));
                            break;
                        case 'include_not_assigned':
                            $ac_additional_suggestions[] = array('value' => 'no_user_assigned',
                                                                 'label' => sprintf('<< %s >>', $this->i18n('no_user_assigned')));
                            break;
                        default:
                            break;
                        }
                    }
                }

                foreach ($ac_additional_suggestions as $ac_key => $ac_value) {
                    $label = $ac_value['label'];
                    $value = $ac_value['value'];
                    $title = htmlspecialchars($label);

                    // prepare data, which will be parsed by the javascript processing this AJAX request
                    $data = array('row'          => $row,
                                  'id'           => $value,
                                  'type' => strtolower($autocomplete['type'])
                    );
                    foreach ($fill_options as $idx => $option) {
                        @list($var_name, $var_value) = preg_split('#\s*=>\s*#', $option);
                        $var_name = trim($var_name);
                        $var_value = trim($var_value);
                        if (preg_match('/^\$values_(autocomplete|oldvalue)$/', $var_name)) {
                            $data[$var_name] = $label;
                        } elseif (preg_match('/^\$values$/', $var_name)) {
                            // value can be formatted something like "user-<id>"
                            // so we need to keep it in the same format as specified in autocompleter
                            preg_match_all('#<[^<>]*>#', $var_value, $matches);
                            $matches = $matches[0];
                            $data[$var_name] = isset($matches[0]) ? preg_replace('#' . $matches[0] . '#u', $value, $var_value) : $value;
                        }
                    }

                    $suggestions = $label;
                    if ($regex_key != '#()#iu') {
                        //replace the keyword with bold tags so that it is obvious what we search
                        $suggestions = preg_replace($regex_key, '<strong>$1</strong>', $suggestions);
                        $suggestions = preg_replace(htmlspecialchars('#<(/?strong)>#'), '<$1>', htmlspecialchars($suggestions));
                    }

                    printf('  <li id="%s_%s" title="%s">%s' . "\n",
                                strtolower($autocomplete['type']), $value, $title, $suggestions);
                    printf('    <input type="hidden" name="%s_%s_data" id="%s_%s_data" value=\'%s\' disabled="disabled" />' . "\n",
                                    strtolower($autocomplete['type']), $value,
                                    strtolower($autocomplete['type']), $value, json_encode($data));
                    printf('  </li>' . "\n");
                }
            }
        }

        if ($this->registry['db']->ErrorMsg() && preg_match('#interrupted#', $this->registry['db']->ErrorMsg())) {
            //exit if interrupted query by another autocomplete search
            ob_clean();
            exit;
        } elseif ($models) {
            //get the additional variables values from the database, not from POST
            //there is a problem when getting additional variable value from the model
            //when a the form (the request) contains exctly the same variable
            $this->registry->set('get_old_vars', true, true);
            foreach ($models as $model) {
                if (is_array($model)) {
                    $model = new Model($this->registry, $model);
                }
                $suggestions = $suggestions_format;
                //replace matched properties with their values
                foreach ($sugg_matches as $match) {
                    $property = preg_replace('#<|>|\s#', '', $match);
                    if (preg_match('#^a_+#', $property)) {
                        //additional variable
                        $property = preg_replace('#^a_+(' . PH_ADDITIONAL_VAR_PREFIX . ')?#', '', $property);
                        if ($this->registry['trademarks_search_requested']) {
                            $tmp_id = $model->get('id');
                            $model->set('id', $model->get('trademark'), true);
                            $replacement = $model->getVarValue($property);
                            $model->set('id', $tmp_id, true);
                        } else {
                            $replacement = $model->getVarValue($property);
                        }
                    } else {
                        $replacement = $model->get($property);
                    }
                    if (is_array($replacement)) {
                        $replacement = array_shift($replacement);
                    }
                    //special formatting for dates
                    //we can expand this condition if needed
                    if (strpos($property, 'date') !== false) {
                        //date/time formatting
                        if (preg_match('#^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$#', $replacement)) {
                            if (strpos($replacement, '0000-00-00') === false) {
                                $replacement = General::strftime($this->i18n('date_mid'), $replacement);
                            } else {
                                $replacement = '  .  .       :  ';
                            }
                        } elseif (preg_match('#^\d{4}-\d{2}-\d{2}$#', $replacement)) {
                            if (strpos($replacement, '0000-00-00') === false) {
                                $replacement = General::strftime($this->i18n('date_short'), $replacement);
                            } else {
                                $replacement = '  .  .    ';
                            }
                        }
                    }
                    $suggestions = preg_replace('#\s*$#u', '', str_replace($match, $replacement, $suggestions));
                }
                //set the title of the <li> to be the full text
                $title = htmlspecialchars($suggestions);

                /* TEMPORARILY DO NOT TRUNCATE THE SUGGESTIONS
                //cut the suggestion to fit in the holder
                if (mb_strlen($suggestions, "UTF-8") > PH_MAX_STRING_CHARS) {
                    $suggestions = mb_substr($suggestions, 0, PH_MAX_STRING_CHARS, "UTF-8");
                }
                */

                if ($regex_key != '#()#iu') {
                    //replace the keyword with bold tags so that it is obvious what we search
                    $suggestions = preg_replace($regex_key, '<strong>$1</strong>', $suggestions);
                    $suggestions = preg_replace(htmlspecialchars('#<(/?strong)>#'), '<$1>', htmlspecialchars($suggestions));
                }

                //prepare data, which will be parsed by the javascript processing this AJAX request
                $data = array('row'  => $row,
                              'id'   => $model->get('id'),
                              'type' => strtolower($autocomplete['type'])
                );
                if (strtolower($autocomplete['type']) == 'customers') {
                    if (isset($autocomplete['additional_where']) && array_key_exists('contactpersons', $autocomplete['additional_where'])) {
                        // contact persons
                    } else {
                        $data['is_company'] = $model->get('is_company');
                        $data['trademark'] = $model->get('main_trademark');
                        $data['model_type'] = $model->get('type');
                    }
                }
                if (isset($autocomplete['additional_where']) && array_key_exists('customer_trademark', $autocomplete['additional_where'])) {
                    $data['subtype'] = 'trademarks';
                    $data['customer_id'] = $model->get('customer');
                    $data['is_company'] = $model->get('customer_is_company');
                }
                foreach ($fill_options as $idx => $option) {
                    @list($var_name, $var_value) = preg_split('#\s*=>\s*#', $option);
                    $var_name = trim($var_name);
                    $var_value = trim($var_value);
                    preg_match_all('#<[^<>]*>#', $var_value, $matches);
                    $matches = $matches[0];
                    $data[$var_name] = $var_value;
                    foreach ($matches as $match) {
                        $property = preg_replace('#<|>|\s#', '', $match);
                        if (preg_match('#^a_+#', $property)) {
                            //additional variable
                            $property = preg_replace('#^a_+(' . PH_ADDITIONAL_VAR_PREFIX . ')?#', '', $property);
                            // if true, get raw value, otherwise get formatted value
                            $force_raw = !(empty($autocomplete['fill_types'][$idx]) || $autocomplete['fill_types'][$idx] == 'text');
                            // get the value without checking layout permissions
                            if ($this->registry['trademarks_search_requested']) {
                                $tmp_id = $model->get('id');
                                $model->set('id', $model->get('trademark'), true);
                                $replacement = $model->getVarValue($property, $force_raw, true);
                                $model->set('id', $tmp_id, true);
                            } else {
                                $replacement = $model->getVarValue($property, $force_raw, true);
                            }
                        } else {
                            $replacement = $model->get($property);
                            //special formatting for dates
                            //we can expand this condition if needed
                            if (strpos($property, 'date') !== false) {
                                //date/time formatting
                                if (preg_match('#^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$#', $replacement)) {
                                    if (strpos($replacement, '0000-00-00') === false) {
                                        $replacement = General::strftime($this->i18n('date_mid'), $replacement);
                                    } else {
                                        $replacement = '  .  .       :  ';
                                    }
                                } elseif (preg_match('#^\d{4}-\d{2}-\d{2}$#', $replacement)) {
                                    if (strpos($replacement, '0000-00-00') === false) {
                                        $replacement = General::strftime($this->i18n('date_short'), $replacement);
                                    } else {
                                        $replacement = '  .  .    ';
                                    }
                                }
                            }
                        }
                        if (is_array($replacement)) {
                            // This is added for ALVIS, to be able to auto fill checkbox groups with AK
                            // @see #5064
                            if ($request->get('array_as_list')) {
                                $replacement = implode(',', $replacement);
                            } else {
                                $replacement = reset($replacement);
                            }
                        }
                        if (empty($replacement) && in_array(preg_replace('#<|>|\s#u', '', $match), $mandatory_num)) {
                            $replacement = 0;
                        }
                        $data[$var_name] = preg_replace('#\s*$#u', '', str_replace($match, $replacement, $data[$var_name]));
                        if (!$request->get('ajax_filter')) {
                            //replace single quotation marks (apostrophes) ' with html entity &#39;
                            $data[$var_name] = str_replace("'", "&#39;", $data[$var_name]);
                        }
                    }
                }

                if (!$request->get('ajax_filter')) {
                    $class_name = $model->modelName == 'User' ? ($model->get('is_portal') ? 'user_portal' : 'user_normal') : '';
                    if (!$model->isActivated()) {
                        $class_name .= ' inactive_option';
                        $class_name = trim($class_name);
                    }
                    printf('  <li id="%s_%s" title="%s" class="%s">%s' . "\n",
                                strtolower($autocomplete['type']), $model->get('id'), $title, $class_name, $suggestions);
                    printf('    <input type="hidden" name="%s_%s_data" id="%s_%s_data" value=\'%s\' disabled="disabled" />' . "\n",
                                    strtolower($autocomplete['type']), $model->get('id'),
                                    strtolower($autocomplete['type']), $model->get('id'), json_encode($data));
                    printf('  </li>' . "\n");
                } else {
                    if (!empty($search_ids) && !is_array($search_ids)) {
                        $search_ids = preg_split('#\s*,\s*#', $search_ids);
                        foreach ($search_ids as $s_id) {
                            $all_data[$autocomplete['type'] . '_' . $s_id] = array();
                        }
                    }
                    $all_data[$autocomplete['type'] . '_' . $model->get('id')] = $data;
                }
            }
            if ($request->get('ajax_filter') && $all_data) {
                exit(json_encode($all_data));
            }
            if (count($models) < $pagination['total'] && !$request->get('ajax_filter')) {
                if (empty($autocomplete['plugin']['method'])) {
                    printf('  <li><a href="javascript: void(0);" onclick="filterAutocompleteItems(params_%s)"><em>%d %s</em></a></li>',
                        $request->get('uniqid'), $pagination['total'], $this->i18n('items_found'));
                } else {
                    // If the autocomplete plugin parameter is set
                    // then the last option, which shows the results count, should not be a link to additional search, because it will not work properly
                    printf('  <li><em>%d %s</em></li>',
                        $pagination['total'], $this->i18n('items_found'));
                }
            } elseif ($pagination['found'] > PH_MAX_AUTOCOMPLETER_OPTIONS && !$request->get('ajax_filter')) {
                if (empty($autocomplete['plugin']['method'])) {
                    printf('  <li><a href="javascript: void(0);" onclick="filterAutocompleteItems(params_%s)"><em>%s</em></a></li>',
                        $request->get('uniqid'), $this->i18n('more_items_found'));
                } else {
                    printf('  <li><em>%d %s</em></li>',
                        $pagination['total'], $this->i18n('items_found'));
                }
            }
        } else {
            if ($request->get('ajax_filter')) {
                if (!empty($search_ids) && !is_array($search_ids)) {
                    $search_ids = preg_split('#\s*,\s*#', $search_ids);
                    foreach ($search_ids as $s_id) {
                        $all_data[$autocomplete['type'] . '_' . $s_id] = array();
                    }
                }
                exit(json_encode($all_data));
            } elseif (empty($autocomplete['plugin']['method'])) {
                printf('  <li><a href="javascript: void(0);" onclick="filterAutocompleteItems(params_%s)">%s</a></li>',
                    $request->get('uniqid'), $this->i18n('no_items_found'));
            } else {
                // don't put total option when plugin is set
                if (!empty($autocomplete['no_results_error'])) {
                    printf('  <li>%s</li>', $autocomplete['no_results_error']);
                } else {
                    printf('  <li>%s</li>', $this->i18n('no_items_found'));
                }
            }
        }
        printf('</ul>');

        exit;

    }

    /**
     * Prepare export options with plugins where needed
     *
     * @return array $export_options - the prepared array with options for export
     */
    public function prepareExportOptions() {

        $model_factory_name = $this->modelFactoryName;
        //try to guess table alias for the filters
        $alias = $model_factory_name::getAlias($this->registry->get('module'), $this->registry->get('controller'));
        //save search params in the session in order to get model type
        $filters = $model_factory_name::saveSearchParams($this->registry, array(), $this->action . '_');
        $types = array();
        $sections = array();
        if (!empty($filters['where'])) {
            foreach ($filters['where'] as $where) {
                // types of payments are strings, not integers
                if ($this->modelFactoryName == 'Finance_Payments') {
                    if (preg_match('/'.$alias.'\.type\s*=\s*\'?(\w+)\'?(\s+(AND|OR))?/iu', $where)) {
                        $types[] = trim(preg_replace('/'.$alias.'\.type\s*=\s*\'?(\w+)\'?(\s+(AND|OR))?/iu', '$1', $where));
                    }
                } else {
                    if (preg_match('/'.$alias.'\.type\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', $where)) {
                        $types[] = trim(preg_replace('/'.$alias.'\.type\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', '$1', $where));
                    }
                }
                // type section for financial documents
                if (preg_match('/^Finance_((Incomes|Expenses)_Reasons|Warehouses_Documents|Annulments)$/', $this->modelFactoryName)) {
                    if (preg_match('/fdt\.type_section\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', $where)) {
                        $sections[] = trim(preg_replace('/fdt\.type_section\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', '$1', $where));
                    }
                } else {
                    if (preg_match('/'.$alias.'t\.type_section\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', $where)) {
                        $sections[] = trim(preg_replace('/'.$alias.'t\.type_section\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', '$1', $where));
                    }
                }
            }
        }

        //default value is the standard plugin
        $last_export_plugin = 'standard';

        //the default export does not require section
        $selected_section = 0;
        if (count($sections) == 1) {
            $selected_section = $sections[0];
        }

        //the default export does not require type
        $selected_model_type = 0;

        //check for plugins for export
        //prepare the filters
        if (count($types) == 1) {
            $selected_model_type = $types[0];
        } else {
            $selected_model_type = 0;
        }

        //try to get plugin for the listed model and model_type
        $filters = array(
            'model' => $this->modelName,
            'model_type' => $selected_model_type,
            'sanitize' => true
        );
        include_once PH_MODULES_DIR . 'exports/models/exports.factory.php';
        $plugins = Exports::search($this->registry, $filters);
        if (!empty($plugins)) {
            //get the last used export plugin
            $last_export_plugins = $this->registry['session']->get('last_export_plugins');
            //the session param contains the plugin and the model_type
            $last_export_plugin_session_param = sprintf('%s_%s_type_%s',
                $this->module,
                $this->controller,
                $selected_model_type
            );
            if (!empty($last_export_plugins[$last_export_plugin_session_param])) {
                $last_export_plugin = $last_export_plugins[$last_export_plugin_session_param];
            }

            //prepare the default standard export in the list of options for the dropdown
            $plugin_options = array(
                array('label' => $plugins[0]->i18n('export_plugin_standard'),
                      'option_value' => 'standard')
            );

            foreach ($plugins as $idx => $plugin) {
                //prepare the plugin options for the dropdown
                $plugin_options[] = array(
                    'label' => $plugin->get('name'),
                    'option_value' => $plugin->get('id'));

                //get the default export filters and the visibility of fields
                if ($plugin->get('id') == $last_export_plugin) {
                    $settings = $plugin->get('settings');
                    //default export filters
                    foreach (Exports::$filtersToStore as $param) {
                        if (in_array('export_' . $param, array_keys($settings))) {
                            $default_export_filters[$param] = $settings['export_' . $param];
                        }
                    }
                    //visibility of fields for selected plugin
                    if (!empty($settings['export_hide_filters'])) {
                        $hide_filters = preg_split('#\s*,\s*#', $settings['export_hide_filters']);
                        foreach ($hide_filters as $param) {
                            $filters_hide[$param] = true;
                        }
                    }
                }
            }
            $export_options_plugins = array (
                array (
                    'custom_id' => 'plugin',
                    'name' => 'plugin',
                    'type' => 'dropdown',
                    'required' => 1,
                    //this javascript function toggles plugins with AJAX saving filters in session and hiding filters if needed
                    'onchange' => 'selectExport(this, \'' . implode(',', Exports::$filtersToStore) . '\')',
                    'label' => $plugin->i18n('export_plugin'),
                    'help' => $plugin->i18n('help_export_plugin'),
                    'options' => $plugin_options,
                    'value' => ($last_export_plugin) ? $last_export_plugin : 'standard'),
                array (
                    'custom_id' => 'previous_plugin',
                    'name' => 'previous_plugin',
                    'type' => 'hidden',
                    'hidden' => 1,
                    'value' => ($last_export_plugin) ? $last_export_plugin : 'standard'),
            );
        }

        //get the saved session filters for the last used plugin
        $export_filters_session_param = sprintf('%s_%s_plugin_%s%s',
            $this->module,
            $this->controller,
            $last_export_plugin,
            ($selected_model_type) ? '_type_' . $selected_model_type : ''
        );
        $export_filters = $this->registry['session']->get('export_filters');
        if (isset($export_filters[$export_filters_session_param])) {
            $export_filters = $export_filters[$export_filters_session_param];
            //apply the selected plugin's default filters
            if (!empty($default_export_filters)) {
                foreach ($default_export_filters as $f => $v) {
                    $export_filters[$f] = $v;
                }
            }
        } else {
            $export_filters = array();
        }
        //prepare the visibility of filters (some plugins do not need some of the filters)
        foreach (Exports::$filtersToStore as $param) {
            if (!isset($filters_hide[$param])) {
                $filters_hide[$param] = false;
            }
        }

        //prepare export options
        $export_options = array (
            array (
                'custom_id' => 'file_name',
                'name' => 'file_name',
                'type' => 'text',
                'required' => 1,
                'label' => $this->i18n('file_name'),
                'help' => $this->i18n('file_name'),
                'value' => (isset($export_filters['file_name'])) ? $export_filters['file_name'] : $this->modelFactoryName,
                'hidden' => (isset($filters_hide['file_name'])) ? $filters_hide['file_name'] : 0,
            ),
            array (
                'custom_id' => 'format',
                'name' => 'format',
                'type' => 'dropdown',
                'required' => 1,
                'label' => $this->i18n('file_format'),
                'help' => $this->i18n('file_format'),
                'options' => array(
                                array(
                                    'label' => $this->i18n('file_format_xls'),
                                    'option_value' => $this->i18n('file_format_xls')),
                                array(
                                    'label' => $this->i18n('file_format_xlsx'),
                                    'option_value' => $this->i18n('file_format_xlsx')),
                                array(
                                    'label' => $this->i18n('file_format_csv'),
                                    'option_value' => $this->i18n('file_format_csv')),
                            ),
                //this javascript function toggles the visibility of the separator filter
                'onchange' => 'toggleExportFormat(this)',
                'value' => (isset($export_filters['format'])) ? $export_filters['format'] : '',
                'hidden' => (isset($filters_hide['format'])) ? $filters_hide['format'] : 0,
            ),
            array (
                'custom_id' => 'group_tables',
                'name' => 'group_tables',
                'type' => 'radio',
                'required' => 1,
                'options_align' => 'horizontal',
                'label' => $this->i18n('include_group_tables'),
                'help' => $this->i18n('include_group_tables'),
                'options' => array(
                    array(
                        'label' => $this->i18n('yes'),
                        'option_value' => 1),
                    array(
                        'label' => $this->i18n('no'),
                        'option_value' => 0),
                ),
                'value' => (isset($export_filters['group_tables'])) ? $export_filters['group_tables'] : 0,
                'hidden' => (empty($export_filters['format']) || $export_filters['format'] != 'xlsx') ? 1 : ((isset($filters_hide['group_tables'])) ? $filters_hide['group_tables'] : 0),
            ),
            array (
                'custom_id' => 'separator',
                'name' => 'separator',
                'type' => 'dropdown',
                'required' => 1,
                'label' => $this->i18n('separator'),
                'help' => $this->i18n('separator'),
                'options' => array(
                    array(
                        'label' => $this->i18n('delimiter_comma'),
                        'option_value' => 'comma'
                    ),
                    array(
                        'label' => $this->i18n('delimiter_semicolon'),
                        'option_value' => 'semicolon'
                    ),
                    array(
                        'label' => $this->i18n('delimiter_tab'),
                        'option_value' => 'tab'
                    ),

                ),
                'value' => (isset($export_filters['separator'])) ? $export_filters['separator'] : '',
                'hidden' => (empty($export_filters['format']) || $export_filters['format'] != 'csv') ? 1 : ((isset($filters_hide['separator'])) ? $filters_hide['separator'] : 0),
            ),
        );

        //the standard and plugin options are prepared, just merge them
        if (!empty($export_options_plugins)) {
            $export_options = array_merge($export_options_plugins, $export_options);
        }

        //add more options
        $export_options = array_merge($export_options,
            array(
                'export_what' => array (
                    'custom_id' => 'export_what',
                    'name' => 'export_what',
                    'type' => 'dropdown',
                    'required' => 1,
                    'label' => $this->i18n('export_what'),
                    'help' => $this->i18n('export_what'),
                    'options' => array(
                                    array(
                                        'label' => $this->i18n('export_selected'),
                                        'option_value' => 'selected'),
                                    array(
                                        'label' => $this->i18n('export_all'),
                                        'option_value' => 'all'),
                                ),
                    'value' => (isset($export_filters['export_what'])) ? $export_filters['export_what'] : '',
                    'hidden' => (isset($filters_hide['export_what'])) ? $filters_hide['export_what'] : 0,
                ),
                array (
                    'custom_id' => 'session_param',
                    'name' => 'session_param',
                    'type' => 'hidden',
                    'value' => $this->action . '_' . strtolower($this->modelName),
                    'hidden' => 1,
                ),
                array (
                    'custom_id' => 'type_section_id',
                    'name' => 'type_section_id',
                    'type' => 'hidden',
                    'value' => ($selected_section) ? $selected_section : '',
                    'hidden' => 1,
                ),
                array (
                    'custom_id' => 'type_id',
                    'name' => 'type_id',
                    'type' => 'hidden',
                    'value' => ($selected_model_type) ? $selected_model_type : '',
                    'hidden' => 1,
                ),
                array (
                    'custom_id' => 'export_previous_action',
                    'name'      => 'export_previous_action',
                    'type'      => 'hidden',
                    'value'     => $this->action,
                    'hidden'    => 1,
                ),
            )
        );
        if (!empty($filters['model_type'])) {
            $export_options['export_what']['options'][] = [
                'label' => $this->i18n('export_empty_file'),
                'option_value' => 'empty'
            ];
        }

        if (! $this->registry['theme']->isModern()) {
            // This brakes Modern themes (Evolution)
            $export_options[] = array(
                'custom_id' => 'disable_preload',
                'name' => 'disable_preload',
                'type' => 'hidden',
                'value' => 1,
                'hidden' => 1,
            );
        }

        return $export_options;
    }

    /**
     * Exports list of objects (documents, tasks, projects, etc.) in specific format (XLS, CSV, PDF)
     * This method checks for plugin that is passed and starts it if found
     */
    protected function _export() {
        set_time_limit(0);
        ini_set('memory_limit', '2048M');
        if ($this->registry['request']->get('file_name') || $this->registry['request']->isRequested('pattern')) {
            include_once PH_MODULES_DIR . 'exports/models/exports.factory.php';

            if (!$this->registry['request']->isRequested('pattern')) {
                //save the filters values in the session
                $export_filters = $this->registry['session']->get('export_filters');
                //the session param contains the plugin and the model_type
                $export_filters_session_param = sprintf('%s_%s_plugin_%s%s',
                    $this->module,
                    $this->controller,
                    ($this->registry['request']->get('plugin')) ? $this->registry['request']->get('plugin') : 'standard',
                    ($this->registry['request']->get('type_id')) ? '_type_' . $this->registry['request']->get('type_id') : ''
                );
                foreach (Exports::$filtersToStore as $f) {
                    $export_filters[$export_filters_session_param][$f] = $this->registry['request']->get($f);
                }

                $this->registry['session']->set('export_filters', $export_filters, '', true);

                //also save the last export plugin for this module and controller
                $last_export_plugins = $this->registry['session']->get('last_export_plugins');
                //the session param contains the plugin and the model_type
                $last_export_plugin_session_param = sprintf('%s_%s%s',
                    $this->module,
                    $this->controller,
                    ($this->registry['request']->get('type_id')) ? '_type_' . $this->registry['request']->get('type_id') : ''
                );
                $last_export_plugins[$last_export_plugin_session_param] = ($this->registry['request']->get('plugin')) ? $this->registry['request']->get('plugin') : 'standard';
                $this->registry['session']->set('last_export_plugins', $last_export_plugins, '', true);
            } else {
                $this->registry['request']->set('format', 'pdf', 'all', true);
            }

            if ($this->registry['request']->get('plugin') && $this->registry['request']->get('plugin') != 'standard') {
                //check if such plugin exists
                $filters = array('id' => $this->registry['request']->get('plugin'));
                $plugin = Exports::searchOne($this->registry, $filters);
                if ($plugin) {
                    $plugin->launch();
                } else {
                    $this->registry['messages']->setError($this->i18n('error_export_failed'), '', -1);
                    $this->registry['messages']->setError($this->i18n('error_export_invalid_plugin'), '');
                    $this->registry['messages']->insertInSession($this->registry);
                }
            } else {
                $format = $this->registry['request']->get('format');
                switch ($format) {
                    case 'xls':
                    case 'xlsx':
                        $this->exportSpreadsheet($format);
                        break;
                    case 'csv':
                        $this->exportCSV();
                        break;
                    case 'pdf':
                        $this->exportPDF();
                        break;
                }
                if ($this->registry['request']->get('export_previous_action')) {
                    $session_param = $this->registry['request']->get('export_previous_action') . '_' . strtolower($this->modelName);
                    $this->registry['session']->remove($session_param, 'selected_items');
                }
                exit;
            }
        } else {
            $this->registry['messages']->setError($this->i18n('error_export_failed'), '', -1);
            $this->registry['messages']->setError($this->i18n('error_export_filename'), '');
            $this->registry['messages']->insertInSession($this->registry);
        }
        $this->actionCompleted = true;
    }

    /**
     * Export in XLSX/XLS format
     */
    public function exportSpreadsheet(string $format) {
        //set flag to get tags for current model
        $this->registry->set('getTags', true, true);
        $this->registry->set('prepareModels', true, true);
        $this->registry->set('getAssignments', true, true);
        $this->registry->set('getCategoryNames', true, true);

        $model_factory_name = $this->modelFactoryName;
        //get the previous list filters
        $prev_filters = $model_factory_name::saveSearchParams(
            $this->registry,
            array(),
            (preg_match('#^(search|list)_.*#', $this->registry['request']->get('session_param')) ?
                preg_replace('#^(search|list)_.*#', '$1', $this->registry['request']->get('session_param')) :
                'list') . '_'
        );

        $exportRecordIds = $this->registry['request']->get('items');

        // get available filter for the current action or selected items from the request
        if ($exportRecordIds) {
            //try to guess table alias for the filters
            $alias = $model_factory_name::getAlias($this->registry['module'], $this->registry['controller'], $this->registry['action']);

            $filters = array(
                'where' => array($alias . '.id IN (' . implode(', ', $exportRecordIds) . ')'),
                'sort'  => array('FIND_IN_SET(' . $alias . '.id, "' . implode(',', $exportRecordIds) . '")')
            );

            // get archived models, if they are included in search
            if (!empty($prev_filters['archive'])) {
                $filters['archive'] = $prev_filters['archive'];
            }
            /*if (!empty($prev_filters['withouth_observer_answere'])) {
                $filters['withouth_observer_answere'] = true;
            }*/
        } else {
            $filters = $prev_filters;
        }

        // define viewer
        $viewer = new Viewer($this->registry);

        // path to the default export template
        $viewer->template = '_export_list.html';
        if ($this->module != $this->controller) {
            $viewer->template = '_export_list_' . $this->controller . '.html';
        }

        $type_id = $this->registry['request']->get('type_id');
        $type_section = $this->registry['request']->get('type_section_id');

        //assign export template if such exists
        // check if the template is for type
        if ($type_id > 0 || $type_section > 0 || ($type_id && $this->modelName == 'Finance_Payment')) {
            $customize = array(
                'model_name' => $this->modelName,
                'name' => ($type_id > 0 || $this->modelName == 'Finance_Payment') ? 'type' : 'section',
                'value' => ($type_id > 0 || $this->modelName == 'Finance_Payment') ? $type_id : $type_section,
                'export' => true
            );
        } else {
            $customize = array(
                'model_name' => $this->modelName,
                'export' => true
            );
        }
        $viewer->setCustomTemplate($customize);

        // modelFields contains visible columns
        if (!empty($viewer->modelFields)) {
            $filters['get_fields'] = $viewer->modelFields;
        }
        $models = [];
        if ($this->registry['request']->get('export_what') !== 'empty') {
            // get models for the defined filters
            $models = $model_factory_name::search($this->registry, $filters);
        }


        if ($this->modelFactoryName == 'Finance_Invoices_Templates') {
            $models = Finance_Invoices_Templates::preparePreviewInvoicesGT2($this->registry, $models);
        }

        if ($format == 'xlsx') {
            $exportGroupTables = $this->registry['request']->get('group_tables');
            return $this->exportXLSX($viewer, $models, $exportGroupTables);
        } else {
            return $this->exportXLS($viewer, $models);
        }
    }

    /**
     * Prepare grouping tables definitions per all types
     *
     * @param array $models - records to export
     */
    public function getGroupigTablesTypes(&$models) {
        $processed_types = array();
        $group_table_types = array();
        foreach($models as $idx => $model) {
            $type = $model->get('type');
            //get all group vars
            if (!isset($group_table_types[$type]) && !in_array($type, $processed_types)) {
                $permittedLayouts = $model->getPermittedLayouts('view');
                $processed_types[] = $type;
                $assocVars = $model->getVarsForTemplateAssoc(true);
                //for finance/warehouse documents
                if ($model->modelName == 'Finance_Warehouses_Document') {
                    $model->unsanitize();
                    $model->prepareAvailableQuantity();
                    $model->prepareGT2DynamicFields();
                    $model->sanitize();
                    $assocVars['group_table_2'] = $model->get('grouping_table_2');
                }
                $models[$idx] = $model;
                $tables = array_filter(
                    $assocVars,
                    function ($var) use ($permittedLayouts) {
                        return !empty($var['type']) &&
                            ((($var['type'] == 'grouping' && empty($var['hidden_all']) ||
                                $var['type'] == 'gt2' && empty($var['hidden']))
                            && in_array($var['layout_id'], $permittedLayouts)) ||
                            //finance documents do not have layout for gt2
                            $var['type'] == 'gt2' && preg_match('#^Finance.*#', $var['model']));
                    }
                );
                //no group tables
                if (empty($tables)) {
                    continue;
                }

                $group_table_types[$model->get('type')] = array(
                    'name' => $model->get('type_name'),
                    'tables' => $tables,
                );

                foreach ($group_table_types[$model->get('type')]['tables'] as $gidx => $gt) {
                    //remove the hidden columns
                    if ($gt['type'] == 'grouping') {
                        foreach ($gt['hidden'] as $idx => $hidden) {
                            if ($hidden) {
                                unset($gt['names'][$idx]);
                                unset($gt['labels'][$idx]);
                                unset($gt[$gidx][$idx]);
                            }
                        }
                    } elseif ($gt['type'] == 'gt2') {
                        $gt['labels'] = array();
                        foreach ($gt['vars'] as $var_name => $var) {
                            if ($var['hidden']) {
                                unset($gt['vars'][$var_name]);
                            } else {
                                $gt['labels'][] = $var['label'];
                            }
                        }
                    }
                    $group_table_types[$model->get('type')]['tables'][$gidx] = $gt;
                }

            }
        }

        return $group_table_types;
    }

    /**
     * Prepare grouping tables data for all models per group variable
     *
     * @param array $models - records to export
     * @return array $exportData - export data per group/gt2
     */
    public function getGroupigTablesExportData(&$models) {
        $group_table_types = $this->getGroupigTablesTypes($models);

        $typesCount = count(array_keys($group_table_types));

        if (!empty($group_table_types)) {
            //get all variables in template assoc manner and encapsulate them
            array_walk($models, function(&$model, &$key) {
                $model->getVarsForTemplateAssoc(true);
            });
        }

        $exportData = array();
        foreach($group_table_types as $typeID => $details) {
            foreach($details['tables'] as $group_table) {
                $group_id = $group_table['id'];

                $group_table_label = trim($group_table['label']) ? $group_table['label'] : $group_table['name'];
                //the name of the sheet is type name + label/name of the grouping variable
                $title = $group_table_label;
                if ($typesCount > 1) {
                    //add the name of the type/outlook only if more than one type exported
                    $title = $details['name'] . ' - ' . $group_table_label;
                }
                $title = mb_substr($title, 0, 31);

                $exportData[$group_id] = array(
                    'name' => $group_table['name'],
                    'title' => $title,
                    'rows' => array(),
                );


                // num, full_num, code or anything else
                $modelCodeNum = 'code';
                switch($models[0]->modelName) {
                    case 'Document':
                        $modelCodeNum = 'full_num';
                        break;
                    case 'Finance_Incomes_Reason':
                    case 'Finance_Expenses_Reason':
                    case 'Finance_Warehouses_Document':
                        $modelCodeNum = 'num';
                        break;
                }

                $labels = array_merge(
                    [
                        //id of the model
                        //IMPORTANT: set this column (A) as invisible later on
                        'Id',
                        //code/num/full_num
                        ucfirst(str_replace('_', '', $modelCodeNum)),
                        //row number in the table
                        '#'
                    ],
                    $group_table['labels']
                );

                $sheetData = array($labels);
                foreach($models as $idx => $model) {
                    if ($model->get('type') != $typeID) {
                        continue;
                    }

                    if ($group_table['type'] == 'grouping') {
                        //get the group var values for each model (it is cached within model)
                        $assocVars = $model->getVarsForTemplateAssoc();
                        $table_values = $assocVars[$group_table['name']]['values'] ?? [];
                    } elseif ($group_table['type'] == 'gt2') {
                        $gt2 = $model->getGT2Vars();
                        //for finance/warehouse documents
                        if ($model->modelName == 'Finance_Warehouses_Document') {
                            $model->unsanitize();
                            $model->prepareAvailableQuantity();
                            $model->prepareGT2DynamicFields();
                            $model->sanitize();
                            $gt2 = $model->get('grouping_table_2');
                        }
                        $table_values = $gt2['values'];
                    }

                    //if only one row and empty should it be exported?!?!?!
                    if (count($table_values) == 1) {
                        $first_row = reset($table_values);
                        if (!array_filter($first_row)) {
                            $table_values = [];
                        }
                    }

                    //row values
                    $rowNum = 0;
                    foreach($table_values as $ridx => $rowValues) {
                        //system model data id and code/num
                        $rowData = [
                            $model->get('id'),
                            $model->get($modelCodeNum)
                        ];
                        //row number
                        //group tables start row numbering from 1, but this is not applicable for GT2
                        // so use incrementing counter
                        $rowNum++;

                        //add the row number as well
                        $rowData[] = $rowNum;

                        if ($group_table['type'] == 'grouping') {
                            foreach($group_table['names'] as $nidx => $varName) {
                                //elaborate this (dropdown, radio, files, etc)!
                                //IMPORTANT: some variable might not have values (the variable added after the last save of the model)
                                $value = $rowValues[$nidx] ?? '';

                                switch ($group_table['types'][$nidx]) {
                                    case 'radio':
                                    case 'dropdown':
                                        $optionLabels = array();
                                        if (isset($group_table[$varName]['options'])) {
                                            $optionLabels = array_combine(
                                                array_column($group_table[$varName]['options'], 'option_value'),
                                                array_column($group_table[$varName]['options'], 'label')
                                            );
                                        }
                                        //ToDo: opt groups

                                        //get the label of the option
                                        $value = $optionLabels[$value] ?? '';
                                        break;
                                    case 'file_upload':
                                        if (is_object($value) && is_a($value, 'File')) {
                                            //get the filename of the file
                                            $value = $value->get('filename');
                                        }
                                        break;
                                }
                                $rowData[] = $value;
                            }
                        } elseif ($group_table['type'] == 'gt2') {
                            foreach($group_table['vars'] as $varName => $var) {
                                //elaborate this (dropdown, radio, files, etc)!
                                $value = $rowValues[$varName] ?? '';

                                switch ($var['type']) {
                                    case 'radio':
                                    case 'dropdown':
                                        $optionLabels = array();
                                        if (isset($var['options'])) {
                                            $optionLabels = array_combine(
                                                array_column($var['options'], 'option_value'),
                                                array_column($var['options'], 'label')
                                            );
                                        }
                                        //ToDo: opt groups

                                        //get the label of the option
                                        $value = $optionLabels[$value] ?? $value;
                                        break;
                                    case 'file_upload':
                                        if (is_object($value) && is_a($value, 'File')) {
                                            //get the filename of the file
                                            $value = $value->get('filename');
                                        }
                                        break;
                                }
                                $rowData[] = $value;
                            }
                        }
                        //add to sheet data
                        $sheetData[] = $rowData;
                    }

                }
                $exportData[$group_id]['rows'] = $sheetData;
            }
        }

        return $exportData;
    }

    /**
     * Export in XLSX
     *
     * @param Viewer $viewer - viewer to get the columns needed
     * @param array $models - records to export
     * @param bool $exportGroupTables - records to export
     */
    public function exportXLSX(Viewer $viewer, array $models, $exportGroupTables) {
        $title = $viewer->title;
        $author = $this->registry['currentUser']->get('firstname') . ' ' . $this->registry['currentUser']->get('lastname');

        set_time_limit(0);
        ini_set('memory_limit', '8G');
        require_once PH_PHPEXCEL_DIR . 'PHPExcel/IOFactory.php';
        $objReader = PHPExcel_IOFactory::createReader('Excel2007');

        $objPHPExcel = new PHPExcel();

        //the main sheet
        $objPHPExcel->setActiveSheetIndex(0);
        $sheet = $objPHPExcel->getActiveSheet();
        $invalidCharacters = $sheet->getInvalidCharacters();
        $title = str_replace($invalidCharacters, '', $title);
        //the name of the sheet is type name + label/name of the grouping variable
        $title = mb_substr($title, 0, 31);
        $sheet->setTitle($title);
        $modelFactoryName = $this->modelFactoryName;
        //get the previous list filters
        $prev_filters = $modelFactoryName::saveSearchParams(
            $this->registry,
            array(),
            (preg_match('#^(search|list)_.*#', $this->registry['request']->get('session_param')) ?
                preg_replace('#^(search|list)_.*#', '$1', $this->registry['request']->get('session_param')) :
                'list') . '_'
        );

        $all_fields = $viewer->outlook->get('current_custom_fields');

        $fields = array_filter($all_fields, function($field) {
            return $field['position'];
        });

        $labels = array_merge(['id', '#'], array_column($fields, 'label'));
        $rangeNames = array_merge(['id', ''], array_column($fields, 'name'));
        $sheetData = array($labels);
        foreach($models as $idx => $model) {
            //id of the record
            $rowData = [$model->get('id')];

            //row number
            $rowData[] = $idx+1;

            //row values
            foreach($fields as $field) {
                //ToDo: elaborate this! outlooks/templates/td/
                $rowData[] = $model->getExportVarValue($field);
            }
            //add to sheet data
            $sheetData[] = $rowData;
        }

        //set formating and complete the data
        $fieldsDescription = array_combine(array_column($all_fields, 'name'), $all_fields);
        $namesIndexes = array_flip($rangeNames);
        foreach ($sheetData as $row => $rowData) {
            foreach ($rowData as $varIndex => $varValue) {
                $varName = $rangeNames[$varIndex];
                $formatting = $this->getExcelFormatting($fieldsDescription[$varName] ?? []);
                $columnLetter = PHPExcel_Cell::stringFromColumnIndex($namesIndexes[$varName]);
                $currentCell = $columnLetter . strval($row+1);
                $currentCellValue = ($varValue !== false ? $varValue : '');
                if ($formatting === PHPExcel_Style_NumberFormat::FORMAT_TEXT) {
                    $sheet->setCellValueExplicit($currentCell, $currentCellValue , PHPExcel_Cell_DataType::TYPE_STRING);
                } else {
                    $sheet->getStyle($currentCell)
                          ->getNumberFormat()
                          ->setFormatCode($formatting);
                    $sheet->setCellValue($currentCell, $currentCellValue);
                }
            }
        }

        //set named ranges in the first row
        foreach($rangeNames as $idx => $name) {
            if (empty($name)) {
                continue;
            }
            $columnLetter = PHPExcel_Cell::stringFromColumnIndex($idx);
            $objPHPExcel->addNamedRange(
                new PHPExcel_NamedRange(
                    $name,
                    $sheet,
                    "{$columnLetter}1",
                    true
                )
            );
        }

        //the A column contains the Id of the record - hide it
        $sheet->getColumnDimension('A')->setVisible(false);

        if ($exportGroupTables) {
            $tablesExportData = $this->getGroupigTablesExportData($models);

            foreach($tablesExportData as $tableId => $data) {
                //the first row always contains only the labels
                if (count($data['rows']) == 1) {
                    //do not export empty sheets
                    continue;
                }
                $sheet = $objPHPExcel->createSheet();
                $data['title'] = str_replace($invalidCharacters, '', $data['title']);
                $sheet->setTitle($data['title']);

                //the A column contains the Id of the record - hide it
                $sheet->getColumnDimension('A')->setVisible(false);

                $sheet->fromArray($data['rows'], NULL, 'A1');
                //ToDo: set formats
            }
        }

        //autosize all the columns and bold the first row
        foreach ($objPHPExcel->getWorksheetIterator() as $worksheet) {
            $objPHPExcel->setActiveSheetIndex($objPHPExcel->getIndex($worksheet));

            $sheet = $objPHPExcel->getActiveSheet();
            $cellIterator = $sheet->getRowIterator()->current()->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(true);
            /** @var PHPExcel_Cell $cell */
            foreach ($cellIterator as $cell) {
                $sheet->getColumnDimension()->setAutoSize(true);
                $sheet->getStyle($cell->getCoordinate())->getFont()->setBold(true);
            }
        }

        //set the first sheet as active
        $objPHPExcel->setActiveSheetIndex(0);

        //document properties
        $objPHPExcel->getProperties()->setCreator($author)
            ->setLastModifiedBy($author)
            ->setTitle($title)
            ->setSubject($title)
            ->setDescription($title);

        if (!empty($prev_filters['hidden_type'])) {
            $objPHPExcel->getProperties()->setCustomProperty('model_type',$prev_filters['hidden_type'], PHPExcel_DocumentProperties::PROPERTY_TYPE_STRING);
        }

        $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');

        $fileName = $this->registry['request']->get('file_name') . '.xlsx';

        try {
            //IMPORTANT: do not perform calculations
            header('Content-Disposition: attachment; filename="' . $fileName . '"');
            header("Content-type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            header('Content-Transfer-Encoding: binary');
            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
            header('Pragma: public');

            $objWriter->save('php://output');
            $objPHPExcel->disconnectWorksheets();
            unset($objPHPExcel);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Export in XLS
     */
    public function exportXLS(Viewer $viewer, array $models) {
        //assign the models with the proper model name
        $viewer->data[strtolower($this->modelFactoryName)] = $models;

        // sets a frameset
        $viewer->setFrameset('frameset_export.html');

        //fetch content
        $content = $viewer->fetch($viewer->template);

        //constrcut the filename
        $filename = $this->registry['request']->get('file_name') . '.' . $this->registry['request']->get('format');

        //send it to the user
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header("Content-type: application/x-msexcel; charset=utf-8");
        header('Content-Transfer-Encoding: binary');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');

        print $content;
    }

    /**
     * Export in CSV format
     */
    public function exportCSV() {
        //prepare the delimiter
        switch ($this->registry['request']->get('separator')) {
            case 'semicolon':
                $separator = ';';
                break;
            case 'comma':
                $separator = ',';
                break;
            case 'tab':
                $separator = "\t";
                break;
        }
        //windows format of new line and windows cyrillic enconding
        $new_line = "\r\n";
        //$encoding = 'windows-1251';

        //set flag to get tags for current model
        $this->registry->set('getTags', true, true);
        $this->registry->set('prepareModels', true, true);
        $this->registry->set('getAssignments', true, true);
        $this->registry->set('getCategoryNames', true, true);

        $model_factory_name = $this->modelFactoryName;
        //get the previous list filters
        $prev_filters = $model_factory_name::saveSearchParams(
            $this->registry,
            array(),
            (preg_match('#^(search|list)_.*#', $this->registry['request']->get('session_param')) ?
                preg_replace('#^(search|list)_.*#', '$1', $this->registry['request']->get('session_param')) :
                'list') . '_'
        );

        // get available filter for the current action or selected items from the request
        if ($this->registry['request']->get('items')) {
            //try to guess table alias for the filters
            $alias = $model_factory_name::getAlias($this->registry['module'], $this->registry['controller'], $this->registry['action']);

            /*
             * Prevent from exporting of records for which the user has no permissions
             */
            // Get the ids of all records using the previous list filters
            $all_items = $model_factory_name::getIds($this->registry, $prev_filters);
            // Get the ids of all selected items which exist in the $all_items array
            $items = array_intersect($this->registry['request']->get('items'), $all_items);

            $filters = array(
                'where' => array($alias . '.id IN (' . implode(', ', $items) . ')'),
                'sort'  => array('FIND_IN_SET(' . $alias . '.id, "' . implode(',', $all_items) . '")')
            );
            //we don't need to check again for the permissions for types
            //as the IDs are already filtered through these filters
            $filters['skip_permissions_check'] = true;
            // get archived models, if they are included in search
            if (!empty($prev_filters['archive'])) {
                $filters['archive'] = $prev_filters['archive'];
            }
            if (!empty($prev_filters['withouth_observer_answere'])) {
                $filters['withouth_observer_answere'] = true;
            }
        } else {
            $filters = $prev_filters;
        }

        // get the outlook
        require_once PH_MODULES_DIR . 'outlooks/models/outlooks.factory.php';

        $module = $this->registry['module'];
        $controller = $this->registry['controller'];

        $type_id = $this->modelFactoryName == 'Finance_Payments' && $this->registry['request']->get('type_id') ?
                   $this->registry['request']->get('type_id') :
                   intval($this->registry['request']->get('type_id'));
        $type_section = intval($this->registry['request']->get('type_section_id'));

        $outlook_filters = array(
            'where' => array(
                'o.module="' . $module . '"',
                'o.controller="' . $controller . '"',
                'o.model_id="' . ($type_section ? $type_section : $type_id) . '"',
                'o.section="' . ($type_section ? 1 : 0) . '"'
            )
        );

        $outlook = '';
        $filters_user = $outlook_filters;
        $filters_user['where'][] = "oa.assigned_to='Users-" . $this->registry['currentUser']->get('id') . "'";
        $outlook = Outlooks::searchOne($this->registry, $filters_user);

        if (! $outlook) {
            $filters_role = $outlook_filters;
            $filters_role['where'][] = "oa.assigned_to='Roles-" . $this->registry['currentUser']->get('role') . "'";
            $outlook = Outlooks::searchOne($this->registry, $filters_role);
        }

        if (! $outlook) {
            $filters_all = $outlook_filters;
            $filters_all['where'][] = 'oa.assignments_type="All"';
            $outlook = Outlooks::searchOne($this->registry, $filters_all);
        }

        if (empty($outlook)) {
            //create an empty outlook model
            $outlook = new Outlook($this->registry);
            $outlook->set('module', $module, true);
            $outlook->set('controller', $controller, true);
            $outlook->set('module_controller', $module . (($module != $controller) ? '_' . $controller : ''), true);
            $m_c_words = explode('_', General::plural2singular($outlook->get('module_controller')));
            foreach ($m_c_words as $k => $v) {
                $m_c_words[$k] = ucfirst($v);
            }
            $outlook->set('model', implode('_', $m_c_words), true);
            $outlook->set('section', $type_section, true);
            $outlook->set('model_id', $type_id, true);

            // takes the standard list of fields
            $fields_list = $outlook->getModelFields();

            //takes the additional vars list
            $additional_vars = $outlook->getModelAdditionalFields();

            $full_fields_list = array_values(array_merge($fields_list, $additional_vars));

            $outlook->set('current_custom_fields', $full_fields_list, true);
        }

        $template_cols = $outlook->get('current_custom_fields');

        // get visible columns from outlook
        if (!empty($template_cols)) {
            $filters['get_fields'] = array_map(
                function($a) {
                    return $a['name'];
                },
                array_filter(
                    $template_cols,
                    function($a) {
                        return !empty($a['position']) && $a['position'] > 0;
                    }
                )
            );
        }

        // get models for the defined filters
        $models = $model_factory_name::search($this->registry, $filters);

        // construct the header row
        $header_row = array();
        foreach ($template_cols as $col) {
            if ($col['position'] > 0) {
                $header_row[] = $col['label'];
            } else {
                //the columns are sorted, do not loop - not necessary
                break;
            }
        }
        //$data[] = implode($separator, $header_row);
        $data[] = $header_row;

        if ($this->modelFactoryName == 'Finance_Invoices_Templates') {
            //prepare gt2 tables for invoices
            foreach ($models as $key => $template) {
                $template_id = $template->get('id');
                $db = $this->registry['db'];
                $query = 'SELECT fiti.id AS idx, fit.id, fiti.issue_date' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' AS fiti' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' AS fit' . "\n" .
                         '  ON fit.id = fiti.parent_id' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
                         '  ON co.id = fit.contract_id' . "\n" .
                         'WHERE fiti.invoice_id = 0' . "\n" .
                         '  AND co.subtype="contract" AND co.status="closed"' . "\n" .
                         '  AND fit.deleted_by = 0' . "\n" .
                         '  AND fiti.id=' . $template_id;
                $records = $db->GetAssoc($query);

                if (!empty($records)) {
                    require_once PH_MODULES_DIR . 'finance/models/finance.invoices_templates.factory.php';
                    $results = Finance_Invoices_Templates::issueInvoices($this->registry, $records, true);

                    $info = reset($results);

                    //check if any errors occurred
                    if (!empty($info['errors'])) {
                        continue;
                    }

                    //there should be only one invoice in the results array, but just to be sure loop them all
                    foreach ($info['invoices'] as $invoice) {
                        if (is_object($invoice)) {
                            $gt2 = $invoice->get('grouping_table_2');
                            $models[$key]->set('grouping_table_2', $gt2, true);
                            $models[$key]->set('total', $gt2['plain_values']['total'], true);
                            $models[$key]->set('total_with_vat', $gt2['plain_values']['total_with_vat'], true);
                            $models[$key]->set('currency', $gt2['plain_values']['currency'], true);
                        }
                    }
                    //check if fiscal event date has been assigned already
                    $records = reset($records);
                    if (!empty($records['fiscal_event_date']) && $records['fiscal_event_date'] > '0000-00-00') {
                        $models[$key]->set('fiscal_event_date', $records['fiscal_event_date'], true);
                    }
                }
            }
        }

        foreach ($models as $model) {
            $row = array();
            foreach ($template_cols as $col) {
                if ($col['position'] > 0) {
                    $value = '';
                    if ($col['origin'] == 'basic') {
                        //there are some exceptions for some of the basic variables
                        switch ($col['name']) {
                        case 'name_code':
                            $value = '[' . $model->get('code') . '] ' . $model->get('name');
                            break;
                        case 'kind':
                            //for customers
                            $value = $this->i18n('customers_' . ($model->get('is_company') ? 'company' : 'person'));
                            break;
                        case 'tags':
                            $tags = array();
                            if ($model->get('model_tags')) {
                                foreach ($model->get('model_tags') as $tag) {
                                    $tags[] = $tag->get('name');
                                }
                            }
                            $value = implode(', ', $tags);
                            break;
                        case 'status':
                            $value = $model->get('substatus_name') ?:
                                $this->i18n(
                                    (preg_match('#^Finance#', $model->modelName) && $model->get('type') > 0 ?
                                        'finance_documents' :
                                        General::singular2plural(strtolower($model->modelName))) .
                                    '_status_' . $model->get('status'));
                            break;
                        case 'assignments':
                        case 'owner':
                        case 'responsible':
                        case 'observer':
                        case 'decision':
                            $assignments = array();
                            if ($model->get($col['depends_on'])) {
                                foreach ($model->get($col['depends_on']) as $assignment) {
                                    $assignments[] = $assignment['assigned_to_name'];
                                }
                            }
                            $value = implode(', ', $assignments);
                            break;
                        case 'category':
                        case 'categories':
                            //for nomenclatures
                            $value = ($model->get('categories_names')) ? implode(', ', $model->get('categories_names')) : '';
                            break;
                        case 'timesheet_time':
                            if (($model->modelName == 'Task' || $model->get('generate_system_task')) && $model->isDefined($col['depends_on']) && is_numeric($model->get($col['depends_on']))) {
                                $value = intval($model->get($col['depends_on']));
                                $value = sprintf('%d:%02d', floor($value / 60), floor($value % 60));
                            }
                            break;
                        default:
                            if (in_array($col['name'], array('added', 'modified', 'deleted')) || preg_match('#date#', $col['name'])) {
                                $value = General::strftime('%d.%m.%Y', $model->get($col['depends_on']));
                            } elseif (preg_match('#\[\]$#', $col['depends_on'])) {
                                $value = implode(', ', array_filter($model->get(preg_replace('#\[\]$#', '', $col['depends_on'])) ?: array()));
                            } else {
                                $value = $model->get($col['depends_on']);
                            }
                        }
                    } else {
                        if (!empty($col['field_type']) && $col['field_type'] == 'checkbox_group') {
                            $value = implode(', ', $model->getSelectedAddVarCheckboxLabels($col['name']));
                        } else {
                            $value = $model->getVarValue($col['name']);
                        }
                    }
                    //convert array values into CSV strings
                    if (is_array($value)) {
                        $value = implode(', ', $value);
                    }

                    //remove the new lines from the value
                    $row[] = preg_replace('#(\r\n|\n)#', ' ', $value);
                } else {
                    //the columns are sorted, do not loop - not necessary
                    break;
                }
            }
            //$data[] = implode($separator, $row);
            $data[] = $row;
        }

        //construct the filename
        $filename = $this->registry['request']->get('file_name') . '.' . $this->registry['request']->get('format');

        // SEND HEADERS
        header("Content-type: text/csv");
        header("Pragma: no-cache");
        header("Expires: 0");
        header('Content-Disposition: attachment; filename="' . $filename. '"');

        //UTF-8 BOM
        echo "\xEF\xBB\xBF";
        //print iconv("UTF-8", $encoding, $content);
        $output = fopen("php://output", "wb");
        foreach ($data as $row) {
            fputcsv(
                $output,     //stream
                $row,        //row
                $separator/*,//separator
                "\"",        //enclosure
                "\\",        //escape
                $new_line*/  //eol php 8.1.0+
            );
        }
        fclose($output);
    }

    /**
     * Export in PDF format
     */
    public function exportPDF() {
        $request = &$this->registry['request'];
        $lang = $this->registry['lang'];

        $action_redirect = preg_match('#^(search|list)_.*#', $request->get('session_param')) ?
            preg_replace('#^(search|list)_.*#', '$1', $request->get('session_param')) :
            'list';

        require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
        require_once PH_MODULES_DIR . 'patterns/models/patterns.parts.factory.php';

        $pattern_error = false;

        //check the pattern
        if (!$pattern_id = $request->get('pattern')) {
            $pattern_error = true;
        }

        //get the pattern
        $filters = array('where' => array('p.id = ' . $pattern_id),
                         'sanitize' => true,
                         'model_lang' => $lang);
        $pattern = Patterns::searchOne($this->registry, $filters);

        if (!$pattern || !$pattern->get('list') || !$pattern->get('content') || $pattern->get('model') != $this->modelName ||
        ($this->registry['currentUser']->get('is_portal') && !$pattern->get('is_portal'))) {
            $pattern_error = true;
        }

        if ($pattern_error) {
            $this->registry['messages']->setError($this->i18n('error_printlist_failed'), '', -1);
            $this->registry['messages']->setError($this->i18n('error_printlist_invalid_pattern'));
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect($this->module, $action_redirect, array(), $this->controller);
        }

        //check for plugins to modify the placeholders
        /*if ($pattern->get('plugin')) {
            //get the plugins for the selected model and model_type
            Patterns::launchPlugin($this->registry, $this, $pattern, array('pattern_id' => $pattern_id, 'browser_mode' => $browser_mode));
        }*/

        $this->extender = new Extender();
        $this->extender->model_lang = $lang;
        $this->extender->module = $this->registry['module'];

        require_once PH_MODULES_DIR . 'placeholders/models/placeholders.factory.php';
        $filters = array('model_lang' => $lang,
                         'where' => array('p.usage = \'patterns\'',
                                          '(p.model = \'System\' OR p.model = \'CurrentUser\' OR p.model = \'Records_List\')'));
        $basic_placeholders = Placeholders::search($this->registry, $filters);

        //prepare current user variables
        require_once PH_MODULES_DIR . 'users/models/users.factory.php';
        $filters = array('model_lang' => $lang,
                         'where' => array(
                            'u.id = ' . $this->registry['currentUser']->get('id'),
                            'u.hidden IS NOT NULL'));
        $user = Users::searchOne($this->registry, $filters);

        $user_translations = $user->getTranslations();
        $t_user = array();
        foreach ($user_translations as $t_lang) {
            if ($t_lang != $user->get('model_lang')) {
                $filters = array('model_lang' => $t_lang,
                                 'where' => array(
                                    'u.id = ' . $user->get('id'),
                                    'u.hidden IS NOT NULL'
                                 ),
                                 'sanitize' => true);
                $t_user[$t_lang] = Users::searchOne($this->registry, $filters);
            }
        }

        //set flag to get tags for current model
        $this->registry->set('getTags', true, true);
        $this->registry->set('prepareModels', true, true);
        $this->registry->set('getAssignments', true, true);
        $this->registry->set('getCategoryNames', true, true);

        $model_factory_name = $this->modelFactoryName;
        //get the previous list filters
        $prev_filters = $model_factory_name::saveSearchParams($this->registry, array(), $action_redirect . '_');

        // get available filter for the current action or selected items from the request
        if ($request->get('items')) {
            //try to guess table alias for the filters
            $alias = $model_factory_name::getAlias($this->registry['module'], $this->registry['controller'], $this->registry['action']);

            /*
             * Prevent from exporting of records for which the user has no permissions
             */
            // Get the ids of all records using the previous list filters
            $all_items = $model_factory_name::getIds($this->registry, $prev_filters);
            // Get the ids of all selected items which exist in the $all_items array
            $items = array_intersect($request->get('items'), $all_items);

            $filters = array(
                'where' => array($alias . '.id IN (' . implode(', ', $items) . ')'),
                'sort'  => array('FIND_IN_SET(' . $alias . '.id, "' . implode(',', $all_items) . '")')
            );
            //we don't need to check again for the permissions for types
            //as the IDs are already filtered through these filters
            $filters['skip_permissions_check'] = true;
            // get archived models, if they are included in search
            if (!empty($prev_filters['archive'])) {
                $filters['archive'] = $prev_filters['archive'];
            }
            if (!empty($prev_filters['withouth_observer_answere'])) {
                $filters['withouth_observer_answere'] = true;
            }
        } else {
            //$filters = $prev_filters;
            $this->registry['messages']->setError($this->i18n('no_selected_records'), '');
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect($this->module, $action_redirect, array(), $this->controller);
        }

        // define viewer
        $viewer = new Viewer($this->registry);

        // path to the default export template
        $viewer->template = '_export_list.html';
        if ($this->module != $this->controller) {
            $viewer->template = '_export_list_' . $this->controller . '.html';
        }

        $type_id = $pattern->get('model_type') && !$pattern->get('section') ? $pattern->get('model_type') : 0;
        $type_section = $pattern->get('model_type') && $pattern->get('section') ? $pattern->get('model_type') : 0;

        //assign export template if such exists
        // check if the template is for type
        if ($type_id > 0 || $type_section > 0 || ($type_id && $this->modelName == 'Finance_Payment')) {
            $customize = array('model_name' => $this->modelName,
                               'name' => ($type_id > 0 || $this->modelName == 'Finance_Payment') ? 'type' : 'section',
                               'value' => ($type_id > 0 || $this->modelName == 'Finance_Payment') ? $type_id : $type_section,
                               'export' => true);
        } else {
            $customize = array('model_name' => $this->modelName,
                               'export' => true);
        }
        $viewer->setCustomTemplate($customize);

        // modelFields contains visible columns
        if (!empty($viewer->modelFields)) {
            $filters['get_fields'] = $viewer->modelFields;
        }

        // get models for the defined filters
        $models = $model_factory_name::search($this->registry, $filters);

        //assign the models with the proper model name
        $viewer->data[strtolower($this->modelFactoryName)] = $models;

        // sets a frameset
        $viewer->setFrameset('frameset_blank.html');

        //fetch content
        $content = $viewer->fetch($viewer->template);

        foreach ($basic_placeholders as $placeholder) {
            if ($placeholder->get('type') == 'basic') {
                if ($placeholder->get('model') == 'CurrentUser') {
                    //user variables
                    if (!$placeholder->get('multilang')) {
                        $vars[$placeholder->get('varname')] = $user->get($placeholder->get('source'));
                    } else {
                        foreach ($user_translations as $t_lang) {
                            if ($t_lang != $user->get('model_lang')) {
                                $vars[ $t_user[$t_lang]->get('model_lang') . '_' . $placeholder->get('varname')]
                                = $t_user[$t_lang]->get($placeholder->get('source'));
                            } else {
                                $vars[ $user->get('model_lang') . '_' . $placeholder->get('varname')] =
                                $user->get($placeholder->get('source'));
                            }
                        }
                    }
                } elseif ($placeholder->get('model') == 'Records_List') {
                    //this is the list content fetched from the viewer
                    if ($placeholder->get('varname') == 'content') {
                        $vars[$placeholder->get('varname')] = $content;
                    }
                }
            } elseif ($placeholder->get('type') == 'system') {
                //system variables
                if (strpos($placeholder->get('source'), '::')) {
                    [$method, $value] = preg_split('/\s*\::\s*/', $placeholder->get('source'));
                    if (!empty($value)) {
                        $var = $this->i18n($value);
                        if (empty($var)) {
                            $var = $value;
                        }
                        $res = General::$method($this->registry, $var);
                    } else {
                        $res = General::$method($this->registry);
                    }
                    $vars[$placeholder->get('varname')] = $res;
                } else {
                    $vars[$placeholder->get('varname')] = $placeholder->get('source');
                }
            }
        }

        foreach ($vars as $key => $value) {
            $this->extender->add($key, $value);
        }

        //get the header
        $header_content = '';
        $header_height = '';
        if ($pattern->get('header')) {
            $filters = array('where' => array('pp.id = ' . $pattern->get('header')),
                             'sanitize' => true,
                             'model_lang' => $lang);
            $header = Patterns_Parts::searchOne($this->registry, $filters);
            $header_content = $header->get('content');
            $header_height = $header->get('height');
            $header_content = $this->extender->expand($header_content);

            //check for additional variables
            if (preg_match('#\[([a-zA-Z0-9-_])+\]#', $header_content)) {
                $header_content = $this->extender->expand($header_content);
            }
            $header_content = str_replace('&nbsp;', '&#160;', $header_content);
            $header_preview = $header_content;
        }

        //get the footer
        $footer_content = '';
        $footer_height = '';
        if ($pattern->get('footer')) {
            $filters = array('where' => array('pp.id = ' . $pattern->get('footer')),
                             'sanitize' => true,
                             'model_lang' => $lang);
            $footer = Patterns_Parts::searchOne($this->registry, $filters);
            $footer_height = $footer->get('height');
            $footer_content = $footer->get('content');
            $footer_content = $this->extender->expand($footer_content);

            //check for additional variables
            if (preg_match('#\[([a-zA-Z0-9-_])+\]#', $footer_content)) {
                $footer_content = $this->extender->expand($footer_content);
            }
            $footer_content = str_replace('&nbsp;', '&#160;', $footer_content);
            $footer_preview = $footer_content;
        }

        $content = $pattern->get('content');
        $content = $this->extender->expand($content);
        //check for additional variables
        if (preg_match('#\[([a-zA-Z0-9\_\|\%\.\:\-])+\]#', $content)) {
            $content = $this->extender->expand($content);
        }

        //define the filename
        $pattern->extender = $this->extender;
        $base_filename = $pattern->composeOutputFileName($pattern->get('prefix'));
        $filename = sprintf('%s.%s', $base_filename, $pattern->get('format'));

        //no value needed for path to the pdf file because it is sent to the browser
        $path_to_pdf = '';

        //prepare the CSS (get the css content from a css template)
        //strip it from the content and put it in the head section
        [$content, $css_styles] = General::stripCSS($content);
        $css = '<style type="text/css">' . "\n" .
               General::fileGetContents($this->registry['theme']->stylesDir . '_print.csst') . "\n" .
               (is_array($css_styles) ? implode("\n", $css_styles) : '') .
               '</style>' . "\n";

        //Convert content to pdf
        $params = array (
            'encoding'      => 'utf-8',
            'header_html'   => $css . $header_content,
            'header_height' => $header_height,
            'footer_html'   => $css . $footer_content,
            'footer_height' => $footer_height,
            'landscape'     => $pattern->get('landscape')
        );

        if ($pattern->get('background_image')) {
            if (!is_dir(PH_PATTERNS_CACHE_DIR)) {
                FilesLib::createDir(PH_PATTERNS_CACHE_DIR, 0777);
            }

            //defines the temporal file name
            $data_file_temp_name = md5(time()) . '.pdf';
            $data_file_path_url = PH_PATTERNS_CACHE_URL . $data_file_temp_name;
            $data_file_path_dir = PH_PATTERNS_CACHE_DIR . $data_file_temp_name;

            // creates temporal with the main data
            $temp_main_file_created = html2pdf::process($data_file_path_url, $data_file_path_dir, $css . $content, $params);

            if ($temp_main_file_created) {
                // create pdf with background image
                $result = $pattern->createPDFwithBackgroundImage($pattern, $data_file_path_dir, true, '', $base_filename);

                if ($result) {
                    // deletes the temporal pdf file
                    if (file_exists($data_file_path_dir)) {
                        unlink($data_file_path_dir);
                    }
                } else {
                    //the browser is the output of the generated file, not server file!
                    $params['output'] = 0;

                    $result = html2pdf::process($base_filename, $path_to_pdf, $css . $content, $params);
                }
            } else {
                $result = false;
            }
        } else {
            //the browser is the output of the generated file, not server file!
            $params['output'] = 0;

            $result = html2pdf::process($base_filename, $path_to_pdf, $css . $content, $params);
        }

        return $result;
    }

    /**
     * Searches for model and starts or stops the watch
     */
    public function _manageStopWatch() {
        $request = $this->registry['request'];

        $result = array('result' => false);

        if ($request->get('model_id') && $request->get('model')) {

            $model_name = $request->get('model');
            $factory = ucfirst(General::singular2plural($model_name));

            //get table alias
            $alias = $factory::getAlias($this->module, $this->controller);

            //prepare some filters
            $filters = array('where' => array($alias . '.id = \'' . $request->get('model_id') . '\''));

            //get the model
            $model = $factory::searchOne($this->registry, $filters);

            if ($model) {
                $res = $model->manageStopWatch($request->get('action'));
                if ($res) {
                    $result = array('result' => true, 'start_date' => $res['date'], 'event_id' => $res['event_id']);
                    if ($request->get('action') == 'start') {
                        $result['id'] = $model->get('id');
                        $result['name'] = $model->get('name');
                        $result['full_num'] = $model_name != 'project' ? $model->get('full_num') : ($model->get('num') ?: $model->get('code'));
                        $result['model'] = $model_name;

                        $viewer = new Viewer($this->registry);
                        $viewer->data = array_merge($viewer->data, $result);
                        $this->registry['currentUser']->getStartedTimers();
                        $viewer->data['currentUser'] = $this->registry['currentUser'];
                        $viewer->setFrameset('stopwatchbar.html');
                        $result['globalIndicatorHtml'] = $viewer->fetch();
                    }
                } else {
                    if ($model->get('event_error')) {
                        $result['event_error'] = true;
                    }
                }
            }
        }

        exit('data=' . json_encode($result));
    }

    /**
     * Sort function for assignments
     *
     * @param array $a - element of the assignments array
     * @param array $b - element of the assignments array
     * @return bool - result of the comparison
     */
    public static function sortAssignments($a, $b) {
        if ($a['value'] && $b['value']) {
            return strcmp($a['label'], $b['label']);
        } elseif ($a['value'] && !$b['value']) {
            return -1;
        } elseif (!$a['value'] && $b['value']) {
            return 1;
        } else {
            return strcmp($a['label'], $b['label']);
        }
    }

    /**
     * Save GT2 configurations
     */
    public function _manageGT2config() {
        //find factory file name
        $factory = PH_MODULES_DIR . $this->module . '/models/' . $this->module . '.';
        if ($this->controller != $this->module) {
            $factory .= $this->controller . '.';
        }
        require_once $factory . 'factory.php';
        //get factory class name
        $factory = preg_replace('#_controller#i', '', get_class($this));
        //get model name
        $model = General::plural2singular($factory);

        //get model
        if (!$this->registry['request']->get('id')) {
            $model = new $model($this->registry);
            $model->set('type', $this->registry['request']->get('type'));
        } else {
            //get table alias
            $alias = $factory::getAlias($this->module, $this->controller);

            $filters = array('where' => array($alias . '.id = ' . $this->registry['request']->get('id')));
            $model = $factory::searchOne($this->registry, $filters);
        }
        //get gt2 var for the model
        $model->getGT2Vars();
        $action = $this->registry['request']->get('config_action');

        if ($action == 'saveGT2config' || $action == 'deleteGT2config') {
            //save config
            $model->manageGT2Config($action);
            //get saved configuration
            $configs = $model->manageGT2config('getGT2configs');

            echo json_encode($configs);
            exit;
        } elseif ($action == 'loadGT2config') {
            $values = $model->manageGT2config($action);
            if (!$values) {
                exit;
            }
            $gt2 = $model->get('grouping_table_2');
            $gt2['values'] = $values['values'];
            $gt2['plain_values'] = $values['plain_values'];
            $gt2['no_container'] = true;
            $gt2['configs'] = $model->manageGT2config('getGT2configs');

            $viewer = new Viewer($this->registry, false);
            $viewer->data['table'] = $gt2;
            $viewer->data['hide_label'] = true;
            $viewer->setFrameset($this->registry['theme']->templatesDir . '_gt2_edit.html');
            $viewer->display();
            exit;
        }
    }

    /**
     * Action to save global variables for all the modules (used only from contracts for now)
     */
    public function _saveGlobalVals() {
        $output = array();
        $status = null;
        if (preg_match('#WIN#i', PHP_OS)) {
            //get active process in WINDOWS with thier command line parameters
            exec('wmic PROCESS get Commandline', $output);
        } else {
            //linux processes
            exec('ps -e -F|grep -i wget', $output);
        }
        $proc_working = false;
        //do it for this installation only (in case there are more than one installations on the server)
        //IMPORTANT: sometimes the installations are accessed from address different from that in settings > crontab > base_host
        $location = sprintf('%s://%s%s?%s',
                        (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                        $_SERVER["HTTP_HOST"], $_SERVER['PHP_SELF'],
                        Router::MODULE_PARAM);
        $regex = '#' . preg_quote($location) . '#';
        $location2 = sprintf('%s/index.php?%s',
                        $this->registry['config']->getParam('crontab', 'base_host'),
                        Router::MODULE_PARAM);
        $regex2 = '#' . preg_quote($location2) . '#';
        foreach ($output as $row) {
            //check if the process is running
            if (preg_match('#saveglobalvals=\d+#', $row) && (preg_match($regex, $row) || preg_match($regex2, $row)) &&
                preg_match('#original_user=\d+#', $row) && preg_match('#background_mode=\d+#', $row)) {
                $proc_working = true;
            }
        }

        // path to save temporary result into
        $result_file_path = sys_get_temp_dir() . '/' . $this->registry['config']->getParam('sys', 'code') . '_save_globals_result.tmp';

        if ($this->registry['request']->get('background_mode')) {
            //small security check
            //if someone tries to run variables save from remote machine
            if ($_SERVER['SERVER_ADDR'] == $_SERVER['REMOTE_ADDR'] && $this->registry['request']->get('original_user')) {
                //get the user to be used for the variables calculation
                $filters = array(
                    'where' => array(
                        'u.id = ' . $this->registry['request']->get('original_user'),
                        'u.hidden IS NOT NULL'
                    )
                );
                require_once PH_MODULES_DIR . 'users/models/users.factory.php';
                $user = Users::searchOne($this->registry, $filters);
                $user->getGroups();
                $user->sanitize();
                $this->registry->set('currentUser', $user, true);
                //save variables
                $result = Model_Factory::saveGlobalVars($this->registry);
                //write result in a file
                if ($file = fopen($result_file_path, 'w')) {
                    fwrite($file, implode(',', $result));
                    fclose($file);
                }
                exit;
            } else {
                //access denied for the remote machine
                //show an error
                $this->registry['session']->set('calc_global_vars_progress', false, '', true);
                $this->registry['messages']->setError($this->i18n('error_no_access_to_module_action'));
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect('index', '', array(), 'index');
                exit;
            }
        } elseif ($this->registry['request']->get('checkSaveGlobals')) {
            //check if variables saving process is already finished
            if ($this->registry['session']->get('calc_global_vars_progress') == 'working' && !$proc_working) {
                //clear session variable for the active process
                $this->registry['session']->set('calc_global_vars_progress', false, '', true);
                //check for the file with results
                if (is_readable($result_file_path) && $file = fopen($result_file_path, 'r')) {
                    $result = fread($file, filesize($result_file_path));
                    $result = preg_split('#\s*,\s*#', $result);
                    fclose($file);
                    unlink($result_file_path);
                    //display message
                    exit($this->i18n('message_global_vars_calculated', $result));
                }
            }
            exit;
        } elseif ($proc_working) {
            //process is still running
            //alert the user and redirect
            $this->registry['messages']->setError($this->i18n('error_global_vars_save_in_progress'));
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect($this->module, '', array(), $this->controller);
            exit;
        }

        $model = new Model($this->registry);
        $force = $this->registry['request']->get('force');

        //if variables are successfully saved start the calculation
        if ($model->saveFormulaVars($force)) {
            //start background_mode
            $location = sprintf('%s/index.php?%s&%s=%s&%s=%s',
                                $this->registry['config']->getParam('crontab', 'base_host'),
                                $_SERVER['QUERY_STRING'], 'background_mode', 1,
                                'original_user', $this->registry['currentUser']->get('id'));

            // START HERE THE BACKGROUND WORK
            if (preg_match('#WIN#i', PHP_OS)) {
                $location = preg_replace('#\&#', '^&', $location);
                $location .= ' > nul &';
                pclose(popen(PH_EXT_DIR . 'wget\wget -q -t10 -b ' . $location, "r"));
            } else {
                $location = preg_replace('#\&#', '\&', $location);
                $location .= ' > /dev/null 2>&1 & ';
                exec('wget -t10 -b -q ' . $location, $output, $status);
            }
            //indicate that the process is running
            $this->registry['session']->set('calc_global_vars_progress', 'working', '', true);
        } else {
            //show error
            $this->registry['messages']->setError($this->i18n('error_global_vars_not_saved'));
            $this->registry['session']->set('calc_global_vars_progress', false, '', true);
        }
    }

    /**
     * Changes distribution data panel when changing selected finance analysis item.
     *
     * @return bool - result of operation
     */
    protected function _changeItem() {
        $registry = &$this->registry;
        $request = &$this->registry['request'];

        $db = $registry['db'];
        $lang = $registry['lang'];

        $item_id = $request->get('item_id', 'get');
        $item_amount = $request->get('item_amount', 'get');
        $input_id = $request->get('input_id', 'get');

        $input_indices = preg_replace('#[^\d]+_([\d_]+)$#', '$1', $input_id);
        $input_indices = explode('_', $input_indices);
        $idx = $input_indices[0];
        $idx2 = $input_indices[1];

        //titles for elements of item
        $elements_titles = array();
        //amounts, ids and names of elements
        $element_amounts = array();
        $element_percentages = array();
        $element_ids = array();
        $element_names = array();
        //whether item has elements with zero amounts or not
        $zero_share_elements = array();
        //amounts of centers
        $center_amounts = array();
        //default distribution of items among elements and of elements among centers
        $elements_dd = array();
        $centers_dd = array();

        //kind of distribution - income or expense
        $distribution_kind = '';

        switch ($this->modelName) {
            case 'Finance_Expenses_Reason':
                $distribution_kind = 'expense';
                break;
            case 'Finance_Incomes_Reason':
            default:
                $distribution_kind = 'income';
                break;
        }

        //get main centers and centers
        require_once PH_MODULES_DIR . 'finance/models/finance.analysis_centers.factory.php';
        require_once PH_MODULES_DIR . 'finance/models/finance.analysis_types.factory.php';

        $filters = array('model_lang' => $lang,
                         'sanitize' => true,
                         'where' => array('(fat.kind = "' . $distribution_kind . '" OR fat.kind = "both")',
                                          'fat.deleted_by=0',
                                          'fat.active=1'),
                         'sort' => array('fati18n.name ASC'));
        $main_centers = Finance_Analysis_Types::search($this->registry, $filters);

        $main_centers_ids = array();
        $main_centers_array = array();
        foreach ($main_centers as $main_center) {
            $filters = array('model_lang' => $lang,
                             'sanitize' => true,
                             'where' => array('fac.type=' . $main_center->get('id'),
                                              'fac.deleted_by=0',
                                              'fac.active=1'),
                             'sort' => array('faci18n.name ASC'));
            $centers = Finance_Analysis_Centers::search($this->registry, $filters);
            //get main centers only if they have belonging centers
            if ($centers) {
                $centers_ids = array();
                $centers_array = array();
                foreach ($centers as $center) {
                    $centers_ids[] = $center->get('id');
                    $centers_array[] = array('id' => $center->get('id'), 'name' => $center->get('name'));
                }

                $elements_distribution = $main_center->getElementsDefaultDistributionValues();
                $main_centers_ids[] = array('id' => $main_center->get('id'),
                                            'centers' => $centers_ids,
                                            'elements_distribution' => $elements_distribution);
                $main_centers_array[] = array('id' => $main_center->get('id'),
                                              'name' => $main_center->get('name'),
                                              'centers' => $centers_array);
            }
        }

        $start_date = '';
        $end_date = '';
        if ($request->isRequested('issue_date')) {
            $start_date = $request->get('issue_date');
        }
        if ($start_date) {
            $date_parts = explode('-', $start_date);
            $end_date = date('Y-m-d', mktime(0, 0, 0, $date_parts[1]+1, $date_parts[2], $date_parts[0]));
        }

        require_once PH_MODULES_DIR . 'finance/models/finance.analysis_items.factory.php';
        $filters = array('where' => array('fai.id = "' . $item_id . '"'),
                         'model_lang' => $lang,
                         'sanitize' => true);

        $this->registry->set('getElementsFilters', true, true);
        $analysis_item = Finance_Analysis_Items::searchOne($this->registry, $filters);

        if ($analysis_item) {
            //set distribution end date based on settings in item
            if ($start_date && $analysis_item->get('default_period')) {
                switch (strtoupper($analysis_item->get('date_period'))) {
                    case 'DAY':
                        $date_parts[2] += $analysis_item->get('default_period');
                        break;
                    case 'WEEK':
                        $date_parts[2] += 7 * $analysis_item->get('default_period');
                        break;
                    case 'MONTH':
                        $date_parts[1] += $analysis_item->get('default_period');
                        break;
                    default:
                        $date_parts[1] += 1;
                        break;
                }
                $end_date = date('Y-m-d', mktime(0, 0, 0, $date_parts[1], $date_parts[2], $date_parts[0]));
            }

            $elements_titles[$idx][$idx2] = $analysis_item->get('elements_title');

            //round all subtotal amounts according to 'gt2_rows' precision setting
            $rows_precision = $this->registry['config']->getParam('precision', 'gt2_rows');

            $item_amount = round(floatval($item_amount), $rows_precision);

            $elements_factory = $analysis_item->get('elements_factory');

            //if item has no 'elements_factory', it is not distributed by elements and centers
            if ($elements_factory) {
                //get ids of elements of analysis item
                $elements_ids = $analysis_item->getElementsIdsOfAnalysisItem($analysis_item);

                //get elements of item
                $elements_arrays = array('names' => array(),
                                         'elements' => array(),
                                         'percentage' => array(),
                                         'elements_factory' => $elements_factory);
                $elements = $elements_factory::getElementsForDistribution($this->registry, $elements_ids, $analysis_item->get('id'));

                $elements_distribution = $analysis_item->getElementsDefaultDistributionValues();

                foreach ($elements as $idx_e => $element) {
                    $elements_arrays['names'][$idx_e] = $element['name'];
                    $elements_arrays['elements'][$idx_e] = $element['element'];
                    foreach ($elements_distribution as $row) {
                        if ($element['element'] == $row['element']) {
                            $elements_arrays['percentage'][$idx_e] = $row['percentage'];
                        }
                    }
                    if (!isset($elements_arrays['percentage'][$idx_e])) {
                        $elements_arrays['percentage'][$idx_e] = 0;
                    }
                }

                $remaining_amount_elements = $item_amount;
                $num_elements = count($elements_arrays['elements']);
                foreach ($elements_arrays['elements'] as $idx3 => $element) {
                    $percentage = $elements_arrays['percentage'][$idx3];
                    if ($idx3 < $num_elements - 1 || $remaining_amount_elements == $item_amount) {
                        $element_amount = round(($item_amount * $percentage / 100), $rows_precision);
                        // we don't want to have a negative value for the last element due to rounding
                        if (abs($element_amount) > abs($remaining_amount_elements)) {
                            $element_amount = $remaining_amount_elements;
                        }
                        $remaining_amount_elements -= $element_amount;
                        $remaining_amount_elements = round($remaining_amount_elements, $rows_precision);
                    } else {
                        // last element gets the remaining amount
                        $element_amount = $remaining_amount_elements;
                    }
                    $element_amount = sprintf('%.' . $rows_precision . 'F', $element_amount);
                    $element_amounts[$idx][$idx2][] = $element_amount;
                    $element_ids[$idx][$idx2][] = $element;
                    $element_names[$idx][$idx2][] = $elements_arrays['names'][$idx3];
                    $elements_dd[$idx][$idx2][] = $percentage;
                    $element_percentages[$idx][$idx2][] = $percentage;

                    foreach ($main_centers_ids as $idx4 => $mc_values) {
                        //get default distribution of main center by current element
                        $element_mc_ed = array();
                        foreach ($mc_values['elements_distribution'] as $mc_ed) {
                            if ($mc_ed['element'] == $element && $mc_ed['elements_factory'] == $elements_arrays['elements_factory']) {
                                $element_mc_ed[] = $mc_ed;
                            }
                        }

                        $remaining_amount_centers = $element_amount;
                        $num_centers = count($mc_values['centers']);
                        foreach ($mc_values['centers'] as $idx5 => $c_id) {
                            $center_amount = 0;
                            $percentage = sprintf('%.' . $rows_precision . 'F', 0);
                            foreach ($element_mc_ed as $mc_ed) {
                                if ($mc_ed['center'] == $c_id) {
                                    $percentage = $mc_ed['percentage'];
                                    $center_amount = round(($element_amount * $percentage / 100), $rows_precision);
                                }
                            }
                            if ($idx5 < $num_centers - 1) {
                                // we don't want to have a negative value for the last center due to rounding
                                if (abs($center_amount) > abs($remaining_amount_centers)) {
                                    $center_amount = $remaining_amount_centers;
                                }
                                $remaining_amount_centers -= $center_amount;
                                $remaining_amount_centers = round($remaining_amount_centers, $rows_precision);
                            } else {
                                // last center gets the remaining amount
                                $center_amount = $remaining_amount_centers;
                            }
                            $center_amount = sprintf('%.' . $rows_precision . 'F', $center_amount);
                            $center_amounts[$idx][$idx2][$idx3][$idx4][$c_id] = $center_amount;
                            $centers_dd[$idx][$idx2][$idx3][$idx4][$c_id] = $percentage;
                        }
                    }
                }

                //whether item has elements with zero amount
                $zero_share_elements[$idx][$idx2] = in_array(0, $element_amounts[$idx][$idx2]);

            } else {
                //item has no factory and is not distributed by elements and centers
            }
        }

        //prepare data for the template
        $this->viewer = new Viewer($registry);
        //indices for gt2 row and item
        $this->viewer->data['gk'] = $idx;
        $this->viewer->data['ik'] = $idx2;
        //distribution start and end dates
        $this->viewer->data['start_date'] = $start_date;
        $this->viewer->data['end_date'] = $end_date;
        //titles of elements of items
        $this->viewer->data['elements_titles'] = $elements_titles;
        //ids, names and amounts of elements
        $this->viewer->data['element_ids'] = $element_ids;
        $this->viewer->data['element_names'] = $element_names;
        $this->viewer->data['element_amounts'] = $element_amounts;
        $this->viewer->data['element_percentages'] = $element_percentages;
        $this->viewer->data['zero_share_elements'] = $zero_share_elements;
        //amounts of centers
        $this->viewer->data['center_amounts'] = $center_amounts;
        //default distribution of elements among items
        $this->viewer->data['elements_dd'] = $elements_dd;
        //default distribution elements among centers
        $this->viewer->data['centers_dd'] = $centers_dd;
        //main centers and belonging centers
        $this->viewer->data['main_centers'] = $main_centers_array;

        $this->viewer->setFrameset('_distribute_item_data.html');
        $this->viewer->display();

        exit;
    }

    /**
     * Replaces pattern placeholders for calculation values in transformation
     * and performs evaluation of result expression
     *
     * @param string $value - initial value to be calculated
     * @param Model $source_model - source model (Document, Contract, Event)
     * @param array $source_additional_vars - source additional vars
     * @param Model $destinationType - destination model type
     * @param User $user - current user for transformation
     * @param int $record_id - id of transformation record in db (used for error logging)
     * @return string - value after replacement and evaluation
     */
    protected function replaceCalcVars($value, Model &$source_model, array &$source_additional_vars, Model &$destinationType, User &$user, $record_id = 0) {
        $vars = preg_match_all("#\[([^\[]*(\[([^\[]*)\])?)\]#", $value, $matches, PREG_PATTERN_ORDER);
        $smatches = array_unique($matches[1]);
        $replace_vars = array();
        foreach ($smatches as $key) {
            if (preg_match('/^(a|b|dt|usr)_(.+)/', $key, $d_matches)) {
                $replace_vars[] = array('var_type' => $d_matches[1], 'name' => $d_matches[2]);
            }
        }
        $data = array();
        foreach ($replace_vars as $var) {
            $replace_index = '[' . $var['var_type'].'_'.$var['name'] . ']';
            if ($var['var_type'] == 'b') {
                $data[$replace_index] = $source_model->get($var['name']);
            } elseif ($var['var_type'] == 'a') {
                if (is_array($source_additional_vars[$var['name']]['value'])) {
                    // if method is transformMulti get value from corresponding row
                    $multi_idx = $source_model->get('multi') ?: 1;
                    $current_value = $source_additional_vars[$var['name']]['value'];
                    // array to single
                    if (isset($current_value[$multi_idx])) {
                        $current_value = $current_value[$multi_idx];
                    } else {
                        $current_value = reset($current_value);
                    }
                    $data[$replace_index] = $current_value;
                } else {
                    $data[$replace_index] = $source_additional_vars[$var['name']]['value'];
                }
            } elseif ($var['var_type'] == 'dt') {
                $methodName = 'getDefault' . ucfirst($var['name']);
                $data[$replace_index] =
                    method_exists($destinationType, $methodName) ?
                    $destinationType->$methodName() :
                    ($destinationType->get('default_' . $var['name']) ?: $destinationType->get($var['name']));
            } elseif ($var['var_type'] == 'usr') {
                $data[$replace_index] = $user->get($var['name']);
            }
        }

        // escape both single and double quotes in replacement values, then
        // strip unnecessary backslashes because we don't know whether single
        // or double quotes are used, neither whether a placeholder is
        // immediately surrounded by quotes or it is part of an expression
        return General::slashesStrip(EvalString::evaluate(
            $this->registry,
            $value,
            array(
                'search' => array_keys($data),
                'replace' => General::slashesEscape(array_values($data)),
                'object' => $this,
                'model' => $source_model,
                'origin' => array(
                    'table' => DB_TABLE_TRANSFORMATIONS,
                    'field' => 'settings',
                    'id' => $record_id,
                ),
            )));
    }

    /**
     * Filters rows of grouping variable for transformation
     * ToDo: move to Transformation_Grouping_Trait
     *
     * @param array $grouping_var - grouping variable of source model
     * @param array $params - filtering parameters: array of variable_name, compare('lt', 'gt', ...), compare_value, and_or
     * @param int $new_type - id of destination type
     * @param int $record_id - id of transformation record in db (used for error logging)
     * @return array - grouping variable after filtering of values
     */
    protected function filterGroupingFields(array $grouping_var, array $params, $new_type = 0, $record_id = 0) {
        $transform_definitions = array();
        foreach ($params as $param) {
            //variable names
            if (preg_match('/^(.+)_type([0-9]+)/', $param[0], $d_matches)) {
                if ($new_type && $new_type == $d_matches[2]) {
                    $transform_definitions[] = $d_matches[1];
                } else {
                    continue;
                }
            } else {
                $transform_definitions[] = $param[0];
            }
            //compare operator - 'lt', 'gt', ...
            $compare[] = strtolower($param[1]);
            //compare value
            $compare_value[] = $param[2];
            //'and', 'or'
            if (isset($param[3])) {
                $and_or[] = strtolower($param[3]);
            } else {
                $and_or[] = 'and';
            }
        }
        $var_names = array_unique($transform_definitions);
        $col = array();
        //find variable positions
        foreach ($grouping_var['names'] as $pos => $varname) {
            if (in_array($varname, $var_names)) {
                $col[$varname] = $pos;
            }
        }
        $e_search   = array(' eq ', ' ne ', ' lt ', ' gt ', ' le ', ' ge ', " \nor\n ", " \nand\n ");
        $e_replace  = array(' == ', ' != ', ' < ', ' > ', ' <= ', ' >= ', ' || ', ' && ');
        if (count($col) && isset($grouping_var['values'])) {
            foreach ($grouping_var['values'] as $row => $vals) {
                //first filter
                // prepare string representation of variables, including surrounding quotes, single quotes in values are escaped
                $val = var_export(isset($vals[$col[$transform_definitions[0]]]) ? $vals[$col[$transform_definitions[0]]] : '', 1);
                $cval = var_export(in_array($compare[0], array('starts', 'ends')) ? '#' . ($compare[0] == 'starts' ? '^' : '') . preg_quote($compare_value[0]) . ($compare[0] == 'ends' ? '$' : '') . '#' : $compare_value[0], 1);
                if ($compare[0] == 'like') {
                    $equation = "strpos({$val}, {$cval}) !== false ";
                } elseif (in_array($compare[0], array('starts', 'ends'))) {
                    $equation = "preg_match({$cval}, {$val}) ";
                } else {
                    $equation = "{$val} {$compare[0]} {$cval} ";
                }
                for ($i = 1; $i < count($transform_definitions); $i++) {
                    $val = var_export(isset($vals[$col[$transform_definitions[$i]]]) ? $vals[$col[$transform_definitions[$i]]] : '', 1);
                    $cval = var_export(in_array($compare[$i], array('starts', 'ends')) ? '#' . ($compare[$i] == 'starts' ? '^' : '') . preg_quote($compare_value[$i]) . ($compare[$i] == 'ends' ? '$' : '') . '#' : $compare_value[$i], 1);
                    $equation .= "\n{$and_or[$i-1]}\n ";
                    if ($compare[$i] == 'like') {
                        $equation .= "strpos({$val}, {$cval}) !== false ";
                    } elseif (in_array($compare[$i], array('starts', 'ends'))) {
                        $equation .= "preg_match({$cval}, {$val}) ";
                    } else {
                        $equation .= "{$val} {$compare[$i]} {$cval} ";
                    }
                }
                //insert ( )
                $equation = explode(" \nand\n ", $equation);
                $equation = '(' . implode(") \nand\n (", $equation) . ')';

                $result = EvalString::evaluate(
                    $this->registry,
                    $equation,
                    array(
                        //replace 'lt', 'gt', ...
                        'search' => $e_search,
                        'replace' => $e_replace,
                        'origin' => array(
                            'table' => DB_TABLE_TRANSFORMATIONS,
                            'field' => 'settings',
                            'id' => $record_id,
                            'model' => $this->modelName,
                            'model_id' => (array_key_exists('model_id', $grouping_var) ? $grouping_var['model_id'] : ''),
                        ),
                    )
                );
                //unset row if false
                if (!$result) {
                    unset($grouping_var['values'][$row]);
                }
            }
        }

        return $grouping_var;
    }

    /**
     * Filters rows of GT2 variable for transformation
     * ToDo: move to Transformation_GT2_Trait
     *
     * @param array $grouping_var - GT2 variable of source model
     * @param array $params - filtering parameters: array of variable_name, compare('lt', 'gt', ...), compare_value, and_or
     * @param int $new_type - id of destination type
     * @param int $record_id - id of transformation record in db (used for error logging)
     * @return array - GT2 variable after filtering of values
     */
    protected function filterGT2Fields(array $grouping_var, array $params, $new_type = 0, $record_id = 0) {
        $transform_definitions = array();
        foreach ($params as $param) {
            //variable names
            if (preg_match('/^(.+)_type([0-9]+)/', $param[0], $d_matches)) {
                if ($new_type && $new_type == $d_matches[2]) {
                    $transform_definitions[] = $d_matches[1];
                } else {
                    continue;
                }
            } else {
                $transform_definitions[] = $param[0];
            }
            //compare operator - 'lt', 'gt', ...
            $compare[] = strtolower($param[1]);
            //compare value
            $compare_value[] = $param[2];
            //'and', 'or'
            if (isset($param[3])) {
                $and_or[] = strtolower($param[3]);
            } else {
                $and_or[] = 'and';
            }
        }
        $e_search   = array(' eq ', ' ne ', ' lt ', ' gt ', ' le ', ' ge ', " \nor\n ", " \nand\n ");
        $e_replace  = array(' == ', ' != ', ' < ', ' > ', ' <= ', ' >= ', ' || ', ' && ');
        if (isset($grouping_var['values'])) {
            foreach ($grouping_var['values'] as $row => $vals) {
                //first filter
                // prepare string representation of variables, including surrounding quotes, single quotes are escaped
                $val = var_export(isset($vals[$transform_definitions[0]]) ? $vals[$transform_definitions[0]] : '', 1);
                $cval = var_export(in_array($compare[0], array('starts', 'ends')) ? '#' . ($compare[0] == 'starts' ? '^' : '') . preg_quote($compare_value[0]) . ($compare[0] == 'ends' ? '$' : '') . '#' : $compare_value[0], 1);
                if ($compare[0] == 'like') {
                    $equation = "strpos({$val}, {$cval}) !== false ";
                } elseif (in_array($compare[0], array('starts', 'ends'))) {
                    $equation = "preg_match({$cval}, {$val}) ";
                } else {
                    $equation = "{$val} {$compare[0]} {$cval} ";
                }
                for ($i = 1; $i < count($transform_definitions); $i++) {
                    $val = var_export(isset($vals[$transform_definitions[$i]]) ? $vals[$transform_definitions[$i]] : '', 1);
                    $cval = var_export(in_array($compare[$i], array('starts', 'ends')) ? '#' . ($compare[$i] == 'starts' ? '^' : '') . preg_quote($compare_value[$i]) . ($compare[$i] == 'ends' ? '$' : '') . '#' : $compare_value[$i], 1);
                    $equation .= "\n{$and_or[$i-1]}\n ";
                    if ($compare[$i] == 'like') {
                        $equation .= "strpos({$val}, {$cval}) !== false ";
                    } elseif (in_array($compare[$i], array('starts', 'ends'))) {
                        $equation .= "preg_match({$cval}, {$val}) ";
                    } else {
                        $equation .= "{$val} {$compare[$i]} {$cval} ";
                    }
                }
                //insert ( )
                $equation = explode(" \nand\n ", $equation);
                $equation = '(' . implode(") \nand\n (", $equation) . ')';

                $result = EvalString::evaluate(
                    $this->registry,
                    $equation,
                    array(
                        //replace 'lt', 'gt', ...
                        'search' => $e_search,
                        'replace' => $e_replace,
                        'origin' => array(
                            'table' => DB_TABLE_TRANSFORMATIONS,
                            'field' => 'settings',
                            'id' => $record_id,
                            'model' => $this->modelName,
                            'model_id' => (array_key_exists('model_id', $vals) ? $vals['model_id'] : ''),
                        ),
                    )
                );
                //unset row if false
                if (!$result) {
                    unset($grouping_var['values'][$row]);
                }
            }
        }

        return $grouping_var;
    }

    /**
     * Archivates list of models
     */
    protected function _archive() {

        $request = &$this->registry['request'];

        if ($ids = $request->get($this->action)) {
            $ids = array($ids);
            $request->set('archive_action', $this->action, '', true);
            $single_model = true;
        } else {
            $ids = $request->get('items');
            $single_model = false;
        }
        if (empty($ids)) {
            $this->registry['messages']->setError($this->i18n('error_' . ($request->get('archive_action') ?: $this->action) . '_failed'));
            $this->registry['messages']->setError($this->i18n('no_selected_records'));
            $this->registry['messages']->insertInSession($this->registry);
        } else {
            $model_factory_name = $this->modelFactoryName;
            $this->actionCompleted = $model_factory_name::archive($this->registry, array('ids' => $ids, 'action' => $request->get('archive_action')));
            if ($this->actionCompleted) {
                $this->registry['messages']->setMessage($this->i18n('message_' . $request->get('archive_action') . '_success'), '', -1);
            } else {
                $this->registry['messages']->setError($this->i18n('error_' . $request->get('archive_action') . '_failed'), '', -1);
            }
            $this->registry['messages']->insertInSession($this->registry);
        }

        $after_action = 'list';
        if (!empty($_SERVER['HTTP_REFERER'])) {
            preg_match('/&' . $this->controller . '=([^&]*)(&.+)?$/', $_SERVER['HTTP_REFERER'], $matches);
            if (isset($matches[1]) && !preg_match('#archive|extract#', $matches[1])) {
                $after_action = $matches[1];
            }
        }

        if ($single_model) {
            $params = array($after_action => $ids[0]);
            if ($this->action == 'archive') {
                $params['archive'] = 1;
            }
        } else {
            $params = array();
        }

        $this->redirect($this->module, $after_action, $params, $this->controller);
        exit;
    }

    /**
     * Get totals for a list/search action with AJAX
     */
    public function _getTotals() {
        $registry = &$this->registry;
        $session = &$registry['session'];
        $request = &$registry['request'];

        if ($session_param = $request->get('session_param')) {
            $model = preg_split('#_#', $session_param);
            $action = array_shift($model);
            // 'ajax' check is added for subpanels
            if (reset($model) == 'ajax') {
                array_shift($model);
                if (reset($model) == 'referent') {
                    array_shift($model);
                }
            }
            $model = str_replace(' ', '_', ucwords(implode(' ', $model)));
            $session_param_prefix = preg_replace('#' . strtolower($model) . '$#', '', $session_param);

        } else {
            $model = $this->modelName;
            $action = $request->get('action_requested');
            if ($model == 'Task' && preg_match('#^my(assigned|observer|responsible|records)$#', $action)) {
                $action = 'list';
            }
            $session_param_prefix = $action . '_';
            $session_param = $session_param_prefix . strtolower($model);
        }
        $factory = General::singular2plural($model);
        $filters = $factory::saveSearchParams($this->registry, array(), $session_param_prefix);

        // additional custom filters
        switch ($model) {
        case 'Task':
            $action_requested = $request->get('action_requested');
            switch ($action_requested) {
            case 'list':
                $filters['where'][] = 't.status != \'finished\'';
                break;
            case 'myassigned':
                $filters['where'][] = 'ta.assigned_to assignment_owner = ' . $this->registry['currentUser']->get('id');
                if (!strpos(implode(' ', $filters['where']), '.status')) {
                    $filters['where'][] = 't.status != \'finished\'';
                }
                break;
            case 'myobserver':
                $filters['where'][] = 'ta.assigned_to assignment_observer = ' . $this->registry['currentUser']->get('id');
                if (!strpos(implode(' ', $filters['where']), '.status')) {
                    $filters['where'][] = 't.status != \'finished\'';
                }
                break;
            case 'myresponsible':
                $filters['where'][] = 'ta.assigned_to assignment_responsible = ' . $this->registry['currentUser']->get('id');
                if (!strpos(implode(' ', $filters['where']), '.status')) {
                    $filters['where'][] = 't.status != \'finished\'';
                }
                break;
            case 'myrecords':
                $filters['where'][] = 't.added_by = ' . $this->registry['currentUser']->get('id');
                if (!strpos(implode(' ', $filters['where']), '.status')) {
                    $filters['where'][] = 't.status != \'finished\'';
                }
                break;
            }
            break;
        default:
            break;
        }

        $result = $sql = array();
        $result['total'] = $factory::getIds($this->registry, $filters, $sql, true);
        $result['last_page'] = ceil($result['total'] / $filters['display']);
        $selected_ids = $session->get($session_param, 'selected_items');
        if ($selected_ids && !empty($selected_ids['select_all'])) {
            $result['selected'] = $result['total'] - (!empty($selected_ids['ignore_ids']) ? count($selected_ids['ignore_ids']) : 0);
        }
        echo json_encode($result);
        die;
    }

    /**
     * Prepares the link depending on the form params passed from a button click
     */
    public function _buttonLinkPrepare() {
        // the string that will be added to the param
        $return_url = array();
        $return_url_properties = array();

        // get factory and model name for the current module
        $model_name = $this->modelName;
        $model_factory_name = $this->modelFactoryName;

        $request_model = $model_factory_name::buildModel($this->registry);
        $button_params = unserialize(base64_decode($request_model->get('button_params')));

        // check if there are button params which have to be processed
        if (!empty($button_params)) {
            // if the action is 'view' there will be no id in the post
            // and because of this the id is taken from the params
            if ($request_model->get('current_model_id')) {
                $request_model->set('id', $request_model->get('current_model_id'), true);
            }

            // check if model has ID
            if ($request_model->get('id')) {
                // gets recorded model from the DB
                $alias = $model_factory_name::getAlias($this->module, $this->controller);
                $filters = array('where'      => array($alias . '.id = ' . $request_model->get('id')),
                                 'model_lang' => $request_model->get('model_lang'),
                                 'sanitize'   => true);
                $db_model = $model_factory_name::searchOne($this->registry, $filters);
            } else {
                // the model has no ID so it is presumable that the action is add
                //an empty model is built
                $db_model = new $model_name($this->registry);
            }

            // goes through all the params and tries to fill it with values
            foreach ($button_params as $key => $param) {
                if (preg_match('#^php\s*\((.*)\)$#s', $param, $matches)) {
                    preg_match_all("#(a|b)_[a-z0-9][a-z0-9_]*#", $param, $m);

                    $search = $m[0];
                    $replace = array();
                    foreach($search as $s) {
                        $replace[] = $this->processButtonParamValues($s, $request_model, $db_model);
                    }
                    $value = EvalString::evaluate($this->registry, $matches[1], array(
                        'origin' => array(
                            'table' => DB_TABLE_FIELDS_META,
                            'field' => 'source',
                            'id' => 1, //field id
                            'model' => $request_model, //model,
                            'model_id' => $request_model->get('id'), //model_id,
                        ),
                        'search' => $search,
                        'replace' => $replace,
                    ));
                } elseif (preg_match('#^(a|b)_#', $param)) {
                    $value = $this->processButtonParamValues($param, $request_model, $db_model);
                } else {
                    // the var does not match the conditions for additional or basic var, nor is a evaluable equation
                    //so it will be processed as an array
                    $value = preg_split('#\s*,\s*#', $param);
                }

                $return_url_properties[$key] = $value;
            }
        }

        // prepare the the final look of the returned url
        foreach ($return_url_properties as $key => $property) {
            if (preg_match('#\[\]$#', $key)) {
                // the properties has to be passed as array
                if (is_array($property)) {
                    foreach ($property as $prop) {
                        $return_url[] = array(
                            'param' => $key,
                            'value' => $prop
                        );
                    }
                } else {
                    $return_url[] = array(
                        'param' => $key,
                        'value' => $property
                    );
                }
            } else {
                // the properties has to be passed as string
                if (is_array($property)) {
                    $return_url[] = array(
                        'param' => $key,
                        'value' => implode(',', $property)
                    );
                } else {
                    $return_url[] = array(
                        'param' => $key,
                        'value' => $property
                    );
                }
            }
        }

        echo('&d=' . General::encodeUrlData($return_url));
        exit;
    }

    /**
     * Function to process the value of the params which are passed to the link buttons
     */
    public function processButtonParamValues($process_value, &$request_model, &$db_model) {
        //$process_value = '';
        $param = '';

        // check if the var is basic or additional depending on the prefix
        // After defining it the prefix is cleared
        $type_var = '';

        if (preg_match('#^a_[^_]#', $process_value)) {
            $type_var = 'additional';
            $param = preg_replace('#^a_#', '', $process_value);
        } elseif (preg_match('#^b_#', $process_value)) {
            $type_var = 'basic';
            $param = preg_replace('#^b_#', '', $process_value);
        } else {
            // the var does not match the conditions for additional or basic var
            //so it will be processed as an array
            $process_value = preg_split('#\s*,\s*#', $process_value);
        }

        if ($type_var) {
            // check for the var first in the request model and if it is not presented there
            // it is taken from the database model
            if ($request_model->get($param)) {
                $process_value = $request_model->get($param);
            } elseif ($type_var == 'basic' && preg_match('#_name$#', $param) &&
                $request_model->isDefined(preg_replace('#_name$#', '', $param)) &&
                $request_model->isDefined(preg_replace('#_name$#', '_autocomplete', $param))) {
                // a special case for autocompleters for basic variables
                // (necessary for add mode)
                $process_value = preg_replace('#^(\[[^\]]*\]\s*)#', '', $request_model->get(preg_replace('#_name$#', '_autocomplete', $param)));
            } else {
                //search the var in the model taken from the DB
                if ($type_var == 'basic') {
                    // FOR BASIC VARS
                    // get value for the basic var as an array or as plain value
                    $process_value = $db_model->get($param);
                } else {
                    // FOR ADDITIONAL VARS
                    // get vars as assoc array
                    $model_assoc_vars = $db_model->getAssocVars();

                    // if there is no such var in the array, the value taking is skipped
                    if (isset($model_assoc_vars[$param])) {
                        // it should be checked if the var is in BB
                        if ($model_assoc_vars[$param]['bb'] == 1) {
                            // get data for the main BB vars
                            $db_model->unsanitize();
                            $bb_fields = $db_model->getBBFields();
                            $db_model->sanitize();

                            // once taken the values for the main BB vars are filled in assoc array
                            foreach ($bb_fields as $bb_var => $bb_properties) {
                                if (isset($model_assoc_vars[$bb_var]) && $bb_properties['type']!='bb') {
                                    $model_assoc_vars[$bb_var]['value'] = $bb_properties['value'];
                                }
                            }
                        }

                        // get value of the additional var
                        $process_value = $model_assoc_vars[$param]['value'];
                    } else {
                        $process_value = '';
                    }
                }
            }
        }

        return $process_value;
    }

    /**
     * Prepares the generation of the map
     */
    public function _prepareMap() {
        // the string that will be added to the param
        $return_url = array();
        $return_url_properties = array();
        $row = '';

        // get factory and model name for the current module
        $model_name = $this->modelName;
        $model_factory_name = $this->modelFactoryName;

        $request_model = $model_factory_name::buildModel($this->registry);
        $map_params = unserialize(base64_decode($request_model->get('map_params')));
        $row = $request_model->get('row');
        $bb_contain_row = $request_model->get('bb_contain_row');

        $address = '';
        // check if there are map params which have to be processed
        if (!empty($map_params) && !empty($map_params['address'])) {
            // if the action is 'view' there will be no id in the post
            // and because of this the id is taken from the params
            if ($request_model->get('current_model_id')) {
                $request_model->set('id', $request_model->get('current_model_id'), true);
            }

            // check if model has ID
            if ($request_model->get('id')) {
                // gets saved model from the DB
                $alias = $model_factory_name::getAlias($this->module, $this->controller);
                $filters = array('where'      => array($alias . '.id = ' . $request_model->get('id')),
                                 'model_lang' => $request_model->get('model_lang'),
                                 'sanitize'   => true);
                $db_model = $model_factory_name::searchOne($this->registry, $filters);
            } else {
                // the model has no ID so it is presumable that the action is add
                //an empty model is built
                $db_model = new $model_name($this->registry);
            }

            // find address elements
            preg_match_all('#(\$[a-z0-9\[\]\_]*)#', $map_params['address'], $matches);
            if (!empty($matches[1])) {
                $address_elements = array();
                foreach ($matches[1] as $match) {
                    $address_elements[$match] = '';
                }

                // take the model vars as associative array
                $model_assoc_vars = array();

                // flag to mark if the bb vars has been taken
                $bb_vars_taken = false;

                // goes through all address elements and completes them with values
                foreach ($address_elements as $var_name => $empty_value) {
                    $type_var = '';
                    $cleared_var_name = preg_replace('#^\$([a-z0-9_]*)(\[([0-9]*)?\])?$#', '\1', $var_name);

                    // check if the var is basic or additional depending on the prefix
                    // After defining it the prefix is cleared
                    if (preg_match('#^a_#', $cleared_var_name)) {
                        $type_var = 'additional';
                        $cleared_var_name = preg_replace('#^a_#', '', $cleared_var_name);
                    } elseif (preg_match('#^b_#', $cleared_var_name)) {
                        $type_var = 'basic';
                        $cleared_var_name = preg_replace('#^b_#', '', $cleared_var_name);
                    } else {
                        continue;
                    }

                    // check for the var first in the request model and
                    // if it is not presented there it is taken from the database model
                    if ($request_model->get($cleared_var_name)) {
                        $address_elements[$var_name] = $request_model->get($cleared_var_name);
                    } else {
                        //search the var in the model taken from the DB
                        if ($type_var == 'basic') {
                            // FOR BASIC VARS
                            // get value for the basic var as an array or as plain value
                           $address_elements[$var_name] = $db_model->get($cleared_var_name);
                        } else {
                            // FOR ADDITIONAL VARS
                            // get vars as assoc array
                            if (empty($model_assoc_vars)) {
                                $model_assoc_vars = $db_model->getAssocVars();
                            }

                            // if there is no such var in the array, the value taking is skipped
                            if (!isset($model_assoc_vars[$cleared_var_name])) {
                                continue;
                            }

                            // it should be checked if the var is in BB
                            if ($model_assoc_vars[$cleared_var_name]['bb'] == 1 && !$bb_vars_taken) {
                                // get data for the main BB vars
                                $db_model->unsanitize();
                                $bb_fields = $db_model->getBBFields();

                                //get the bb var
                                foreach ($bb_fields as $bb_var_data) {
                                    if ($bb_var_data['type'] == 'bb') {
                                        $bb_inside_var_values = $db_model->getBB(array('model_id' => $db_model->get('id')));

                                        // complete the values for required BB variant
                                        foreach ($bb_inside_var_values as $bb_row) {
                                            foreach ($bb_row['params'] as $bb_ins_var => $bb_ins_vals) {
                                                if (isset($model_assoc_vars[$bb_ins_var])) {
                                                    $model_assoc_vars[$bb_ins_var]['value'][$bb_row['id']] = $bb_ins_vals;
                                                }
                                            }
                                        }
                                        break;
                                    }
                                }

                                // once taken the values for the main BB vars are filled in assoc array
                                foreach ($bb_fields as $bb_var => $bb_properties) {
                                    if (isset($model_assoc_vars[$bb_var]) && $bb_properties['type'] != 'bb') {
                                        $model_assoc_vars[$bb_var]['value'] = $bb_properties['value'];
                                    }
                                }
                                $bb_vars_taken = true;
                                $db_model->sanitize();
                            }

                            // get value of the additional var
                            $address_elements[$var_name] = $model_assoc_vars[$cleared_var_name]['value'];
                        }
                    }
                }

                // goes through all address elements and replaces them the found values
                if (!empty($address_elements)) {
                    $address = $map_params['address'];
                    foreach ($address_elements as $adr_el => $adr_value) {
                        // by default the value is empty
                        $replace_value = '';
                        if (is_array($adr_value)) {
                            // for fields in BB and grouping tables
                            if ($bb_contain_row && isset($adr_value[$bb_contain_row])) {
                                // BB field
                                if (is_array($adr_value[$bb_contain_row])) {
                                    if ($row!='' && isset($adr_value[$bb_contain_row][$row-1])) {
                                        // field in grouping table, in BB var
                                        $replace_value = $adr_value[$bb_contain_row][$row-1];
                                    }
                                } else {
                                    $replace_value = $adr_value[$bb_contain_row];
                                }
                            } elseif ($row!='' && isset($adr_value[$row])) {
                                // grouping table field
                                $replace_value = $adr_value[$row];
                            }
                        } else {
                            // for all fields which are not in grouping and BB tables
                            $replace_value = $adr_value;
                        }

                        // replace the value in the address
                        $address = preg_replace('#' . preg_quote($adr_el) . '#', $replace_value, $address, 1);
                    }
                }
            } else {
                // gets only the written in the source address
                $address = $map_params['address'];
            }

            // prepare map display parameters
            if (!empty($map_params['static'])) {
                // prepare static map
                $url_address_elements = array();

                // set all the needed elements
                if (!empty($map_params['width']) && !empty($map_params['height'])) {
                    $url_address_elements[] = sprintf('size=%dx%d', $map_params['width'], $map_params['height']);
                }
                if ($address) {
                    $url_address_elements[] = sprintf('markers=%s', urlencode($address));
                }
                if (!empty($map_params['map_type'])) {
                    $url_address_elements[] = sprintf('maptype=%s', $map_params['map_type']);
                }
                if ($this->registry['lang']) {
                    $url_address_elements[] = sprintf('language=%s', $this->registry['lang']);
                }

                // add senesor parameter because it is required
                $url_address_elements[] = 'sensor=false';

                // defines the full address of the static picture
                $url_map = sprintf('%s?%s', PH_GOOGLE_MAPS_STATIC_BASE_ADDRESS, implode('&', $url_address_elements));

                echo ($url_map);
                exit;
            } else {
                // TO DO!!!
                // GENERATE DYNAMIC MAP
            }
        } else {

        }
    }

    /********************************** TAGS *********************************/

    /**
     * Add specified tag to selected models
     */
    protected function _multiTag() {
        $request = &$this->registry['request'];

        //get the requested models IDs
        $ids = $request->get('items');

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        /** @var Model_Factory $factory */
        $factory = $this->modelFactoryName;
        if ($result = $factory::multiTag($this->registry, $this)) {
            $this->actionCompleted = true;
            // set general success message
            if ($result > 0) {
                $this->registry['messages']->setMessage(
                    $this->i18n('message_add_tags_' . ($result !== true ? 'some_' : '') . 'success',
                                array($result)), '', -1);
            }
            // set warning if some models were not updated (skipped)
            if ($result !== true) {
                $this->registry['messages']->setWarning($this->i18n('warning_add_tags_not_all'));
            }
        } else {
            // if method returns false, set error message
            $this->registry['messages']->setError($this->i18n('error_add_tags_failed'), '', -1);
        }
        $this->registry['messages']->insertInSession($this->registry);

        return true;
    }

    /**
     * Remove specified tag from selected models
     */
    protected function _multiRemoveTag() {
        $request = &$this->registry['request'];

        //get the requested models IDs
        $ids = $request->get('items');

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        /** @var Model_Factory $factory */
        $factory = $this->modelFactoryName;
        if ($result = $factory::multiRemoveTag($this->registry, $this)) {
            $this->actionCompleted = true;
            // set general success message
            if ($result > 0) {
                $this->registry['messages']->setMessage(
                    $this->i18n('message_remove_tags_' . ($result !== true ? 'some_' : '') . 'success',
                                array($result)), '', -1);
            }
            // set warning if some models were not updated (skipped)
            if ($result !== true) {
                $this->registry['messages']->setWarning($this->i18n('warning_remove_tags_not_all'));
            }
        } else {
            // if method returns false, set error message
            $this->registry['messages']->setError($this->i18n('error_remove_tags_failed'), '', -1);
        }
        $this->registry['messages']->insertInSession($this->registry);

        return true;
    }

    /**
     * Add/remove tags to current model
     */
    protected function _tag() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        $factory = $this->modelFactoryName;
        $alias = $factory::getAlias($this->module, $this->controller);

        $filters = array('where' => array($alias . '.id = \'' . $id . '\''),
                         'model_lang' => $request->get('model_lang'));
        if ($request->get('archive') == 1) {
            $filters['where'][] = $alias . '.archive = \'archive\'';
        }
        $model = $factory::searchOne($this->registry, $filters);

        if (empty($model)) {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_' . strtolower($this->modelName)));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        //get referer's action
        if (!empty($_SERVER['HTTP_REFERER'])) {
            preg_match('/&' . $this->controller . '=([^&]*)&/', $_SERVER['HTTP_REFERER'], $matches);
        }
        $after_action = isset($matches[1]) ? $matches[1] : 'view';

        $old_model = clone $model;
        $old_model->getModelTagsForAudit();

        $model->set('tags', $request->get('tags'), true);

        $old_model->new_model = $model;
        $this->checkTransition($old_model, true);

        $old_model->sanitize();
        $this->old_model = clone $old_model;

        if ($model->updateTags()) {
            $this->registry['messages']->setMessage($this->i18n('message_tags_success'), '', -2);

            $filters = array('where' => array($alias . '.id = \'' . $id . '\''),
                             'model_lang' => $request->get('model_lang'),
                             'skip_assignments' => true,
                             'skip_permissions_check' => true);
            $new_model = $factory::searchOne($this->registry, $filters);
            $new_model->getModelTagsForAudit();
            $new_model->sendTagNotification($old_model->get('tag_names_for_audit'));
            //set after action to view if having no permission
            if (!$new_model->checkPermissions($after_action)) {
                $after_action = 'view';
            }
            $new_model->sanitize();
            $this->model = clone $new_model;

            // save history
            require_once PH_MODULES_DIR . $this->module . '/models/' . $this->module . ($this->controller != $this->module ? '.' . $this->controller : '') . '.history.php';
            require_once PH_MODULES_DIR . $this->module . '/models/' . $this->module . ($this->controller != $this->module ? '.' . $this->controller : '') . '.audit.php';
            $factory .= '_History';
            $factory::saveData(
                $this->registry,
                array(
                    'model' => $model,
                    'action_type' => 'tag',
                    'new_model' => $new_model,
                    'old_model' => $old_model
                ));

            //the model was successfully saved set action as completed
            $this->actionCompleted = true;
        } else {
            //some error occurred
            $this->registry['messages']->setError($this->i18n('error_tags_failed'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //manually set custom after action so that the navigation is redirected to previous action or view mode
        $request->set('after_action', $after_action, 'get', true);

        //set parameters in registry - check them in router
        //set redirect url
        $this->registry->set('redirect_to_url', $request->get('redirect_to_url') ?: $_SERVER['HTTP_REFERER'], true);
        //set exit parameter
        $this->registry->set('exit_after', true, true);

        return true;
    }

    /**
     * Prepare available and current tags for model for tagging from lightbox
     */
    protected function _getTags() {
        $request = &$this->registry['request'];

        $factory = $this->modelFactoryName;
        $alias = $factory::getAlias($this->module, $this->controller);

        $filters = array('where' => array($alias . '.id = \'' . $request->get('id') . '\''),
                         'model_lang' => $request->get('model_lang'));
        $model = $factory::searchOne($this->registry, $filters);
        if (!$model) {
            exit;
        }

        $model->getAvailableTags();
        $model->getTags();

        $this->viewer = new Viewer($this->registry);
        $this->viewer->setFrameset('_action_tag.html');
        $this->viewer->data['model'] = $model->sanitize();
        if ($request->get('redirect_to_url')) {
            $this->viewer->data['redirect_to_url'] = $request->get('redirect_to_url');

            if ($request->get('update_target')) {
                $this->viewer->data['update_target'] = $request->get('update_target');
            } else {
                // set id of update target to be the same as the session param
                parse_str(rawurldecode($request->get('redirect_to_url')), $redirect_to_url_parts);
                if (isset($redirect_to_url_parts['session_param'])) {
                    $this->viewer->data['update_target'] = $redirect_to_url_parts['session_param'];
                }
            }
        }
        $name = 'tag';
        $this->viewer->data['available_action'] = array(
            'module_param'     => $this->registry['module_param'],
            'module'           => $this->module,
            'controller_param' => ($this->controller != $this->module) ? $this->registry['controller_param'] : '',
            'controller'       => ($this->controller != $this->module) ? $this->controller : '',
            'action_param'     => $this->registry['action_param'],
            'action'           => $name,
            'model_id'         => $model->get('id'),
            'model_lang'       => $model->get('model_lang'),
            'name'             => $name,
            'label'            => $this->i18n($name),
            'options'          => array('label' => $this->i18n('confirm_tags')),
            'show_form'        => 1
        );
        $this->viewer->display();
        exit;
    }

    /******************************** END TAGS *******************************/

    /****************************** ASSIGNMENTS ******************************/

    /**
     * General method that manages assignments for all models
     */
    protected function _assign() {

        //load the lang files for assignments
        $module_i18n_dir = PH_MODULES_DIR . 'assignments/i18n/' . $this->registry['lang'] . '/';
        $module_lang_files = FilesLib::readDir($module_i18n_dir, false, 'files_only', 'ini', true);
        $this->registry['translater']->loadFile($module_lang_files, 'module');

        $request = &$this->registry['request'];

        $this->registry->set('getAssignments', true, true);

        //get the alias for the model
        $factory = $this->modelFactoryName;
        $alias = $factory::getAlias($this->module, $this->controller);

        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array($alias . '.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        if ($request->get('archive') == 1) {
            $filters['where'][] = $alias . '.archive = \'archive\'';
        }
        $model = $factory::searchOne($this->registry, $filters);

        if (empty($model)) {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_' . strtolower($this->modelName)));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        $this->settings_assign = $this->registry['config']->getParamAsArray($this->module, 'assignment_types_' . $model->get('type'));
        if (empty($this->settings_assign)) {
            $this->redirect($this->module, 'list');
        }
        foreach ($this->settings_assign as $sa) {
            $model->getAssignments($sa);
        }

        //assign from tab
        if ($request->isPost() && !$request->get('a_type')) {
            if (!empty($model)) {
                $this->old_model = clone $model;
                $this->old_model->sanitize();
                if (in_array('owner', $this->settings_assign)) {
                    $model->set('assignments_owner', $request->get('assignments_owner') ?: array(), true);
                }
                if (in_array('responsible', $this->settings_assign)) {
                    $model->set('assignments_responsible', $request->get('assignments_responsible') ?: array(), true);
                }
                if (in_array('observer', $this->settings_assign)) {
                    $model->set('assignments_observer', $request->get('assignments_observer') ?: array(), true);
                }
                if (in_array('decision', $this->settings_assign)) {
                    $model->set('assignments_decision', $request->get('assignments_decision') ?: array(), true);
                }

                if ($model->assign()) {
                    $this->registry['messages']->setMessage($this->i18n('message_model_assign_success'), '', -1);
                    $this->registry['messages']->insertInSession($this->registry);

                    $filters = array('where' => array($alias . '.id = ' . $request->get('id')),
                                     'model_lang' => $request->get('model_lang'),
                                     'skip_permissions_check' => true);
                    $new_model = $factory::searchOne($this->registry, $filters);

                    require_once PH_MODULES_DIR . $this->module . '/models/' . $this->module . ($this->controller != $this->module ? '.' . $this->controller : '') . '.history.php';
                    require_once PH_MODULES_DIR . $this->module . '/models/' . $this->module . ($this->controller != $this->module ? '.' . $this->controller : '') . '.audit.php';
                    $factory .= '_History';
                    $factory::saveData(
                        $this->registry,
                        array(
                            'model' => $model,
                            'action_type' => 'assign',
                            'new_model' => $new_model,
                            'old_model' => $this->old_model
                        ));

                    $this->actionCompleted = true;
                    if (!$this->registry->isRegistered(strtolower($this->modelName))) {
                        $this->registry->set(strtolower($this->modelName), $model->sanitize());
                    }

                    return true;
                } else {
                    $this->registry['messages']->setError($this->i18n('error_model_not_assigned'), '', -1);
                    foreach ($this->settings_assign as $sa) {
                        $model->getAssignments($sa, true);
                    }
                }
            }
            // modifying only one type of assignments from lightbox
        } elseif ($request->get('a_type')) {
            if (!empty($model)) {
                $this->old_model = clone $model;
                $this->old_model->sanitize();

                $a_type = $request->get('a_type');
                $id = $request->get($this->action);
                $model->set('assignments_' . $a_type, $request->get('assignments_' . $a_type) ?: array(), true);

                if ($model->assign()) {
                    $this->registry['messages']->setMessage($this->i18n('message_model_assign_success'), '', -1);

                    $filters = array('where' => array($alias . '.id = ' . $id),
                                     'model_lang' => $request->get('model_lang'),
                                     'skip_permissions_check' => true);
                    $new_model = $factory::searchOne($this->registry, $filters);

                    require_once PH_MODULES_DIR . $this->module . '/models/' . $this->module . ($this->controller != $this->module ? '.' . $this->controller : '') . '.history.php';
                    require_once PH_MODULES_DIR . $this->module . '/models/' . $this->module . ($this->controller != $this->module ? '.' . $this->controller : '') . '.audit.php';
                    $factory .= '_History';
                    $factory::saveData(
                        $this->registry,
                        array(
                            'model' => $model,
                            'action_type' => 'assign',
                            'new_model' => $new_model,
                            'old_model' => $this->old_model
                        ));

                    $this->actionCompleted = true;
                    if (!$this->registry->isRegistered(strtolower($this->modelName))) {
                        $this->registry->set(strtolower($this->modelName), $model->sanitize());
                    }
                } else {
                    $this->registry['messages']->setError($this->i18n('error_model_not_assigned'), '', -1);
                }

                $this->registry['messages']->insertInSession($this->registry);

                //manually set custom after action so that the navigation is redirected to previous action or list mode
                if (!empty($_SERVER['HTTP_REFERER'])) {
                    preg_match('/&' . $this->controller . '=([^&]*)&/', $_SERVER['HTTP_REFERER'], $matches);
                }
                if (isset($matches[1])) {
                    $after_action = $matches[1];
                } else {
                    $after_action = 'list';
                }
                $request->set('after_action', $after_action, 'get', true);

                if (!empty($_SERVER['HTTP_REFERER'])) {
                    $this->registry->set('redirect_to_url', $_SERVER['HTTP_REFERER'], true);
                }
                $this->registry->set('exit_after', true, true);

                return true;
            }
        }

        if (!empty($model)) {
            $model->set('assignments_settings', $model->getAssignmentsSettings(), true);
            //check access and ownership of the model
            $this->checkAccessOwnership($model);
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_' . strtolower($this->modelName)));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        require_once PH_MODULES_DIR . 'assignments/viewers/assignments.assign.viewer.php';
        $this->viewer = new Assignments_Assign_Viewer($this->registry, true);
        $this->viewer->model = $this->model = $model;
        // !!! DO NOT TOUCH
        $this->viewer->modelName = "Model";
        $this->viewer->templatesDir = PH_MODULES_DIR . 'assignments/templates/';

        $this->viewer->templatesDir = $this->viewer->theme->isModern() && in_array($this->controller, REDESIGNED_CONTROLLERS)
            ? PH_MODULES_DIR . 'assignments/view/templates/'
            : PH_MODULES_DIR . 'assignments/templates/';
        //$this->viewer->i18nDir      = PH_MODULES_DIR . 'assignments/i18n/' . $this->registry['lang'] . '/';
        $this->viewer->loadCustomI18NFiles($module_lang_files);
        $this->viewer->scriptsDir = PH_MODULES_DIR . 'assignments/javascript/';
        $this->viewer->scriptsUrl = PH_MODULES_URL . 'assignments/javascript/';
    }

    /**
     * Prepares a type of assignment for model
     */
    public function _getAssignments() {
        $request = &$this->registry['request'];

        //load the lang files for assignments
        $module_i18n_dir = PH_MODULES_DIR . 'assignments/i18n/' . $this->registry['lang'] . '/';
        $module_lang_files = FilesLib::readDir($module_i18n_dir, false, 'files_only', 'ini', true);
        $this->registry['translater']->loadFile($module_lang_files, 'module');

        //get the requested model ID and assignment type
        $id = $request->get('id');
        $a_type = str_replace('assignments_', '', $request->get('a_type'));

        //get the alias for the model
        $factory = $this->modelFactoryName;
        $alias = $factory::getAlias($this->module, $this->controller);

        $filters = array('where' => array($alias . '.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $this->registry->set('getAssignments', true, true);
        $model = $factory::searchOne($this->registry, $filters);

        if (!empty($model)) {

            $this->settings_assign = $this->registry['config']->getParamAsArray($this->module, 'assignment_types_' . $model->get('type'));
            if (!in_array($a_type, $this->settings_assign)) {
                // invalid or disabled assignment type
                exit;
            }

            if (in_array($model->get('status'), array('closed', 'finished')) && $a_type == 'owner') {
                // no users can be assigned
                exit;
            }

            //check access and ownership of the model
            $this->checkAccessOwnership($model);
            $model->getAssignments();
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_' . strtolower($this->modelName)));
            $this->registry['messages']->insertInSession($this->registry);
            exit;
        }

        $aSettings = $model->getAssignmentsSettings($a_type);

        if (empty($aSettings[$a_type])) {
            $aSettings[$a_type] = array();
        }
        if (empty($aSettings[$a_type]['users'])) {
            if ($a_type == 'owner') {
                if (!$model->get('department')) {
                    $this->registry['messages']->setError($this->i18n('model_department_not_forwarded'));
                } else {
                    $this->registry['messages']->setError($this->i18n('error_no_users_to_assign_as_owner2') . ' ' . $model->get('department_name'));
                }
            } else {
                $this->registry['messages']->setError($this->i18n('error_no_users_to_assign_as_' . $a_type));
            }
        }

        $this->viewer = new Viewer($this->registry);
        $this->viewer->model = $model;
        // !!! DO NOT TOUCH
        $this->viewer->modelName = "Model";
        if ($this->viewer->theme->isModern()) {
            $this->viewer->templatesDir = PH_MODULES_DIR . 'assignments/view/templates/';
        } else {
            $this->viewer->templatesDir = PH_MODULES_DIR . 'assignments/templates/';
        }
        $this->viewer->i18nDir    = PH_MODULES_DIR . 'assignments/i18n/' . $this->registry['lang'] . '/';
        $this->viewer->scriptsDir = PH_MODULES_DIR . 'assignments/javascript/';
        $this->viewer->scriptsUrl = $this->viewer->data['scriptsUrl'] = PH_MODULES_URL . 'assignments/javascript/';
        $this->viewer->setFrameset(PH_MODULES_DIR . 'assignments/templates/_action_assign.html');
        $this->viewer->data['type'] = $a_type;
        $this->viewer->data['data'] = $aSettings[$a_type];
        $this->viewer->data['config_templates'] = $aSettings['config_templates'];
        $this->viewer->data['model_name'] = $model->modelName;
        $this->viewer->data['model_type'] = $model->get('type');
        $this->viewer->data['messages'] = $this->registry['messages'];
        //set submit link
        $this->viewer->data['submitLink'] = sprintf('%s?%s=%s%s&amp;%s=%s&amp;%s=%s',
            $_SERVER['PHP_SELF'],
            $this->registry['module_param'], $this->module,
            ($this->module != $this->controller ? '&amp;' . $this->registry['controller_param'] . '=' . $this->controller : ''),
            $this->registry['action_param'], 'assign',
            'assign', $model->get('id')
        );

        $this->viewer->display();
        exit;
    }

    /**
     * Get assignment parameters for specified model and type of assignment
     */
    protected function _getAssignmentsParams() {

        $type = $this->registry['request']->get('a_type') ?: 'owner';

        $model = Model_Factory::buildFromRequest($this->registry, $this->modelName);
        $data = $model->getAssignmentsSettings($type);

        echo json_encode($data);
        exit;
    }

    /**
     * Add/remove observation (current user being assigned as observer) of model
     */
    public function _observer() {
        $id = $this->registry['request']->get($this->action);

        $module_i18n_dir = PH_MODULES_DIR . 'assignments/i18n/' . $this->registry['lang'] . '/';
        $module_lang_files = FilesLib::readDir($module_i18n_dir, false, 'files_only', 'ini', true);
        $this->registry['translater']->loadFile($module_lang_files, 'module');

        $factory = $this->modelFactoryName;
        $alias = $factory::getAlias($this->module, $this->controller);
        $filters = array('where' => array($alias . '.id = ' . $id),
                         'model_lang' => $this->registry['request']->get('model_lang'));
        $model = $factory::searchOne($this->registry, $filters);

        if (!empty($model)) {
            $this->checkAccessOwnership($model);
            $remove = $factory::checkObserver($this->registry, $model);
            $result = $factory::observer($this->registry, $model, $remove);
            //redirect to view
            if ($result) {
                if ($remove) {
                    $this->registry['messages']->setMessage($this->i18n('message_assignments_observer_del_success', array($model->getModelTypeName())), '', -1);
                } else {
                    $this->registry['messages']->setMessage($this->i18n('message_assignments_observer_add_success', array($model->getModelTypeName())), '', -1);
                }
            } else {
                $this->registry['messages']->setError($this->i18n('error_assignments_observer', array($model->getModelTypeName())), '', -1);
            }
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect($this->module, 'view', 'view=' . $id);
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_' . strtolower($this->modelName)));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
            $result = true;
        }

        return $result;
    }

    /**************************** ASSIGNMENTS END ****************************/

    /**
     * Set the original user as current.
     * The original user is the user logged in nZoom
     * This method is while in automation. The currentUser in automations is the automation user (PH_AUTOMATION_USER)
     * The purpose of this method is to:
     * - TEMPORARILY set the originally logged user as current
     *
     * @return bool - the result of the operations
     */
    public function setOriginalUserAsCurrent() {
        if ($this->registry['currentUser']->get('id') == PH_AUTOMATION_USER
            //in some cases the crontab automations execute action automations in which the originalUser is not set
            && isset($this->registry['originalUser'])) {
            $this->registry->set('currentUser', $this->registry['originalUser'], true);
            $this->registry->remove('originalUser');
        }

        return true;
    }

    /**
     * Set the current user as original and the automations user as current
     * This method is while in automation and aims to:
     * - restore the automation user from a property to currentUser
     * - restore the logged user as originalUser
     *
     * @return bool - the result of the operations
     */
    public function setAutomationUserAsCurrent() {
        $this->getAutomationUser();
        //this is a precaution, though it seems unnecessary
        if ($this->registry['currentUser']->get('id') != PH_AUTOMATION_USER) {
            $this->registry->set('originalUser', $this->registry['currentUser'], true);
            $this->registry->set('currentUser',  $this->automationUser, true);
        }

        return true;
    }

    /**
     * Get the automation user
     *
     * @return object automationUser - the automation user
     */
    public function getAutomationUser() {
        if (!$this->automationUser) {
            $filters = array('where' => array('u.id = ' . PH_AUTOMATION_USER,
                                              'u.hidden IS NOT NULL'));
            $this->automationUser = Users::searchOne($this->registry, $filters);
            $this->automationUser->getGroups();
            $this->automationUser->sanitize();
        }

        return $this->automationUser;
    }

    /**
     * Get the original logged user
     *
     * @param int user_id - id of the user
     * @return object originalUser - the logged user
     */
    public function getOriginalUser($user_id = 0) {
        $user_id = ($user_id) ? $user_id : $this->registry['currentUser']->get('id');
        if ($user_id != PH_AUTOMATION_USER) {
            $filters = array('where' => array('u.id = ' . $user_id,
                                              'u.hidden IS NOT NULL'));
            $this->originalUser = Users::searchOne($this->registry, $filters);
            $this->originalUser->getGroups();
            $this->originalUser->sanitize();
        }

        return $this->originalUser;

    }

    /**
     * Prepares certain type of messages from Registry for display as HTML
     *
     * @param string $display - display mode: error, warning or message; message by default
     * @param string $return - how result should be returned - array, json; json by default
     * @return string|array - JSON-encoded string/array with fetched data
     */
    public function getMessagesForAJAX($display = 'message', $return = 'json') {
        $viewer = new Viewer($this->registry);
        $viewer->setFrameset('message.html');
        $viewer->data['display'] = $display;

        $result = array();
        switch ($display) {
            case 'error':
                $viewer->data['items'] = $this->registry['messages']->getErrors();
                $result = array(
                    'errors' => $viewer->fetch(),
                    'erred_fields' => array_keys($viewer->data['items']),
                );
                break;
            case 'warning':
                $viewer->data['items'] = $this->registry['messages']->getWarnings();
                $result = array(
                    'warnings' => $viewer->fetch(),
                    'erred_fields' => array_keys($viewer->data['items']),
                );
                break;
            case 'message':
            default:
                $viewer->data['items'] = $this->registry['messages']->getMessages();
                $result = array(
                    'messages' => $viewer->fetch(),
                );
                break;
        }

        switch (strtolower($return)) {
            case 'array':
                break;
            case 'json':
            default:
                $result = json_encode($result);
                break;
        }

        return $result;
    }

    /**
     * Prepares form for import of data from file into GT/GT2 table of model;
     * Imports data from file into GT/GT2 table of model
     *
     * @return boolean - always return true
     */
    protected function _importTable() {
        /** @var Registry $registry */
        $registry = &$this->registry;
        /** @var Request $request */
        $request = $this->registry['request'];

        if (!$request->isRequested('model')) {
            $request->set('model', $this->modelName, 'all', true);
        }
        /** @var Imports_Table $importsTable */
        $importsTable = Model_Factory::buildFromRequest($registry, 'Imports_Table');

        $result = array();
        if (!empty($_FILES)) {
            // upload temporary file
            // prepare additional data to load into lightbox after file selection
            $file = isset($_FILES['import_file']) ? $_FILES['import_file'] : array();
            $result = $importsTable->importTempFile($file, true);
        } elseif ($request->isRequested('remove_import_key')) {
            // remove temporary file
            if (!$importsTable->removeImportTempFile($request['remove_import_key'])) {
                //
            }
        } elseif ($request->isRequested('import_key')) {
            //launch plugin if set in table settings
            $importsTable->launchPlugin($request->get('import_key'));
            // prepare import data, process it and return it
            $result = $importsTable->fetchTable();
        } else {
            // prepare initial data for lightbox
            $result = $importsTable->showForm();
        }

        // check if AJAX request; set response header
        // ancient IE sends no header
        if (!empty($_SERVER['HTTP_ACCEPT']) && preg_match('#(json|javascript)#i', $_SERVER['HTTP_ACCEPT'])) {
            header('Content-Type: application/json');
        }

        $registry->set('ajax_result', json_encode((object)$result), true);

        return true;
    }

    /**
     * Manages (load/save/delete) saved configurations for import of data from
     * file into GT/GT2 table of model
     *
     * @return boolean - always return true
     */
    protected function _importTableConfigurator() {
        /** @var Registry $registry */
        $registry = &$this->registry;
        /** @var Request $request */
        $request = $this->registry['request'];

        if (!$request->isRequested('model')) {
            $request->set('model', $this->modelName, 'all', true);
        }
        $importsTablesConfig = Imports_Tables_Configurators::buildModel($registry);
        if ($request->isRequested('saved_imports_tables_configurators')) {
            if (is_numeric($request['saved_imports_tables_configurators']) && !$request['saved_imports_tables_configurators_isCustom']) {
                $importsTablesConfig->set('id', $request['saved_imports_tables_configurators'], true);
            } else {
                $importsTablesConfig->set('name', $request['saved_imports_tables_configurators'], true);
            }
        }
        $errors = array();
        if (in_array($request['config_action'], array('save', 'delete'))) {
            if ($request['config_action'] == 'save') {
                $result = $importsTablesConfig->save();
            } elseif ($request['config_action'] == 'delete') {
                $result = Imports_Tables_Configurators::purge(
                    $registry,
                    array($importsTablesConfig->get('id'))
                );
            }
            if ($result) {
                $viewer = new Viewer($registry);
                $viewer->setFrameset(PH_MODULES_DIR . 'imports/templates/_tables_configurator_panel.html');
                if ($request['config_action'] == 'save') {
                    $viewer->data['config_id'] = $importsTablesConfig->get('id');
                    $viewer->data['user_id'] = $importsTablesConfig->get('user_id');
                }
                $viewer->data['config_templates'] = Imports_Tables_Configurators::getTemplatesOptgroups(
                    $registry,
                    $importsTablesConfig->getVarParams()
                );
                $viewer->loadCustomI18NFiles(PH_MODULES_DIR . 'imports/i18n/' . $registry['lang'] . '/imports.ini');
                // fetch configuration panel
                $result = array('content' => $viewer->fetch());
            } else {
                $errors[] = $this->i18n("error_imports_tables_configurator_{$request['config_action']}");
            }
        } elseif ($request['config_action'] == 'load' && $importsTablesConfig->get('id')) {
            /** @var Imports_Table $importsTable */
            $importsTable = Model_Factory::buildFromRequest($registry, 'Imports_Table');
            $importsTable->set('config_id', $importsTablesConfig->get('id'), true);
            // fetch form with configurator applied
            $result = $importsTable->showForm();
        } else {
            $result = false;
            $errors[] = $this->i18n("error_imports_tables_configurator_action");
        }

        if (!$result) {
            $errors = array_merge($errors, array_values($registry['messages']->getErrors()));
            $result = array('errors' => $errors);
        }

        header('Content-Type: application/json');

        $registry->set('ajax_result', json_encode((object)$result), true);

        return true;
    }

    /**
     * Executes automation actions for a specified model during a specific phase of its lifecycle.
     *
     * @param object $old_model Reference to the old model before the action is performed.
     * @param object $new_model Reference to the new model for which the action is performed.
     * @param string $action The action being performed (e.g., create, update, delete).
     * @param bool $before_action Flag to execute before_action automations. (default: false).
     * @param bool $checkAllSuccess Flag specifying whether to validate the success of all automations (default: false).
     * @return bool The result of executing the automations.
     * @throws Exception
     */
    public function executeActionAutomations(&$old_model, &$new_model, $action, $before_action = false, bool $checkAllSuccess = false) {
        // Temporary change the module name and the action for correct execution of the automations
        $registry = $this->registry;
        $registry_module = $registry->get('module');
        $registry_controller = $registry->get('controller');
        $registry_action = $registry->get('action');

        $automation_module = $new_model->getModule();
        $automation_controller = $new_model->getController();
        $registry->set('module', $automation_module, true);
        $registry->set('controller', $automation_controller, true);

        $registry->set('action', $action, true);

        // Load language files
        $registry['translater']->loadFile(PH_MODULES_DIR . "{$automation_module}/i18n/{$registry['lang']}/{$automation_module}.ini");
        $registry['translater']->loadFile(PH_MODULES_DIR . "{$automation_module}/i18n/{$registry['lang']}/{$automation_module}_types.ini");
        if ($automation_controller == 'warehouses_documents') {
            $registry['translater']->loadFile(PH_MODULES_DIR . "{$automation_module}/i18n/{$registry['lang']}/finance_warehouses.ini");
        }
        if ($automation_module != $automation_controller) {
            $registry['translater']->loadFile(PH_MODULES_DIR . "{$automation_module}/i18n/{$registry['lang']}/{$automation_module}_{$automation_controller}.ini");
        }

        // Build an automation model
        include_once PH_MODULES_DIR . 'automations/controllers/automations.controller.php'; #TODO: Test without it
        $automation = new Automations_Controller($registry);
        if ($before_action) {
            $automation->before_action = true;
        }

        // Set to get vars from the DB (as it is when submitting the documents add page)
        $get_old_vars = $registry->get('get_old_vars');
        $registry->set('get_old_vars', true, true);

        // Perform action type automations
        $result = $automation->performAutomation($new_model, $old_model, 0, $checkAllSuccess);
        if ($before_action) {
            // TODO: Restore the value as it was because it might have been before_action = true
            $automation->before_action = false;
        }

        // Set back the value of the get_old_vars flag
        $registry->set('get_old_vars', $get_old_vars, true);

        // Change back the module name and the action
        $registry->set('module', $registry_module, true);
        $registry->set('controller', $registry_controller, true);
        $registry->set('action', $registry_action, true);

        return $result;
    }

    /**
     * Build factory name from module and controller
     *
     * @param $module - module name
     * @param $controller - controller name
     *
     * @return string - factory name
     */
    public function buildFactoryName($module, $controller = '') {
        return implode('_', array_map('ucfirst', explode('_', $module . (($controller != '' && $module != $controller) ? '_' . $controller : ''))));
    }

    /**
     * @param array $fieldDescription - the info array from the var as taken from the outlook model
     * @return string
     */
    private function getExcelFormatting($fieldDescription = []): string
    {
        $formatting = PHPExcel_Style_NumberFormat::FORMAT_GENERAL;
        if (empty($fieldDescription) || $fieldDescription['origin'] == 'additional') {
            return $formatting;
        }

        switch ($fieldDescription['name']) {
            case 'full_num':
            case 'num':
            case 'invoice_num':
            case 'name_full_num':
            case 'customer_name':
            case 'customer_name_code':
            case 'status':
            case 'tags':
            case 'type':
            case 'customer':
            case 'added_by':
            case 'modified_by':
            case 'deleted_by':
            case 'employee':
            case 'office':
            case 'media':
            case 'project':
            case 'project_name_code':
            case 'department':
            case 'group':
            case 'owner':
            case 'observer':
            case 'decision':
            case 'responsible':
            case 'relatives_children':
            case 'relatives_parent':
            case 'kind':
            case 'country':
            case 'country_name':
            case 'phone':
            case 'gsm':
            case 'fax':
            case 'web':
            case 'email':
            case 'skype':
            case 'othercontact':
            case 'priority':
            case 'visibility':
            case 'availability':
            case 'participants':
            case 'code':
            case 'name_code':
            case 'categories':
            case 'categories_names':
            case 'trademark':
            case 'trademark_name':
            case 'trademark_name_code':
            case 'history_activity':
            case 'comments':
            case 'emails':
            case 'recurrence_type':
            case 'eik':
            case 'postal_code':
            case 'in_dds':
            case 'iban':
            case 'ucn':
            case 'identity_num':
            case 'registration_volume':
            case 'registration_file':
                $formatting = PHPExcel_Style_NumberFormat::FORMAT_TEXT;
                break;
            case 'deadline':
            case 'validity_term':
            case 'planned_start_date':
            case 'planned_finish_date':
            case 'event_start':
            case 'event_end':
                // custom date formatting
                $formatting = 'dd.mm.yyyy';
                break;
            case 'added':
            case 'created':
            case 'modified':
            case 'deleted':
            case 'date':
            case 'identity_date':
            case 'identity_valid':
                // custom date/time formatting
                $formatting = 'dd.mm.yyyy hh:mm';
                break;
            case 'sell_price':
            case 'last_delivery_price':
            case 'average_weighted_delivery_price':
            case 'sell_price_and_currency':
            case 'last_delivery_price_and_currency':
            case 'average_weighted_delivery_price_and_currency':
                $formatting = PHPExcel_Style_NumberFormat::FORMAT_NUMBER_00;
                break;
            case 'exec_time':
            case 'age':
            case 'timesheet_time':
            case 'planned_time':
                $formatting = PHPExcel_Style_NumberFormat::FORMAT_NUMBER;
                break;
            default:
                break;
        }

        return $formatting;
    }
}
