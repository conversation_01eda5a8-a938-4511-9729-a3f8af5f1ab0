<?php
class Spreadsheet_Manager {
    /**
     * Shows if a file was successfully loaded
     */
    public $valid_lodaded_file = false;

    /**
     * The created object of the reader for the current file
     */
    public $spreadsheetManager;

    /**
     * The created object for the current file
     */
    public $spreadsheetFile;

    /**
     * constructor
     *
     * param string $file - path to file
     * param array $params - options when loading file
     */
    public function __construct($file, $params = array()) {
        require_once (PH_PHPEXCEL_DIR . 'PHPExcel.php');

        $valid_lodaded_file = false;

        // if file does not exist the validation flag is set to false
        if (file_exists($file)) {

            //use caching to decrease memory usage
            if (isset($params['use_cache']) && isset($params['memory_cached_size'])) {
                $cacheMethod = PHPExcel_CachedObjectStorageFactory::cache_to_phpTemp;
                $cacheSettings = array(' memoryCacheSize ' => $params['memory_cached_size']);
                PHPExcel_Settings::setCacheStorageMethod($cacheMethod, $cacheSettings);
            }
            // creating an instance of the reader object depending on the file type
            $this->spreadsheetManager = PHPExcel_IOFactory::createReaderForFile($file);

            // if any parameters are passed as arguments the function handles them
            if (isset($params['take_only_data'])) {
                // if set to true the styles, format, etc. would NOT be taken
                $this->spreadsheetManager->setReadDataOnly($params['take_only_data']);
            }

            if (isset($params['worksheets']) && !empty($params['worksheets'])) {
                // can be specified to load only certain sheets
                if (is_array($params['worksheets'])) {
                    $loaded_worksheets = $params['worksheets'];
                } else {
                    $loaded_worksheets = array($params['worksheets']);
                }
               //before selecting by name we need to make sure the names of the sheets that we try to select are existing in the file
                $existingWorksheets = $this->spreadsheetManager->listWorksheetNames($file);
                $containsNames = count(array_intersect($loaded_worksheets, $existingWorksheets)) == count($loaded_worksheets);
                if ($containsNames) {
                    $this->spreadsheetManager->setLoadSheetsOnly($loaded_worksheets);
                }
                //if one of the sheet names we want is missing we load all the sheets
            }

            // create an instance to the spreadsheet file
            $this->spreadsheetFile = $this->spreadsheetManager->load($file);

            // set the valid loaded file flag to true
            $valid_lodaded_file = true;
        }

        $this->valid_lodaded_file = $valid_lodaded_file;
    }

    /**
     * Gets the sheets in the spreadsheet files
     *
     * @return array $names - array with sheet names
     */
    public function getSheetNames() {
        $names = $this->spreadsheetFile->getSheetNames();

        return $names;
    }

    /**
     * Function to select the active worksheet
     *
     * @param string $active_timesheet - the active worksheet to be used to take information from
     * @param bool $taken_by_name      - defines if the worksheet will be taken by name or by index
     *                                   Default - by index (0 for the first, 1 for the second and so on)
     *
     * @return bool - result of the operation
     */
    public function selectWorksheet($active_timesheet, $taken_by_name = false) {
        // if there is no valid loaded file then FALSE is returned
        if ($this->valid_lodaded_file) {
            if ($taken_by_name) {
                $this->spreadsheetFile->setActiveSheetIndexByName($active_timesheet);
            } else {
                $this->spreadsheetFile->setActiveSheetIndex($active_timesheet);
            }
            return true;
        } else {
            return false;
        }
    }

    /**
     * Function to read an array of cells
     *
     * @param string $first_cell - the first cell of the array
     * @param string $last_cell - the last cell of the array
     * @param string $main_key - defines which will be the main key in the array (if an array of cells is provided)
     *                           It could be the 'column' or the 'row' key ('column' by default)
     *
     * @return string/array $result - if an array of cells is provided then will be returned as
     *                                an associative array with values. Otherwise a single value is returned
     */
    public function readCells($first_cell, $last_cell = '', $main_key = 'column') {
        // get the active worksheet
        $active_worksheet = $this->spreadsheetFile->getActiveSheet();

        // converts all characters to uppercase
        $first_cell = strtoupper($first_cell);

        if (!$last_cell) {
            // if there is no last cell defined only the value of the first cell is taken
            $value = $active_worksheet->getCell($first_cell)->getCalculatedValue();
            $result = $value;
        } else {
            // if there is a last defined then ot have to be defined the searched array
            $last_cell = strtoupper($last_cell);

            // taking the rows of the first and the last cell
            $first_cell_row = preg_replace('#[A-Z]*#', '', $first_cell);
            $last_cell_row = preg_replace('#[A-Z]*#', '', $last_cell);

            // defines which cell is at higher place
            if ($first_cell_row < $last_cell_row) {
                $first_row = $first_cell_row;
                $last_row = $last_cell_row;
            } else {
                $first_row = $last_cell_row;
                $last_row = $first_cell_row;
            }

            // take the columns
            $first_cell_column = preg_replace('#' . $first_cell_row . '#', '', $first_cell);
            $last_cell_column = preg_replace('#' . $last_cell_row . '#', '', $last_cell);

            // calculates how long is the column index
            $total_indexes_fcc = strlen($first_cell_column);
            $total_indexes_lcc = strlen($last_cell_column);

            // calculates the index of the first column
            $multiplier_fc = 1;
            $first_column_index = 0;
            for ($p=$total_indexes_fcc-1; $p>=0; $p--) {
                $key_code_fc = ord($first_cell_column[$p])-65;
                if ($p != ($total_indexes_fcc-1)) {
                    $key_code_fc += 1;
                }
                $first_column_index += $key_code_fc*$multiplier_fc;
                $multiplier_fc = $multiplier_fc*26;
            }

            // calculates the index of the last column
            $multiplier_lc = 1;
            $last_column_index = 0;
            for ($k=$total_indexes_lcc-1; $k>=0; $k--) {
                $key_code_lc = ord($last_cell_column[$k])-65;
                if ($k != ($total_indexes_lcc-1)) {
                    $key_code_lc += 1;
                }
                $last_column_index += $key_code_lc*$multiplier_lc;
                $multiplier_lc = $multiplier_lc*26;
            }

            // defines which column is at higher place
            if ($first_column_index < $last_column_index) {
                $first_column = $first_column_index;
                $last_column = $last_column_index;
            } else {
                $first_column = $last_column_index;
                $last_column = $first_column_index;
            }

            // iterates through all the columns in the file and returns an assocative array with values
            for ($row=$first_row; $row<=$last_row; $row++) {
                for ($column=$first_column; $column<=$last_column; $column++) {
                    $current_cell = $active_worksheet->getCellByColumnAndRow($column, $row);
                    $current_column = $current_cell->getColumn();
                    $current_row = $current_cell->getRow();
                    if ($main_key == 'row') {
                        $result[$current_row][$current_column] = $current_cell->getCalculatedValue();
                    } else {
                        $result[$current_column][$current_row] = $current_cell->getCalculatedValue();
                    }
                }
            }
        }

        $calculationObj = PHPExcel_Calculation::getInstance();
        $calculationObj->__destruct();

        return $result;
    }

    /**
     * Function to read the cells of every row bordered between the first and the last column
     * If no params are provided the function will retrieve the first and the last columns and the
     * first and the last active row by itself
     *
     * @param string $first_column - the first column which specifies the beginning of the row
     * @param string $last_column - the first column which specifies the ending of the row
     * @param string $starting_row - the first row where the reading will start from
     * @param string $ending_row - the first row where the reading will end to
     * @param string $main_key - defines which will be the main key in the array (if an array of cells is provided)
     *                           It could be the 'column' or the 'row' key ('column' by default)
     *
     * @return array $result - an associative array with values
     */
    public function readActiveCellsBetweenColumnsByRows($first_column='', $last_column='', $starting_row='', $ending_row='', $main_key = 'column') {
        // get the active worksheet
        $active_worksheet = $this->spreadsheetFile->getActiveSheet();

        // if no first active column is provided the A column is taken
        if (!$first_column) {
            $first_column = 'A';
        }

        // if no last active column is provided the last active column is retrieved
        if (!$last_column) {
            $last_column = $active_worksheet->getHighestColumn();
        }

        // if no first active row is provided the row 1 is taken
        if (!$starting_row) {
            $starting_row = 1;
        }

        // if no last active row is provided the last active row is retrieved
        if (!$ending_row) {
            $ending_row = $active_worksheet->getHighestRow();
        }

        // forms the cells which border the array to be read
        $first_array_cell = $first_column . $starting_row;
        $last_array_cell = $last_column . $ending_row;

        $read_cells = $this->readCells($first_array_cell, $last_array_cell, $main_key);
        return $read_cells;
    }

    /**
    * Format a spreadsheet date
    * Source: http://code.google.com/p/php-excel-reader/issues/detail?id=42
    *
    * @param integer $date  - the date (in Unix or standart format) to be formated
    * @param string $format - the format we want to get (the default is Y-m-d)
    */
    public static function formatSpreadsheetDate($date, $format = '%Y-%m-%d') {
        // If the date is empty
        if (empty($date)) {
            // Return an empty value
            return '';
        } else {
            // If the date is numeric (i.e. if the date is in Unix time)
            if (is_numeric($date)) {
                // Convert the date from Unix time to the given format
                $timestamp = ($date - 25569) * 86400;
                return General::strftime($format, $timestamp);
            } else {
                // Change the format of the date
                return General::strftime($format, $date);
            }
        }
    }
}
?>
