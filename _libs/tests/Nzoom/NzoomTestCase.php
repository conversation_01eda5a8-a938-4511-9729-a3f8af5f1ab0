<?php

namespace Tests\Nzoom;

use PHPUnit\Framework\TestCase;

/**
 * Base test case for all Nzoom namespace tests
 * 
 * Provides common functionality and utilities for testing Nzoom classes
 */
abstract class NzoomTestCase extends TestCase
{
    /**
     * Set up before each test
     */
    protected function setUp(): void
    {
        parent::setUp();
        // Common setup for all Nzoom tests
    }

    /**
     * Clean up after each test
     */
    protected function tearDown(): void
    {
        // Common cleanup for all Nzoom tests
        parent::tearDown();
    }

    /**
     * Create a temporary file for testing
     * 
     * @param string $content File content
     * @param string $extension File extension (without dot)
     * @return string Path to temporary file
     */
    protected function createTempFile(string $content = '', string $extension = 'tmp'): string
    {
        $tempFile = tempnam(sys_get_temp_dir(), 'nzoom_test_') . '.' . $extension;
        file_put_contents($tempFile, $content);
        return $tempFile;
    }

    /**
     * Clean up temporary file
     * 
     * @param string $filePath Path to file to delete
     */
    protected function cleanupTempFile(string $filePath): void
    {
        if (file_exists($filePath)) {
            unlink($filePath);
        }
    }

    /**
     * Assert that a file exists and is readable
     * 
     * @param string $filePath Path to file
     * @param string $message Optional assertion message
     */
    protected function assertFileExistsAndReadable(string $filePath, string $message = ''): void
    {
        $this->assertFileExists($filePath, $message);
        $this->assertTrue(is_readable($filePath), $message ?: "File {$filePath} is not readable");
    }

    /**
     * Assert that a string contains valid JSON
     *
     * @param string $json JSON string to validate
     * @param string $message Optional assertion message
     */
    protected function assertValidJson(string $json, string $message = ''): void
    {
        json_decode($json);
        $this->assertEquals(JSON_ERROR_NONE, json_last_error(),
            $message ?: 'String is not valid JSON: ' . json_last_error_msg());
    }

    /**
     * Invoke a private or protected method on an object
     *
     * @param object $object Object instance
     * @param string $methodName Method name to invoke
     * @param array $args Method arguments
     * @return mixed Method return value
     */
    protected function invokePrivateMethod($object, string $methodName, array $args = [])
    {
        $reflection = new \ReflectionClass($object);
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);

        return $method->invokeArgs($object, $args);
    }
}
