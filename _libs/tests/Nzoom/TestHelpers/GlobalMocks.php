<?php

/**
 * Mock Config class for testing (in global namespace)
 */
class Config
{
    public static function get($key, $default = null)
    {
        // Return default values for common config keys
        $config = [
            'language' => 'en',
            'default_language' => 'en',
            'timezone' => 'UTC'
        ];

        return $config[$key] ?? $default;
    }
}

/**
 * Global Mock Outlook class for testing (in global namespace)
 */
class Outlook
{
    private $fields;
    
    public function __construct($fields = [])
    {
        $this->fields = $fields;
    }
    
    public function get($key)
    {
        if ($key === 'current_custom_fields') {
            return $this->fields;
        }
        return null;
    }
    
    public function setFields($fields)
    {
        $this->fields = $fields;
    }
}

/**
 * Global Mock Model class for testing (in global namespace)
 */
class Model
{
    private $data;
    private $exportValues;
    private $exportTypes;
    private $varsForTemplate;
    private $sanitized;

    public function __construct($data = [], $exportValues = [], $exportTypes = [], $varsForTemplate = [])
    {
        $this->data = $data;
        $this->exportValues = $exportValues;
        $this->exportTypes = $exportTypes;
        $this->varsForTemplate = $varsForTemplate;
        $this->sanitized = false;
    }

    public function get($key)
    {
        return $this->data[$key] ?? null;
    }

    public function getExportVarValueWithArgs($varName)
    {
        return $this->exportValues[$varName] ?? null;
    }

    public function getExportVarType($varName)
    {
        return $this->exportTypes[$varName] ?? null;
    }

    public function checkForVariables()
    {
        return !empty($this->varsForTemplate);
    }

    public function isSanitized()
    {
        return $this->sanitized;
    }

    public function sanitize()
    {
        $this->sanitized = true;
        return $this;
    }

    public function unsanitize()
    {
        $this->sanitized = false;
        return $this;
    }

    public function getVarsForTemplateAssoc()
    {
        return $this->varsForTemplate;
    }

    public function setVarsForTemplate($vars)
    {
        $this->varsForTemplate = $vars;
    }
}

/**
 * Mock Factory class for testing streaming functionality
 */
class TestFactory
{
    public static function search($registry, $filters)
    {
        // Return mock models based on filters
        $models = [];

        $limit = $filters['limit'] ?? 10;
        $offset = $filters['offset'] ?? 0;

        // Simulate pagination - return empty if offset is too high
        if ($offset >= 5) {
            return [];
        }

        // Create mock models
        for ($i = 0; $i < min($limit, 5 - $offset); $i++) {
            $id = $offset + $i + 1;
            $model = new Model(
                ['id' => $id],
                ['id' => $id, 'name' => "User {$id}"],
                ['id' => 'integer', 'name' => 'string']
            );
            $models[] = $model;
        }

        return $models;
    }
}

/**
 * Mock Model class for testing cursor streaming functionality
 */
class TestModel
{
    public static function search($registry, $filters)
    {
        // Return mock models based on cursor filters
        $models = [];

        $limit = $filters['limit'] ?? 10;
        $whereConditions = $filters['where'] ?? [];

        // Parse cursor condition if present
        $minId = 0;
        $minTimestamp = 0;
        $hasHighIdFilter = false;

        foreach ($whereConditions as $condition) {
            // Handle id cursor
            if (preg_match('/id > (\d+)/', $condition, $matches)) {
                $minId = (int)$matches[1];
            }
            // Handle timestamp cursor
            if (preg_match('/timestamp > (\d+)/', $condition, $matches)) {
                $minTimestamp = (int)$matches[1];
            }
            // Check for filters that would return no results
            if (preg_match('/id > 15/', $condition)) {
                $hasHighIdFilter = true;
            }
        }

        // Simulate cursor pagination - return empty if cursor is too high
        if ($minId >= 10 || $hasHighIdFilter) {
            return [];
        }

        // Create mock models starting from cursor
        $maxRecords = 10;
        $startId = max(1, $minId + 1);

        for ($i = 0; $i < $limit && ($startId + $i) <= $maxRecords; $i++) {
            $id = $startId + $i;
            $model = new Model(
                ['id' => $id, 'timestamp' => $id * 1000],
                [
                    'id' => $id,
                    'name' => "User {$id}",
                    'status' => 'active',
                    'timestamp' => $id * 1000
                ],
                [
                    'id' => 'integer',
                    'name' => 'string',
                    'status' => 'string',
                    'timestamp' => 'datetime'
                ]
            );
            $models[] = $model;
        }

        return $models;
    }
}

/**
 * Mock Export Plugin class for testing
 */
class MockExportPlugin
{
    private $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function get($key)
    {
        return $this->data[$key] ?? null;
    }
}

/**
 * Mock Exports class for testing ExportActionFactory
 */
class Exports
{
    public static $filtersToStore = [
        'file_name',
        'format',
        'separator',
        'group_tables',
        'plugin'
    ];

    public static function search($registry, $filters)
    {
        // Return mock export plugins based on filters
        $plugins = [];

        // Only return plugins if model matches
        if (isset($filters['model']) && $filters['model'] === 'TestModel') {
            $plugins[] = new MockExportPlugin(['id' => 1, 'name' => 'Custom Export Plugin']);
            $plugins[] = new MockExportPlugin(['id' => 2, 'name' => 'Advanced Export Plugin']);
        }

        return $plugins;
    }

    public static function exportLog($registry, $data)
    {
        // Mock implementation - just return success
        return true;
    }
}
