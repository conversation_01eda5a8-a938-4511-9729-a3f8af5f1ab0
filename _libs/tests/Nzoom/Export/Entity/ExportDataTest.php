<?php

namespace Tests\Nzoom\Export\Entity;

use Tests\Nzoom\Export\ExportTestCase;
use Nzoom\Export\Entity\ExportData;
use Nzoom\Export\Entity\ExportHeader;
use Nzoom\Export\Entity\ExportColumn;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;

/**
 * Test case for ExportData
 */
class ExportDataTest extends ExportTestCase
{
    private ExportData $exportData;
    private ExportHeader $header;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test header
        $this->header = new ExportHeader();
        $this->header->addColumn(new ExportColumn('id', 'ID', 'integer'));
        $this->header->addColumn(new ExportColumn('name', 'Name', 'string'));

        // Create export data instance
        $this->exportData = new ExportData('TestModel', $this->header);
    }

    public function testConstructorWithHeader(): void
    {
        $exportData = new ExportData('TestModel', $this->header);

        $this->assertEquals('TestModel', $exportData->getModelType());
        $this->assertSame($this->header, $exportData->getHeader());
        $this->assertFalse($exportData->isLazy());
        $this->assertTrue($exportData->isEmpty());
    }

    public function testConstructorWithoutHeader(): void
    {
        $exportData = new ExportData('TestModel');

        $this->assertEquals('TestModel', $exportData->getModelType());
        $this->assertInstanceOf(ExportHeader::class, $exportData->getHeader());
        $this->assertFalse($exportData->isLazy());
    }

    public function testConstructorWithMetadata(): void
    {
        $metadata = ['created_by' => 'test_user', 'version' => '1.0'];
        $exportData = new ExportData('TestModel', $this->header, $metadata);

        $this->assertEquals('TestModel', $exportData->getModelType());
        $this->assertEquals($metadata, $exportData->getMetadata());
    }

    public function testAddRecord(): void
    {
        $record = new ExportRecord();
        $record->addValue('id', 1, 'integer');
        $record->addValue('name', 'Test', 'string');
        
        $this->exportData->addRecord($record);
        
        $this->assertFalse($this->exportData->isEmpty());
        $this->assertEquals(1, $this->exportData->count());
        $this->assertSame($record, $this->exportData->getRecordAt(0));
    }

    public function testAddMultipleRecords(): void
    {
        $record1 = new ExportRecord();
        $record1->addValue('id', 1, 'integer');
        $record1->addValue('name', 'Test 1', 'string');

        $record2 = new ExportRecord();
        $record2->addValue('id', 2, 'integer');
        $record2->addValue('name', 'Test 2', 'string');
        
        $this->exportData->addRecord($record1);
        $this->exportData->addRecord($record2);
        
        $this->assertEquals(2, $this->exportData->count());
        $records = $this->exportData->getRecords();
        $this->assertCount(2, $records);
        $this->assertSame($record1, $records[0]);
        $this->assertSame($record2, $records[1]);
    }

    public function testCreateRecord(): void
    {
        $record = $this->exportData->createRecord(['test' => 'metadata']);
        
        $this->assertInstanceOf(ExportRecord::class, $record);
        $this->assertEquals(1, $this->exportData->count());
        $this->assertSame($record, $this->exportData->getRecordAt(0));
    }

    public function testGetRecordAtInvalidIndex(): void
    {
        $this->assertNull($this->exportData->getRecordAt(0));
        $this->assertNull($this->exportData->getRecordAt(-1));
        $this->assertNull($this->exportData->getRecordAt(999));
    }

    public function testMetadataOperations(): void
    {
        $this->exportData->setMetadataValue('test_key', 'test_value');
        $this->assertEquals('test_value', $this->exportData->getMetadataValue('test_key'));
        
        $this->assertNull($this->exportData->getMetadataValue('nonexistent'));
        $this->assertEquals('default', $this->exportData->getMetadataValue('nonexistent', 'default'));
        
        $metadata = ['key1' => 'value1', 'key2' => 'value2'];
        $this->exportData->setMetadata($metadata);
        $this->assertEquals($metadata, $this->exportData->getMetadata());
    }

    public function testIterator(): void
    {
        $record1 = new ExportRecord();
        $record2 = new ExportRecord();
        
        $this->exportData->addRecord($record1);
        $this->exportData->addRecord($record2);
        
        $iteratedRecords = [];
        foreach ($this->exportData as $record) {
            $iteratedRecords[] = $record;
        }
        
        $this->assertCount(2, $iteratedRecords);
        $this->assertSame($record1, $iteratedRecords[0]);
        $this->assertSame($record2, $iteratedRecords[1]);
    }

    public function testToArray(): void
    {
        $record = new ExportRecord();
        $record->addValue('id', 1, 'integer');
        $record->addValue('name', 'Test', 'string');
        
        $this->exportData->addRecord($record);
        
        $array = $this->exportData->toArray();
        
        $this->assertIsArray($array);
        $this->assertCount(2, $array); // Header + 1 record
        $this->assertEquals(['ID', 'Name'], $array[0]); // Header row
        $this->assertEquals([1, 'Test'], $array[1]); // Data row
    }

    public function testLazyLoading(): void
    {
        $recordProvider = function (int $offset, int $limit) {
            if ($offset >= 2) {
                return []; // No more records
            }
            
            $records = [];
            for ($i = $offset; $i < min($offset + $limit, 2); $i++) {
                $record = new ExportRecord();
                $record->addValue('id', $i + 1, 'integer');
                $record->addValue('name', "Test " . ($i + 1), 'string');
                $records[] = $record;
            }
            return $records;
        };
        
        $this->exportData->setRecordProvider($recordProvider, 1);
        
        $this->assertTrue($this->exportData->isLazy());
        $this->assertEquals(1, $this->exportData->getPageSize());
        
        // Test iteration
        $iteratedRecords = [];
        foreach ($this->exportData as $record) {
            $iteratedRecords[] = $record;
        }
        
        $this->assertCount(2, $iteratedRecords);
    }

    public function testLazyLoadingPreventDirectRecordOperations(): void
    {
        $recordProvider = function () { return []; };
        $this->exportData->setRecordProvider($recordProvider);
        
        $this->expectException(\LogicException::class);
        $this->expectExceptionMessage('Cannot add records directly when using lazy loading');
        
        $this->exportData->addRecord(new ExportRecord());
    }

    public function testLazyLoadingPreventCreateRecord(): void
    {
        $recordProvider = function () { return []; };
        $this->exportData->setRecordProvider($recordProvider);
        
        $this->expectException(\LogicException::class);
        $this->expectExceptionMessage('Cannot create records directly when using lazy loading');
        
        $this->exportData->createRecord();
    }

    public function testSetPageSize(): void
    {
        $this->exportData->setPageSize(500);
        $this->assertEquals(500, $this->exportData->getPageSize());
    }

    public function testSetHeader(): void
    {
        $newHeader = new ExportHeader();
        $newHeader->addColumn(new ExportColumn('email', 'Email', 'string'));
        
        $this->exportData->setHeader($newHeader);
        $this->assertSame($newHeader, $this->exportData->getHeader());
    }

    public function testValidate(): void
    {
        // Add a valid record
        $record = new ExportRecord();
        $record->addValue('id', 1, 'integer');
        $record->addValue('name', 'Test', 'string');

        $this->exportData->addRecord($record);
        $this->assertTrue($this->exportData->validate());
    }

    public function testSortByColumn(): void
    {
        $record1 = new ExportRecord();
        $record1->addValue('id', 2, 'integer');
        $record1->addValue('name', 'Beta', 'string');

        $record2 = new ExportRecord();
        $record2->addValue('id', 1, 'integer');
        $record2->addValue('name', 'Alpha', 'string');

        $this->exportData->addRecord($record1);
        $this->exportData->addRecord($record2);

        // Sort by name ascending
        $this->exportData->sortByColumn('name', true);
        $records = $this->exportData->getRecords();

        $this->assertEquals('Alpha', $records[0]->getValueByColumnName('name')->getValue());
        $this->assertEquals('Beta', $records[1]->getValueByColumnName('name')->getValue());

        // Sort by name descending
        $this->exportData->sortByColumn('name', false);
        $records = $this->exportData->getRecords();

        $this->assertEquals('Beta', $records[0]->getValueByColumnName('name')->getValue());
        $this->assertEquals('Alpha', $records[1]->getValueByColumnName('name')->getValue());
    }

    public function testSortByColumnInvalidColumn(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Column with name "nonexistent" does not exist');

        $this->exportData->sortByColumn('nonexistent');
    }

    public function testSortByColumnWithNullValues(): void
    {
        $record1 = new ExportRecord();
        $record1->addValue('id', 1, 'integer');
        $record1->addValue('name', 'Test', 'string');

        $record2 = new ExportRecord();
        $record2->addValue('id', 2, 'integer');
        $record2->addValue('name', null, 'string');

        $this->exportData->addRecord($record1);
        $this->exportData->addRecord($record2);

        $this->exportData->sortByColumn('name', true);
        $records = $this->exportData->getRecords();

        // Null values should come first in ascending order
        $this->assertNull($records[0]->getValueByColumnName('name')->getValue());
        $this->assertEquals('Test', $records[1]->getValueByColumnName('name')->getValue());
    }

    public function testFilter(): void
    {
        $record1 = new ExportRecord();
        $record1->addValue('id', 1, 'integer');
        $record1->addValue('name', 'Keep', 'string');

        $record2 = new ExportRecord();
        $record2->addValue('id', 2, 'integer');
        $record2->addValue('name', 'Remove', 'string');

        $this->exportData->addRecord($record1);
        $this->exportData->addRecord($record2);

        // Filter to keep only records with name 'Keep'
        $this->exportData->filter(function($record) {
            return $record->getValueByColumnName('name')->getValue() === 'Keep';
        });

        $records = $this->exportData->getRecords();
        $this->assertCount(1, $records);
        $this->assertEquals('Keep', $records[0]->getValueByColumnName('name')->getValue());
    }

    public function testToArrayFormatted(): void
    {
        $record = new ExportRecord();
        $record->addValue('id', 1, 'integer');
        $record->addValue('name', 'Test', 'string');

        $this->exportData->addRecord($record);

        $array = $this->exportData->toArray(true); // formatted = true

        $this->assertIsArray($array);
        $this->assertCount(2, $array); // Header + 1 record
        $this->assertEquals(['ID', 'Name'], $array[0]); // Header row
        $this->assertEquals([1, 'Test'], $array[1]); // Data row (formatted values)
    }

    public function testGetRecordsWithLazyLoading(): void
    {
        $recordProvider = function (int $offset, int $limit) {
            if ($offset >= 1) {
                return []; // No more records
            }

            $record = new ExportRecord();
            $record->addValue('id', 1, 'integer');
            $record->addValue('name', 'Lazy Test', 'string');
            return [$record];
        };

        $this->exportData->setRecordProvider($recordProvider, 1);

        // This should load all records into memory
        $records = $this->exportData->getRecords();
        $this->assertCount(1, $records);
        $this->assertEquals('Lazy Test', $records[0]->getValueByColumnName('name')->getValue());
    }

    public function testGetRecordAtWithLazyLoading(): void
    {
        $recordProvider = function (int $offset, int $limit) {
            if ($offset >= 2) {
                return []; // No more records
            }

            $records = [];
            for ($i = $offset; $i < min($offset + $limit, 2); $i++) {
                $record = new ExportRecord();
                $record->addValue('id', $i + 1, 'integer');
                $record->addValue('name', "Test " . ($i + 1), 'string');
                $records[] = $record;
            }
            return $records;
        };

        $this->exportData->setRecordProvider($recordProvider, 1);

        $record = $this->exportData->getRecordAt(1);
        $this->assertNotNull($record);
        $this->assertEquals(2, $record->getValueByColumnName('id')->getValue());

        // Test out of bounds
        $this->assertNull($this->exportData->getRecordAt(10));
    }

    public function testIsEmptyWithLazyLoading(): void
    {
        $recordProvider = function () { return []; };
        $this->exportData->setRecordProvider($recordProvider);

        // In lazy mode, we don't know if data is empty until we iterate
        // count() returns 1000 to avoid premature database queries
        // So isEmpty() returns false - we assume there might be data
        $this->assertFalse($this->exportData->isEmpty());
        $this->assertEquals(1000, $this->exportData->count());
    }

    public function testAddRecordWithValidation(): void
    {
        $record = new ExportRecord();
        $record->addValue('id', 1, 'integer');
        $record->addValue('name', 'Test', 'string');

        // This should not throw an exception
        $this->exportData->addRecord($record, true);
        $this->assertEquals(1, $this->exportData->count());
    }

    public function testCountWithEagerLoading(): void
    {
        $this->assertEquals(0, $this->exportData->count());

        $record = new ExportRecord();
        $this->exportData->addRecord($record);

        $this->assertEquals(1, $this->exportData->count());
    }
}
