###############################################################################
### SQL nZoom Specific Updates - РепроБиоМед (http://rbm.n-zoom.com/) ###
###############################################################################

########################################################################
# 2012-10-04 - Added dashlet plugin for quick adding of patients for ReproBioMed installation (1780)

# Added dashlet plugin for quick adding of patients for ReproBioMed installation (1780)
INSERT INTO `dashlets_plugins` (`id`, `type`, `settings`, `is_portal`, `visible`) VALUES
(NULL, 'reprobiomed_add_patient', 'customer_type_patient := 2', 0, 1);
INSERT INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Регистрация на пациент', 'Инфо панел, предназначен за регистрация на пациент', 'bg'),
(LAST_INSERT_ID(), 'Patient Registration', 'Dashlet for quick adding of patients', 'en');

######################################################################################
# 2012-10-10 - Added Microbiological Research pattern plugin
#            - Added placeholders for Microbiological Research pattern plugin

# Added Microbiological Research pattern plugin
INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
  ('38', 'Document', '1', 'reprobiomed', 'prepareMicrobiologicalResearch', 'tag := 4', '', NOW(), NOW());
INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
  ('38', 'Подготовка на "Микробиологично изследване"', 'Подготвят се данни за печат на "Микробиологично изследване"', 'bg', NOW()),
  ('38', 'Preparation for "Microbiological Research"', 'Prepare data for printing of "Microbiological Research"',     'en', NOW());

# Added placeholders for Microbiological Research pattern plugin
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'patient_age_years', 'Document', 'basic', 'pattern_plugins', ',38,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Възраст (в години)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Age (in years)',     NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'patient_donor', 'Document', 'basic', 'pattern_plugins', ',38,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Донор', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Donor', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'research_type', 'Document', 'basic', 'pattern_plugins', ',38,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Тип на изследването', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Research type',       NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'microbial_flora', 'Document', 'basic', 'pattern_plugins', ',38,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Микробна флора (таблици)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Microbial flora (tables)', NULL, 'en');

######################################################################################
# 2012-12-20 - Added new report - 'reprobiomed_schedule' - for ReproBiomed installation (1780)
#            - Added dashlet plugin for insert nomenclatures visits in ReproBiomed installation (1780)

# Added new report - 'reprobiomed_schedule' - for ReproBiomed installation (1780)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(252, 'reprobiomed_schedule', 'pacient_type_id := 2\r\ndoctor_week_day := week_days\r\ndoctor_start_hour := start_hour\r\ndoctor_end_hour := finish_hour\r\n\r\nnomenclature_visit_type_id := 7\r\nactivity_var := examination_act\r\nactivity_plus_var := examination\r\nvisit_patient := patient_id\r\nvisit_start_date := start_date\r\nvisit_end_date := finish_date\r\nvisit_start_hour := start_hour\r\nvisit_end_hour := finish_hour\r\nvisit_notes := operation_other\r\nvisit_type:= reception\r\nvisit_doctor := doctor_id\r\n\r\nvisit_type_paid := 1\r\nvisit_type_free := 2', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(252, 'График', NULL, NULL, 'bg'),
(252, 'Schedule', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 252, 0, 1);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND action='generate_report' AND model_type=252;

# Added dashlet plugin for insert nomenclatures visits in ReproBiomed installation (1780)
INSERT INTO `dashlets_plugins` (`id`, `type`, `settings`, `is_portal`, `visible`) VALUES
(NULL, 'reprobiomed_schedule', 'pacient_type_id := 2\r\nreport_name := reprobiomed_schedule\r\nvisit_nom_type := 7\r\nvisit_patient_id := patient_id\r\nvisit_patient_name := patient\r\nvisit_start_date := start_date\r\nvisit_start_hour := start_hour\r\nvisit_finish_hour := finish_hour\r\nvisit_doctor_grouping_table := doctor_group\r\nvisit_doctor_id := doctor_id\r\nvisit_doctor_name := doctor', 0, 1);
INSERT INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'График', 'График на лекарите', 'bg'),
(LAST_INSERT_ID(), 'Schedule', 'Doctor''s schedule', 'en');

######################################################################################
# 2013-01-16 - Added new automation for ReproBiomed installation (1780) to validate the data in the new added visit nomenclature
#            - Added setting for 'reprobiomed_schedule'  report for ReproBiomed installation (1780) to point the refused visit status
#            - Added setting to contain the name of the additional var for status of the visit for 'reprobiomed_schedule' dashlet

# Added new automation for ReproBiomed installation (1780) to validate the data in the new added visit nomenclature
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
('Валидация на посещение', 0, NULL, 1, 'nomenclatures', NULL, 'before_action', 7, 'nomenclature_about := type_sector\r\nstart_date := start_date\r\nend_date := finish_date\r\nstart_hour := start_hour\r\nend_hour := finish_hour', 'condition := 1', 'plugin := repro_biomed\r\nmethod := validateVisit', 'cancel_action_on_fail := 1', 0, 0);

# Added setting for 'reprobiomed_schedule'  report for ReproBiomed installation (1780) to point the refused visit status
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\nvisit_status_refused := 3\r\nvisit_status := event_status') WHERE `type`='reprobiomed_schedule' AND `settings` NOT LIKE '%visit_status_refused%';

# Added setting to contain the name of the additional var for status of the visit for 'reprobiomed_schedule' dashlet
UPDATE `dashlets_plugins` SET `settings` = CONCAT(`settings`, '\r\nvisit_status := event_status\r\nvisit_status_refused := 3') WHERE `type`='reprobiomed_schedule' AND `settings` NOT LIKE '%visit_status%';

######################################################################################
# 2013-01-22 - Added setting for 'reprobiomed_schedule' report for ReproBiomed installation (1780) to contain the id of the doctor who is working in Saturday without schedule
#            - Added setting for 'reprobiomed_schedule' dashlet plugin for ReproBiomed installation (1780) to contain the var for end date of the visit

# Added setting for 'reprobiomed_schedule'  report for ReproBiomed installation (1780) to contain the id of the doctor who is working in Saturday without schedule
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\ndoctor_saturday_normal_working_time := 8') WHERE `type`='reprobiomed_schedule' AND `settings` NOT LIKE '%doctor_saturday_normal_working_time%';

# Added setting for 'reprobiomed_schedule' dashlet plugin for ReproBiomed installation (1780) to contain the var for end date of the visit
UPDATE `dashlets_plugins` SET `settings` = CONCAT(`settings`, '\r\nvisit_end_date := finish_date') WHERE `type`='reprobiomed_schedule' AND `settings` NOT LIKE '%visit_end_date%';

######################################################################################
# 2013-01-28 - Added Clinical Research pattern plugin
#            - Added placeholders for Clinical Research pattern plugin

# Added Clinical Research pattern plugin
INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
  (45, 'Document', 8, 'reprobiomed', 'prepareClinicalResearch', 'tag := 4', '', NOW(), NOW());
INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
  (45, 'Подготовка на "Клинично изследване"', 'Подготвят се данни за печат на "Клинично изследване"', 'bg', NOW()),
  (45, 'Preparation for "Clinical Research"', 'Prepare data for printing of "Clinical Research"', 'en', NOW());

# Added placeholders for Clinical Research pattern plugin
UPDATE `placeholders` SET `pattern_id`=CONCAT(`pattern_id`, '45,')
WHERE `varname` IN ('patient_age_years', 'patient_donor') AND `model` = 'Document' AND  `usage` = 'pattern_plugins' AND `pattern_id` NOT LIKE '%,45,%';

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'clinical_examination_results', 'Document', 'basic', 'pattern_plugins', ',45,', '', 1);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Данни за извършени изследвания (таблици)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Data of performed research (tables)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'clinical_examination_other', 'Document', 'basic', 'pattern_plugins', ',45,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Други параметри на изследването (таблицa)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Other parameters of research (table)', NULL, 'en');

######################################################################################
# 2013-01-29 - Added additional settings for the 'reprobiomed_schedule' report for ReproBiomed installation (1780)
#            - Added plugin before_action automation to set code of Patient customer before saving

# Added additional settings for the 'reprobiomed_schedule' report for ReproBiomed installation (1780)
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\ndoctor_is_doctor := is_a_doctor\r\nvisit_status_patient_examined := 2\r\nvisit_status_patient_in_clinic := 4\r\nvisit_status_patient_not_presented := 1') WHERE `type`='reprobiomed_schedule' AND `settings` NOT LIKE '%doctor_is_doctor%';

# Added plugin before_action automation to set code of Patient customer before saving
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Попълване на код преди запис на пациент', 0, NULL, 1, 'customers', NULL, 'before_action', 2, 'code_prefix := RBM\r\ncode_date_format := %Y%m%d', 'condition := 1', 'plugin := repro_biomed\r\nmethod := setPatientCode', NULL, 0, 0);

######################################################################################
# 2013-02-08 - Copied Microbiological Research to 8 more document types 6, 7, 9, 10, 11, 12, 13, 16

# Copied Microbiological Research pattern plugin
INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
  (1046, 'Document', '16', 'reprobiomed', 'prepareMicrobiologicalResearch', 'tag := 4', '', NOW(), NOW());
INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
  (1046, 'Подготовка на "Микробиологично изследване"', 'Подготвят се данни за печат на "Микробиологично изследване"', 'bg', NOW()),
  (1046, 'Preparation for "Microbiological Research"', 'Prepare data for printing of "Microbiological Research"',     'en', NOW());
INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
  (1047, 'Document', '6', 'reprobiomed', 'prepareMicrobiologicalResearch', 'tag := 4', '', NOW(), NOW());
INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
  (1047, 'Подготовка на "Микробиологично изследване"', 'Подготвят се данни за печат на "Микробиологично изследване"', 'bg', NOW()),
  (1047, 'Preparation for "Microbiological Research"', 'Prepare data for printing of "Microbiological Research"',     'en', NOW());
INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
  (1048, 'Document', '7', 'reprobiomed', 'prepareMicrobiologicalResearch', 'tag := 4', '', NOW(), NOW());
INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
  (1048, 'Подготовка на "Микробиологично изследване"', 'Подготвят се данни за печат на "Микробиологично изследване"', 'bg', NOW()),
  (1048, 'Preparation for "Microbiological Research"', 'Prepare data for printing of "Microbiological Research"',     'en', NOW());
INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
  (1049, 'Document', '9', 'reprobiomed', 'prepareMicrobiologicalResearch', 'tag := 4', '', NOW(), NOW());
INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
  (1049, 'Подготовка на "Микробиологично изследване"', 'Подготвят се данни за печат на "Микробиологично изследване"', 'bg', NOW()),
  (1049, 'Preparation for "Microbiological Research"', 'Prepare data for printing of "Microbiological Research"',     'en', NOW());
INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
  (1050, 'Document', '10', 'reprobiomed', 'prepareMicrobiologicalResearch', 'tag := 4', '', NOW(), NOW());
INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
  (1050, 'Подготовка на "Микробиологично изследване"', 'Подготвят се данни за печат на "Микробиологично изследване"', 'bg', NOW()),
  (1050, 'Preparation for "Microbiological Research"', 'Prepare data for printing of "Microbiological Research"',     'en', NOW());
INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
  (1051, 'Document', '11', 'reprobiomed', 'prepareMicrobiologicalResearch', 'tag := 4', '', NOW(), NOW());
INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
  (1051, 'Подготовка на "Микробиологично изследване"', 'Подготвят се данни за печат на "Микробиологично изследване"', 'bg', NOW()),
  (1051, 'Preparation for "Microbiological Research"', 'Prepare data for printing of "Microbiological Research"',     'en', NOW());
INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
  (1052, 'Document', '12', 'reprobiomed', 'prepareMicrobiologicalResearch', 'tag := 4', '', NOW(), NOW());
INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
  (1052, 'Подготовка на "Микробиологично изследване"', 'Подготвят се данни за печат на "Микробиологично изследване"', 'bg', NOW()),
  (1052, 'Preparation for "Microbiological Research"', 'Prepare data for printing of "Microbiological Research"',     'en', NOW());
INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
  (1053, 'Document', '13', 'reprobiomed', 'prepareMicrobiologicalResearch', 'tag := 4', '', NOW(), NOW());
INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
  (1053, 'Подготовка на "Микробиологично изследване"', 'Подготвят се данни за печат на "Микробиологично изследване"', 'bg', NOW()),
  (1053, 'Preparation for "Microbiological Research"', 'Prepare data for printing of "Microbiological Research"',     'en', NOW());

# Added placeholders for Microbiological Research pattern plugin
UPDATE `placeholders` SET `pattern_id`=CONCAT(`pattern_id`, '1046,1047,1048,1049,1050,1051,1052,1053,')
WHERE `varname` IN ('patient_age_years') AND `model` = 'Document' AND  `usage` = 'pattern_plugins' AND `pattern_id` NOT LIKE '%,1046,1047,1048,1049,1050,1051,1052,1053,%';

######################################################################################
# 2013-02-13 - Fixed new lines in settings of Clinical Research pattern plugin

# Fixed new lines in settings of Clinical Research pattern plugin
UPDATE `patterns_plugins`
SET `settings` = 'tag := 4\r\nconfig_label_column_style := background-color: #DDDBDB; color: #696969; font-size: 20px; font-family: opalcyr; width: 20%; \r\nconfig_value_column_style := background-color: #EFEFEF; font-size: 18px; font-family: opalcyr;\r\ngt2_header_style := background-color: #DDDBDB!important;'
WHERE  `model` = 'Document' AND `model_type` = 8 AND `folder` = 'reprobiomed' AND `method` = 'prepareClinicalResearch';

######################################################################################
# 2013-02-14 - Copied Microbiological Research to 1 more document type 15

INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
  (1054, 'Document', '15', 'reprobiomed', 'prepareMicrobiologicalResearch', 'tag := 4', '', NOW(), NOW());
INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
  (1054, 'Подготовка на "Микробиологично изследване"', 'Подготвят се данни за печат на "Микробиологично изследване"', 'bg', NOW()),
  (1054, 'Preparation for "Microbiological Research"', 'Prepare data for printing of "Microbiological Research"',     'en', NOW());

# Added placeholders for Microbiological Research pattern plugin
UPDATE `placeholders` SET `pattern_id`=CONCAT(`pattern_id`, '1054,')
WHERE `varname` IN ('patient_age_years') AND `model` = 'Document' AND  `usage` = 'pattern_plugins' AND `pattern_id` NOT LIKE '%,1054,%';

######################################################################################
# 2013-04-03 - Modifications to reprobiomed_add_patient dashlet plugin

# Modifications to reprobiomed_add_patient dashlet plugin
UPDATE `dashlets_plugins` SET `settings` = 'customer_type_patient := 2\r\ncustomer_type_donor := 4\r\n' WHERE `dashlets_plugins`.`type`='reprobiomed_add_patient';

######################################################################################
# 2013-06-17 - Added dashlet plugin for adding of leaving form

# Added dashlet plugin for adding of leaving form
INSERT INTO `dashlets_plugins` (`id`, `type`, `settings`, `is_portal`, `visible`) VALUES
(NULL, 'reprobiomed_leaving_form', 'customer_type_patient := 2\r\nfir_type_leaving_form := 101\r\n\r\ncompany := 1\r\noffice := 1\r\ncashbox := 1\r\nbank_account := 1\r\ncurrency := BGN\r\ndepartment_all := 1\r\n\r\nemployee_doctor_var := is_a_doctor\r\n', 0, 1);
INSERT INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Обходен лист', 'Добавяне на обходен лист', 'bg'),
(LAST_INSERT_ID(), 'Leaving form', 'Add leaving form', 'en');

######################################################################################
# 2013-06-18 - Updated settings for the 'reprobiomed_schedule' report for ReproBiomed installation (1780)

# Updated settings for the 'reprobiomed_schedule' report for ReproBiomed installation (1780)
UPDATE `reports` SET `settings` = 'pacient_type_id := 2\r\ndoctor_days_week := days_week\r\ndoctor_work_start := work_start\r\ndoctor_work_finish := worrk_finish\r\ndoctor_time_period := time_period\r\ndoctor_break_start := break_start\r\ndoctor_break_finish := break_finish\r\n\r\nnomenclature_visit_type_id := 7\r\nactivity_var := examination_act\r\nactivity_plus_var := examination\r\nvisit_patient := patient_id\r\nvisit_start_date := start_date\r\nvisit_end_date := finish_date\r\nvisit_start_hour := start_hour\r\nvisit_end_hour := finish_hour\r\nvisit_notes := operation_other\r\nvisit_type:= reception\r\nvisit_doctor := doctor_id\r\n\r\nvisit_type_paid := 1\r\nvisit_type_free := 2\r\n\r\nvisit_status_refused := 3\r\nvisit_status := event_status\r\ndoctor_saturday_normal_working_time := 8\r\ndoctor_is_doctor := is_a_doctor\r\nvisit_status_patient_examined := 2\r\nvisit_status_patient_in_clinic := 4\r\nvisit_status_patient_not_presented := 1' WHERE `type`='reprobiomed_schedule';

######################################################################################
# 2013-07-17 - Added pattern plugin to prepare for printing of "Pass list" type financial incomes document
#            - Added placeholders for the pattern plugin

# Added pattern plugin to prepare for printing of "Pass list" type financial incomes document
INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
  ('51', 'Finance_Incomes_Reason', '101', 'reprobiomed', 'prepareFinanceIncomesPassList', 'total_field := total_with_vat', '', NOW(), NOW());
INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
  ('51', 'Подготовка на "Обходен лист"', 'Подготвят се данни за печат на приходен финансов документ от тип "Обходен лист"', 'bg', NOW()),
  ('51', 'Preparation for "Pass list"', 'Prepare data for printing of "Pass list" type financial incomes document',         'en', NOW());

# Added placeholders for the pattern plugin
INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  ('pass_lists_total_amount_due', 'Finance_Incomes_Reason', 'basic', 'pattern_plugins', ',51,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Обща дължима сума от обходни листи за контрагента', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Total amount due for pass lists of the customer',   NULL, 'en');

INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  ('current_pass_list_total_amount_due', 'Finance_Incomes_Reason', 'basic', 'pattern_plugins', ',51,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Дължима сума за текущият обходен лист', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Amount due for the current pass list',  NULL, 'en');

######################################################################################
# 2013-07-19 - Added pattern plugin to prepare for printing of "Cytological/histological research" type document
#            - Added placeholders for the pattern plugin

# Added pattern plugin to prepare for printing of "Cytological/histological research" type document
INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `added`, `modified`) VALUES
  ('52', 'Document', '23', 'reprobiomed', 'prepareCytologicalHistologicalResearch', 'url_to_img_check_yes := http://nzoom.bgservice.net/1780/resources/uploads/image/check_yes.png\r\nurl_to_img_check_no := http://nzoom.bgservice.net/1780/resources/uploads/image/check_no.png', NOW(), NOW());
INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
  ('52', 'Подготовка на "Цитологично/хистологично изследване"', 'Подготвят се данни за печат на документ от тип "Цитологично/хистологично изследване"', 'bg', NOW()),
  ('52', 'Preparation for "Cytological/histological research"', 'Prepare data for printing of "Cytological/histological research" type document',       'en', NOW());

# Added placeholders for the pattern plugin
UPDATE `placeholders`
  SET `pattern_id` = CONCAT(`pattern_id`, '52,')
  WHERE `varname` = 'patient_age_years'
    AND `model`   = 'Document'
    AND `type`    = 'basic'
    AND `usage`   = 'pattern_plugins'
    AND `pattern_id` NOT LIKE '%,52,%';

INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`) VALUES
  ('cellular_material_location', 'Document', 'basic', 'pattern_plugins', ',52,', '');
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Материала е взет от',         'bg'),
  (LAST_INSERT_ID(), 'The material was taken from', 'en');

INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`) VALUES
  ('colposcopic_image', 'Document', 'basic', 'pattern_plugins', ',52,', '');
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Колпоскопски образ', 'bg'),
  (LAST_INSERT_ID(), 'Colposcopic image',  'en');

INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`) VALUES
  ('recommendations', 'Document', 'basic', 'pattern_plugins', ',52,', '');
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Препоръки',       'bg'),
  (LAST_INSERT_ID(), 'Recommendations', 'en');

INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`) VALUES
  ('recommended_tests', 'Document', 'basic', 'pattern_plugins', ',52,', '');
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Препоръчителни тестове', 'bg'),
  (LAST_INSERT_ID(), 'Recommended tests',      'en');

INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`) VALUES
  ('results_interpretation_bb', 'Document', 'basic', 'pattern_plugins', ',52,', '');
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Интерпретация на резултат (таблица)', 'bg'),
  (LAST_INSERT_ID(), 'Result interpretation (table)',       'en');

# Changed ID of pattern plugin 'prepareFinanceIncomesPassList'
UPDATE `patterns_plugins`
  SET `id` = '51'
  WHERE `id` = '1060';
UPDATE `patterns_plugins_i18n`
  SET `parent_id` = '51'
  WHERE `parent_id` = '1060';
UPDATE `placeholders`
  SET `pattern_id` = REPLACE(`pattern_id`, ',1060,', ',51,')
  WHERE `model` = 'Finance_Incomes_Reason'
    AND `type`  = 'basic'
    AND `usage` = 'pattern_plugins'
    AND `pattern_id` LIKE '%,1060,%';
UPDATE `patterns`
  SET `plugin` = '51'
  WHERE `plugin` = '1060';

# Changed settings for pattern plugin 'prepareCytologicalHistologicalResearch'
UPDATE `patterns_plugins`
  SET `settings` = 'url_to_img_check_yes := uploads/image/check_yes.png\r\nurl_to_img_check_no := uploads/image/check_no.png'
  WHERE `method` = 'prepareCytologicalHistologicalResearch';

######################################################################################
# 2013-07-22 - Modified leaving forms (created from dashlet) to have id of doctor, not name, in invoice_code field

# Modified leaving forms (created from dashlet) to have id of doctor, not name, in invoice_code field
UPDATE `fin_incomes_reasons` fir, `customers_i18n` ci18n
SET fir.invoice_code=ci18n.parent_id
WHERE CONCAT(ci18n.name, ' ', ci18n.lastname)=fir.invoice_code;

######################################################################################
# 2013-07-23 - Added new report - 'reprobiomed_generate_examination' - for ReproBiomed installation (1780)

# Added new report - 'reprobiomed_generate_examination' - for ReproBiomed installation (1780)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(264, 'reprobiomed_generate_examination', 'nom_type_service := 10\r\nnom_laboratory_type := laboratory_type\r\nnom_include_in_report := include_report\r\nnom_document_type_to_create := type_recording\r\nnom_include_test := include_test_id\r\nnom_type_clinical := 8\r\nnom_type_clinical_test_unit := test_unit\r\nnom_type_clinical_reference_range := reference_range\r\n\r\ninclude_in_report_yes := 1\r\nfir_type_leaving_form := 101\r\nclinical_laboratory := 1\r\nmicrobiological_laboratory := 2\r\nhistological_laboratory := 3\r\ndna_laboratory := 4\r\nspermogram_article_id := 479\r\npap_article_id := 507\r\n\r\ncustomer_type_patient := 2\r\ncustomer_type_donor := 4\r\n\r\nmicrobiological_panel_target_noms := 133,132,131,296,139,129,297,141\r\nmicrobiological_panel_type_examination_var := type_research\r\nmicrobiological_panel_antimicrobial_therapy := antimicrobial_therapy_yesno\r\nmicrobiological_type_material_other_id := 5\r\nmicrobiological_examination_var := type_material\r\nmicrobiological_var_doctor_id := doctor_id\r\nmicrobiological_var_clinical_diagnosis := clinical_diagnosis\r\nmicrobiological_var_pregnancy_period := pregnancy_period\r\nmicrobiological_var_antimicrobial_therapy := antimicrobial_therapy\r\nmicrobiological_var_other_secretions := other_secretions\r\nmicrobiological_var_research_notes := research_notes\r\nmicrobiological_antimicrobiological_therapy_yes := 2\r\n\r\nclinical_panel_hormonal_noms := 129,130,131,132,133,134,135,136,137,138,139\r\nclinical_panel_pkk_biochemistry_noms := 129,140,141,142,143,144\r\nclinical_panel_hemostaziologiya := 129,140,141,145,142,143,146\r\nclinical_panel_urine := 129,141\r\nclinical_panel_sexually_transmissible_diseases := 129,147,148,131,132,133,134,135,139\r\nclinical_examination_var := biological_material\r\nclinical_var_doctor_id := doctor_id\r\nclinical_var_doctor_name := doctor\r\nclinical_var_hormonal_examination := hormonal\r\nclinical_var_prm := last_regular_menstruation\r\nclinical_var_prm_calculate := last_regular_menstruation__days\r\nclinical_var_amenorrhea := amenorrhea\r\nclinical_var_procedure_date := procedure_date\r\nclinical_var_procedure_date_calculated := procedure_date__days\r\nclinical_var_pkk_biochemistry := complete_blood_count\r\nclinical_var_hemostaziologiya := hemostaziologiya\r\nclinical_var_sexually_transmissible_diseases := sexually_transmissible_diseases\r\nclinical_var_urine := urine\r\n\r\nhistological_panel_examination_nom_ids := 133,132,131,296,139,129,297,141\r\nhistological_panel_examination_type_var := test_for\r\nhistological_panel_material_taken_var := cellular_material_location\r\nhistological_panel_expected_risk_var := expected_risk\r\nhistological_panel_colposcopic_image_var := colposcopic_image\r\nhistological_var_colposcopic_image_notes := colposcopic_image_notes\r\nhistological_var_clinical_diagnosis := clinical_diagnosis\r\nhistological_var_last_regular_menstruation := last_regular_menstruation\r\nhistological_var_last_regular_menstruation_days := last_regular_menstruation_days\r\nhistological_var_doctor_id := doctor_id\r\n\r\ndocument_clinical_examination := 8\r\ndocument_microbiology_examination := 1\r\ndocument_histological_examination := 23\r\ndocument_spermogram_examination := 15\r\ndocument_dna1_examination := 6,7,9,10,11,12,13\r\ndocument_dna2_examination := 17,18,19,20\r\n\r\ndna_examination_nom_ids := 253,254,255,256,257,258\r\ndna1_var_biological_material := biological_material\r\ndna_var_doctor_id := doctor_id\r\ndna_var_doctor_name := doctor\r\n\r\nspermogram_var_doctor_id := doctor_id\r\nspermogram_var_doctor_name := doctor\r\nspermogram_var_sexual_abstinence := sexual_abstinence\r\nspermogram_material_taken := material_taken\r\n\r\nmicrobiology_create_one_document_noms := 504,505,506\r\n\r\nclinical_hormonal_examination_noms := \r\nclinical_pkk_noms := \r\nclinical_urine_noms := \r\nclinical_hemostaziologiya_noms := \r\nclinical_sexually_transmissible_diseases_noms := ', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(264, 'Генерирай изследване', NULL, NULL, 'bg'),
(264, 'Generate examination', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 264, 0, 1);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND action='generate_report' AND model_type=264;

######################################################################################
# 2013-09-13 - Added new automation for ReproBiomed installation (1780) which is relating patient with partner when a file nomenclature is edited

# Added new automation for ReproBiomed installation (1780) which is relating patient with partner  when a file nomenclature is edited
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
  ('Свързване на Пациент и Пртньор', 0, NULL, 1, 'nomenclatures', NULL, 'action', 1, 'partner_id_var := partner_id\r\npartner_name_var := partner_name\r\npatient_id_var := patient_id\r\npatient_name_var := patient_name\r\n\r\ncustomer_related_id_var := partner_id\r\ncustomer_related_name_var := partner_name', 'condition := ''[a_patient_id]'' != ''[prev_a_patient_id]'' || ''[a_partner_id]'' != ''[prev_a_partner_id]''', 'plugin := repro_biomed\r\nmethod := relatePatientPartner', NULL, 0, 0);

######################################################################################
# 2013-11-13 - Updated settings for the 'reprobiomed_generate_examination' report for ReproBiomed installation (1780) to include some new vars that will be used in the future

# Updated settings for the 'reprobiomed_generate_examination' report for ReproBiomed installation (1780) to include some new vars that will be used in the future
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\nhistological_var_amenorrhea := amenorrhea\r\nhistological_var_prm := last_regular_menstruation\r\nhistological_var_prm_calculate := last_regular_menstruation_days') WHERE `type`='reprobiomed_generate_examination' AND `settings` NOT LIKE '%histological_var_amenorrhea%';

######################################################################################
# 2013-11-20 - Added automations that update RID (fin_field_1) in Leaving forms and change their status to finished

# Added automations that update RID (fin_field_1) in Leaving forms and change their status to finished
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Попълване на RID при приключване на Обходен лист', 0, NULL, 1, 'finance', 'incomes_reasons', 'action', '101', '', 'condition := ''[prev_b_status]'' != ''finished'' && ''[b_status]'' == ''finished''', 'plugin := repro_biomed\r\nmethod := setRID\r\n', NULL, 1, 1),
(NULL, 'Приключване на Обходни листа', 0, NULL, 1, 'finance', 'incomes_reasons', 'crontab', '101', 'start_time := 21:00\r\nstart_before := 23:00', 'condition := ''[b_status]'' != ''finished'' && ''[b_active]'' == ''1''\r\n\r\nfilter_sql_condition := status != ''finished'' AND active = 1', 'method := status\r\nnew_status := finished', NULL, 1, 1),
(NULL, 'Попълване на RID на приключени от crontab Обходни листа', 0, NULL, 1, 'finance', 'incomes_reasons', 'crontab', '101', 'start_time := 21:00\r\nstart_before := 23:00', 'condition := 1', 'plugin := repro_biomed\r\nmethod := setRIDCrontab', NULL, 2, 1);

######################################################################################
# 2013-12-05 - Changed the way of defining the patient/donor code (as per Bug 3787)

# Changed the way of defining the patient/donor code (as per Bug 3787)
UPDATE `dashlets_plugins` SET `settings`='customer_type_patient := 2\r\ncustomer_type_donor := 4\r\ncode_prefix := RBM\r\ncode_date_format := %Y%m%d' WHERE `type`='reprobiomed_add_patient' AND `settings` NOT LIKE '%code_prefix%';

######################################################################################
# 2013-12-06 - Added new report - 'reprobiomed_period_examinations' - for ReproBiomed installation (1780)

# Added new report - 'reprobiomed_period_examinations' - for ReproBiomed installation (1780)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (280, 'reprobiomed_period_examinations', 'document_chlamydia_dna_diagnostics := 7\r\ndocument_clinical_examination := 8\r\ncustomer_patient_type := 2\r\n\r\ntag_woman_id := 2\r\ntag_man_id := 3\r\n\r\nnom_syphilis_id := 120\r\nnom_hiv_id := 119', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (280, 'Изследвания за период', NULL, NULL, 'bg'),
  (280, 'Period examinations', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 280, 0, 1),
  ('reports', 'export', 279, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=280;

######################################################################################
# 2015-11-02 - Fixed relations between nomenclature file and patient/partner ReproBiomed installation (RBM)

# Fixed relations between nomenclature file and patient/partner ReproBiomed installation (RBM)
INSERT IGNORE INTO customers_trademarks (parent_id, trademark_id, is_default)
  SELECT nc.value, nom.id, 0
  FROM nom as nom
  INNER JOIN nom_cstm as nc
    ON (nc.model_id=nom.id AND nc.var_id=1 AND nc.value IS NOT NULL AND nc.value!="" AND nc.value!="0")
  WHERE nom.active=1 AND nom.deleted_by=0 AND nom.type=1;

INSERT IGNORE INTO customers_trademarks (parent_id, trademark_id, is_default)
  SELECT nc.value, nom.id, 0
  FROM nom as nom
  INNER JOIN nom_cstm as nc
    ON (nc.model_id=nom.id AND nc.var_id=3 AND nc.value IS NOT NULL AND nc.value!="" AND nc.value!="0")
  WHERE nom.active=1 AND nom.deleted_by=0 AND nom.type=1;

######################################################################################
# 2015-11-17 - Modified settings of dashlet for adding of leaving form
#            - Modified settings of free_field3 and article_name fields in leaving form
#            - Deleted incorrect additional variable belonging to SKR installation
#            - Modified settings of tag group for tags of leaving form
#            - Added button for redirect to leaving form dashlet from patient profile
#            - Added before_viewer automation to set some GT2 rows of "Direction for laboratory testing" document as readonly in edit mode
#            - Modified settings of free_field4, article_name and article_code fields in "Direction for laboratory testing"

# Modified settings of dashlet for adding of leaving form
UPDATE `dashlets_plugins`
SET `settings` = 'customer_type_patient := 1, 2, 4\r\nfir_type_leaving_form := 101\r\ndocument_type_direction := 29\r\n\r\ncompany := 1\r\noffice := 1\r\ncashbox := 1\r\nbank_account := 2\r\ncurrency := BGN\r\ndepartment_all := 1\r\n\r\nemployee_doctor_var := is_a_doctor\r\n\r\ntag_free := 17\r\ntag_cfar := 15\r\ntag_donor := 16\r\n\r\ntest_recommended := 1\r\ntest_performed := 2\r\ntest_denied := 3'
WHERE `type` = 'reprobiomed_leaving_form';

# Modified settings of free_field3 and article_name fields in leaving form
SELECT @pos := `position` FROM `_fields_meta` WHERE `name` = 'free_field3' AND `model` = 'Finance_Incomes_Reason' AND `model_type` = 101 AND `position` > 2;
UPDATE `_fields_meta`
SET `position` = `position` + 1
WHERE `model` = 'Finance_Incomes_Reason' AND `model_type` = 101 AND `position` > 1 AND `position` < @pos;

UPDATE `_fields_meta` f1, `_fields_meta` f2
SET
    f1.`type` = f2.`type`,
    f1.`source` = CONCAT(f2.`source`, '\njs_method := onchange => clearAutocompleteItems(window[''params_'' + this.up(''tr'').select(''input.autocompletebox.article_name'')[0].getAttribute(''uniqid'')], 1);'),
    f1.`hidden` = f2.`hidden`,
    f1.`readonly` = f2.`readonly`,
    f1.`position` = 2,
    f1.`width` = f2.`width`
WHERE f1.`id` = 101847 AND f2.`id` = 200508;

UPDATE `_fields_i18n` f1, `_fields_i18n` f2
SET f1.`content` = f2.`content`
WHERE f1.`parent_id` = 101847 AND f2.`parent_id` = 200508 AND f1.`lang` = f2.`lang` = f1.`content_type` = f2.`content_type`;

UPDATE `_fields_meta`
SET `source` = REPLACE(`source`, 'autocomplete_currency := $currency', 'autocomplete_filter := <category> => $free_field3\nautocomplete_currency := $currency')
WHERE `id` = 101793 AND `source` NOT LIKE '%autocomplete_filter := <category>%';

# Deleted incorrect additional variable belonging to SKR installation
DELETE FROM `_fields_meta` WHERE `id` = 101732;

# Modified settings of tag group for tags of leaving form
UPDATE `tags_sections` SET `tag_limit` = 1 WHERE `id` = 1;

# Added button for redirect to leaving form dashlet from patient profile
SELECT @btn_id := MAX(`id`) + 1 FROM `_fields_meta` WHERE `model` = 'Customer' AND `model_type` = 2;

INSERT IGNORE INTO `_fields_meta` (`id`, `model`, `model_type`, `name`, `type`, `searchable`, `sortable`, `source`, `validate`, `required`, `hidden`, `readonly`, `calculate`, `bb`, `grouping`, `gt2`, `configurator`, `table`, `multilang`, `multiadd`, `multiedit`, `auditable`, `layout_id`, `position`, `width`, `width_print`, `height`) VALUES
(@btn_id, 'Customer', 2, 'leaving_form_button', 'button', '', 0, 'onclick := document.cookie = ''rlf_patient='' + $(''id'').value;\r\nhref := index\r\ntarget := _self\r\npermissions := view,edit', '', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', '');

INSERT IGNORE INTO `_fields_i18n` (`parent_id`, `content_type`, `content`, `lang`)
SELECT `id`, 'label', 'Зареждане в панел Обходен лист', 'bg'
FROM `_fields_meta` WHERE `model` = 'Customer' AND `model_type` = 2 AND `name` = 'leaving_form_button';

# Added before_viewer automation to set some GT2 rows of "Direction for laboratory testing" document as readonly in edit mode
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'В Редакция на "Направление за ЛИ" редовете с направени и отказани изследвания стават само за четене', 0, NULL, 1, 'documents', NULL, 'before_viewer', '29', 'check_field := free_field4\r\ncheck_value := 2,3', 'condition := ''[action]'' == ''edit''', 'plugin := repro_biomed\r\nmethod := setReadonlyGT2Rows', NULL, 1, 0, 1);

# Modified settings of free_field4, article_name and article_code fields in "Direction for laboratory testing"
UPDATE `_fields_meta` SET `readonly` = 1 WHERE `id` = 200509;

UPDATE `_fields_meta`
SET `source` = REPLACE(`source`, 'autocomplete_fill_options := $price => <sell_price>', 'autocomplete_fill_options := $price => <sell_price>\r\nautocomplete_fill_options := $free_field4 => 1')
WHERE `id` IN (200454, 200470) AND `source` NOT LIKE '%autocomplete_fill_options := $free_field4%';

######################################################################################
# 2015-11-20 - Modified settings of some of the autocompleters to use optional filter for category

# Modified settings of some of the autocompleters to use optional filter for category
UPDATE _fields_meta SET source=REPLACE(source, 'autocomplete_filter := <category> =>', 'autocomplete_optional_filter := <category> =>') WHERE source LIKE '%autocomplete_filter := <category> =>%';

######################################################################################
# 2015-11-24 - Removed settings for code from dashlet for adding of patient
#            - Modified settings for bank accounts for dashlet for leaving form
#            - Removed settings of free_field3 field in leaving form

# Removed settings for code from dashlet for adding of patient
UPDATE `dashlets_plugins` SET `settings` = REPLACE(`settings`, '\r\ncode_prefix := RBM\r\ncode_date_format := %Y%m%d', '')
WHERE `type` = 'reprobiomed_add_patient' AND `settings` LIKE '%code_prefix%';

# Modified settings for bank accounts for dashlet for leaving form
UPDATE `dashlets_plugins` SET `settings` = REPLACE(`settings`, 'bank_account := 2', 'bank_account_bank := 1\r\nbank_account_card := 2')
WHERE `type` = 'reprobiomed_leaving_form' AND `settings` NOT LIKE '%bank_account_bank%';

# Removed settings of free_field3 field in leaving form
UPDATE `_fields_meta`
SET `source` = REPLACE(`source`, '\njs_method := onchange => clearAutocompleteItems(window[''params_'' + this.up(''tr'').select(''input.autocompletebox.article_name'')[0].getAttribute(''uniqid'')], 1);', '')
WHERE `id` = 101847;

######################################################################################
# 2015-12-17 - Changed settings of 'reprobiomed_generate_examination' to update the new conditions for creating documents for spermal laboratory

# Changed settings of 'reprobiomed_generate_examination' to update the new conditions for creating documents for spermal laboratory
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\nspermogram_article_id', '\r\nsperm_laboratory := 5\r\nspermogram_article_id') WHERE `type`='reprobiomed_generate_examination' AND `settings` NOT LIKE '%sperm_laboratory%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\nspermogram_article_id := 479', '') WHERE `type`='reprobiomed_generate_examination' AND `settings` LIKE '%spermogram_article_id := 479%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ndocument_spermogram_examination := 15', '\r\ndocument_spermogram1_examination := 15,27\r\ndocument_spermogram2_examination := 14,24') WHERE `type`='reprobiomed_generate_examination' AND `settings` NOT LIKE '%document_spermogram1_examination%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ndocument_dna2_examination := 17,18,19,20,25,14,24,26,27', '\r\ndocument_dna2_examination := 17,18,19,20,25,26') WHERE `type`='reprobiomed_generate_examination' AND `settings` LIKE '%document_dna2_examination := 17,18,19,20,25,14,24,26,27%';

######################################################################################
# 2016-01-06 - Added new report - 'reprobiomed_daily_report' - for ReproBiomed installation (RBM)

# Added new report - 'reprobiomed_daily_report' - for ReproBiomed installation (RBM)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (339, 'reprobiomed_daily_report', 'finance_type_work_leaving_form := 101\r\nfinance_type_expense := 102\r\n\r\nwork_leaving_form_fond_tag := 15\r\nwork_leaving_form_donor_tag := 16\r\nwork_leaving_form_free_tag := 17\r\n\r\ncustomer_tag_man := 3\r\ncustomer_tag_woman := 2\r\n\r\nincluded_nom_categories := 46,47,48,49\r\n\r\ncontainer_cash := cashbox_1\r\ncontainer_card := bank_account_2\r\ncontainer_bank := bank_account_1', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (339, 'Дневен отчет', NULL, NULL, 'bg'),
  (339, 'Daily report', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 339, 0, 1),
  ('reports', 'export', 339, 0, 2);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND (action='generate_report' OR action='export') AND `model_type` = 339;

######################################################################################
# 2016-01-12 - Added new report - 'reprobiomed_ambulatory_and_laboratory' - for ReproBiomed installation (RBM)

# Added new report - 'reprobiomed_ambulatory_and_laboratory' - for ReproBiomed installation (RBM)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (340, 'reprobiomed_ambulatory_and_laboratory', 'finance_type_work_leaving_form := 101\r\nfinance_type_expense := 102\r\n\r\nwork_leaving_form_fond_tag := 15\r\nwork_leaving_form_donor_tag := 16\r\nwork_leaving_form_free_tag := 17\r\n\r\ncustomer_tag_man := 3\r\ncustomer_tag_woman := 2\r\n\r\nambulatory_categories := 46,47,48\r\nlaboratory_categories := 49\r\n\r\ncontainer_cash := cashbox_1\r\ncontainer_card := bank_account_2\r\ncontainer_bank := bank_account_1', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (340, 'Амбулатори и лаборатория', NULL, NULL, 'bg'),
  (340, 'Ambulatory and laboratory', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 340, 0, 1),
  ('reports', 'export', 340, 0, 2);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND (action='generate_report' OR action='export') AND `model_type` = 340;

######################################################################################
# 2016-01-18 - Fix the name of 'reprobiomed_ambulatory_and_laboratory' report

# Fix the name of 'reprobiomed_ambulatory_and_laboratory' report
UPDATE `reports_i18n` SET `name`='Амбулатория и Лаборатория' WHERE `name`='Амбулатори и Лаборатория' AND `parent_id`='340' AND `lang`='bg';

######################################################################################
# 2016-01-27 - Added new report - 'reprobiomed_patient_file' - for ReproBiomed installation (RBM)
#            - Added custom view to be used in 'reprobiomed_patient_file' report

# PRE-DEPLOYED # Added new report - 'reprobiomed_patient_file' - for ReproBiomed installation (RBM)
#INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
#  (342, 'reprobiomed_patient_file', 'patient_type := 2\r\n\r\npatient_partner := partner_id\r\n\r\nmandatory_categories := 46,47,48,49\r\n\r\ndoc_types_microbiologic := 1\r\ndoc_types_histological := 23\r\ndoc_types_dna := 7,9,10,11,12,17,18,19,20,25,30\r\ndoc_types_spermogram := 15\r\ndoc_types_hpv_screening := 13\r\ndoc_types_clinical := 8\r\ndoc_types_hpv_genotype := 6\r\ndoc_types_hormonal := 8\r\n\r\nclinical_nom_categories := 23,24,25,26,28,29\r\nhormonal_nom_categories := 27\r\n\r\nwork_leaving_form_id := 101\r\n\r\nhistologic_pap_group := pap_group\r\nhistologic_general_categorization := general_categorization\r\nhistologic_recomendations := recommendations\r\nhistologic_recomended_test := recommended_tests\r\nhistologic_next_smear := next_smear\r\n\r\nmicrobiologic_microscope_slide := microscope_slide\r\n\r\ndna_result_1 := result\r\ndna_result_2 := dna_result\r\n\r\nspermogram_cfs := cfs\r\nspermogram_sperm_diagnosis := sperm_diagnosis\r\n\r\nhpv_screening_result := result\r\nhpv_screening_concentration := hpv_concentration\r\n\r\nhpv_genotype_type := hpv_type\r\nhpv_genotype_result := result\r\n\r\nhormonal_clinical_results := clinical_examination_result', 0, 0, 1);
#INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
#  (342, 'Досие на пациент', NULL, NULL, 'bg'),
#  (342, 'Patient file', NULL, NULL, 'en');
#INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
#  ('reports', 'generate_report', 342, 0, 1),
#  ('reports', 'export', 342, 0, 2);
#INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
#  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND (action='generate_report' OR action='export') AND `model_type` = 342;

# PRE-DEPLOYED # Added custom view to be used in 'reprobiomed_patient_file' report
#DROP VIEW IF EXISTS _report_patient_file_config ;
#CREATE DEFINER = 'nzoomer'@'localhost' VIEW _report_patient_file_config (label, col1, col2, col3, col4, num, model_id) AS
#SELECT fmi18n.content as label, fo1.label as col1, d_cstm_col2.value as col2, fo3.label as col3, fo4.label as col4, 1 as num, d.id as model_id
# FROM documents as d
# LEFT JOIN _fields_i18n as fmi18n
#   ON (fmi18n.content_type="label" AND fmi18n.parent_id=110)
# LEFT JOIN documents_cstm as d_cstm_col1
#   ON (d_cstm_col1.var_id=110 AND d_cstm_col1.model_id=d.id)
# LEFT JOIN _fields_options as fo1
#   ON (fo1.parent_name="pathogens" AND fo1.option_value=d_cstm_col1.value AND fo1.lang="bg")
# LEFT JOIN documents_cstm as d_cstm_col2
#   ON (d_cstm_col2.var_id=111 AND d_cstm_col2.model_id=d.id)
# LEFT JOIN documents_cstm as d_cstm_col3
#   ON (d_cstm_col3.var_id=136 AND d_cstm_col3.model_id=d.id)
# LEFT JOIN _fields_options as fo3
#   ON (fo3.parent_name="mycoplasma_ureaplasma__comparison" AND fo3.option_value=d_cstm_col3.value AND fo3.lang="bg")
# LEFT JOIN documents_cstm as d_cstm_col4
#   ON (d_cstm_col4.var_id=112 AND d_cstm_col4.model_id=d.id)
# LEFT JOIN _fields_options as fo4
#   ON (fo4.parent_name="pathogens__quantity" AND fo4.option_value=d_cstm_col4.value AND fo4.lang="bg")
# WHERE d.active=1 AND d.type=1 AND d.deleted_by=0 AND ((fo1.label IS NOT NULL AND fo1.label!="") OR (d_cstm_col2.value IS NOT NULL AND d_cstm_col2.value!="") OR (fo3.label IS NOT NULL AND fo3.label!="") OR (fo4.label IS NOT NULL AND fo4.label!=""))
#
#UNION
#
#SELECT fmi18n.content as label, fo1.label as col1, d_cstm_col2.value as col2, NULL as col3, fo4.label as col4, 2 as num, d.id as model_id
# FROM documents as d
# LEFT JOIN _fields_i18n as fmi18n
#   ON (fmi18n.content_type="label" AND fmi18n.parent_id=113)
# LEFT JOIN documents_cstm as d_cstm_col1
#   ON (d_cstm_col1.var_id=113 AND d_cstm_col1.model_id=d.id)
# LEFT JOIN _fields_options as fo1
#   ON (fo1.parent_name="candida" AND fo1.option_value=d_cstm_col1.value AND fo1.lang="bg")
# LEFT JOIN documents_cstm as d_cstm_col2
#   ON (d_cstm_col2.var_id=114 AND d_cstm_col2.model_id=d.id)
# LEFT JOIN _fields_options as fo2
#   ON (fo2.parent_name="candida__pathogen" AND fo2.option_value=d_cstm_col2.value AND fo2.lang="bg")
# LEFT JOIN documents_cstm as d_cstm_col4
#   ON (d_cstm_col4.var_id=115 AND d_cstm_col4.model_id=d.id)
# LEFT JOIN _fields_options as fo4
#   ON (fo4.parent_name="candida__quantity" AND fo4.option_value=d_cstm_col4.value AND fo4.lang="bg")
# WHERE d.active=1 AND d.type=1 AND d.deleted_by=0 AND ((fo1.label IS NOT NULL AND fo1.label!="") OR (fo2.label IS NOT NULL AND fo2.label!="") OR (fo4.label IS NOT NULL AND fo4.label!=""))
#
#UNION
#
#SELECT fmi18n.content as label, fo1.label as col1, NULL as col2, NULL as col3, NULL as col4, 3 as num, d.id as model_id
# FROM documents as d
# LEFT JOIN _fields_i18n as fmi18n
#   ON (fmi18n.content_type="label" AND fmi18n.parent_id=116)
# LEFT JOIN documents_cstm as d_cstm_col1
#   ON (d_cstm_col1.var_id=116 AND d_cstm_col1.model_id=d.id)
# LEFT JOIN _fields_options as fo1
#   ON (fo1.parent_name="trichomonas_vaginalis" AND fo1.option_value=d_cstm_col1.value AND fo1.lang="bg")
# WHERE d.active=1 AND d.type=1 AND d.deleted_by=0 AND fo1.label IS NOT NULL AND fo1.label!=""
#
#UNION
#
#SELECT fmi18n.content as label, fo1.label as col1, NULL as col2, fo3.label as col3, fo4.label as col4, 4 as num, d.id as model_id
# FROM documents as d
# LEFT JOIN _fields_i18n as fmi18n
#   ON (fmi18n.content_type="label" AND fmi18n.parent_id=117)
# LEFT JOIN documents_cstm as d_cstm_col1
#   ON (d_cstm_col1.var_id=117 AND d_cstm_col1.model_id=d.id)
# LEFT JOIN _fields_options as fo1
#   ON (fo1.parent_name="mycoplasma_ureaplasma" AND fo1.option_value=d_cstm_col1.value AND fo1.lang="bg")
# LEFT JOIN documents_cstm as d_cstm_col3
#   ON (d_cstm_col3.var_id=130 AND d_cstm_col3.model_id=d.id)
# LEFT JOIN _fields_options as fo3
#   ON (fo3.parent_name="mycoplasma_ureaplasma__comparison" AND fo3.option_value=d_cstm_col3.value AND fo3.lang="bg")
# LEFT JOIN documents_cstm as d_cstm_col4
#   ON (d_cstm_col4.var_id=131 AND d_cstm_col4.model_id=d.id)
# LEFT JOIN _fields_options as fo4
#   ON (fo4.parent_name="mycoplasma_ureaplasma__quantity" AND fo4.option_value=d_cstm_col4.value AND fo4.lang="bg")
# WHERE d.active=1 AND d.type=1 AND d.deleted_by=0 AND ((fo1.label IS NOT NULL AND fo1.label!="") OR (fo3.label IS NOT NULL AND fo3.label!="") OR (fo4.label IS NOT NULL AND fo4.label!=""))
#
#UNION
#
#SELECT fmi18n.content as label, fo1.label as col1, NULL as col2, fo3.label as col3, fo4.label as col4, 5 as num, d.id as model_id
# FROM documents as d
# LEFT JOIN _fields_i18n as fmi18n
#   ON (fmi18n.content_type="label" AND fmi18n.parent_id=132)
# LEFT JOIN documents_cstm as d_cstm_col1
#   ON (d_cstm_col1.var_id=132 AND d_cstm_col1.model_id=d.id)
# LEFT JOIN _fields_options as fo1
#   ON (fo1.parent_name="ureaplasma" AND fo1.option_value=d_cstm_col1.value AND fo1.lang="bg")
# LEFT JOIN documents_cstm as d_cstm_col3
#   ON (d_cstm_col3.var_id=133 AND d_cstm_col3.model_id=d.id)
# LEFT JOIN _fields_options as fo3
#   ON (fo3.parent_name="mycoplasma_ureaplasma__comparison" AND fo3.option_value=d_cstm_col3.value AND fo3.lang="bg")
# LEFT JOIN documents_cstm as d_cstm_col4
#   ON (d_cstm_col4.var_id=134 AND d_cstm_col4.model_id=d.id)
# LEFT JOIN _fields_options as fo4
#   ON (fo4.parent_name="mycoplasma_ureaplasma__quantity" AND fo4.option_value=d_cstm_col4.value AND fo4.lang="bg")
# WHERE d.active=1 AND d.type=1 AND d.deleted_by=0 AND ((fo1.label IS NOT NULL AND fo1.label!="") OR (fo3.label IS NOT NULL AND fo3.label!="") OR (fo4.label IS NOT NULL AND fo4.label!=""))
#
#UNION
#
#SELECT fmi18n.content as label, d_cstm_col1.value as col1, NULL as col2, NULL as col3, NULL as col4, 6 as num, d.id as model_id
# FROM documents as d
# LEFT JOIN _fields_i18n as fmi18n
#   ON (fmi18n.content_type="label" AND fmi18n.parent_id=118)
# LEFT JOIN documents_cstm as d_cstm_col1
#   ON (d_cstm_col1.var_id=118 AND d_cstm_col1.model_id=d.id)
# WHERE d.active=1 AND d.type=1 AND d.deleted_by=0 AND d_cstm_col1.value IS NOT NULL AND d_cstm_col1.value!="";

######################################################################################
# 2016-03-01 - Added additional settings for 'reprobiomed_daily_report' report for ReproBiomed installation (RBM)

# Added additional settings for 'reprobiomed_daily_report' report for ReproBiomed installation (RBM)
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\nemployee_doctor_var := is_a_doctor\r\ndoctors_categories := 46,47,48,49') WHERE `type`='reprobiomed_daily_report' AND `settings` NOT LIKE '%employee_doctor_var%';

######################################################################################
# 2016-05-13 - Updated conditions of crontab automations

# Updated conditions of crontab automations
UPDATE `automations` SET `conditions` = 'where := fir.active = 1\r\nwhere := fir.status != ''finished''' WHERE `id` = 21;

######################################################################################
# 2016-10-19 - Copied Microbiological Research to 1 more document type 32

# PRE-DEPLOYED # Copied Microbiological Research to 1 more document type 32
# INSERT INTO `patterns_plugins` (`model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
#   ('Document', '32', 'reprobiomed', 'prepareMicrobiologicalResearch', 'tag := 4', '', NOW(), NOW());
# INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
#   (LAST_INSERT_ID(), 'Подготовка на "Микробиологично изследване"', 'Подготвят се данни за печат на "Микробиологично изследване"', 'bg', NOW()),
#   (LAST_INSERT_ID(), 'Preparation for "Microbiological Research"', 'Prepare data for printing of "Microbiological Research"', 'en', NOW());
# UPDATE `placeholders`
#   SET `pattern_id` = CONCAT(`pattern_id`, LAST_INSERT_ID(), ',')
#   WHERE `model` = 'Document'
#     AND `usage` = 'pattern_plugins'
#     AND `varname` = 'patient_age_years'
#     AND `pattern_id` NOT LIKE CONCAT('%,', LAST_INSERT_ID(), ',%');

######################################################################################
# 2019-05-22 - Added additional settings for 'reprobiomed_patient_file' report for ReproBiomed installation (RBM)

# Added additional settings for 'reprobiomed_patient_file' report for ReproBiomed installation (RBM)
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\nhormonal_export_noms := 86,85,90,88,84,89,95\r\nclinical_export_noms := 119,120,117,116,118,87,66') WHERE `type`='reprobiomed_patient_file' AND `settings` NOT LIKE '%hormonal_export_noms%';

######################################################################################
# 2019-05-29 - Added 'dna_chlamydia_document_type' settings for 'reprobiomed_patient_file' report for ReproBiomed installation (RBM)

# Added 'dna_chlamydia_document_type' settings for 'reprobiomed_patient_file' report for ReproBiomed installation (RBM)
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\ndna_chlamydia_document_type := 7') WHERE `type`='reprobiomed_patient_file' AND `settings` NOT LIKE '%dna_chlamydia_document_type%';

######################################################################################
# 2020-12-17 - Added setting to contain the tag for clinic pack in 'reprobiomed_leaving_form' dashlet

# Added setting to contain the tag for clinic pack in 'reprobiomed_leaving_form' dashlet
UPDATE `dashlets_plugins` SET `settings` = REPLACE(`settings`, '\r\n\r\ntest_recommended :=', '\r\ntag_clinicpack := 18\r\n\r\ntest_recommended :=') WHERE `type`='reprobiomed_leaving_form' AND `settings` NOT LIKE '%tag_clinicpack%';

######################################################################################
# 2021-01-11 - Added alternative query to add the setting to contain the tag for clinic pack in 'reprobiomed_leaving_form' dashlet

# Added setting to contain the tag for clinic pack in 'reprobiomed_leaving_form' dashlet
UPDATE `dashlets_plugins` SET `settings` = REPLACE(`settings`, '\n\ntest_recommended :=', '\ntag_clinicpack := 18\n\ntest_recommended :=') WHERE `type`='reprobiomed_leaving_form' AND `settings` NOT LIKE '%tag_clinicpack%';
