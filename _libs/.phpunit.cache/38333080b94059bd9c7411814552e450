a:6:{s:9:"classesIn";a:1:{s:26:"Nzoom\Export\ExportService";a:6:{s:4:"name";s:13:"ExportService";s:14:"namespacedName";s:26:"Nzoom\Export\ExportService";s:9:"namespace";s:12:"Nzoom\Export";s:9:"startLine";i:13;s:7:"endLine";i:359;s:7:"methods";a:17:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:81:"__construct(Registry $registry, string $module, string $controller, string $type)";s:10:"visibility";s:6:"public";s:9:"startLine";i:69;s:7:"endLine";i:76;s:3:"ccn";i:1;}s:12:"setModelName";a:6:{s:10:"methodName";s:12:"setModelName";s:9:"signature";s:31:"setModelName(string $modelName)";s:10:"visibility";s:6:"public";s:9:"startLine";i:84;s:7:"endLine";i:88;s:3:"ccn";i:1;}s:19:"setModelFactoryName";a:6:{s:10:"methodName";s:19:"setModelFactoryName";s:9:"signature";s:45:"setModelFactoryName(string $modelFactoryName)";s:10:"visibility";s:6:"public";s:9:"startLine";i:96;s:7:"endLine";i:100;s:3:"ccn";i:1;}s:18:"createExportAction";a:6:{s:10:"methodName";s:18:"createExportAction";s:9:"signature";s:83:"createExportAction(string $module_check, array $types, array $typeSections): ?array";s:10:"visibility";s:6:"public";s:9:"startLine";i:110;s:7:"endLine";i:124;s:3:"ccn";i:1;}s:16:"createExportData";a:6:{s:10:"methodName";s:16:"createExportData";s:9:"signature";s:113:"createExportData(Outlook $outlook, array $filters, string $modelClass, $pageSize): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:127;s:7:"endLine";i:131;s:3:"ccn";i:1;}s:26:"createExportDataWithTables";a:6:{s:10:"methodName";s:26:"createExportDataWithTables";s:9:"signature";s:123:"createExportDataWithTables(Outlook $outlook, array $filters, string $modelClass, $pageSize): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:133;s:7:"endLine";i:138;s:3:"ccn";i:1;}s:27:"createGeneratorFileStreamer";a:6:{s:10:"methodName";s:27:"createGeneratorFileStreamer";s:9:"signature";s:154:"createGeneratorFileStreamer(callable $generatorFunction, string $filename, string $mimeType, ?int $totalSize): Nzoom\Export\Streamer\GeneratorFileStreamer";s:10:"visibility";s:6:"public";s:9:"startLine";i:149;s:7:"endLine";i:152;s:3:"ccn";i:1;}s:6:"export";a:6:{s:10:"methodName";s:6:"export";s:9:"signature";s:84:"export(string $filename, Nzoom\Export\Entity\ExportData $data, array $options): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:163;s:7:"endLine";i:180;s:3:"ccn";i:3;}s:16:"createTempStream";a:6:{s:10:"methodName";s:16:"createTempStream";s:9:"signature";s:18:"createTempStream()";s:10:"visibility";s:7:"private";s:9:"startLine";i:188;s:7:"endLine";i:197;s:3:"ccn";i:2;}s:15:"streamToBrowser";a:6:{s:10:"methodName";s:15:"streamToBrowser";s:9:"signature";s:74:"streamToBrowser($stream, string $downloadFilename, string $mimeType): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:208;s:7:"endLine";i:231;s:3:"ccn";i:3;}s:16:"getFormatFactory";a:6:{s:10:"methodName";s:16:"getFormatFactory";s:9:"signature";s:60:"getFormatFactory(): Nzoom\Export\Factory\ExportFormatFactory";s:10:"visibility";s:6:"public";s:9:"startLine";i:239;s:7:"endLine";i:246;s:3:"ccn";i:2;}s:16:"setFormatFactory";a:6:{s:10:"methodName";s:16:"setFormatFactory";s:9:"signature";s:79:"setFormatFactory(Nzoom\Export\Factory\ExportFormatFactory $formatFactory): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:254;s:7:"endLine";i:258;s:3:"ccn";i:1;}s:10:"getAdapter";a:6:{s:10:"methodName";s:10:"getAdapter";s:9:"signature";s:63:"getAdapter(): Nzoom\Export\Adapter\ExportFormatAdapterInterface";s:10:"visibility";s:6:"public";s:9:"startLine";i:266;s:7:"endLine";i:273;s:3:"ccn";i:2;}s:19:"getSupportedFormats";a:6:{s:10:"methodName";s:19:"getSupportedFormats";s:9:"signature";s:28:"getSupportedFormats(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:280;s:7:"endLine";i:283;s:3:"ccn";i:1;}s:17:"isFormatSupported";a:6:{s:10:"methodName";s:17:"isFormatSupported";s:9:"signature";s:39:"isFormatSupported(string $format): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:291;s:7:"endLine";i:294;s:3:"ccn";i:1;}s:17:"getExportFilename";a:6:{s:10:"methodName";s:17:"getExportFilename";s:9:"signature";s:45:"getExportFilename($prefix, string $extension)";s:10:"visibility";s:9:"protected";s:9:"startLine";i:303;s:7:"endLine";i:323;s:3:"ccn";i:4;}s:17:"handleExportError";a:6:{s:10:"methodName";s:17:"handleExportError";s:9:"signature";s:40:"handleExportError($message, $statusCode)";s:10:"visibility";s:9:"protected";s:9:"startLine";i:333;s:7:"endLine";i:358;s:3:"ccn";i:4;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:360;s:18:"commentLinesOfCode";i:150;s:21:"nonCommentLinesOfCode";i:210;}s:15:"ignoredLinesFor";a:1:{i:0;i:13;}s:17:"executableLinesIn";a:78:{i:71;i:11;i:72;i:12;i:73;i:13;i:74;i:14;i:75;i:15;i:86;i:16;i:87;i:17;i:98;i:18;i:99;i:19;i:113;i:20;i:114;i:20;i:115;i:20;i:116;i:20;i:117;i:20;i:118;i:20;i:119;i:20;i:120;i:20;i:123;i:21;i:129;i:22;i:130;i:23;i:135;i:24;i:136;i:25;i:137;i:26;i:151;i:27;i:163;i:28;i:168;i:29;i:170;i:30;i:171;i:31;i:174;i:32;i:175;i:33;i:176;i:34;i:177;i:35;i:178;i:36;i:190;i:37;i:192;i:38;i:193;i:39;i:196;i:40;i:210;i:41;i:211;i:42;i:216;i:43;i:217;i:43;i:218;i:43;i:219;i:43;i:220;i:43;i:223;i:44;i:227;i:45;i:228;i:46;i:241;i:47;i:242;i:48;i:245;i:49;i:256;i:50;i:257;i:51;i:268;i:52;i:269;i:53;i:272;i:54;i:282;i:55;i:293;i:56;i:306;i:57;i:309;i:58;i:310;i:59;i:313;i:60;i:314;i:61;i:318;i:62;i:319;i:63;i:322;i:64;i:336;i:65;i:337;i:66;i:341;i:67;i:342;i:67;i:343;i:67;i:344;i:67;i:345;i:67;i:348;i:68;i:349;i:69;i:351;i:70;i:352;i:71;i:353;i:72;i:354;i:73;}}