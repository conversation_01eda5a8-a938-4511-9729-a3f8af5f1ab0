a:6:{s:9:"classesIn";a:1:{s:30:"Nzoom\Export\Entity\ExportData";a:6:{s:4:"name";s:10:"ExportData";s:14:"namespacedName";s:30:"Nzoom\Export\Entity\ExportData";s:9:"namespace";s:19:"Nzoom\Export\Entity";s:9:"startLine";i:12;s:7:"endLine";i:507;s:7:"methods";a:30:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:70:"__construct(Nzoom\Export\Entity\ExportHeader $header, array $metadata)";s:10:"visibility";s:6:"public";s:9:"startLine";i:60;s:7:"endLine";i:64;s:3:"ccn";i:1;}s:17:"setRecordProvider";a:6:{s:10:"methodName";s:17:"setRecordProvider";s:9:"signature";s:64:"setRecordProvider(callable $recordProvider, int $pageSize): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:74;s:7:"endLine";i:84;s:3:"ccn";i:1;}s:6:"isLazy";a:6:{s:10:"methodName";s:6:"isLazy";s:9:"signature";s:14:"isLazy(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:91;s:7:"endLine";i:94;s:3:"ccn";i:1;}s:11:"getPageSize";a:6:{s:10:"methodName";s:11:"getPageSize";s:9:"signature";s:18:"getPageSize(): int";s:10:"visibility";s:6:"public";s:9:"startLine";i:101;s:7:"endLine";i:104;s:3:"ccn";i:1;}s:11:"setPageSize";a:6:{s:10:"methodName";s:11:"setPageSize";s:9:"signature";s:32:"setPageSize(int $pageSize): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:112;s:7:"endLine";i:116;s:3:"ccn";i:1;}s:9:"getHeader";a:6:{s:10:"methodName";s:9:"getHeader";s:9:"signature";s:45:"getHeader(): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:6:"public";s:9:"startLine";i:123;s:7:"endLine";i:126;s:3:"ccn";i:1;}s:9:"setHeader";a:6:{s:10:"methodName";s:9:"setHeader";s:9:"signature";s:57:"setHeader(Nzoom\Export\Entity\ExportHeader $header): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:133;s:7:"endLine";i:136;s:3:"ccn";i:1;}s:11:"getMetadata";a:6:{s:10:"methodName";s:11:"getMetadata";s:9:"signature";s:20:"getMetadata(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:143;s:7:"endLine";i:146;s:3:"ccn";i:1;}s:11:"setMetadata";a:6:{s:10:"methodName";s:11:"setMetadata";s:9:"signature";s:34:"setMetadata(array $metadata): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:153;s:7:"endLine";i:156;s:3:"ccn";i:1;}s:16:"getMetadataValue";a:6:{s:10:"methodName";s:16:"getMetadataValue";s:9:"signature";s:39:"getMetadataValue(string $key, $default)";s:10:"visibility";s:6:"public";s:9:"startLine";i:165;s:7:"endLine";i:168;s:3:"ccn";i:1;}s:16:"setMetadataValue";a:6:{s:10:"methodName";s:16:"setMetadataValue";s:9:"signature";s:43:"setMetadataValue(string $key, $value): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:176;s:7:"endLine";i:179;s:3:"ccn";i:1;}s:9:"addRecord";a:6:{s:10:"methodName";s:9:"addRecord";s:9:"signature";s:73:"addRecord(Nzoom\Export\Entity\ExportRecord $record, bool $validate): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:190;s:7:"endLine";i:201;s:3:"ccn";i:4;}s:10:"getRecords";a:6:{s:10:"methodName";s:10:"getRecords";s:9:"signature";s:19:"getRecords(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:209;s:7:"endLine";i:221;s:3:"ccn";i:3;}s:11:"getRecordAt";a:6:{s:10:"methodName";s:11:"getRecordAt";s:9:"signature";s:58:"getRecordAt(int $index): ?Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:6:"public";s:9:"startLine";i:230;s:7:"endLine";i:245;s:3:"ccn";i:4;}s:5:"count";a:6:{s:10:"methodName";s:5:"count";s:9:"signature";s:12:"count(): int";s:10:"visibility";s:6:"public";s:9:"startLine";i:252;s:7:"endLine";i:259;s:3:"ccn";i:2;}s:12:"createRecord";a:6:{s:10:"methodName";s:12:"createRecord";s:9:"signature";s:63:"createRecord(array $metadata): Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:6:"public";s:9:"startLine";i:269;s:7:"endLine";i:278;s:3:"ccn";i:2;}s:7:"isEmpty";a:6:{s:10:"methodName";s:7:"isEmpty";s:9:"signature";s:15:"isEmpty(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:285;s:7:"endLine";i:292;s:3:"ccn";i:2;}s:12:"sortByColumn";a:6:{s:10:"methodName";s:12:"sortByColumn";s:9:"signature";s:55:"sortByColumn(string $columnName, bool $ascending): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:301;s:7:"endLine";i:342;s:3:"ccn";i:15;}s:6:"filter";a:6:{s:10:"methodName";s:6:"filter";s:9:"signature";s:32:"filter(callable $callback): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:349;s:7:"endLine";i:353;s:3:"ccn";i:1;}s:8:"validate";a:6:{s:10:"methodName";s:8:"validate";s:9:"signature";s:16:"validate(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:360;s:7:"endLine";i:369;s:3:"ccn";i:3;}s:7:"toArray";a:6:{s:10:"methodName";s:7:"toArray";s:9:"signature";s:31:"toArray(bool $formatted): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:378;s:7:"endLine";i:391;s:3:"ccn";i:3;}s:11:"getIterator";a:6:{s:10:"methodName";s:11:"getIterator";s:9:"signature";s:26:"getIterator(): Traversable";s:10:"visibility";s:6:"public";s:9:"startLine";i:398;s:7:"endLine";i:405;s:3:"ccn";i:2;}s:12:"enableTables";a:6:{s:10:"methodName";s:12:"enableTables";s:9:"signature";s:33:"enableTables(array $config): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:412;s:7:"endLine";i:416;s:3:"ccn";i:1;}s:13:"disableTables";a:6:{s:10:"methodName";s:13:"disableTables";s:9:"signature";s:21:"disableTables(): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:421;s:7:"endLine";i:425;s:3:"ccn";i:1;}s:15:"isTablesEnabled";a:6:{s:10:"methodName";s:15:"isTablesEnabled";s:9:"signature";s:23:"isTablesEnabled(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:432;s:7:"endLine";i:435;s:3:"ccn";i:1;}s:14:"getTableConfig";a:6:{s:10:"methodName";s:14:"getTableConfig";s:9:"signature";s:23:"getTableConfig(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:442;s:7:"endLine";i:445;s:3:"ccn";i:1;}s:14:"setTableConfig";a:6:{s:10:"methodName";s:14:"setTableConfig";s:9:"signature";s:35:"setTableConfig(array $config): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:452;s:7:"endLine";i:455;s:3:"ccn";i:1;}s:19:"getTableConfigValue";a:6:{s:10:"methodName";s:19:"getTableConfigValue";s:9:"signature";s:42:"getTableConfigValue(string $key, $default)";s:10:"visibility";s:6:"public";s:9:"startLine";i:464;s:7:"endLine";i:467;s:3:"ccn";i:1;}s:19:"setTableConfigValue";a:6:{s:10:"methodName";s:19:"setTableConfigValue";s:9:"signature";s:46:"setTableConfigValue(string $key, $value): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:475;s:7:"endLine";i:478;s:3:"ccn";i:1;}s:15:"getLazyIterator";a:6:{s:10:"methodName";s:15:"getLazyIterator";s:9:"signature";s:28:"getLazyIterator(): Generator";s:10:"visibility";s:7:"private";s:9:"startLine";i:485;s:7:"endLine";i:506;s:3:"ccn";i:4;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:508;s:18:"commentLinesOfCode";i:210;s:21:"nonCommentLinesOfCode";i:298;}s:15:"ignoredLinesFor";a:1:{i:0;i:12;}s:17:"executableLinesIn";a:109:{i:60;i:9;i:62;i:10;i:63;i:11;i:76;i:12;i:77;i:13;i:78;i:14;i:81;i:15;i:83;i:16;i:93;i:17;i:103;i:18;i:114;i:19;i:115;i:20;i:125;i:21;i:135;i:22;i:145;i:23;i:155;i:24;i:167;i:25;i:178;i:26;i:192;i:27;i:193;i:28;i:196;i:29;i:197;i:30;i:200;i:31;i:211;i:32;i:213;i:33;i:214;i:34;i:215;i:35;i:217;i:36;i:220;i:37;i:232;i:38;i:234;i:39;i:235;i:40;i:236;i:41;i:237;i:42;i:239;i:43;i:241;i:44;i:244;i:45;i:254;i:46;i:255;i:47;i:258;i:48;i:269;i:49;i:271;i:50;i:272;i:51;i:275;i:52;i:276;i:53;i:277;i:54;i:287;i:55;i:288;i:56;i:291;i:57;i:303;i:58;i:307;i:60;i:304;i:60;i:305;i:60;i:306;i:60;i:310;i:61;i:341;i:61;i:311;i:62;i:312;i:63;i:314;i:64;i:315;i:65;i:318;i:66;i:319;i:67;i:322;i:68;i:323;i:69;i:326;i:70;i:327;i:71;i:328;i:72;i:330;i:73;i:331;i:74;i:332;i:75;i:333;i:76;i:334;i:77;i:335;i:78;i:337;i:79;i:340;i:80;i:351;i:81;i:352;i:82;i:362;i:83;i:363;i:84;i:364;i:85;i:368;i:86;i:380;i:87;i:383;i:88;i:386;i:89;i:387;i:90;i:390;i:91;i:400;i:92;i:401;i:93;i:404;i:94;i:412;i:95;i:414;i:96;i:415;i:97;i:423;i:98;i:424;i:99;i:434;i:100;i:444;i:101;i:454;i:102;i:466;i:103;i:477;i:104;i:487;i:105;i:488;i:106;i:491;i:107;i:505;i:108;i:493;i:109;i:495;i:110;i:496;i:111;i:499;i:112;i:500;i:113;i:503;i:114;}}