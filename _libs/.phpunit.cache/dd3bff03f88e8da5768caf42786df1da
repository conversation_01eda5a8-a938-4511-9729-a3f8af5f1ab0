a:6:{s:9:"classesIn";a:1:{s:24:"Nzoom\Export\DataFactory";a:6:{s:4:"name";s:11:"DataFactory";s:14:"namespacedName";s:24:"Nzoom\Export\DataFactory";s:9:"namespace";s:12:"Nzoom\Export";s:9:"startLine";i:17;s:7:"endLine";i:493;s:7:"methods";a:16:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:31:"__construct(Registry $registry)";s:10:"visibility";s:6:"public";s:9:"startLine";i:39;s:7:"endLine";i:42;s:3:"ccn";i:1;}s:16:"setTableProvider";a:6:{s:10:"methodName";s:16:"setTableProvider";s:9:"signature";s:90:"setTableProvider(?Nzoom\Export\Provider\ExportTableProviderInterface $tableProvider): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:49;s:7:"endLine";i:52;s:3:"ccn";i:1;}s:15:"isTablesEnabled";a:6:{s:10:"methodName";s:15:"isTablesEnabled";s:9:"signature";s:23:"isTablesEnabled(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:59;s:7:"endLine";i:62;s:3:"ccn";i:1;}s:22:"withModelTableProvider";a:6:{s:10:"methodName";s:22:"withModelTableProvider";s:9:"signature";s:44:"withModelTableProvider(array $options): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:70;s:7:"endLine";i:75;s:3:"ccn";i:1;}s:8:"__invoke";a:6:{s:10:"methodName";s:8:"__invoke";s:9:"signature";s:73:"__invoke(array $models, Outlook $outlook): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:84;s:7:"endLine";i:111;s:3:"ccn";i:3;}s:23:"createHeaderFromOutlook";a:6:{s:10:"methodName";s:23:"createHeaderFromOutlook";s:9:"signature";s:75:"createHeaderFromOutlook(Outlook $outlook): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:119;s:7:"endLine";i:145;s:3:"ccn";i:6;}s:18:"processModelsChunk";a:6:{s:10:"methodName";s:18:"processModelsChunk";s:9:"signature";s:125:"processModelsChunk(array $models, Nzoom\Export\Entity\ExportData $exportData, Nzoom\Export\Entity\ExportHeader $header): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:154;s:7:"endLine";i:159;s:3:"ccn";i:2;}s:21:"createRecordFromModel";a:6:{s:10:"methodName";s:21:"createRecordFromModel";s:9:"signature";s:111:"createRecordFromModel(Model $model, Nzoom\Export\Entity\ExportHeader $header): Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:168;s:7:"endLine";i:209;s:3:"ccn";i:7;}s:15:"getModelFullNum";a:6:{s:10:"methodName";s:15:"getModelFullNum";s:9:"signature";s:38:"getModelFullNum(Model $model): ?string";s:10:"visibility";s:7:"private";s:9:"startLine";i:217;s:7:"endLine";i:262;s:3:"ccn";i:10;}s:22:"extractTablesForRecord";a:6:{s:10:"methodName";s:22:"extractTablesForRecord";s:9:"signature";s:84:"extractTablesForRecord(Nzoom\Export\Entity\ExportRecord $record, Model $model): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:270;s:7:"endLine";i:286;s:3:"ccn";i:5;}s:24:"mapFieldTypeToExportType";a:6:{s:10:"methodName";s:24:"mapFieldTypeToExportType";s:9:"signature";s:51:"mapFieldTypeToExportType(string $fieldType): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:294;s:7:"endLine";i:316;s:3:"ccn";i:1;}s:12:"setChunkSize";a:6:{s:10:"methodName";s:12:"setChunkSize";s:9:"signature";s:28:"setChunkSize(int $chunkSize)";s:10:"visibility";s:6:"public";s:9:"startLine";i:323;s:7:"endLine";i:326;s:3:"ccn";i:1;}s:15:"createStreaming";a:6:{s:10:"methodName";s:15:"createStreaming";s:9:"signature";s:118:"createStreaming(string $factoryClass, array $filters, Outlook $outlook, int $pageSize): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:337;s:7:"endLine";i:377;s:3:"ccn";i:2;}s:21:"createCursorStreaming";a:6:{s:10:"methodName";s:21:"createCursorStreaming";s:9:"signature";s:143:"createCursorStreaming(string $modelClass, array $filters, Outlook $outlook, int $pageSize, string $cursorField): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:389;s:7:"endLine";i:448;s:3:"ccn";i:4;}s:32:"extractModelTypeFromFactoryClass";a:6:{s:10:"methodName";s:32:"extractModelTypeFromFactoryClass";s:9:"signature";s:62:"extractModelTypeFromFactoryClass(string $factoryClass): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:457;s:7:"endLine";i:464;s:3:"ccn";i:1;}s:25:"extractModelTypeFromModel";a:6:{s:10:"methodName";s:25:"extractModelTypeFromModel";s:9:"signature";s:47:"extractModelTypeFromModel(Model $model): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:472;s:7:"endLine";i:492;s:3:"ccn";i:2;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:494;s:18:"commentLinesOfCode";i:166;s:21:"nonCommentLinesOfCode";i:328;}s:15:"ignoredLinesFor";a:1:{i:0;i:17;}s:17:"executableLinesIn";a:160:{i:41;i:4;i:51;i:5;i:61;i:6;i:70;i:7;i:72;i:8;i:73;i:9;i:74;i:10;i:87;i:11;i:88;i:12;i:89;i:13;i:90;i:14;i:94;i:15;i:97;i:16;i:98;i:16;i:99;i:16;i:100;i:16;i:105;i:17;i:106;i:18;i:107;i:19;i:110;i:20;i:121;i:21;i:122;i:22;i:124;i:23;i:125;i:24;i:127;i:25;i:129;i:26;i:131;i:27;i:132;i:28;i:134;i:29;i:135;i:29;i:136;i:29;i:137;i:29;i:138;i:29;i:139;i:29;i:140;i:29;i:144;i:30;i:156;i:31;i:157;i:32;i:170;i:33;i:171;i:33;i:172;i:33;i:175;i:34;i:176;i:35;i:177;i:36;i:180;i:37;i:182;i:38;i:183;i:39;i:186;i:40;i:189;i:41;i:190;i:42;i:193;i:43;i:194;i:44;i:195;i:45;i:196;i:46;i:200;i:47;i:204;i:48;i:205;i:49;i:208;i:50;i:220;i:51;i:221;i:52;i:222;i:53;i:226;i:54;i:229;i:55;i:230;i:56;i:231;i:57;i:232;i:58;i:237;i:59;i:238;i:60;i:239;i:61;i:240;i:62;i:245;i:63;i:246;i:64;i:247;i:65;i:248;i:66;i:253;i:67;i:254;i:68;i:255;i:69;i:257;i:70;i:261;i:71;i:273;i:72;i:275;i:73;i:276;i:74;i:278;i:75;i:280;i:76;i:281;i:77;i:282;i:77;i:283;i:77;i:296;i:78;i:297;i:78;i:298;i:78;i:299;i:78;i:300;i:78;i:301;i:78;i:302;i:78;i:303;i:78;i:304;i:78;i:305;i:78;i:306;i:78;i:307;i:78;i:308;i:78;i:309;i:78;i:310;i:78;i:311;i:78;i:312;i:78;i:313;i:78;i:315;i:79;i:325;i:80;i:340;i:81;i:343;i:82;i:346;i:83;i:347;i:83;i:348;i:83;i:349;i:83;i:354;i:84;i:371;i:84;i:356;i:85;i:357;i:85;i:358;i:85;i:359;i:85;i:362;i:86;i:365;i:87;i:366;i:88;i:367;i:89;i:370;i:90;i:374;i:91;i:376;i:92;i:392;i:93;i:395;i:94;i:398;i:95;i:399;i:95;i:400;i:95;i:401;i:95;i:406;i:96;i:409;i:97;i:442;i:97;i:411;i:98;i:414;i:99;i:415;i:100;i:416;i:101;i:417;i:102;i:421;i:103;i:422;i:104;i:425;i:105;i:427;i:106;i:428;i:107;i:432;i:108;i:433;i:109;i:436;i:110;i:437;i:111;i:438;i:112;i:441;i:113;i:445;i:114;i:447;i:115;i:460;i:116;i:463;i:117;i:475;i:118;i:478;i:119;i:486;i:120;i:487;i:121;i:491;i:122;}}