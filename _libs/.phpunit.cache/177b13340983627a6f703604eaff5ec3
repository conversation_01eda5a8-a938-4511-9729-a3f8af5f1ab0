a:6:{s:9:"classesIn";a:1:{s:24:"Nzoom\Export\DataFactory";a:6:{s:4:"name";s:11:"DataFactory";s:14:"namespacedName";s:24:"Nzoom\Export\DataFactory";s:9:"namespace";s:12:"Nzoom\Export";s:9:"startLine";i:16;s:7:"endLine";i:299;s:7:"methods";a:9:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:31:"__construct(Registry $registry)";s:10:"visibility";s:6:"public";s:9:"startLine";i:33;s:7:"endLine";i:36;s:3:"ccn";i:1;}s:8:"__invoke";a:6:{s:10:"methodName";s:8:"__invoke";s:9:"signature";s:73:"__invoke(array $models, Outlook $outlook): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:45;s:7:"endLine";i:63;s:3:"ccn";i:2;}s:23:"createHeaderFromOutlook";a:6:{s:10:"methodName";s:23:"createHeaderFromOutlook";s:9:"signature";s:75:"createHeaderFromOutlook(Outlook $outlook): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:71;s:7:"endLine";i:97;s:3:"ccn";i:6;}s:18:"processModelsChunk";a:6:{s:10:"methodName";s:18:"processModelsChunk";s:9:"signature";s:125:"processModelsChunk(array $models, Nzoom\Export\Entity\ExportData $exportData, Nzoom\Export\Entity\ExportHeader $header): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:106;s:7:"endLine";i:111;s:3:"ccn";i:2;}s:21:"createRecordFromModel";a:6:{s:10:"methodName";s:21:"createRecordFromModel";s:9:"signature";s:111:"createRecordFromModel(Model $model, Nzoom\Export\Entity\ExportHeader $header): Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:120;s:7:"endLine";i:146;s:3:"ccn";i:4;}s:24:"mapFieldTypeToExportType";a:6:{s:10:"methodName";s:24:"mapFieldTypeToExportType";s:9:"signature";s:51:"mapFieldTypeToExportType(string $fieldType): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:154;s:7:"endLine";i:176;s:3:"ccn";i:1;}s:12:"setChunkSize";a:6:{s:10:"methodName";s:12:"setChunkSize";s:9:"signature";s:28:"setChunkSize(int $chunkSize)";s:10:"visibility";s:6:"public";s:9:"startLine";i:183;s:7:"endLine";i:186;s:3:"ccn";i:1;}s:15:"createStreaming";a:6:{s:10:"methodName";s:15:"createStreaming";s:9:"signature";s:118:"createStreaming(string $factoryClass, array $filters, Outlook $outlook, int $pageSize): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:197;s:7:"endLine";i:232;s:3:"ccn";i:2;}s:21:"createCursorStreaming";a:6:{s:10:"methodName";s:21:"createCursorStreaming";s:9:"signature";s:143:"createCursorStreaming(string $modelClass, array $filters, Outlook $outlook, int $pageSize, string $cursorField): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:244;s:7:"endLine";i:298;s:3:"ccn";i:4;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:300;s:18:"commentLinesOfCode";i:99;s:21:"nonCommentLinesOfCode";i:201;}s:15:"ignoredLinesFor";a:1:{i:0;i:16;}s:17:"executableLinesIn";a:104:{i:35;i:3;i:48;i:4;i:51;i:5;i:52;i:5;i:53;i:5;i:54;i:5;i:57;i:6;i:58;i:7;i:59;i:8;i:62;i:9;i:73;i:10;i:74;i:11;i:76;i:12;i:77;i:13;i:79;i:14;i:81;i:15;i:83;i:16;i:84;i:17;i:86;i:18;i:87;i:18;i:88;i:18;i:89;i:18;i:90;i:18;i:91;i:18;i:92;i:18;i:96;i:19;i:108;i:20;i:109;i:21;i:122;i:22;i:124;i:23;i:125;i:24;i:128;i:25;i:131;i:26;i:132;i:27;i:135;i:28;i:136;i:29;i:137;i:30;i:138;i:31;i:142;i:32;i:145;i:33;i:156;i:34;i:157;i:34;i:158;i:34;i:159;i:34;i:160;i:34;i:161;i:34;i:162;i:34;i:163;i:34;i:164;i:34;i:165;i:34;i:166;i:34;i:167;i:34;i:168;i:34;i:169;i:34;i:170;i:34;i:171;i:34;i:172;i:34;i:173;i:34;i:175;i:35;i:185;i:36;i:200;i:37;i:203;i:38;i:204;i:38;i:205;i:38;i:206;i:38;i:209;i:39;i:226;i:39;i:211;i:40;i:212;i:40;i:213;i:40;i:214;i:40;i:217;i:41;i:220;i:42;i:221;i:43;i:222;i:44;i:225;i:45;i:229;i:46;i:231;i:47;i:247;i:48;i:250;i:49;i:251;i:49;i:252;i:49;i:253;i:49;i:256;i:50;i:259;i:51;i:292;i:51;i:261;i:52;i:264;i:53;i:265;i:54;i:266;i:55;i:267;i:56;i:271;i:57;i:272;i:58;i:275;i:59;i:277;i:60;i:278;i:61;i:282;i:62;i:283;i:63;i:286;i:64;i:287;i:65;i:288;i:66;i:291;i:67;i:295;i:68;i:297;i:69;}}