a:6:{s:9:"classesIn";a:1:{s:30:"Nzoom\Export\Entity\ExportData";a:6:{s:4:"name";s:10:"ExportData";s:14:"namespacedName";s:30:"Nzoom\Export\Entity\ExportData";s:9:"namespace";s:19:"Nzoom\Export\Entity";s:9:"startLine";i:12;s:7:"endLine";i:445;s:7:"methods";a:24:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:89:"__construct(string $modelType, Nzoom\Export\Entity\ExportHeader $header, array $metadata)";s:10:"visibility";s:6:"public";s:9:"startLine";i:58;s:7:"endLine";i:63;s:3:"ccn";i:1;}s:17:"setRecordProvider";a:6:{s:10:"methodName";s:17:"setRecordProvider";s:9:"signature";s:64:"setRecordProvider(callable $recordProvider, int $pageSize): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:73;s:7:"endLine";i:83;s:3:"ccn";i:1;}s:6:"isLazy";a:6:{s:10:"methodName";s:6:"isLazy";s:9:"signature";s:14:"isLazy(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:90;s:7:"endLine";i:93;s:3:"ccn";i:1;}s:11:"getPageSize";a:6:{s:10:"methodName";s:11:"getPageSize";s:9:"signature";s:18:"getPageSize(): int";s:10:"visibility";s:6:"public";s:9:"startLine";i:100;s:7:"endLine";i:103;s:3:"ccn";i:1;}s:11:"setPageSize";a:6:{s:10:"methodName";s:11:"setPageSize";s:9:"signature";s:32:"setPageSize(int $pageSize): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:111;s:7:"endLine";i:115;s:3:"ccn";i:1;}s:12:"getModelType";a:6:{s:10:"methodName";s:12:"getModelType";s:9:"signature";s:22:"getModelType(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:122;s:7:"endLine";i:125;s:3:"ccn";i:1;}s:9:"getHeader";a:6:{s:10:"methodName";s:9:"getHeader";s:9:"signature";s:45:"getHeader(): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:6:"public";s:9:"startLine";i:132;s:7:"endLine";i:135;s:3:"ccn";i:1;}s:9:"setHeader";a:6:{s:10:"methodName";s:9:"setHeader";s:9:"signature";s:57:"setHeader(Nzoom\Export\Entity\ExportHeader $header): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:142;s:7:"endLine";i:145;s:3:"ccn";i:1;}s:11:"getMetadata";a:6:{s:10:"methodName";s:11:"getMetadata";s:9:"signature";s:20:"getMetadata(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:152;s:7:"endLine";i:155;s:3:"ccn";i:1;}s:11:"setMetadata";a:6:{s:10:"methodName";s:11:"setMetadata";s:9:"signature";s:34:"setMetadata(array $metadata): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:162;s:7:"endLine";i:165;s:3:"ccn";i:1;}s:16:"getMetadataValue";a:6:{s:10:"methodName";s:16:"getMetadataValue";s:9:"signature";s:39:"getMetadataValue(string $key, $default)";s:10:"visibility";s:6:"public";s:9:"startLine";i:174;s:7:"endLine";i:177;s:3:"ccn";i:1;}s:16:"setMetadataValue";a:6:{s:10:"methodName";s:16:"setMetadataValue";s:9:"signature";s:43:"setMetadataValue(string $key, $value): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:185;s:7:"endLine";i:188;s:3:"ccn";i:1;}s:9:"addRecord";a:6:{s:10:"methodName";s:9:"addRecord";s:9:"signature";s:73:"addRecord(Nzoom\Export\Entity\ExportRecord $record, bool $validate): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:199;s:7:"endLine";i:210;s:3:"ccn";i:4;}s:10:"getRecords";a:6:{s:10:"methodName";s:10:"getRecords";s:9:"signature";s:19:"getRecords(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:218;s:7:"endLine";i:230;s:3:"ccn";i:3;}s:11:"getRecordAt";a:6:{s:10:"methodName";s:11:"getRecordAt";s:9:"signature";s:58:"getRecordAt(int $index): ?Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:6:"public";s:9:"startLine";i:239;s:7:"endLine";i:254;s:3:"ccn";i:4;}s:5:"count";a:6:{s:10:"methodName";s:5:"count";s:9:"signature";s:12:"count(): int";s:10:"visibility";s:6:"public";s:9:"startLine";i:261;s:7:"endLine";i:268;s:3:"ccn";i:2;}s:12:"createRecord";a:6:{s:10:"methodName";s:12:"createRecord";s:9:"signature";s:63:"createRecord(array $metadata): Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:6:"public";s:9:"startLine";i:278;s:7:"endLine";i:287;s:3:"ccn";i:2;}s:7:"isEmpty";a:6:{s:10:"methodName";s:7:"isEmpty";s:9:"signature";s:15:"isEmpty(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:294;s:7:"endLine";i:301;s:3:"ccn";i:2;}s:12:"sortByColumn";a:6:{s:10:"methodName";s:12:"sortByColumn";s:9:"signature";s:55:"sortByColumn(string $columnName, bool $ascending): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:310;s:7:"endLine";i:351;s:3:"ccn";i:15;}s:6:"filter";a:6:{s:10:"methodName";s:6:"filter";s:9:"signature";s:32:"filter(callable $callback): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:358;s:7:"endLine";i:362;s:3:"ccn";i:1;}s:8:"validate";a:6:{s:10:"methodName";s:8:"validate";s:9:"signature";s:16:"validate(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:369;s:7:"endLine";i:378;s:3:"ccn";i:3;}s:7:"toArray";a:6:{s:10:"methodName";s:7:"toArray";s:9:"signature";s:31:"toArray(bool $formatted): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:387;s:7:"endLine";i:400;s:3:"ccn";i:3;}s:11:"getIterator";a:6:{s:10:"methodName";s:11:"getIterator";s:9:"signature";s:26:"getIterator(): Traversable";s:10:"visibility";s:6:"public";s:9:"startLine";i:407;s:7:"endLine";i:414;s:3:"ccn";i:2;}s:15:"getLazyIterator";a:6:{s:10:"methodName";s:15:"getLazyIterator";s:9:"signature";s:28:"getLazyIterator(): Generator";s:10:"visibility";s:7:"private";s:9:"startLine";i:423;s:7:"endLine";i:444;s:3:"ccn";i:4;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:446;s:18:"commentLinesOfCode";i:177;s:21:"nonCommentLinesOfCode";i:269;}s:15:"ignoredLinesFor";a:1:{i:0;i:12;}s:17:"executableLinesIn";a:101:{i:58;i:8;i:60;i:9;i:61;i:10;i:62;i:11;i:75;i:12;i:76;i:13;i:77;i:14;i:80;i:15;i:82;i:16;i:92;i:17;i:102;i:18;i:113;i:19;i:114;i:20;i:124;i:21;i:134;i:22;i:144;i:23;i:154;i:24;i:164;i:25;i:176;i:26;i:187;i:27;i:201;i:28;i:202;i:29;i:205;i:30;i:206;i:31;i:209;i:32;i:220;i:33;i:222;i:34;i:223;i:35;i:224;i:36;i:226;i:37;i:229;i:38;i:241;i:39;i:243;i:40;i:244;i:41;i:245;i:42;i:246;i:43;i:248;i:44;i:250;i:45;i:253;i:46;i:263;i:47;i:264;i:48;i:267;i:49;i:278;i:50;i:280;i:51;i:281;i:52;i:284;i:53;i:285;i:54;i:286;i:55;i:296;i:56;i:297;i:57;i:300;i:58;i:312;i:59;i:316;i:61;i:313;i:61;i:314;i:61;i:315;i:61;i:319;i:62;i:350;i:62;i:320;i:63;i:321;i:64;i:323;i:65;i:324;i:66;i:327;i:67;i:328;i:68;i:331;i:69;i:332;i:70;i:335;i:71;i:336;i:72;i:337;i:73;i:339;i:74;i:340;i:75;i:341;i:76;i:342;i:77;i:343;i:78;i:344;i:79;i:346;i:80;i:349;i:81;i:360;i:82;i:361;i:83;i:371;i:84;i:372;i:85;i:373;i:86;i:377;i:87;i:389;i:88;i:392;i:89;i:395;i:90;i:396;i:91;i:399;i:92;i:409;i:93;i:410;i:94;i:413;i:95;i:425;i:96;i:426;i:97;i:429;i:98;i:443;i:99;i:431;i:100;i:433;i:101;i:434;i:102;i:437;i:103;i:438;i:104;i:441;i:105;}}