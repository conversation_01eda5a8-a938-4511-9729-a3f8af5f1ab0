a:6:{s:9:"classesIn";a:1:{s:31:"Nzoom\Export\Entity\ExportTable";a:6:{s:4:"name";s:11:"ExportTable";s:14:"namespacedName";s:31:"Nzoom\Export\Entity\ExportTable";s:9:"namespace";s:19:"Nzoom\Export\Entity";s:9:"startLine";i:11;s:7:"endLine";i:300;s:7:"methods";a:21:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:134:"__construct(string $tableType, string $tableName, Nzoom\Export\Entity\ExportHeader $header, ?string $parentReference, array $metadata)";s:10:"visibility";s:6:"public";s:9:"startLine";i:52;s:7:"endLine";i:64;s:3:"ccn";i:1;}s:12:"getTableType";a:6:{s:10:"methodName";s:12:"getTableType";s:9:"signature";s:22:"getTableType(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:71;s:7:"endLine";i:74;s:3:"ccn";i:1;}s:12:"getTableName";a:6:{s:10:"methodName";s:12:"getTableName";s:9:"signature";s:22:"getTableName(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:81;s:7:"endLine";i:84;s:3:"ccn";i:1;}s:12:"setTableName";a:6:{s:10:"methodName";s:12:"setTableName";s:9:"signature";s:37:"setTableName(string $tableName): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:91;s:7:"endLine";i:94;s:3:"ccn";i:1;}s:9:"getHeader";a:6:{s:10:"methodName";s:9:"getHeader";s:9:"signature";s:45:"getHeader(): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:6:"public";s:9:"startLine";i:101;s:7:"endLine";i:104;s:3:"ccn";i:1;}s:9:"setHeader";a:6:{s:10:"methodName";s:9:"setHeader";s:9:"signature";s:57:"setHeader(Nzoom\Export\Entity\ExportHeader $header): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:111;s:7:"endLine";i:114;s:3:"ccn";i:1;}s:18:"getParentReference";a:6:{s:10:"methodName";s:18:"getParentReference";s:9:"signature";s:29:"getParentReference(): ?string";s:10:"visibility";s:6:"public";s:9:"startLine";i:121;s:7:"endLine";i:124;s:3:"ccn";i:1;}s:18:"setParentReference";a:6:{s:10:"methodName";s:18:"setParentReference";s:9:"signature";s:50:"setParentReference(?string $parentReference): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:131;s:7:"endLine";i:134;s:3:"ccn";i:1;}s:9:"addRecord";a:6:{s:10:"methodName";s:9:"addRecord";s:9:"signature";s:73:"addRecord(Nzoom\Export\Entity\ExportRecord $record, bool $validate): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:143;s:7:"endLine";i:150;s:3:"ccn";i:3;}s:10:"getRecords";a:6:{s:10:"methodName";s:10:"getRecords";s:9:"signature";s:19:"getRecords(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:157;s:7:"endLine";i:160;s:3:"ccn";i:1;}s:10:"setRecords";a:6:{s:10:"methodName";s:10:"setRecords";s:9:"signature";s:48:"setRecords(array $records, bool $validate): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:169;s:7:"endLine";i:180;s:3:"ccn";i:4;}s:12:"clearRecords";a:6:{s:10:"methodName";s:12:"clearRecords";s:9:"signature";s:20:"clearRecords(): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:185;s:7:"endLine";i:188;s:3:"ccn";i:1;}s:10:"hasRecords";a:6:{s:10:"methodName";s:10:"hasRecords";s:9:"signature";s:18:"hasRecords(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:195;s:7:"endLine";i:198;s:3:"ccn";i:1;}s:11:"getMetadata";a:6:{s:10:"methodName";s:11:"getMetadata";s:9:"signature";s:20:"getMetadata(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:205;s:7:"endLine";i:208;s:3:"ccn";i:1;}s:11:"setMetadata";a:6:{s:10:"methodName";s:11:"setMetadata";s:9:"signature";s:34:"setMetadata(array $metadata): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:215;s:7:"endLine";i:218;s:3:"ccn";i:1;}s:16:"getMetadataValue";a:6:{s:10:"methodName";s:16:"getMetadataValue";s:9:"signature";s:39:"getMetadataValue(string $key, $default)";s:10:"visibility";s:6:"public";s:9:"startLine";i:227;s:7:"endLine";i:230;s:3:"ccn";i:1;}s:16:"setMetadataValue";a:6:{s:10:"methodName";s:16:"setMetadataValue";s:9:"signature";s:43:"setMetadataValue(string $key, $value): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:238;s:7:"endLine";i:241;s:3:"ccn";i:1;}s:8:"validate";a:6:{s:10:"methodName";s:8:"validate";s:9:"signature";s:16:"validate(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:248;s:7:"endLine";i:257;s:3:"ccn";i:3;}s:7:"toArray";a:6:{s:10:"methodName";s:7:"toArray";s:9:"signature";s:31:"toArray(bool $formatted): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:266;s:7:"endLine";i:279;s:3:"ccn";i:3;}s:5:"count";a:6:{s:10:"methodName";s:5:"count";s:9:"signature";s:12:"count(): int";s:10:"visibility";s:6:"public";s:9:"startLine";i:286;s:7:"endLine";i:289;s:3:"ccn";i:1;}s:11:"getIterator";a:6:{s:10:"methodName";s:11:"getIterator";s:9:"signature";s:26:"getIterator(): Traversable";s:10:"visibility";s:6:"public";s:9:"startLine";i:296;s:7:"endLine";i:299;s:3:"ccn";i:1;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:301;s:18:"commentLinesOfCode";i:142;s:21:"nonCommentLinesOfCode";i:159;}s:15:"ignoredLinesFor";a:1:{i:0;i:11;}s:17:"executableLinesIn";a:39:{i:57;i:7;i:59;i:8;i:60;i:9;i:61;i:10;i:62;i:11;i:63;i:12;i:73;i:13;i:83;i:14;i:93;i:15;i:103;i:16;i:113;i:17;i:123;i:18;i:133;i:19;i:145;i:20;i:146;i:21;i:149;i:22;i:159;i:23;i:171;i:24;i:172;i:25;i:173;i:26;i:174;i:27;i:179;i:28;i:187;i:29;i:197;i:30;i:207;i:31;i:217;i:32;i:229;i:33;i:240;i:34;i:250;i:35;i:251;i:36;i:252;i:37;i:256;i:38;i:268;i:39;i:271;i:40;i:274;i:41;i:275;i:42;i:278;i:43;i:288;i:44;i:298;i:45;}}