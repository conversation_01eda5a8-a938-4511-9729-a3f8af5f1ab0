a:6:{s:9:"classesIn";a:1:{s:30:"Nzoom\Export\Entity\ExportData";a:6:{s:4:"name";s:10:"ExportData";s:14:"namespacedName";s:30:"Nzoom\Export\Entity\ExportData";s:9:"namespace";s:19:"Nzoom\Export\Entity";s:9:"startLine";i:12;s:7:"endLine";i:428;s:7:"methods";a:23:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:70:"__construct(Nzoom\Export\Entity\ExportHeader $header, array $metadata)";s:10:"visibility";s:6:"public";s:9:"startLine";i:52;s:7:"endLine";i:56;s:3:"ccn";i:1;}s:17:"setRecordProvider";a:6:{s:10:"methodName";s:17:"setRecordProvider";s:9:"signature";s:64:"setRecordProvider(callable $recordProvider, int $pageSize): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:66;s:7:"endLine";i:76;s:3:"ccn";i:1;}s:6:"isLazy";a:6:{s:10:"methodName";s:6:"isLazy";s:9:"signature";s:14:"isLazy(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:83;s:7:"endLine";i:86;s:3:"ccn";i:1;}s:11:"getPageSize";a:6:{s:10:"methodName";s:11:"getPageSize";s:9:"signature";s:18:"getPageSize(): int";s:10:"visibility";s:6:"public";s:9:"startLine";i:93;s:7:"endLine";i:96;s:3:"ccn";i:1;}s:11:"setPageSize";a:6:{s:10:"methodName";s:11:"setPageSize";s:9:"signature";s:32:"setPageSize(int $pageSize): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:104;s:7:"endLine";i:108;s:3:"ccn";i:1;}s:9:"getHeader";a:6:{s:10:"methodName";s:9:"getHeader";s:9:"signature";s:45:"getHeader(): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:6:"public";s:9:"startLine";i:115;s:7:"endLine";i:118;s:3:"ccn";i:1;}s:9:"setHeader";a:6:{s:10:"methodName";s:9:"setHeader";s:9:"signature";s:57:"setHeader(Nzoom\Export\Entity\ExportHeader $header): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:125;s:7:"endLine";i:128;s:3:"ccn";i:1;}s:11:"getMetadata";a:6:{s:10:"methodName";s:11:"getMetadata";s:9:"signature";s:20:"getMetadata(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:135;s:7:"endLine";i:138;s:3:"ccn";i:1;}s:11:"setMetadata";a:6:{s:10:"methodName";s:11:"setMetadata";s:9:"signature";s:34:"setMetadata(array $metadata): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:145;s:7:"endLine";i:148;s:3:"ccn";i:1;}s:16:"getMetadataValue";a:6:{s:10:"methodName";s:16:"getMetadataValue";s:9:"signature";s:39:"getMetadataValue(string $key, $default)";s:10:"visibility";s:6:"public";s:9:"startLine";i:157;s:7:"endLine";i:160;s:3:"ccn";i:1;}s:16:"setMetadataValue";a:6:{s:10:"methodName";s:16:"setMetadataValue";s:9:"signature";s:43:"setMetadataValue(string $key, $value): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:168;s:7:"endLine";i:171;s:3:"ccn";i:1;}s:9:"addRecord";a:6:{s:10:"methodName";s:9:"addRecord";s:9:"signature";s:73:"addRecord(Nzoom\Export\Entity\ExportRecord $record, bool $validate): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:182;s:7:"endLine";i:193;s:3:"ccn";i:4;}s:10:"getRecords";a:6:{s:10:"methodName";s:10:"getRecords";s:9:"signature";s:19:"getRecords(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:201;s:7:"endLine";i:213;s:3:"ccn";i:3;}s:11:"getRecordAt";a:6:{s:10:"methodName";s:11:"getRecordAt";s:9:"signature";s:58:"getRecordAt(int $index): ?Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:6:"public";s:9:"startLine";i:222;s:7:"endLine";i:237;s:3:"ccn";i:4;}s:5:"count";a:6:{s:10:"methodName";s:5:"count";s:9:"signature";s:12:"count(): int";s:10:"visibility";s:6:"public";s:9:"startLine";i:244;s:7:"endLine";i:251;s:3:"ccn";i:2;}s:12:"createRecord";a:6:{s:10:"methodName";s:12:"createRecord";s:9:"signature";s:63:"createRecord(array $metadata): Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:6:"public";s:9:"startLine";i:261;s:7:"endLine";i:270;s:3:"ccn";i:2;}s:7:"isEmpty";a:6:{s:10:"methodName";s:7:"isEmpty";s:9:"signature";s:15:"isEmpty(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:277;s:7:"endLine";i:284;s:3:"ccn";i:2;}s:12:"sortByColumn";a:6:{s:10:"methodName";s:12:"sortByColumn";s:9:"signature";s:55:"sortByColumn(string $columnName, bool $ascending): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:293;s:7:"endLine";i:334;s:3:"ccn";i:15;}s:6:"filter";a:6:{s:10:"methodName";s:6:"filter";s:9:"signature";s:32:"filter(callable $callback): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:341;s:7:"endLine";i:345;s:3:"ccn";i:1;}s:8:"validate";a:6:{s:10:"methodName";s:8:"validate";s:9:"signature";s:16:"validate(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:352;s:7:"endLine";i:361;s:3:"ccn";i:3;}s:7:"toArray";a:6:{s:10:"methodName";s:7:"toArray";s:9:"signature";s:31:"toArray(bool $formatted): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:370;s:7:"endLine";i:383;s:3:"ccn";i:3;}s:11:"getIterator";a:6:{s:10:"methodName";s:11:"getIterator";s:9:"signature";s:26:"getIterator(): Traversable";s:10:"visibility";s:6:"public";s:9:"startLine";i:390;s:7:"endLine";i:397;s:3:"ccn";i:2;}s:15:"getLazyIterator";a:6:{s:10:"methodName";s:15:"getLazyIterator";s:9:"signature";s:28:"getLazyIterator(): Generator";s:10:"visibility";s:7:"private";s:9:"startLine";i:406;s:7:"endLine";i:427;s:3:"ccn";i:4;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:429;s:18:"commentLinesOfCode";i:168;s:21:"nonCommentLinesOfCode";i:261;}s:15:"ignoredLinesFor";a:1:{i:0;i:12;}s:17:"executableLinesIn";a:99:{i:52;i:7;i:54;i:8;i:55;i:9;i:68;i:10;i:69;i:11;i:70;i:12;i:73;i:13;i:75;i:14;i:85;i:15;i:95;i:16;i:106;i:17;i:107;i:18;i:117;i:19;i:127;i:20;i:137;i:21;i:147;i:22;i:159;i:23;i:170;i:24;i:184;i:25;i:185;i:26;i:188;i:27;i:189;i:28;i:192;i:29;i:203;i:30;i:205;i:31;i:206;i:32;i:207;i:33;i:209;i:34;i:212;i:35;i:224;i:36;i:226;i:37;i:227;i:38;i:228;i:39;i:229;i:40;i:231;i:41;i:233;i:42;i:236;i:43;i:246;i:44;i:247;i:45;i:250;i:46;i:261;i:47;i:263;i:48;i:264;i:49;i:267;i:50;i:268;i:51;i:269;i:52;i:279;i:53;i:280;i:54;i:283;i:55;i:295;i:56;i:299;i:58;i:296;i:58;i:297;i:58;i:298;i:58;i:302;i:59;i:333;i:59;i:303;i:60;i:304;i:61;i:306;i:62;i:307;i:63;i:310;i:64;i:311;i:65;i:314;i:66;i:315;i:67;i:318;i:68;i:319;i:69;i:320;i:70;i:322;i:71;i:323;i:72;i:324;i:73;i:325;i:74;i:326;i:75;i:327;i:76;i:329;i:77;i:332;i:78;i:343;i:79;i:344;i:80;i:354;i:81;i:355;i:82;i:356;i:83;i:360;i:84;i:372;i:85;i:375;i:86;i:378;i:87;i:379;i:88;i:382;i:89;i:392;i:90;i:393;i:91;i:396;i:92;i:408;i:93;i:409;i:94;i:412;i:95;i:426;i:96;i:414;i:97;i:416;i:98;i:417;i:99;i:420;i:100;i:421;i:101;i:424;i:102;}}