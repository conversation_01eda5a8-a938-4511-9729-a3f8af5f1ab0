a:6:{s:9:"classesIn";a:1:{s:32:"Nzoom\Export\Entity\ExportRecord";a:6:{s:4:"name";s:12:"ExportRecord";s:14:"namespacedName";s:32:"Nzoom\Export\Entity\ExportRecord";s:9:"namespace";s:19:"Nzoom\Export\Entity";s:9:"startLine";i:11;s:7:"endLine";i:353;s:7:"methods";a:26:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:28:"__construct(array $metadata)";s:10:"visibility";s:6:"public";s:9:"startLine";i:43;s:7:"endLine";i:47;s:3:"ccn";i:1;}s:8:"addValue";a:6:{s:10:"methodName";s:8:"addValue";s:9:"signature";s:68:"addValue(string $columnName, $value, ?string $type, ?string $format)";s:10:"visibility";s:6:"public";s:9:"startLine";i:57;s:7:"endLine";i:65;s:3:"ccn";i:2;}s:10:"setValueAt";a:6:{s:10:"methodName";s:10:"setValueAt";s:9:"signature";s:62:"setValueAt(int $index, $value, ?string $type, ?string $format)";s:10:"visibility";s:6:"public";s:9:"startLine";i:76;s:7:"endLine";i:91;s:3:"ccn";i:4;}s:20:"setValueByColumnName";a:6:{s:10:"methodName";s:20:"setValueByColumnName";s:9:"signature";s:80:"setValueByColumnName(string $columnName, $value, ?string $type, ?string $format)";s:10:"visibility";s:6:"public";s:9:"startLine";i:103;s:7:"endLine";i:114;s:3:"ccn";i:2;}s:9:"getValues";a:6:{s:10:"methodName";s:9:"getValues";s:9:"signature";s:18:"getValues(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:121;s:7:"endLine";i:124;s:3:"ccn";i:1;}s:10:"getValueAt";a:6:{s:10:"methodName";s:10:"getValueAt";s:9:"signature";s:22:"getValueAt(int $index)";s:10:"visibility";s:6:"public";s:9:"startLine";i:132;s:7:"endLine";i:135;s:3:"ccn";i:1;}s:20:"getValueByColumnName";a:6:{s:10:"methodName";s:20:"getValueByColumnName";s:9:"signature";s:40:"getValueByColumnName(string $columnName)";s:10:"visibility";s:6:"public";s:9:"startLine";i:143;s:7:"endLine";i:150;s:3:"ccn";i:2;}s:8:"hasValue";a:6:{s:10:"methodName";s:8:"hasValue";s:9:"signature";s:34:"hasValue(string $columnName): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:158;s:7:"endLine";i:161;s:3:"ccn";i:1;}s:12:"getRawValues";a:6:{s:10:"methodName";s:12:"getRawValues";s:9:"signature";s:21:"getRawValues(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:168;s:7:"endLine";i:173;s:3:"ccn";i:1;}s:18:"getFormattedValues";a:6:{s:10:"methodName";s:18:"getFormattedValues";s:9:"signature";s:27:"getFormattedValues(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:180;s:7:"endLine";i:185;s:3:"ccn";i:1;}s:11:"getMetadata";a:6:{s:10:"methodName";s:11:"getMetadata";s:9:"signature";s:20:"getMetadata(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:192;s:7:"endLine";i:195;s:3:"ccn";i:1;}s:11:"setMetadata";a:6:{s:10:"methodName";s:11:"setMetadata";s:9:"signature";s:28:"setMetadata(array $metadata)";s:10:"visibility";s:6:"public";s:9:"startLine";i:202;s:7:"endLine";i:205;s:3:"ccn";i:1;}s:16:"getMetadataValue";a:6:{s:10:"methodName";s:16:"getMetadataValue";s:9:"signature";s:39:"getMetadataValue(string $key, $default)";s:10:"visibility";s:6:"public";s:9:"startLine";i:214;s:7:"endLine";i:217;s:3:"ccn";i:1;}s:16:"setMetadataValue";a:6:{s:10:"methodName";s:16:"setMetadataValue";s:9:"signature";s:37:"setMetadataValue(string $key, $value)";s:10:"visibility";s:6:"public";s:9:"startLine";i:225;s:7:"endLine";i:228;s:3:"ccn";i:1;}s:18:"getTableCollection";a:6:{s:10:"methodName";s:18:"getTableCollection";s:9:"signature";s:64:"getTableCollection(): ?Nzoom\Export\Entity\ExportTableCollection";s:10:"visibility";s:6:"public";s:9:"startLine";i:235;s:7:"endLine";i:238;s:3:"ccn";i:1;}s:18:"setTableCollection";a:6:{s:10:"methodName";s:18:"setTableCollection";s:9:"signature";s:85:"setTableCollection(?Nzoom\Export\Entity\ExportTableCollection $tableCollection): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:245;s:7:"endLine";i:248;s:3:"ccn";i:1;}s:9:"hasTables";a:6:{s:10:"methodName";s:9:"hasTables";s:9:"signature";s:17:"hasTables(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:255;s:7:"endLine";i:258;s:3:"ccn";i:2;}s:8:"getTable";a:6:{s:10:"methodName";s:8:"getTable";s:9:"signature";s:61:"getTable(string $tableType): ?Nzoom\Export\Entity\ExportTable";s:10:"visibility";s:6:"public";s:9:"startLine";i:266;s:7:"endLine";i:269;s:3:"ccn";i:2;}s:8:"addTable";a:6:{s:10:"methodName";s:8:"addTable";s:9:"signature";s:54:"addTable(Nzoom\Export\Entity\ExportTable $table): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:276;s:7:"endLine";i:283;s:3:"ccn";i:2;}s:8:"validate";a:6:{s:10:"methodName";s:8:"validate";s:9:"signature";s:56:"validate(Nzoom\Export\Entity\ExportHeader $header): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:291;s:7:"endLine";i:294;s:3:"ccn";i:1;}s:5:"count";a:6:{s:10:"methodName";s:5:"count";s:9:"signature";s:12:"count(): int";s:10:"visibility";s:6:"public";s:9:"startLine";i:301;s:7:"endLine";i:304;s:3:"ccn";i:1;}s:6:"rewind";a:6:{s:10:"methodName";s:6:"rewind";s:9:"signature";s:14:"rewind(): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:311;s:7:"endLine";i:314;s:3:"ccn";i:1;}s:7:"current";a:6:{s:10:"methodName";s:7:"current";s:9:"signature";s:42:"current(): Nzoom\Export\Entity\ExportValue";s:10:"visibility";s:6:"public";s:9:"startLine";i:321;s:7:"endLine";i:324;s:3:"ccn";i:1;}s:3:"key";a:6:{s:10:"methodName";s:3:"key";s:9:"signature";s:10:"key(): int";s:10:"visibility";s:6:"public";s:9:"startLine";i:331;s:7:"endLine";i:334;s:3:"ccn";i:1;}s:4:"next";a:6:{s:10:"methodName";s:4:"next";s:9:"signature";s:12:"next(): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:339;s:7:"endLine";i:342;s:3:"ccn";i:1;}s:5:"valid";a:6:{s:10:"methodName";s:5:"valid";s:9:"signature";s:13:"valid(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:349;s:7:"endLine";i:352;s:3:"ccn";i:1;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:354;s:18:"commentLinesOfCode";i:168;s:21:"nonCommentLinesOfCode";i:186;}s:15:"ignoredLinesFor";a:1:{i:0;i:11;}s:17:"executableLinesIn";a:55:{i:43;i:6;i:45;i:7;i:46;i:8;i:59;i:9;i:60;i:10;i:61;i:11;i:63;i:12;i:64;i:13;i:78;i:14;i:83;i:16;i:79;i:16;i:80;i:16;i:81;i:16;i:82;i:16;i:86;i:17;i:87;i:18;i:88;i:19;i:90;i:20;i:105;i:21;i:109;i:23;i:106;i:23;i:107;i:23;i:108;i:23;i:112;i:24;i:113;i:25;i:123;i:26;i:134;i:27;i:145;i:28;i:146;i:29;i:149;i:30;i:160;i:31;i:170;i:32;i:172;i:32;i:171;i:33;i:182;i:34;i:184;i:34;i:183;i:35;i:194;i:36;i:204;i:37;i:216;i:38;i:227;i:39;i:237;i:40;i:247;i:41;i:257;i:42;i:268;i:43;i:278;i:44;i:279;i:45;i:282;i:46;i:293;i:47;i:303;i:48;i:313;i:49;i:323;i:50;i:333;i:51;i:341;i:52;i:351;i:53;}}