a:6:{s:9:"classesIn";a:1:{s:45:"Nzoom\Export\Adapter\ExcelExportFormatAdapter";a:6:{s:4:"name";s:24:"ExcelExportFormatAdapter";s:14:"namespacedName";s:45:"Nzoom\Export\Adapter\ExcelExportFormatAdapter";s:9:"namespace";s:20:"Nzoom\Export\Adapter";s:9:"startLine";i:26;s:7:"endLine";i:1095;s:7:"methods";a:33:{s:6:"export";a:6:{s:10:"methodName";s:6:"export";s:9:"signature";s:93:"export($file, string $type, Nzoom\Export\Entity\ExportData $exportData, array $options): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:55;s:7:"endLine";i:97;s:3:"ccn";i:5;}s:20:"extractSizingOptions";a:6:{s:10:"methodName";s:20:"extractSizingOptions";s:9:"signature";s:42:"extractSizingOptions(array $options): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:105;s:7:"endLine";i:129;s:3:"ccn";i:11;}s:17:"createSpreadsheet";a:6:{s:10:"methodName";s:17:"createSpreadsheet";s:9:"signature";s:99:"createSpreadsheet(Nzoom\Export\Entity\ExportData $exportData): PhpOffice\PhpSpreadsheet\Spreadsheet";s:10:"visibility";s:7:"private";s:9:"startLine";i:137;s:7:"endLine";i:159;s:3:"ccn";i:1;}s:20:"setSpreadsheetLocale";a:6:{s:10:"methodName";s:20:"setSpreadsheetLocale";s:9:"signature";s:28:"setSpreadsheetLocale(): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:166;s:7:"endLine";i:182;s:3:"ccn";i:4;}s:20:"getApplicationLocale";a:6:{s:10:"methodName";s:20:"getApplicationLocale";s:9:"signature";s:31:"getApplicationLocale(): ?string";s:10:"visibility";s:7:"private";s:9:"startLine";i:189;s:7:"endLine";i:231;s:3:"ccn";i:4;}s:21:"setDocumentProperties";a:6:{s:10:"methodName";s:21:"setDocumentProperties";s:9:"signature";s:96:"setDocumentProperties(PhpOffice\PhpSpreadsheet\Spreadsheet $spreadsheet, string $filename): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:240;s:7:"endLine";i:252;s:3:"ccn";i:2;}s:17:"processExportData";a:6:{s:10:"methodName";s:17:"processExportData";s:9:"signature";s:120:"processExportData(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, Nzoom\Export\Entity\ExportData $exportData): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:261;s:7:"endLine";i:282;s:3:"ccn";i:1;}s:10:"addHeaders";a:6:{s:10:"methodName";s:10:"addHeaders";s:9:"signature";s:85:"addHeaders(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, array $headers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:291;s:7:"endLine";i:297;s:3:"ccn";i:2;}s:14:"addNamedRanges";a:6:{s:10:"methodName";s:14:"addNamedRanges";s:9:"signature";s:115:"addNamedRanges(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, Nzoom\Export\Entity\ExportHeader $header): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:306;s:7:"endLine";i:333;s:3:"ccn";i:4;}s:14:"styleHeaderRow";a:6:{s:10:"methodName";s:14:"styleHeaderRow";s:9:"signature";s:91:"styleHeaderRow(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, int $headerCount): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:342;s:7:"endLine";i:349;s:3:"ccn";i:1;}s:24:"processExportDataRecords";a:6:{s:10:"methodName";s:24:"processExportDataRecords";s:9:"signature";s:127:"processExportDataRecords(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, Nzoom\Export\Entity\ExportData $exportData): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:358;s:7:"endLine";i:397;s:3:"ccn";i:8;}s:19:"processExportRecord";a:6:{s:10:"methodName";s:19:"processExportRecord";s:9:"signature";s:130:"processExportRecord(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, Nzoom\Export\Entity\ExportRecord $record, int $row): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:407;s:7:"endLine";i:422;s:3:"ccn";i:3;}s:26:"setCellValueWithFormatting";a:6:{s:10:"methodName";s:26:"setCellValueWithFormatting";s:9:"signature";s:152:"setCellValueWithFormatting(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, string $cellAddress, Nzoom\Export\Entity\ExportValue $exportValue): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:432;s:7:"endLine";i:482;s:3:"ccn";i:17;}s:16:"setCellDateValue";a:6:{s:10:"methodName";s:16:"setCellDateValue";s:9:"signature";s:118:"setCellDateValue(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, string $cellAddress, $value, string $type): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:493;s:7:"endLine";i:513;s:3:"ccn";i:5;}s:29:"getExcelFormatFromExportValue";a:6:{s:10:"methodName";s:29:"getExcelFormatFromExportValue";s:9:"signature";s:83:"getExcelFormatFromExportValue(Nzoom\Export\Entity\ExportValue $exportValue): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:521;s:7:"endLine";i:564;s:3:"ccn";i:15;}s:25:"convertCustomNumberFormat";a:6:{s:10:"methodName";s:25:"convertCustomNumberFormat";s:9:"signature";s:49:"convertCustomNumberFormat(string $format): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:572;s:7:"endLine";i:587;s:3:"ccn";i:3;}s:23:"convertCustomDateFormat";a:6:{s:10:"methodName";s:23:"convertCustomDateFormat";s:9:"signature";s:47:"convertCustomDateFormat(string $format): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:595;s:7:"endLine";i:613;s:3:"ccn";i:1;}s:27:"handleExportRecordCellError";a:6:{s:10:"methodName";s:27:"handleExportRecordCellError";s:9:"signature";s:186:"handleExportRecordCellError(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, string $colLetter, int $row, Exception $e, int $colIndex, Nzoom\Export\Entity\ExportRecord $record): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:626;s:7:"endLine";i:635;s:3:"ccn";i:2;}s:25:"finalizeExportDataColumns";a:6:{s:10:"methodName";s:25:"finalizeExportDataColumns";s:9:"signature";s:100:"finalizeExportDataColumns(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, array $headers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:644;s:7:"endLine";i:660;s:3:"ccn";i:2;}s:27:"applyColumnWidthConstraints";a:6:{s:10:"methodName";s:27:"applyColumnWidthConstraints";s:9:"signature";s:86:"applyColumnWidthConstraints(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:668;s:7:"endLine";i:694;s:3:"ccn";i:5;}s:25:"applyRowHeightConstraints";a:6:{s:10:"methodName";s:25:"applyRowHeightConstraints";s:9:"signature";s:84:"applyRowHeightConstraints(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:705;s:7:"endLine";i:754;s:3:"ccn";i:8;}s:22:"applyVerticalAlignment";a:6:{s:10:"methodName";s:22:"applyVerticalAlignment";s:9:"signature";s:81:"applyVerticalAlignment(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:762;s:7:"endLine";i:772;s:3:"ccn";i:3;}s:12:"createWriter";a:6:{s:10:"methodName";s:12:"createWriter";s:9:"signature";s:123:"createWriter(PhpOffice\PhpSpreadsheet\Spreadsheet $spreadsheet, string $extension): PhpOffice\PhpSpreadsheet\Writer\IWriter";s:10:"visibility";s:7:"private";s:9:"startLine";i:782;s:7:"endLine";i:794;s:3:"ccn";i:4;}s:22:"handleSpreadsheetError";a:6:{s:10:"methodName";s:22:"handleSpreadsheetError";s:9:"signature";s:92:"handleSpreadsheetError(PhpOffice\PhpSpreadsheet\Writer\Exception $e, string $filename): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:803;s:7:"endLine";i:825;s:3:"ccn";i:4;}s:18:"getExcelFormatting";a:6:{s:10:"methodName";s:18:"getExcelFormatting";s:9:"signature";s:43:"getExcelFormatting(string $varName): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:833;s:7:"endLine";i:928;s:3:"ccn";i:83;}s:23:"optimizeMemoryForExport";a:6:{s:10:"methodName";s:23:"optimizeMemoryForExport";s:9:"signature";s:31:"optimizeMemoryForExport(): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:935;s:7:"endLine";i:950;s:3:"ccn";i:3;}s:14:"convertToBytes";a:6:{s:10:"methodName";s:14:"convertToBytes";s:9:"signature";s:40:"convertToBytes(string $memoryLimit): int";s:10:"visibility";s:7:"private";s:9:"startLine";i:958;s:7:"endLine";i:976;s:3:"ccn";i:4;}s:22:"getSupportedExtensions";a:6:{s:10:"methodName";s:22:"getSupportedExtensions";s:9:"signature";s:31:"getSupportedExtensions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:981;s:7:"endLine";i:984;s:3:"ccn";i:1;}s:11:"getMimeType";a:6:{s:10:"methodName";s:11:"getMimeType";s:9:"signature";s:35:"getMimeType(string $format): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:989;s:7:"endLine";i:1001;s:3:"ccn";i:4;}s:19:"getDefaultExtension";a:6:{s:10:"methodName";s:19:"getDefaultExtension";s:9:"signature";s:29:"getDefaultExtension(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:1006;s:7:"endLine";i:1009;s:3:"ccn";i:1;}s:14:"supportsFormat";a:6:{s:10:"methodName";s:14:"supportsFormat";s:9:"signature";s:36:"supportsFormat(string $format): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:1014;s:7:"endLine";i:1017;s:3:"ccn";i:1;}s:13:"getFormatName";a:6:{s:10:"methodName";s:13:"getFormatName";s:9:"signature";s:23:"getFormatName(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:1022;s:7:"endLine";i:1025;s:3:"ccn";i:1;}s:16:"getFormatOptions";a:6:{s:10:"methodName";s:16:"getFormatOptions";s:9:"signature";s:25:"getFormatOptions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:1030;s:7:"endLine";i:1094;s:3:"ccn";i:1;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:1096;s:18:"commentLinesOfCode";i:315;s:21:"nonCommentLinesOfCode";i:781;}s:15:"ignoredLinesFor";a:1:{i:0;i:26;}s:17:"executableLinesIn";a:462:{i:55;i:4;i:59;i:5;i:60;i:6;i:64;i:7;i:67;i:8;i:68;i:9;i:69;i:10;i:73;i:11;i:76;i:12;i:79;i:13;i:82;i:14;i:85;i:15;i:86;i:16;i:88;i:17;i:90;i:18;i:91;i:19;i:95;i:20;i:107;i:21;i:108;i:22;i:111;i:23;i:112;i:24;i:115;i:25;i:116;i:26;i:120;i:27;i:122;i:28;i:123;i:29;i:124;i:30;i:125;i:31;i:140;i:32;i:143;i:33;i:146;i:34;i:149;i:35;i:152;i:36;i:153;i:37;i:156;i:38;i:158;i:39;i:170;i:40;i:172;i:41;i:174;i:42;i:176;i:43;i:178;i:44;i:179;i:45;i:192;i:46;i:193;i:47;i:196;i:48;i:197;i:48;i:198;i:48;i:199;i:48;i:200;i:48;i:201;i:48;i:202;i:48;i:203;i:48;i:204;i:48;i:205;i:48;i:206;i:48;i:207;i:48;i:208;i:48;i:209;i:48;i:210;i:48;i:211;i:48;i:212;i:48;i:213;i:48;i:214;i:48;i:216;i:49;i:220;i:50;i:221;i:51;i:225;i:52;i:226;i:53;i:230;i:54;i:242;i:55;i:243;i:56;i:244;i:57;i:247;i:58;i:248;i:58;i:249;i:58;i:250;i:58;i:251;i:58;i:264;i:59;i:265;i:60;i:268;i:61;i:271;i:62;i:274;i:63;i:277;i:64;i:280;i:65;i:293;i:66;i:294;i:67;i:295;i:68;i:308;i:69;i:310;i:70;i:311;i:71;i:312;i:72;i:317;i:73;i:318;i:73;i:319;i:73;i:320;i:73;i:321;i:73;i:322;i:73;i:323;i:73;i:324;i:74;i:326;i:75;i:327;i:76;i:328;i:76;i:329;i:76;i:344;i:77;i:345;i:78;i:346;i:79;i:347;i:79;i:348;i:79;i:360;i:80;i:361;i:81;i:362;i:82;i:365;i:83;i:367;i:84;i:369;i:85;i:370;i:86;i:373;i:87;i:374;i:88;i:377;i:89;i:378;i:90;i:382;i:91;i:383;i:92;i:389;i:93;i:390;i:94;i:394;i:95;i:395;i:96;i:409;i:97;i:411;i:98;i:412;i:99;i:413;i:100;i:417;i:101;i:418;i:102;i:419;i:103;i:434;i:104;i:435;i:105;i:438;i:106;i:439;i:107;i:440;i:108;i:445;i:109;i:446;i:110;i:447;i:111;i:448;i:112;i:450;i:113;i:451;i:114;i:452;i:115;i:453;i:116;i:454;i:117;i:456;i:118;i:457;i:119;i:458;i:120;i:460;i:121;i:461;i:122;i:462;i:123;i:463;i:124;i:464;i:125;i:465;i:126;i:469;i:127;i:470;i:128;i:472;i:129;i:474;i:130;i:478;i:131;i:479;i:132;i:480;i:133;i:496;i:134;i:498;i:135;i:499;i:136;i:500;i:137;i:502;i:138;i:503;i:139;i:504;i:140;i:507;i:141;i:509;i:142;i:511;i:143;i:523;i:144;i:524;i:145;i:527;i:146;i:528;i:147;i:529;i:148;i:531;i:149;i:532;i:150;i:533;i:151;i:535;i:152;i:537;i:153;i:538;i:154;i:540;i:155;i:542;i:156;i:543;i:157;i:545;i:158;i:546;i:159;i:548;i:160;i:550;i:161;i:551;i:162;i:553;i:163;i:554;i:164;i:556;i:165;i:558;i:166;i:559;i:167;i:562;i:168;i:575;i:169;i:577;i:170;i:578;i:171;i:579;i:172;i:581;i:173;i:586;i:174;i:598;i:175;i:599;i:175;i:600;i:175;i:601;i:175;i:602;i:175;i:603;i:175;i:604;i:175;i:605;i:175;i:606;i:175;i:607;i:175;i:608;i:175;i:609;i:175;i:612;i:176;i:629;i:177;i:630;i:178;i:631;i:178;i:634;i:179;i:647;i:180;i:648;i:181;i:649;i:182;i:653;i:183;i:656;i:184;i:659;i:185;i:671;i:186;i:674;i:187;i:675;i:188;i:676;i:189;i:679;i:190;i:680;i:191;i:682;i:192;i:683;i:193;i:686;i:194;i:687;i:195;i:688;i:196;i:689;i:197;i:690;i:198;i:707;i:199;i:708;i:200;i:709;i:201;i:712;i:202;i:713;i:203;i:716;i:204;i:718;i:205;i:719;i:206;i:720;i:207;i:723;i:208;i:724;i:209;i:725;i:210;i:728;i:211;i:729;i:212;i:733;i:213;i:734;i:214;i:735;i:215;i:737;i:216;i:738;i:217;i:739;i:218;i:740;i:219;i:745;i:220;i:747;i:221;i:749;i:222;i:751;i:223;i:764;i:224;i:765;i:225;i:767;i:226;i:769;i:227;i:770;i:228;i:784;i:229;i:787;i:230;i:788;i:231;i:789;i:232;i:790;i:233;i:792;i:234;i:806;i:235;i:807;i:236;i:811;i:237;i:812;i:238;i:815;i:239;i:817;i:240;i:818;i:240;i:819;i:240;i:820;i:240;i:821;i:241;i:836;i:242;i:837;i:243;i:838;i:244;i:839;i:245;i:840;i:246;i:841;i:247;i:842;i:248;i:843;i:249;i:844;i:250;i:845;i:251;i:846;i:252;i:847;i:253;i:848;i:254;i:849;i:255;i:850;i:256;i:851;i:257;i:852;i:258;i:853;i:259;i:854;i:260;i:855;i:261;i:856;i:262;i:857;i:263;i:858;i:264;i:859;i:265;i:860;i:266;i:861;i:267;i:862;i:268;i:863;i:269;i:864;i:270;i:865;i:271;i:866;i:272;i:867;i:273;i:868;i:274;i:869;i:275;i:870;i:276;i:871;i:277;i:872;i:278;i:873;i:279;i:874;i:280;i:875;i:281;i:876;i:282;i:877;i:283;i:878;i:284;i:879;i:285;i:880;i:286;i:881;i:287;i:882;i:288;i:883;i:289;i:884;i:290;i:885;i:291;i:886;i:292;i:887;i:293;i:888;i:294;i:889;i:295;i:890;i:296;i:891;i:297;i:892;i:298;i:893;i:299;i:894;i:300;i:895;i:301;i:896;i:302;i:897;i:303;i:898;i:304;i:899;i:305;i:900;i:306;i:901;i:307;i:903;i:308;i:904;i:309;i:905;i:310;i:906;i:311;i:907;i:312;i:908;i:313;i:909;i:314;i:910;i:315;i:912;i:316;i:913;i:317;i:914;i:318;i:915;i:319;i:916;i:320;i:917;i:321;i:918;i:322;i:919;i:323;i:920;i:324;i:921;i:325;i:922;i:326;i:923;i:327;i:924;i:328;i:927;i:329;i:938;i:330;i:939;i:331;i:943;i:332;i:944;i:333;i:947;i:334;i:948;i:335;i:960;i:336;i:961;i:337;i:962;i:338;i:965;i:339;i:966;i:340;i:968;i:341;i:969;i:342;i:971;i:343;i:972;i:344;i:975;i:345;i:983;i:346;i:991;i:347;i:993;i:348;i:994;i:349;i:995;i:350;i:996;i:351;i:997;i:352;i:999;i:353;i:1008;i:354;i:1016;i:355;i:1024;i:356;i:1032;i:357;i:1033;i:357;i:1034;i:357;i:1035;i:357;i:1036;i:357;i:1037;i:357;i:1038;i:357;i:1039;i:357;i:1040;i:357;i:1041;i:357;i:1042;i:357;i:1043;i:357;i:1044;i:357;i:1045;i:357;i:1046;i:357;i:1047;i:357;i:1048;i:357;i:1049;i:357;i:1050;i:357;i:1051;i:357;i:1052;i:357;i:1053;i:357;i:1054;i:357;i:1055;i:357;i:1056;i:357;i:1057;i:357;i:1058;i:357;i:1059;i:357;i:1060;i:357;i:1061;i:357;i:1062;i:357;i:1063;i:357;i:1064;i:357;i:1065;i:357;i:1066;i:357;i:1067;i:357;i:1068;i:357;i:1069;i:357;i:1070;i:357;i:1071;i:357;i:1072;i:357;i:1073;i:357;i:1074;i:357;i:1075;i:357;i:1076;i:357;i:1077;i:357;i:1078;i:357;i:1079;i:357;i:1080;i:357;i:1081;i:357;i:1082;i:357;i:1083;i:357;i:1084;i:357;i:1085;i:357;i:1086;i:357;i:1087;i:357;i:1088;i:357;i:1089;i:357;i:1090;i:357;i:1091;i:357;i:1092;i:357;i:1093;i:357;}}