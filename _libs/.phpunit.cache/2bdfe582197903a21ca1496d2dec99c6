a:6:{s:9:"classesIn";a:1:{s:45:"Nzoom\Export\Adapter\ExcelExportFormatAdapter";a:6:{s:4:"name";s:24:"ExcelExportFormatAdapter";s:14:"namespacedName";s:45:"Nzoom\Export\Adapter\ExcelExportFormatAdapter";s:9:"namespace";s:20:"Nzoom\Export\Adapter";s:9:"startLine";i:27;s:7:"endLine";i:1677;s:7:"methods";a:53:{s:6:"export";a:6:{s:10:"methodName";s:6:"export";s:9:"signature";s:93:"export($file, string $type, Nzoom\Export\Entity\ExportData $exportData, array $options): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:61;s:7:"endLine";i:103;s:3:"ccn";i:5;}s:20:"extractSizingOptions";a:6:{s:10:"methodName";s:20:"extractSizingOptions";s:9:"signature";s:42:"extractSizingOptions(array $options): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:111;s:7:"endLine";i:138;s:3:"ccn";i:11;}s:17:"createSpreadsheet";a:6:{s:10:"methodName";s:17:"createSpreadsheet";s:9:"signature";s:99:"createSpreadsheet(Nzoom\Export\Entity\ExportData $exportData): PhpOffice\PhpSpreadsheet\Spreadsheet";s:10:"visibility";s:7:"private";s:9:"startLine";i:146;s:7:"endLine";i:175;s:3:"ccn";i:1;}s:20:"setSpreadsheetLocale";a:6:{s:10:"methodName";s:20:"setSpreadsheetLocale";s:9:"signature";s:28:"setSpreadsheetLocale(): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:182;s:7:"endLine";i:198;s:3:"ccn";i:4;}s:20:"getApplicationLocale";a:6:{s:10:"methodName";s:20:"getApplicationLocale";s:9:"signature";s:31:"getApplicationLocale(): ?string";s:10:"visibility";s:7:"private";s:9:"startLine";i:205;s:7:"endLine";i:247;s:3:"ccn";i:4;}s:21:"setDocumentProperties";a:6:{s:10:"methodName";s:21:"setDocumentProperties";s:9:"signature";s:96:"setDocumentProperties(PhpOffice\PhpSpreadsheet\Spreadsheet $spreadsheet, string $filename): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:256;s:7:"endLine";i:268;s:3:"ccn";i:2;}s:17:"processExportData";a:6:{s:10:"methodName";s:17:"processExportData";s:9:"signature";s:120:"processExportData(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, Nzoom\Export\Entity\ExportData $exportData): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:277;s:7:"endLine";i:307;s:3:"ccn";i:2;}s:10:"addHeaders";a:6:{s:10:"methodName";s:10:"addHeaders";s:9:"signature";s:85:"addHeaders(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, array $headers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:316;s:7:"endLine";i:322;s:3:"ccn";i:2;}s:25:"addHeadersWithEnumeration";a:6:{s:10:"methodName";s:25:"addHeadersWithEnumeration";s:9:"signature";s:100:"addHeadersWithEnumeration(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, array $headers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:331;s:7:"endLine";i:341;s:3:"ccn";i:2;}s:14:"addNamedRanges";a:6:{s:10:"methodName";s:14:"addNamedRanges";s:9:"signature";s:141:"addNamedRanges(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, Nzoom\Export\Entity\ExportHeader $header, bool $includeEnumeration): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:351;s:7:"endLine";i:384;s:3:"ccn";i:5;}s:24:"shouldIncludeEnumeration";a:6:{s:10:"methodName";s:24:"shouldIncludeEnumeration";s:9:"signature";s:32:"shouldIncludeEnumeration(): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:391;s:7:"endLine";i:395;s:3:"ccn";i:2;}s:14:"styleHeaderRow";a:6:{s:10:"methodName";s:14:"styleHeaderRow";s:9:"signature";s:91:"styleHeaderRow(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, int $headerCount): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:404;s:7:"endLine";i:411;s:3:"ccn";i:1;}s:24:"processExportDataRecords";a:6:{s:10:"methodName";s:24:"processExportDataRecords";s:9:"signature";s:127:"processExportDataRecords(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, Nzoom\Export\Entity\ExportData $exportData): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:420;s:7:"endLine";i:459;s:3:"ccn";i:8;}s:19:"processExportRecord";a:6:{s:10:"methodName";s:19:"processExportRecord";s:9:"signature";s:130:"processExportRecord(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, Nzoom\Export\Entity\ExportRecord $record, int $row): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:469;s:7:"endLine";i:472;s:3:"ccn";i:1;}s:22:"processMainSheetRecord";a:6:{s:10:"methodName";s:22:"processMainSheetRecord";s:9:"signature";s:133:"processMainSheetRecord(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, Nzoom\Export\Entity\ExportRecord $record, int $row): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:482;s:7:"endLine";i:518;s:3:"ccn";i:6;}s:26:"setCellValueWithFormatting";a:6:{s:10:"methodName";s:26:"setCellValueWithFormatting";s:9:"signature";s:152:"setCellValueWithFormatting(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, string $cellAddress, Nzoom\Export\Entity\ExportValue $exportValue): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:530;s:7:"endLine";i:580;s:3:"ccn";i:17;}s:16:"setCellDateValue";a:6:{s:10:"methodName";s:16:"setCellDateValue";s:9:"signature";s:118:"setCellDateValue(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, string $cellAddress, $value, string $type): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:591;s:7:"endLine";i:611;s:3:"ccn";i:5;}s:29:"getExcelFormatFromExportValue";a:6:{s:10:"methodName";s:29:"getExcelFormatFromExportValue";s:9:"signature";s:83:"getExcelFormatFromExportValue(Nzoom\Export\Entity\ExportValue $exportValue): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:619;s:7:"endLine";i:662;s:3:"ccn";i:15;}s:25:"convertCustomNumberFormat";a:6:{s:10:"methodName";s:25:"convertCustomNumberFormat";s:9:"signature";s:49:"convertCustomNumberFormat(string $format): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:670;s:7:"endLine";i:685;s:3:"ccn";i:3;}s:23:"convertCustomDateFormat";a:6:{s:10:"methodName";s:23:"convertCustomDateFormat";s:9:"signature";s:47:"convertCustomDateFormat(string $format): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:693;s:7:"endLine";i:711;s:3:"ccn";i:1;}s:27:"handleExportRecordCellError";a:6:{s:10:"methodName";s:27:"handleExportRecordCellError";s:9:"signature";s:186:"handleExportRecordCellError(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, string $colLetter, int $row, Exception $e, int $colIndex, Nzoom\Export\Entity\ExportRecord $record): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:724;s:7:"endLine";i:733;s:3:"ccn";i:2;}s:25:"finalizeExportDataColumns";a:6:{s:10:"methodName";s:25:"finalizeExportDataColumns";s:9:"signature";s:100:"finalizeExportDataColumns(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, array $headers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:742;s:7:"endLine";i:751;s:3:"ccn";i:2;}s:24:"finalizeMainSheetColumns";a:6:{s:10:"methodName";s:24:"finalizeMainSheetColumns";s:9:"signature";s:99:"finalizeMainSheetColumns(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, array $headers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:760;s:7:"endLine";i:785;s:3:"ccn";i:3;}s:42:"finalizeMainSheetColumnsWithoutEnumeration";a:6:{s:10:"methodName";s:42:"finalizeMainSheetColumnsWithoutEnumeration";s:9:"signature";s:117:"finalizeMainSheetColumnsWithoutEnumeration(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, array $headers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:794;s:7:"endLine";i:804;s:3:"ccn";i:2;}s:27:"applyColumnWidthConstraints";a:6:{s:10:"methodName";s:27:"applyColumnWidthConstraints";s:9:"signature";s:86:"applyColumnWidthConstraints(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:812;s:7:"endLine";i:838;s:3:"ccn";i:5;}s:25:"applyRowHeightConstraints";a:6:{s:10:"methodName";s:25:"applyRowHeightConstraints";s:9:"signature";s:84:"applyRowHeightConstraints(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:849;s:7:"endLine";i:898;s:3:"ccn";i:8;}s:22:"applyVerticalAlignment";a:6:{s:10:"methodName";s:22:"applyVerticalAlignment";s:9:"signature";s:81:"applyVerticalAlignment(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:906;s:7:"endLine";i:916;s:3:"ccn";i:3;}s:19:"processExportTables";a:6:{s:10:"methodName";s:19:"processExportTables";s:9:"signature";s:120:"processExportTables(PhpOffice\PhpSpreadsheet\Spreadsheet $spreadsheet, Nzoom\Export\Entity\ExportData $exportData): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:925;s:7:"endLine";i:941;s:3:"ccn";i:4;}s:19:"collectTablesByType";a:6:{s:10:"methodName";s:19:"collectTablesByType";s:9:"signature";s:70:"collectTablesByType(Nzoom\Export\Entity\ExportData $exportData): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:949;s:7:"endLine";i:980;s:3:"ccn";i:5;}s:20:"createTableWorksheet";a:6:{s:10:"methodName";s:20:"createTableWorksheet";s:9:"signature";s:169:"createTableWorksheet(PhpOffice\PhpSpreadsheet\Spreadsheet $spreadsheet, string $tableType, array $tablesWithRecordNumbers): ?PhpOffice\PhpSpreadsheet\Worksheet\Worksheet";s:10:"visibility";s:7:"private";s:9:"startLine";i:990;s:7:"endLine";i:1011;s:3:"ccn";i:4;}s:21:"sanitizeWorksheetName";a:6:{s:10:"methodName";s:21:"sanitizeWorksheetName";s:9:"signature";s:43:"sanitizeWorksheetName(string $name): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:1019;s:7:"endLine";i:1024;s:3:"ccn";i:1;}s:22:"populateTableWorksheet";a:6:{s:10:"methodName";s:22:"populateTableWorksheet";s:9:"signature";s:117:"populateTableWorksheet(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $worksheet, array $tablesWithRecordNumbers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:1033;s:7:"endLine";i:1065;s:3:"ccn";i:4;}s:23:"addTableDataToWorksheet";a:6:{s:10:"methodName";s:23:"addTableDataToWorksheet";s:9:"signature";s:166:"addTableDataToWorksheet(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $worksheet, Nzoom\Export\Entity\ExportTable $table, int $startRow, int $startingEnumeration): int";s:10:"visibility";s:7:"private";s:9:"startLine";i:1076;s:7:"endLine";i:1088;s:3:"ccn";i:2;}s:34:"addTableDataToWorksheetWithFullNum";a:6:{s:10:"methodName";s:34:"addTableDataToWorksheetWithFullNum";s:9:"signature";s:200:"addTableDataToWorksheetWithFullNum(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $worksheet, Nzoom\Export\Entity\ExportTable $table, int $startRow, int $startingEnumeration, array $recordMetadata): int";s:10:"visibility";s:7:"private";s:9:"startLine";i:1100;s:7:"endLine";i:1112;s:3:"ccn";i:2;}s:30:"addTableHeadersWithEnumeration";a:6:{s:10:"methodName";s:30:"addTableHeadersWithEnumeration";s:9:"signature";s:109:"addTableHeadersWithEnumeration(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $worksheet, array $headers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:1121;s:7:"endLine";i:1129;s:3:"ccn";i:2;}s:40:"addTableHeadersWithFullNumAndEnumeration";a:6:{s:10:"methodName";s:40:"addTableHeadersWithFullNumAndEnumeration";s:9:"signature";s:119:"addTableHeadersWithFullNumAndEnumeration(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $worksheet, array $headers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:1138;s:7:"endLine";i:1151;s:3:"ccn";i:2;}s:19:"addTableNamedRanges";a:6:{s:10:"methodName";s:19:"addTableNamedRanges";s:9:"signature";s:124:"addTableNamedRanges(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $worksheet, Nzoom\Export\Entity\ExportHeader $header): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:1160;s:7:"endLine";i:1184;s:3:"ccn";i:4;}s:30:"addTableNamedRangesWithFullNum";a:6:{s:10:"methodName";s:30:"addTableNamedRangesWithFullNum";s:9:"signature";s:135:"addTableNamedRangesWithFullNum(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $worksheet, Nzoom\Export\Entity\ExportHeader $header): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:1193;s:7:"endLine";i:1234;s:3:"ccn";i:6;}s:18:"processTableRecord";a:6:{s:10:"methodName";s:18:"processTableRecord";s:9:"signature";s:159:"processTableRecord(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $worksheet, Nzoom\Export\Entity\ExportRecord $record, int $row, int $tableRowEnumeration): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:1245;s:7:"endLine";i:1260;s:3:"ccn";i:3;}s:29:"processTableRecordWithFullNum";a:6:{s:10:"methodName";s:29:"processTableRecordWithFullNum";s:9:"signature";s:193:"processTableRecordWithFullNum(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $worksheet, Nzoom\Export\Entity\ExportRecord $record, int $row, int $tableRowEnumeration, array $recordMetadata): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:1272;s:7:"endLine";i:1293;s:3:"ccn";i:3;}s:20:"finalizeTableColumns";a:6:{s:10:"methodName";s:20:"finalizeTableColumns";s:9:"signature";s:99:"finalizeTableColumns(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $worksheet, array $headers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:1302;s:7:"endLine";i:1320;s:3:"ccn";i:3;}s:31:"finalizeTableColumnsWithFullNum";a:6:{s:10:"methodName";s:31:"finalizeTableColumnsWithFullNum";s:9:"signature";s:110:"finalizeTableColumnsWithFullNum(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $worksheet, array $headers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:1329;s:7:"endLine";i:1354;s:3:"ccn";i:3;}s:12:"createWriter";a:6:{s:10:"methodName";s:12:"createWriter";s:9:"signature";s:123:"createWriter(PhpOffice\PhpSpreadsheet\Spreadsheet $spreadsheet, string $extension): PhpOffice\PhpSpreadsheet\Writer\IWriter";s:10:"visibility";s:7:"private";s:9:"startLine";i:1364;s:7:"endLine";i:1376;s:3:"ccn";i:4;}s:22:"handleSpreadsheetError";a:6:{s:10:"methodName";s:22:"handleSpreadsheetError";s:9:"signature";s:92:"handleSpreadsheetError(PhpOffice\PhpSpreadsheet\Writer\Exception $e, string $filename): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:1385;s:7:"endLine";i:1407;s:3:"ccn";i:4;}s:18:"getExcelFormatting";a:6:{s:10:"methodName";s:18:"getExcelFormatting";s:9:"signature";s:43:"getExcelFormatting(string $varName): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:1415;s:7:"endLine";i:1510;s:3:"ccn";i:83;}s:23:"optimizeMemoryForExport";a:6:{s:10:"methodName";s:23:"optimizeMemoryForExport";s:9:"signature";s:31:"optimizeMemoryForExport(): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:1517;s:7:"endLine";i:1532;s:3:"ccn";i:3;}s:14:"convertToBytes";a:6:{s:10:"methodName";s:14:"convertToBytes";s:9:"signature";s:40:"convertToBytes(string $memoryLimit): int";s:10:"visibility";s:7:"private";s:9:"startLine";i:1540;s:7:"endLine";i:1558;s:3:"ccn";i:4;}s:22:"getSupportedExtensions";a:6:{s:10:"methodName";s:22:"getSupportedExtensions";s:9:"signature";s:31:"getSupportedExtensions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:1563;s:7:"endLine";i:1566;s:3:"ccn";i:1;}s:11:"getMimeType";a:6:{s:10:"methodName";s:11:"getMimeType";s:9:"signature";s:35:"getMimeType(string $format): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:1571;s:7:"endLine";i:1583;s:3:"ccn";i:4;}s:19:"getDefaultExtension";a:6:{s:10:"methodName";s:19:"getDefaultExtension";s:9:"signature";s:29:"getDefaultExtension(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:1588;s:7:"endLine";i:1591;s:3:"ccn";i:1;}s:14:"supportsFormat";a:6:{s:10:"methodName";s:14:"supportsFormat";s:9:"signature";s:36:"supportsFormat(string $format): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:1596;s:7:"endLine";i:1599;s:3:"ccn";i:1;}s:13:"getFormatName";a:6:{s:10:"methodName";s:13:"getFormatName";s:9:"signature";s:23:"getFormatName(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:1604;s:7:"endLine";i:1607;s:3:"ccn";i:1;}s:16:"getFormatOptions";a:6:{s:10:"methodName";s:16:"getFormatOptions";s:9:"signature";s:25:"getFormatOptions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:1612;s:7:"endLine";i:1676;s:3:"ccn";i:1;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:1678;s:18:"commentLinesOfCode";i:507;s:21:"nonCommentLinesOfCode";i:1171;}s:15:"ignoredLinesFor";a:1:{i:0;i:27;}s:17:"executableLinesIn";a:673:{i:61;i:5;i:65;i:6;i:66;i:7;i:70;i:8;i:73;i:9;i:74;i:10;i:75;i:11;i:79;i:12;i:82;i:13;i:85;i:14;i:88;i:15;i:91;i:16;i:92;i:17;i:94;i:18;i:96;i:19;i:97;i:20;i:101;i:21;i:114;i:22;i:116;i:23;i:117;i:24;i:120;i:25;i:121;i:26;i:124;i:27;i:125;i:28;i:129;i:29;i:131;i:30;i:132;i:31;i:133;i:32;i:134;i:33;i:149;i:34;i:152;i:35;i:155;i:36;i:156;i:37;i:159;i:38;i:162;i:39;i:165;i:40;i:166;i:41;i:169;i:42;i:172;i:43;i:174;i:44;i:186;i:45;i:188;i:46;i:190;i:47;i:192;i:48;i:194;i:49;i:195;i:50;i:208;i:51;i:209;i:52;i:212;i:53;i:213;i:53;i:214;i:53;i:215;i:53;i:216;i:53;i:217;i:53;i:218;i:53;i:219;i:53;i:220;i:53;i:221;i:53;i:222;i:53;i:223;i:53;i:224;i:53;i:225;i:53;i:226;i:53;i:227;i:53;i:228;i:53;i:229;i:53;i:230;i:53;i:232;i:54;i:236;i:55;i:237;i:56;i:241;i:57;i:242;i:58;i:246;i:59;i:258;i:60;i:259;i:61;i:260;i:62;i:263;i:63;i:264;i:63;i:265;i:63;i:266;i:63;i:267;i:63;i:280;i:64;i:281;i:65;i:284;i:66;i:286;i:67;i:288;i:68;i:290;i:69;i:293;i:70;i:295;i:71;i:299;i:72;i:302;i:73;i:305;i:74;i:318;i:75;i:319;i:76;i:320;i:77;i:334;i:78;i:337;i:79;i:338;i:80;i:339;i:81;i:353;i:82;i:355;i:83;i:356;i:84;i:358;i:85;i:361;i:86;i:363;i:87;i:368;i:88;i:369;i:88;i:370;i:88;i:371;i:88;i:372;i:88;i:373;i:88;i:374;i:88;i:375;i:89;i:377;i:90;i:378;i:91;i:379;i:91;i:380;i:91;i:394;i:92;i:406;i:93;i:407;i:94;i:408;i:95;i:409;i:95;i:410;i:95;i:422;i:96;i:423;i:97;i:424;i:98;i:427;i:99;i:429;i:100;i:431;i:101;i:432;i:102;i:435;i:103;i:436;i:104;i:439;i:105;i:440;i:106;i:444;i:107;i:445;i:108;i:451;i:109;i:452;i:110;i:456;i:111;i:457;i:112;i:471;i:113;i:484;i:114;i:485;i:115;i:487;i:116;i:489;i:117;i:490;i:118;i:493;i:119;i:494;i:120;i:495;i:121;i:499;i:122;i:500;i:123;i:501;i:124;i:506;i:125;i:507;i:126;i:508;i:127;i:512;i:128;i:513;i:129;i:514;i:130;i:532;i:131;i:533;i:132;i:536;i:133;i:537;i:134;i:538;i:135;i:543;i:136;i:544;i:137;i:545;i:138;i:546;i:139;i:548;i:140;i:549;i:141;i:550;i:142;i:551;i:143;i:552;i:144;i:554;i:145;i:555;i:146;i:556;i:147;i:558;i:148;i:559;i:149;i:560;i:150;i:561;i:151;i:562;i:152;i:563;i:153;i:567;i:154;i:568;i:155;i:570;i:156;i:572;i:157;i:576;i:158;i:577;i:159;i:578;i:160;i:594;i:161;i:596;i:162;i:597;i:163;i:598;i:164;i:600;i:165;i:601;i:166;i:602;i:167;i:605;i:168;i:607;i:169;i:609;i:170;i:621;i:171;i:622;i:172;i:625;i:173;i:626;i:174;i:627;i:175;i:629;i:176;i:630;i:177;i:631;i:178;i:633;i:179;i:635;i:180;i:636;i:181;i:638;i:182;i:640;i:183;i:641;i:184;i:643;i:185;i:644;i:186;i:646;i:187;i:648;i:188;i:649;i:189;i:651;i:190;i:652;i:191;i:654;i:192;i:656;i:193;i:657;i:194;i:660;i:195;i:673;i:196;i:675;i:197;i:676;i:198;i:677;i:199;i:679;i:200;i:684;i:201;i:696;i:202;i:697;i:202;i:698;i:202;i:699;i:202;i:700;i:202;i:701;i:202;i:702;i:202;i:703;i:202;i:704;i:202;i:705;i:202;i:706;i:202;i:707;i:202;i:710;i:203;i:727;i:204;i:728;i:205;i:729;i:205;i:732;i:206;i:744;i:207;i:746;i:208;i:747;i:209;i:749;i:210;i:763;i:211;i:764;i:212;i:765;i:213;i:766;i:214;i:767;i:214;i:768;i:214;i:772;i:215;i:773;i:216;i:774;i:217;i:778;i:218;i:781;i:219;i:784;i:220;i:796;i:221;i:797;i:222;i:798;i:223;i:801;i:224;i:802;i:225;i:803;i:226;i:815;i:227;i:818;i:228;i:819;i:229;i:820;i:230;i:823;i:231;i:824;i:232;i:826;i:233;i:827;i:234;i:830;i:235;i:831;i:236;i:832;i:237;i:833;i:238;i:834;i:239;i:851;i:240;i:852;i:241;i:853;i:242;i:856;i:243;i:857;i:244;i:860;i:245;i:862;i:246;i:863;i:247;i:864;i:248;i:867;i:249;i:868;i:250;i:869;i:251;i:872;i:252;i:873;i:253;i:877;i:254;i:878;i:255;i:879;i:256;i:881;i:257;i:882;i:258;i:883;i:259;i:884;i:260;i:889;i:261;i:891;i:262;i:893;i:263;i:895;i:264;i:908;i:265;i:909;i:266;i:911;i:267;i:913;i:268;i:914;i:269;i:927;i:270;i:929;i:271;i:930;i:272;i:931;i:273;i:935;i:274;i:937;i:275;i:938;i:276;i:951;i:277;i:952;i:278;i:954;i:279;i:955;i:280;i:956;i:281;i:957;i:282;i:960;i:283;i:961;i:284;i:963;i:285;i:964;i:286;i:965;i:287;i:966;i:288;i:969;i:289;i:970;i:289;i:971;i:289;i:972;i:289;i:973;i:289;i:976;i:290;i:979;i:291;i:992;i:292;i:993;i:293;i:997;i:294;i:998;i:295;i:999;i:296;i:1002;i:297;i:1003;i:298;i:1004;i:299;i:1005;i:300;i:1006;i:301;i:1007;i:302;i:1009;i:303;i:1022;i:304;i:1023;i:305;i:1035;i:306;i:1036;i:307;i:1040;i:308;i:1041;i:309;i:1042;i:310;i:1043;i:311;i:1045;i:312;i:1046;i:313;i:1047;i:314;i:1049;i:315;i:1050;i:316;i:1052;i:317;i:1053;i:318;i:1054;i:319;i:1056;i:320;i:1057;i:321;i:1060;i:322;i:1061;i:323;i:1064;i:324;i:1078;i:325;i:1079;i:326;i:1081;i:327;i:1082;i:328;i:1083;i:329;i:1084;i:330;i:1087;i:331;i:1102;i:332;i:1103;i:333;i:1105;i:334;i:1106;i:335;i:1107;i:336;i:1108;i:337;i:1111;i:338;i:1123;i:339;i:1125;i:340;i:1126;i:341;i:1127;i:342;i:1141;i:343;i:1144;i:344;i:1147;i:345;i:1148;i:346;i:1149;i:347;i:1162;i:348;i:1164;i:349;i:1165;i:350;i:1166;i:351;i:1169;i:352;i:1170;i:352;i:1171;i:352;i:1172;i:352;i:1173;i:352;i:1174;i:352;i:1175;i:352;i:1176;i:353;i:1177;i:354;i:1178;i:355;i:1179;i:355;i:1180;i:355;i:1197;i:356;i:1198;i:356;i:1199;i:356;i:1200;i:356;i:1201;i:356;i:1202;i:356;i:1203;i:356;i:1204;i:357;i:1205;i:358;i:1206;i:359;i:1207;i:359;i:1208;i:359;i:1212;i:360;i:1214;i:361;i:1215;i:362;i:1216;i:363;i:1219;i:364;i:1220;i:364;i:1221;i:364;i:1222;i:364;i:1223;i:364;i:1224;i:364;i:1225;i:364;i:1226;i:365;i:1227;i:366;i:1228;i:367;i:1229;i:367;i:1230;i:367;i:1247;i:368;i:1249;i:369;i:1250;i:370;i:1251;i:371;i:1252;i:372;i:1255;i:373;i:1256;i:374;i:1257;i:375;i:1275;i:376;i:1276;i:377;i:1279;i:378;i:1282;i:379;i:1283;i:380;i:1284;i:381;i:1285;i:382;i:1288;i:383;i:1289;i:384;i:1290;i:385;i:1304;i:386;i:1305;i:387;i:1306;i:388;i:1307;i:389;i:1308;i:389;i:1309;i:389;i:1312;i:390;i:1313;i:391;i:1314;i:392;i:1317;i:393;i:1318;i:394;i:1319;i:395;i:1332;i:396;i:1335;i:397;i:1337;i:398;i:1338;i:399;i:1340;i:400;i:1341;i:400;i:1342;i:400;i:1346;i:401;i:1347;i:402;i:1348;i:403;i:1351;i:404;i:1352;i:405;i:1353;i:406;i:1366;i:407;i:1369;i:408;i:1370;i:409;i:1371;i:410;i:1372;i:411;i:1374;i:412;i:1388;i:413;i:1389;i:414;i:1393;i:415;i:1394;i:416;i:1397;i:417;i:1399;i:418;i:1400;i:418;i:1401;i:418;i:1402;i:418;i:1403;i:419;i:1418;i:420;i:1419;i:421;i:1420;i:422;i:1421;i:423;i:1422;i:424;i:1423;i:425;i:1424;i:426;i:1425;i:427;i:1426;i:428;i:1427;i:429;i:1428;i:430;i:1429;i:431;i:1430;i:432;i:1431;i:433;i:1432;i:434;i:1433;i:435;i:1434;i:436;i:1435;i:437;i:1436;i:438;i:1437;i:439;i:1438;i:440;i:1439;i:441;i:1440;i:442;i:1441;i:443;i:1442;i:444;i:1443;i:445;i:1444;i:446;i:1445;i:447;i:1446;i:448;i:1447;i:449;i:1448;i:450;i:1449;i:451;i:1450;i:452;i:1451;i:453;i:1452;i:454;i:1453;i:455;i:1454;i:456;i:1455;i:457;i:1456;i:458;i:1457;i:459;i:1458;i:460;i:1459;i:461;i:1460;i:462;i:1461;i:463;i:1462;i:464;i:1463;i:465;i:1464;i:466;i:1465;i:467;i:1466;i:468;i:1467;i:469;i:1468;i:470;i:1469;i:471;i:1470;i:472;i:1471;i:473;i:1472;i:474;i:1473;i:475;i:1474;i:476;i:1475;i:477;i:1476;i:478;i:1477;i:479;i:1478;i:480;i:1479;i:481;i:1480;i:482;i:1481;i:483;i:1482;i:484;i:1483;i:485;i:1485;i:486;i:1486;i:487;i:1487;i:488;i:1488;i:489;i:1489;i:490;i:1490;i:491;i:1491;i:492;i:1492;i:493;i:1494;i:494;i:1495;i:495;i:1496;i:496;i:1497;i:497;i:1498;i:498;i:1499;i:499;i:1500;i:500;i:1501;i:501;i:1502;i:502;i:1503;i:503;i:1504;i:504;i:1505;i:505;i:1506;i:506;i:1509;i:507;i:1520;i:508;i:1521;i:509;i:1525;i:510;i:1526;i:511;i:1529;i:512;i:1530;i:513;i:1542;i:514;i:1543;i:515;i:1544;i:516;i:1547;i:517;i:1548;i:518;i:1550;i:519;i:1551;i:520;i:1553;i:521;i:1554;i:522;i:1557;i:523;i:1565;i:524;i:1573;i:525;i:1575;i:526;i:1576;i:527;i:1577;i:528;i:1578;i:529;i:1579;i:530;i:1581;i:531;i:1590;i:532;i:1598;i:533;i:1606;i:534;i:1614;i:535;i:1615;i:535;i:1616;i:535;i:1617;i:535;i:1618;i:535;i:1619;i:535;i:1620;i:535;i:1621;i:535;i:1622;i:535;i:1623;i:535;i:1624;i:535;i:1625;i:535;i:1626;i:535;i:1627;i:535;i:1628;i:535;i:1629;i:535;i:1630;i:535;i:1631;i:535;i:1632;i:535;i:1633;i:535;i:1634;i:535;i:1635;i:535;i:1636;i:535;i:1637;i:535;i:1638;i:535;i:1639;i:535;i:1640;i:535;i:1641;i:535;i:1642;i:535;i:1643;i:535;i:1644;i:535;i:1645;i:535;i:1646;i:535;i:1647;i:535;i:1648;i:535;i:1649;i:535;i:1650;i:535;i:1651;i:535;i:1652;i:535;i:1653;i:535;i:1654;i:535;i:1655;i:535;i:1656;i:535;i:1657;i:535;i:1658;i:535;i:1659;i:535;i:1660;i:535;i:1661;i:535;i:1662;i:535;i:1663;i:535;i:1664;i:535;i:1665;i:535;i:1666;i:535;i:1667;i:535;i:1668;i:535;i:1669;i:535;i:1670;i:535;i:1671;i:535;i:1672;i:535;i:1673;i:535;i:1674;i:535;i:1675;i:535;}}