a:6:{s:9:"classesIn";a:1:{s:24:"Nzoom\Export\DataFactory";a:6:{s:4:"name";s:11:"DataFactory";s:14:"namespacedName";s:24:"Nzoom\Export\DataFactory";s:9:"namespace";s:12:"Nzoom\Export";s:9:"startLine";i:16;s:7:"endLine";i:298;s:7:"methods";a:9:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:31:"__construct(Registry $registry)";s:10:"visibility";s:6:"public";s:9:"startLine";i:33;s:7:"endLine";i:36;s:3:"ccn";i:1;}s:8:"__invoke";a:6:{s:10:"methodName";s:8:"__invoke";s:9:"signature";s:73:"__invoke(array $models, Outlook $outlook): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:45;s:7:"endLine";i:63;s:3:"ccn";i:2;}s:23:"createHeaderFromOutlook";a:6:{s:10:"methodName";s:23:"createHeaderFromOutlook";s:9:"signature";s:75:"createHeaderFromOutlook(Outlook $outlook): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:71;s:7:"endLine";i:96;s:3:"ccn";i:5;}s:18:"processModelsChunk";a:6:{s:10:"methodName";s:18:"processModelsChunk";s:9:"signature";s:125:"processModelsChunk(array $models, Nzoom\Export\Entity\ExportData $exportData, Nzoom\Export\Entity\ExportHeader $header): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:105;s:7:"endLine";i:110;s:3:"ccn";i:2;}s:21:"createRecordFromModel";a:6:{s:10:"methodName";s:21:"createRecordFromModel";s:9:"signature";s:111:"createRecordFromModel(Model $model, Nzoom\Export\Entity\ExportHeader $header): Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:119;s:7:"endLine";i:145;s:3:"ccn";i:4;}s:24:"mapFieldTypeToExportType";a:6:{s:10:"methodName";s:24:"mapFieldTypeToExportType";s:9:"signature";s:51:"mapFieldTypeToExportType(string $fieldType): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:153;s:7:"endLine";i:175;s:3:"ccn";i:1;}s:12:"setChunkSize";a:6:{s:10:"methodName";s:12:"setChunkSize";s:9:"signature";s:28:"setChunkSize(int $chunkSize)";s:10:"visibility";s:6:"public";s:9:"startLine";i:182;s:7:"endLine";i:185;s:3:"ccn";i:1;}s:15:"createStreaming";a:6:{s:10:"methodName";s:15:"createStreaming";s:9:"signature";s:118:"createStreaming(string $factoryClass, array $filters, Outlook $outlook, int $pageSize): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:196;s:7:"endLine";i:231;s:3:"ccn";i:2;}s:21:"createCursorStreaming";a:6:{s:10:"methodName";s:21:"createCursorStreaming";s:9:"signature";s:143:"createCursorStreaming(string $modelClass, array $filters, Outlook $outlook, int $pageSize, string $cursorField): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:243;s:7:"endLine";i:297;s:3:"ccn";i:4;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:299;s:18:"commentLinesOfCode";i:98;s:21:"nonCommentLinesOfCode";i:201;}s:15:"ignoredLinesFor";a:1:{i:0;i:16;}s:17:"executableLinesIn";a:104:{i:35;i:3;i:48;i:4;i:51;i:5;i:52;i:5;i:53;i:5;i:54;i:5;i:57;i:6;i:58;i:7;i:59;i:8;i:62;i:9;i:73;i:10;i:74;i:11;i:76;i:12;i:77;i:13;i:78;i:14;i:80;i:15;i:82;i:16;i:83;i:17;i:85;i:18;i:86;i:18;i:87;i:18;i:88;i:18;i:89;i:18;i:90;i:18;i:91;i:18;i:95;i:19;i:107;i:20;i:108;i:21;i:121;i:22;i:123;i:23;i:124;i:24;i:127;i:25;i:130;i:26;i:131;i:27;i:134;i:28;i:135;i:29;i:136;i:30;i:137;i:31;i:141;i:32;i:144;i:33;i:155;i:34;i:156;i:34;i:157;i:34;i:158;i:34;i:159;i:34;i:160;i:34;i:161;i:34;i:162;i:34;i:163;i:34;i:164;i:34;i:165;i:34;i:166;i:34;i:167;i:34;i:168;i:34;i:169;i:34;i:170;i:34;i:171;i:34;i:172;i:34;i:174;i:35;i:184;i:36;i:199;i:37;i:202;i:38;i:203;i:38;i:204;i:38;i:205;i:38;i:208;i:39;i:225;i:39;i:210;i:40;i:211;i:40;i:212;i:40;i:213;i:40;i:216;i:41;i:219;i:42;i:220;i:43;i:221;i:44;i:224;i:45;i:228;i:46;i:230;i:47;i:246;i:48;i:249;i:49;i:250;i:49;i:251;i:49;i:252;i:49;i:255;i:50;i:258;i:51;i:291;i:51;i:260;i:52;i:263;i:53;i:264;i:54;i:265;i:55;i:266;i:56;i:270;i:57;i:271;i:58;i:274;i:59;i:276;i:60;i:277;i:61;i:281;i:62;i:282;i:63;i:285;i:64;i:286;i:65;i:287;i:66;i:290;i:67;i:294;i:68;i:296;i:69;}}