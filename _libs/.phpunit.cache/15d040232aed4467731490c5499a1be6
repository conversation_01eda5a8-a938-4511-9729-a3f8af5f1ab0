a:6:{s:9:"classesIn";a:1:{s:24:"Nzoom\Export\DataFactory";a:6:{s:4:"name";s:11:"DataFactory";s:14:"namespacedName";s:24:"Nzoom\Export\DataFactory";s:9:"namespace";s:12:"Nzoom\Export";s:9:"startLine";i:17;s:7:"endLine";i:427;s:7:"methods";a:16:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:31:"__construct(Registry $registry)";s:10:"visibility";s:6:"public";s:9:"startLine";i:44;s:7:"endLine";i:47;s:3:"ccn";i:1;}s:16:"setTableProvider";a:6:{s:10:"methodName";s:16:"setTableProvider";s:9:"signature";s:105:"setTableProvider(?Nzoom\Export\Provider\ExportTableProviderInterface $tableProvider, array $config): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:55;s:7:"endLine";i:59;s:3:"ccn";i:1;}s:12:"enableTables";a:6:{s:10:"methodName";s:12:"enableTables";s:9:"signature";s:33:"enableTables(array $config): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:66;s:7:"endLine";i:69;s:3:"ccn";i:1;}s:13:"disableTables";a:6:{s:10:"methodName";s:13:"disableTables";s:9:"signature";s:21:"disableTables(): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:74;s:7:"endLine";i:78;s:3:"ccn";i:1;}s:15:"isTablesEnabled";a:6:{s:10:"methodName";s:15:"isTablesEnabled";s:9:"signature";s:23:"isTablesEnabled(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:85;s:7:"endLine";i:88;s:3:"ccn";i:2;}s:22:"withModelTableProvider";a:6:{s:10:"methodName";s:22:"withModelTableProvider";s:9:"signature";s:48:"withModelTableProvider(array $tableConfig): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:96;s:7:"endLine";i:101;s:3:"ccn";i:1;}s:16:"createWithTables";a:6:{s:10:"methodName";s:16:"createWithTables";s:9:"signature";s:101:"createWithTables(array $models, Outlook $outlook, array $tableConfig): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:111;s:7:"endLine";i:118;s:3:"ccn";i:2;}s:8:"__invoke";a:6:{s:10:"methodName";s:8:"__invoke";s:9:"signature";s:73:"__invoke(array $models, Outlook $outlook): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:127;s:7:"endLine";i:150;s:3:"ccn";i:3;}s:23:"createHeaderFromOutlook";a:6:{s:10:"methodName";s:23:"createHeaderFromOutlook";s:9:"signature";s:75:"createHeaderFromOutlook(Outlook $outlook): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:158;s:7:"endLine";i:184;s:3:"ccn";i:6;}s:18:"processModelsChunk";a:6:{s:10:"methodName";s:18:"processModelsChunk";s:9:"signature";s:125:"processModelsChunk(array $models, Nzoom\Export\Entity\ExportData $exportData, Nzoom\Export\Entity\ExportHeader $header): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:193;s:7:"endLine";i:198;s:3:"ccn";i:2;}s:21:"createRecordFromModel";a:6:{s:10:"methodName";s:21:"createRecordFromModel";s:9:"signature";s:111:"createRecordFromModel(Model $model, Nzoom\Export\Entity\ExportHeader $header): Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:207;s:7:"endLine";i:240;s:3:"ccn";i:6;}s:22:"extractTablesForRecord";a:6:{s:10:"methodName";s:22:"extractTablesForRecord";s:9:"signature";s:84:"extractTablesForRecord(Nzoom\Export\Entity\ExportRecord $record, Model $model): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:248;s:7:"endLine";i:264;s:3:"ccn";i:5;}s:24:"mapFieldTypeToExportType";a:6:{s:10:"methodName";s:24:"mapFieldTypeToExportType";s:9:"signature";s:51:"mapFieldTypeToExportType(string $fieldType): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:272;s:7:"endLine";i:294;s:3:"ccn";i:1;}s:12:"setChunkSize";a:6:{s:10:"methodName";s:12:"setChunkSize";s:9:"signature";s:28:"setChunkSize(int $chunkSize)";s:10:"visibility";s:6:"public";s:9:"startLine";i:301;s:7:"endLine";i:304;s:3:"ccn";i:1;}s:15:"createStreaming";a:6:{s:10:"methodName";s:15:"createStreaming";s:9:"signature";s:118:"createStreaming(string $factoryClass, array $filters, Outlook $outlook, int $pageSize): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:315;s:7:"endLine";i:355;s:3:"ccn";i:3;}s:21:"createCursorStreaming";a:6:{s:10:"methodName";s:21:"createCursorStreaming";s:9:"signature";s:143:"createCursorStreaming(string $modelClass, array $filters, Outlook $outlook, int $pageSize, string $cursorField): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:367;s:7:"endLine";i:426;s:3:"ccn";i:5;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:428;s:18:"commentLinesOfCode";i:149;s:21:"nonCommentLinesOfCode";i:279;}s:15:"ignoredLinesFor";a:1:{i:0;i:17;}s:17:"executableLinesIn";a:137:{i:46;i:5;i:55;i:6;i:57;i:7;i:58;i:8;i:66;i:9;i:68;i:10;i:76;i:11;i:77;i:12;i:87;i:13;i:98;i:14;i:99;i:15;i:100;i:16;i:111;i:17;i:113;i:18;i:114;i:19;i:117;i:20;i:130;i:21;i:133;i:22;i:134;i:22;i:135;i:22;i:136;i:22;i:139;i:23;i:140;i:24;i:144;i:25;i:145;i:26;i:146;i:27;i:149;i:28;i:160;i:29;i:161;i:30;i:163;i:31;i:164;i:32;i:166;i:33;i:168;i:34;i:170;i:35;i:171;i:36;i:173;i:37;i:174;i:37;i:175;i:37;i:176;i:37;i:177;i:37;i:178;i:37;i:179;i:37;i:183;i:38;i:195;i:39;i:196;i:40;i:209;i:41;i:210;i:41;i:211;i:41;i:213;i:42;i:214;i:43;i:217;i:44;i:220;i:45;i:221;i:46;i:224;i:47;i:225;i:48;i:226;i:49;i:227;i:50;i:231;i:51;i:235;i:52;i:236;i:53;i:239;i:54;i:251;i:55;i:253;i:56;i:254;i:57;i:256;i:58;i:258;i:59;i:259;i:60;i:260;i:60;i:261;i:60;i:274;i:61;i:275;i:61;i:276;i:61;i:277;i:61;i:278;i:61;i:279;i:61;i:280;i:61;i:281;i:61;i:282;i:61;i:283;i:61;i:284;i:61;i:285;i:61;i:286;i:61;i:287;i:61;i:288;i:61;i:289;i:61;i:290;i:61;i:291;i:61;i:293;i:62;i:303;i:63;i:318;i:64;i:321;i:65;i:322;i:65;i:323;i:65;i:324;i:65;i:327;i:66;i:328;i:67;i:332;i:68;i:349;i:68;i:334;i:69;i:335;i:69;i:336;i:69;i:337;i:69;i:340;i:70;i:343;i:71;i:344;i:72;i:345;i:73;i:348;i:74;i:352;i:75;i:354;i:76;i:370;i:77;i:373;i:78;i:374;i:78;i:375;i:78;i:376;i:78;i:379;i:79;i:380;i:80;i:384;i:81;i:387;i:82;i:420;i:82;i:389;i:83;i:392;i:84;i:393;i:85;i:394;i:86;i:395;i:87;i:399;i:88;i:400;i:89;i:403;i:90;i:405;i:91;i:406;i:92;i:410;i:93;i:411;i:94;i:414;i:95;i:415;i:96;i:416;i:97;i:419;i:98;i:423;i:99;i:425;i:100;}}