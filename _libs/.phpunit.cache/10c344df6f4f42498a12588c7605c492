a:6:{s:9:"classesIn";a:1:{s:40:"Nzoom\Export\Provider\ModelTableProvider";a:6:{s:4:"name";s:18:"ModelTableProvider";s:14:"namespacedName";s:40:"Nzoom\Export\Provider\ModelTableProvider";s:9:"namespace";s:21:"Nzoom\Export\Provider";s:9:"startLine";i:18;s:7:"endLine";i:420;s:7:"methods";a:16:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:47:"__construct(Registry $registry, array $options)";s:10:"visibility";s:6:"public";s:9:"startLine";i:41;s:7:"endLine";i:45;s:3:"ccn";i:1;}s:17:"getDefaultOptions";a:6:{s:10:"methodName";s:17:"getDefaultOptions";s:9:"signature";s:26:"getDefaultOptions(): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:52;s:7:"endLine";i:59;s:3:"ccn";i:1;}s:18:"getTablesForRecord";a:6:{s:10:"methodName";s:18:"getTablesForRecord";s:9:"signature";s:86:"getTablesForRecord($record, array $options): Nzoom\Export\Entity\ExportTableCollection";s:10:"visibility";s:6:"public";s:9:"startLine";i:64;s:7:"endLine";i:94;s:3:"ccn";i:8;}s:25:"discoverGroupingVariables";a:6:{s:10:"methodName";s:25:"discoverGroupingVariables";s:9:"signature";s:46:"discoverGroupingVariables(Model $model): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:102;s:7:"endLine";i:124;s:3:"ccn";i:6;}s:17:"getModelVariables";a:6:{s:10:"methodName";s:17:"getModelVariables";s:9:"signature";s:38:"getModelVariables(Model $model): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:134;s:7:"endLine";i:148;s:3:"ccn";i:1;}s:27:"createTableFromGroupingData";a:6:{s:10:"methodName";s:27:"createTableFromGroupingData";s:9:"signature";s:129:"createTableFromGroupingData(Model $model, string $varName, array $groupingData, array $options): ?Nzoom\Export\Entity\ExportTable";s:10:"visibility";s:7:"private";s:9:"startLine";i:159;s:7:"endLine";i:190;s:3:"ccn";i:3;}s:22:"getOrCreateTableHeader";a:6:{s:10:"methodName";s:22:"getOrCreateTableHeader";s:9:"signature";s:119:"getOrCreateTableHeader(string $tableType, array $names, array $labels, array $hidden): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:201;s:7:"endLine";i:228;s:3:"ccn";i:5;}s:15:"formatTableName";a:6:{s:10:"methodName";s:15:"formatTableName";s:9:"signature";s:40:"formatTableName(string $varName): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:236;s:7:"endLine";i:243;s:3:"ccn";i:1;}s:15:"guessColumnType";a:6:{s:10:"methodName";s:15:"guessColumnType";s:9:"signature";s:40:"guessColumnType(string $varName): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:251;s:7:"endLine";i:278;s:3:"ccn";i:6;}s:29:"populateTableFromGroupingData";a:6:{s:10:"methodName";s:29:"populateTableFromGroupingData";s:9:"signature";s:135:"populateTableFromGroupingData(Nzoom\Export\Entity\ExportTable $table, array $values, array $names, array $hidden, array $options): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:289;s:7:"endLine";i:297;s:3:"ccn";i:3;}s:23:"createRecordFromRowData";a:6:{s:10:"methodName";s:23:"createRecordFromRowData";s:9:"signature";s:119:"createRecordFromRowData(array $rowData, array $names, array $hidden, array $options): ?Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:308;s:7:"endLine";i:327;s:3:"ccn";i:4;}s:11:"formatValue";a:6:{s:10:"methodName";s:11:"formatValue";s:9:"signature";s:66:"formatValue($value, string $type, ?string $format, array $options)";s:10:"visibility";s:7:"private";s:9:"startLine";i:340;s:7:"endLine";i:374;s:3:"ccn";i:13;}s:22:"getSupportedTableTypes";a:6:{s:10:"methodName";s:22:"getSupportedTableTypes";s:9:"signature";s:31:"getSupportedTableTypes(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:379;s:7:"endLine";i:384;s:3:"ccn";i:1;}s:17:"supportsTableType";a:6:{s:10:"methodName";s:17:"supportsTableType";s:9:"signature";s:42:"supportsTableType(string $tableType): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:389;s:7:"endLine";i:393;s:3:"ccn";i:1;}s:21:"getTableConfiguration";a:6:{s:10:"methodName";s:21:"getTableConfiguration";s:9:"signature";s:47:"getTableConfiguration(string $tableType): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:398;s:7:"endLine";i:405;s:3:"ccn";i:1;}s:14:"validateRecord";a:6:{s:10:"methodName";s:14:"validateRecord";s:9:"signature";s:57:"validateRecord($record, array $requestedTableTypes): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:410;s:7:"endLine";i:419;s:3:"ccn";i:2;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:421;s:18:"commentLinesOfCode";i:143;s:21:"nonCommentLinesOfCode";i:278;}s:15:"ignoredLinesFor";a:1:{i:0;i:18;}s:17:"executableLinesIn";a:127:{i:41;i:4;i:43;i:5;i:44;i:6;i:54;i:7;i:55;i:7;i:56;i:7;i:57;i:7;i:58;i:7;i:64;i:8;i:66;i:9;i:67;i:10;i:69;i:11;i:70;i:12;i:75;i:13;i:77;i:14;i:78;i:15;i:80;i:16;i:81;i:17;i:84;i:18;i:86;i:19;i:87;i:20;i:88;i:20;i:89;i:20;i:93;i:21;i:104;i:22;i:108;i:23;i:110;i:24;i:112;i:25;i:114;i:26;i:115;i:27;i:117;i:28;i:119;i:29;i:123;i:30;i:141;i:31;i:142;i:31;i:143;i:31;i:144;i:31;i:145;i:31;i:147;i:32;i:162;i:33;i:163;i:34;i:164;i:35;i:165;i:36;i:167;i:37;i:168;i:38;i:172;i:39;i:175;i:40;i:178;i:41;i:179;i:41;i:180;i:41;i:181;i:41;i:182;i:41;i:183;i:41;i:184;i:41;i:187;i:42;i:189;i:43;i:204;i:44;i:205;i:45;i:209;i:46;i:211;i:47;i:213;i:48;i:214;i:49;i:217;i:50;i:218;i:51;i:220;i:52;i:221;i:53;i:225;i:54;i:227;i:55;i:239;i:56;i:240;i:57;i:242;i:58;i:253;i:59;i:256;i:60;i:257;i:61;i:258;i:62;i:260;i:63;i:264;i:64;i:265;i:65;i:266;i:66;i:268;i:67;i:272;i:68;i:273;i:69;i:277;i:70;i:291;i:71;i:292;i:72;i:293;i:73;i:294;i:74;i:310;i:75;i:312;i:76;i:314;i:77;i:315;i:78;i:319;i:79;i:320;i:80;i:321;i:81;i:323;i:82;i:326;i:83;i:342;i:84;i:343;i:85;i:347;i:86;i:348;i:87;i:349;i:88;i:350;i:89;i:351;i:90;i:353;i:91;i:355;i:92;i:356;i:93;i:357;i:94;i:358;i:95;i:359;i:96;i:361;i:97;i:363;i:98;i:364;i:99;i:366;i:100;i:367;i:101;i:369;i:102;i:370;i:103;i:373;i:104;i:383;i:105;i:392;i:106;i:401;i:107;i:402;i:107;i:403;i:107;i:404;i:107;i:410;i:108;i:412;i:109;i:413;i:110;i:418;i:111;}}