a:6:{s:9:"classesIn";a:1:{s:45:"Nzoom\Export\Adapter\ExcelExportFormatAdapter";a:6:{s:4:"name";s:24:"ExcelExportFormatAdapter";s:14:"namespacedName";s:45:"Nzoom\Export\Adapter\ExcelExportFormatAdapter";s:9:"namespace";s:20:"Nzoom\Export\Adapter";s:9:"startLine";i:27;s:7:"endLine";i:1248;s:7:"methods";a:39:{s:6:"export";a:6:{s:10:"methodName";s:6:"export";s:9:"signature";s:93:"export($file, string $type, Nzoom\Export\Entity\ExportData $exportData, array $options): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:56;s:7:"endLine";i:98;s:3:"ccn";i:5;}s:20:"extractSizingOptions";a:6:{s:10:"methodName";s:20:"extractSizingOptions";s:9:"signature";s:42:"extractSizingOptions(array $options): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:106;s:7:"endLine";i:130;s:3:"ccn";i:11;}s:17:"createSpreadsheet";a:6:{s:10:"methodName";s:17:"createSpreadsheet";s:9:"signature";s:99:"createSpreadsheet(Nzoom\Export\Entity\ExportData $exportData): PhpOffice\PhpSpreadsheet\Spreadsheet";s:10:"visibility";s:7:"private";s:9:"startLine";i:138;s:7:"endLine";i:163;s:3:"ccn";i:1;}s:20:"setSpreadsheetLocale";a:6:{s:10:"methodName";s:20:"setSpreadsheetLocale";s:9:"signature";s:28:"setSpreadsheetLocale(): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:170;s:7:"endLine";i:186;s:3:"ccn";i:4;}s:20:"getApplicationLocale";a:6:{s:10:"methodName";s:20:"getApplicationLocale";s:9:"signature";s:31:"getApplicationLocale(): ?string";s:10:"visibility";s:7:"private";s:9:"startLine";i:193;s:7:"endLine";i:235;s:3:"ccn";i:4;}s:21:"setDocumentProperties";a:6:{s:10:"methodName";s:21:"setDocumentProperties";s:9:"signature";s:96:"setDocumentProperties(PhpOffice\PhpSpreadsheet\Spreadsheet $spreadsheet, string $filename): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:244;s:7:"endLine";i:256;s:3:"ccn";i:2;}s:17:"processExportData";a:6:{s:10:"methodName";s:17:"processExportData";s:9:"signature";s:120:"processExportData(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, Nzoom\Export\Entity\ExportData $exportData): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:265;s:7:"endLine";i:286;s:3:"ccn";i:1;}s:10:"addHeaders";a:6:{s:10:"methodName";s:10:"addHeaders";s:9:"signature";s:85:"addHeaders(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, array $headers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:295;s:7:"endLine";i:301;s:3:"ccn";i:2;}s:14:"addNamedRanges";a:6:{s:10:"methodName";s:14:"addNamedRanges";s:9:"signature";s:115:"addNamedRanges(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, Nzoom\Export\Entity\ExportHeader $header): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:310;s:7:"endLine";i:337;s:3:"ccn";i:4;}s:14:"styleHeaderRow";a:6:{s:10:"methodName";s:14:"styleHeaderRow";s:9:"signature";s:91:"styleHeaderRow(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, int $headerCount): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:346;s:7:"endLine";i:353;s:3:"ccn";i:1;}s:24:"processExportDataRecords";a:6:{s:10:"methodName";s:24:"processExportDataRecords";s:9:"signature";s:127:"processExportDataRecords(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, Nzoom\Export\Entity\ExportData $exportData): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:362;s:7:"endLine";i:401;s:3:"ccn";i:8;}s:19:"processExportRecord";a:6:{s:10:"methodName";s:19:"processExportRecord";s:9:"signature";s:130:"processExportRecord(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, Nzoom\Export\Entity\ExportRecord $record, int $row): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:411;s:7:"endLine";i:426;s:3:"ccn";i:3;}s:26:"setCellValueWithFormatting";a:6:{s:10:"methodName";s:26:"setCellValueWithFormatting";s:9:"signature";s:152:"setCellValueWithFormatting(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, string $cellAddress, Nzoom\Export\Entity\ExportValue $exportValue): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:436;s:7:"endLine";i:486;s:3:"ccn";i:17;}s:16:"setCellDateValue";a:6:{s:10:"methodName";s:16:"setCellDateValue";s:9:"signature";s:118:"setCellDateValue(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, string $cellAddress, $value, string $type): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:497;s:7:"endLine";i:517;s:3:"ccn";i:5;}s:29:"getExcelFormatFromExportValue";a:6:{s:10:"methodName";s:29:"getExcelFormatFromExportValue";s:9:"signature";s:83:"getExcelFormatFromExportValue(Nzoom\Export\Entity\ExportValue $exportValue): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:525;s:7:"endLine";i:568;s:3:"ccn";i:15;}s:25:"convertCustomNumberFormat";a:6:{s:10:"methodName";s:25:"convertCustomNumberFormat";s:9:"signature";s:49:"convertCustomNumberFormat(string $format): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:576;s:7:"endLine";i:591;s:3:"ccn";i:3;}s:23:"convertCustomDateFormat";a:6:{s:10:"methodName";s:23:"convertCustomDateFormat";s:9:"signature";s:47:"convertCustomDateFormat(string $format): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:599;s:7:"endLine";i:617;s:3:"ccn";i:1;}s:27:"handleExportRecordCellError";a:6:{s:10:"methodName";s:27:"handleExportRecordCellError";s:9:"signature";s:186:"handleExportRecordCellError(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, string $colLetter, int $row, Exception $e, int $colIndex, Nzoom\Export\Entity\ExportRecord $record): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:630;s:7:"endLine";i:639;s:3:"ccn";i:2;}s:25:"finalizeExportDataColumns";a:6:{s:10:"methodName";s:25:"finalizeExportDataColumns";s:9:"signature";s:100:"finalizeExportDataColumns(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, array $headers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:648;s:7:"endLine";i:664;s:3:"ccn";i:2;}s:27:"applyColumnWidthConstraints";a:6:{s:10:"methodName";s:27:"applyColumnWidthConstraints";s:9:"signature";s:86:"applyColumnWidthConstraints(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:672;s:7:"endLine";i:698;s:3:"ccn";i:5;}s:25:"applyRowHeightConstraints";a:6:{s:10:"methodName";s:25:"applyRowHeightConstraints";s:9:"signature";s:84:"applyRowHeightConstraints(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:709;s:7:"endLine";i:758;s:3:"ccn";i:8;}s:22:"applyVerticalAlignment";a:6:{s:10:"methodName";s:22:"applyVerticalAlignment";s:9:"signature";s:81:"applyVerticalAlignment(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:766;s:7:"endLine";i:776;s:3:"ccn";i:3;}s:19:"processExportTables";a:6:{s:10:"methodName";s:19:"processExportTables";s:9:"signature";s:120:"processExportTables(PhpOffice\PhpSpreadsheet\Spreadsheet $spreadsheet, Nzoom\Export\Entity\ExportData $exportData): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:785;s:7:"endLine";i:801;s:3:"ccn";i:4;}s:19:"collectTablesByType";a:6:{s:10:"methodName";s:19:"collectTablesByType";s:9:"signature";s:70:"collectTablesByType(Nzoom\Export\Entity\ExportData $exportData): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:809;s:7:"endLine";i:829;s:3:"ccn";i:5;}s:20:"createTableWorksheet";a:6:{s:10:"methodName";s:20:"createTableWorksheet";s:9:"signature";s:152:"createTableWorksheet(PhpOffice\PhpSpreadsheet\Spreadsheet $spreadsheet, string $tableType, array $tables): ?PhpOffice\PhpSpreadsheet\Worksheet\Worksheet";s:10:"visibility";s:7:"private";s:9:"startLine";i:839;s:7:"endLine";i:859;s:3:"ccn";i:4;}s:21:"sanitizeWorksheetName";a:6:{s:10:"methodName";s:21:"sanitizeWorksheetName";s:9:"signature";s:43:"sanitizeWorksheetName(string $name): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:867;s:7:"endLine";i:872;s:3:"ccn";i:1;}s:22:"populateTableWorksheet";a:6:{s:10:"methodName";s:22:"populateTableWorksheet";s:9:"signature";s:100:"populateTableWorksheet(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $worksheet, array $tables): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:881;s:7:"endLine";i:905;s:3:"ccn";i:3;}s:23:"addTableDataToWorksheet";a:6:{s:10:"methodName";s:23:"addTableDataToWorksheet";s:9:"signature";s:140:"addTableDataToWorksheet(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $worksheet, Nzoom\Export\Entity\ExportTable $table, int $startRow): int";s:10:"visibility";s:7:"private";s:9:"startLine";i:915;s:7:"endLine";i:925;s:3:"ccn";i:2;}s:12:"createWriter";a:6:{s:10:"methodName";s:12:"createWriter";s:9:"signature";s:123:"createWriter(PhpOffice\PhpSpreadsheet\Spreadsheet $spreadsheet, string $extension): PhpOffice\PhpSpreadsheet\Writer\IWriter";s:10:"visibility";s:7:"private";s:9:"startLine";i:935;s:7:"endLine";i:947;s:3:"ccn";i:4;}s:22:"handleSpreadsheetError";a:6:{s:10:"methodName";s:22:"handleSpreadsheetError";s:9:"signature";s:92:"handleSpreadsheetError(PhpOffice\PhpSpreadsheet\Writer\Exception $e, string $filename): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:956;s:7:"endLine";i:978;s:3:"ccn";i:4;}s:18:"getExcelFormatting";a:6:{s:10:"methodName";s:18:"getExcelFormatting";s:9:"signature";s:43:"getExcelFormatting(string $varName): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:986;s:7:"endLine";i:1081;s:3:"ccn";i:83;}s:23:"optimizeMemoryForExport";a:6:{s:10:"methodName";s:23:"optimizeMemoryForExport";s:9:"signature";s:31:"optimizeMemoryForExport(): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:1088;s:7:"endLine";i:1103;s:3:"ccn";i:3;}s:14:"convertToBytes";a:6:{s:10:"methodName";s:14:"convertToBytes";s:9:"signature";s:40:"convertToBytes(string $memoryLimit): int";s:10:"visibility";s:7:"private";s:9:"startLine";i:1111;s:7:"endLine";i:1129;s:3:"ccn";i:4;}s:22:"getSupportedExtensions";a:6:{s:10:"methodName";s:22:"getSupportedExtensions";s:9:"signature";s:31:"getSupportedExtensions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:1134;s:7:"endLine";i:1137;s:3:"ccn";i:1;}s:11:"getMimeType";a:6:{s:10:"methodName";s:11:"getMimeType";s:9:"signature";s:35:"getMimeType(string $format): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:1142;s:7:"endLine";i:1154;s:3:"ccn";i:4;}s:19:"getDefaultExtension";a:6:{s:10:"methodName";s:19:"getDefaultExtension";s:9:"signature";s:29:"getDefaultExtension(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:1159;s:7:"endLine";i:1162;s:3:"ccn";i:1;}s:14:"supportsFormat";a:6:{s:10:"methodName";s:14:"supportsFormat";s:9:"signature";s:36:"supportsFormat(string $format): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:1167;s:7:"endLine";i:1170;s:3:"ccn";i:1;}s:13:"getFormatName";a:6:{s:10:"methodName";s:13:"getFormatName";s:9:"signature";s:23:"getFormatName(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:1175;s:7:"endLine";i:1178;s:3:"ccn";i:1;}s:16:"getFormatOptions";a:6:{s:10:"methodName";s:16:"getFormatOptions";s:9:"signature";s:25:"getFormatOptions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:1183;s:7:"endLine";i:1247;s:3:"ccn";i:1;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:1249;s:18:"commentLinesOfCode";i:366;s:21:"nonCommentLinesOfCode";i:883;}s:15:"ignoredLinesFor";a:1:{i:0;i:27;}s:17:"executableLinesIn";a:511:{i:56;i:4;i:60;i:5;i:61;i:6;i:65;i:7;i:68;i:8;i:69;i:9;i:70;i:10;i:74;i:11;i:77;i:12;i:80;i:13;i:83;i:14;i:86;i:15;i:87;i:16;i:89;i:17;i:91;i:18;i:92;i:19;i:96;i:20;i:108;i:21;i:109;i:22;i:112;i:23;i:113;i:24;i:116;i:25;i:117;i:26;i:121;i:27;i:123;i:28;i:124;i:29;i:125;i:30;i:126;i:31;i:141;i:32;i:144;i:33;i:147;i:34;i:150;i:35;i:153;i:36;i:154;i:37;i:157;i:38;i:160;i:39;i:162;i:40;i:174;i:41;i:176;i:42;i:178;i:43;i:180;i:44;i:182;i:45;i:183;i:46;i:196;i:47;i:197;i:48;i:200;i:49;i:201;i:49;i:202;i:49;i:203;i:49;i:204;i:49;i:205;i:49;i:206;i:49;i:207;i:49;i:208;i:49;i:209;i:49;i:210;i:49;i:211;i:49;i:212;i:49;i:213;i:49;i:214;i:49;i:215;i:49;i:216;i:49;i:217;i:49;i:218;i:49;i:220;i:50;i:224;i:51;i:225;i:52;i:229;i:53;i:230;i:54;i:234;i:55;i:246;i:56;i:247;i:57;i:248;i:58;i:251;i:59;i:252;i:59;i:253;i:59;i:254;i:59;i:255;i:59;i:268;i:60;i:269;i:61;i:272;i:62;i:275;i:63;i:278;i:64;i:281;i:65;i:284;i:66;i:297;i:67;i:298;i:68;i:299;i:69;i:312;i:70;i:314;i:71;i:315;i:72;i:316;i:73;i:321;i:74;i:322;i:74;i:323;i:74;i:324;i:74;i:325;i:74;i:326;i:74;i:327;i:74;i:328;i:75;i:330;i:76;i:331;i:77;i:332;i:77;i:333;i:77;i:348;i:78;i:349;i:79;i:350;i:80;i:351;i:80;i:352;i:80;i:364;i:81;i:365;i:82;i:366;i:83;i:369;i:84;i:371;i:85;i:373;i:86;i:374;i:87;i:377;i:88;i:378;i:89;i:381;i:90;i:382;i:91;i:386;i:92;i:387;i:93;i:393;i:94;i:394;i:95;i:398;i:96;i:399;i:97;i:413;i:98;i:415;i:99;i:416;i:100;i:417;i:101;i:421;i:102;i:422;i:103;i:423;i:104;i:438;i:105;i:439;i:106;i:442;i:107;i:443;i:108;i:444;i:109;i:449;i:110;i:450;i:111;i:451;i:112;i:452;i:113;i:454;i:114;i:455;i:115;i:456;i:116;i:457;i:117;i:458;i:118;i:460;i:119;i:461;i:120;i:462;i:121;i:464;i:122;i:465;i:123;i:466;i:124;i:467;i:125;i:468;i:126;i:469;i:127;i:473;i:128;i:474;i:129;i:476;i:130;i:478;i:131;i:482;i:132;i:483;i:133;i:484;i:134;i:500;i:135;i:502;i:136;i:503;i:137;i:504;i:138;i:506;i:139;i:507;i:140;i:508;i:141;i:511;i:142;i:513;i:143;i:515;i:144;i:527;i:145;i:528;i:146;i:531;i:147;i:532;i:148;i:533;i:149;i:535;i:150;i:536;i:151;i:537;i:152;i:539;i:153;i:541;i:154;i:542;i:155;i:544;i:156;i:546;i:157;i:547;i:158;i:549;i:159;i:550;i:160;i:552;i:161;i:554;i:162;i:555;i:163;i:557;i:164;i:558;i:165;i:560;i:166;i:562;i:167;i:563;i:168;i:566;i:169;i:579;i:170;i:581;i:171;i:582;i:172;i:583;i:173;i:585;i:174;i:590;i:175;i:602;i:176;i:603;i:176;i:604;i:176;i:605;i:176;i:606;i:176;i:607;i:176;i:608;i:176;i:609;i:176;i:610;i:176;i:611;i:176;i:612;i:176;i:613;i:176;i:616;i:177;i:633;i:178;i:634;i:179;i:635;i:179;i:638;i:180;i:651;i:181;i:652;i:182;i:653;i:183;i:657;i:184;i:660;i:185;i:663;i:186;i:675;i:187;i:678;i:188;i:679;i:189;i:680;i:190;i:683;i:191;i:684;i:192;i:686;i:193;i:687;i:194;i:690;i:195;i:691;i:196;i:692;i:197;i:693;i:198;i:694;i:199;i:711;i:200;i:712;i:201;i:713;i:202;i:716;i:203;i:717;i:204;i:720;i:205;i:722;i:206;i:723;i:207;i:724;i:208;i:727;i:209;i:728;i:210;i:729;i:211;i:732;i:212;i:733;i:213;i:737;i:214;i:738;i:215;i:739;i:216;i:741;i:217;i:742;i:218;i:743;i:219;i:744;i:220;i:749;i:221;i:751;i:222;i:753;i:223;i:755;i:224;i:768;i:225;i:769;i:226;i:771;i:227;i:773;i:228;i:774;i:229;i:787;i:230;i:789;i:231;i:790;i:232;i:791;i:233;i:795;i:234;i:797;i:235;i:798;i:236;i:811;i:237;i:813;i:238;i:814;i:239;i:815;i:240;i:818;i:241;i:819;i:242;i:820;i:243;i:821;i:244;i:822;i:245;i:824;i:246;i:828;i:247;i:841;i:248;i:842;i:249;i:846;i:250;i:847;i:251;i:850;i:252;i:851;i:253;i:852;i:254;i:853;i:255;i:854;i:256;i:855;i:257;i:857;i:258;i:870;i:259;i:871;i:260;i:883;i:261;i:884;i:262;i:888;i:263;i:889;i:264;i:890;i:265;i:893;i:266;i:894;i:267;i:895;i:268;i:898;i:269;i:899;i:270;i:900;i:271;i:904;i:272;i:917;i:273;i:919;i:274;i:920;i:275;i:921;i:276;i:924;i:277;i:937;i:278;i:940;i:279;i:941;i:280;i:942;i:281;i:943;i:282;i:945;i:283;i:959;i:284;i:960;i:285;i:964;i:286;i:965;i:287;i:968;i:288;i:970;i:289;i:971;i:289;i:972;i:289;i:973;i:289;i:974;i:290;i:989;i:291;i:990;i:292;i:991;i:293;i:992;i:294;i:993;i:295;i:994;i:296;i:995;i:297;i:996;i:298;i:997;i:299;i:998;i:300;i:999;i:301;i:1000;i:302;i:1001;i:303;i:1002;i:304;i:1003;i:305;i:1004;i:306;i:1005;i:307;i:1006;i:308;i:1007;i:309;i:1008;i:310;i:1009;i:311;i:1010;i:312;i:1011;i:313;i:1012;i:314;i:1013;i:315;i:1014;i:316;i:1015;i:317;i:1016;i:318;i:1017;i:319;i:1018;i:320;i:1019;i:321;i:1020;i:322;i:1021;i:323;i:1022;i:324;i:1023;i:325;i:1024;i:326;i:1025;i:327;i:1026;i:328;i:1027;i:329;i:1028;i:330;i:1029;i:331;i:1030;i:332;i:1031;i:333;i:1032;i:334;i:1033;i:335;i:1034;i:336;i:1035;i:337;i:1036;i:338;i:1037;i:339;i:1038;i:340;i:1039;i:341;i:1040;i:342;i:1041;i:343;i:1042;i:344;i:1043;i:345;i:1044;i:346;i:1045;i:347;i:1046;i:348;i:1047;i:349;i:1048;i:350;i:1049;i:351;i:1050;i:352;i:1051;i:353;i:1052;i:354;i:1053;i:355;i:1054;i:356;i:1056;i:357;i:1057;i:358;i:1058;i:359;i:1059;i:360;i:1060;i:361;i:1061;i:362;i:1062;i:363;i:1063;i:364;i:1065;i:365;i:1066;i:366;i:1067;i:367;i:1068;i:368;i:1069;i:369;i:1070;i:370;i:1071;i:371;i:1072;i:372;i:1073;i:373;i:1074;i:374;i:1075;i:375;i:1076;i:376;i:1077;i:377;i:1080;i:378;i:1091;i:379;i:1092;i:380;i:1096;i:381;i:1097;i:382;i:1100;i:383;i:1101;i:384;i:1113;i:385;i:1114;i:386;i:1115;i:387;i:1118;i:388;i:1119;i:389;i:1121;i:390;i:1122;i:391;i:1124;i:392;i:1125;i:393;i:1128;i:394;i:1136;i:395;i:1144;i:396;i:1146;i:397;i:1147;i:398;i:1148;i:399;i:1149;i:400;i:1150;i:401;i:1152;i:402;i:1161;i:403;i:1169;i:404;i:1177;i:405;i:1185;i:406;i:1186;i:406;i:1187;i:406;i:1188;i:406;i:1189;i:406;i:1190;i:406;i:1191;i:406;i:1192;i:406;i:1193;i:406;i:1194;i:406;i:1195;i:406;i:1196;i:406;i:1197;i:406;i:1198;i:406;i:1199;i:406;i:1200;i:406;i:1201;i:406;i:1202;i:406;i:1203;i:406;i:1204;i:406;i:1205;i:406;i:1206;i:406;i:1207;i:406;i:1208;i:406;i:1209;i:406;i:1210;i:406;i:1211;i:406;i:1212;i:406;i:1213;i:406;i:1214;i:406;i:1215;i:406;i:1216;i:406;i:1217;i:406;i:1218;i:406;i:1219;i:406;i:1220;i:406;i:1221;i:406;i:1222;i:406;i:1223;i:406;i:1224;i:406;i:1225;i:406;i:1226;i:406;i:1227;i:406;i:1228;i:406;i:1229;i:406;i:1230;i:406;i:1231;i:406;i:1232;i:406;i:1233;i:406;i:1234;i:406;i:1235;i:406;i:1236;i:406;i:1237;i:406;i:1238;i:406;i:1239;i:406;i:1240;i:406;i:1241;i:406;i:1242;i:406;i:1243;i:406;i:1244;i:406;i:1245;i:406;i:1246;i:406;}}