a:6:{s:9:"classesIn";a:1:{s:24:"Nzoom\Export\DataFactory";a:6:{s:4:"name";s:11:"DataFactory";s:14:"namespacedName";s:24:"Nzoom\Export\DataFactory";s:9:"namespace";s:12:"Nzoom\Export";s:9:"startLine";i:17;s:7:"endLine";i:436;s:7:"methods";a:14:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:31:"__construct(Registry $registry)";s:10:"visibility";s:6:"public";s:9:"startLine";i:39;s:7:"endLine";i:42;s:3:"ccn";i:1;}s:16:"setTableProvider";a:6:{s:10:"methodName";s:16:"setTableProvider";s:9:"signature";s:90:"setTableProvider(?Nzoom\Export\Provider\ExportTableProviderInterface $tableProvider): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:49;s:7:"endLine";i:52;s:3:"ccn";i:1;}s:15:"isTablesEnabled";a:6:{s:10:"methodName";s:15:"isTablesEnabled";s:9:"signature";s:23:"isTablesEnabled(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:59;s:7:"endLine";i:62;s:3:"ccn";i:1;}s:22:"withModelTableProvider";a:6:{s:10:"methodName";s:22:"withModelTableProvider";s:9:"signature";s:44:"withModelTableProvider(array $options): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:70;s:7:"endLine";i:75;s:3:"ccn";i:1;}s:8:"__invoke";a:6:{s:10:"methodName";s:8:"__invoke";s:9:"signature";s:73:"__invoke(array $models, Outlook $outlook): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:84;s:7:"endLine";i:104;s:3:"ccn";i:2;}s:23:"createHeaderFromOutlook";a:6:{s:10:"methodName";s:23:"createHeaderFromOutlook";s:9:"signature";s:75:"createHeaderFromOutlook(Outlook $outlook): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:112;s:7:"endLine";i:138;s:3:"ccn";i:6;}s:18:"processModelsChunk";a:6:{s:10:"methodName";s:18:"processModelsChunk";s:9:"signature";s:125:"processModelsChunk(array $models, Nzoom\Export\Entity\ExportData $exportData, Nzoom\Export\Entity\ExportHeader $header): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:147;s:7:"endLine";i:152;s:3:"ccn";i:2;}s:21:"createRecordFromModel";a:6:{s:10:"methodName";s:21:"createRecordFromModel";s:9:"signature";s:111:"createRecordFromModel(Model $model, Nzoom\Export\Entity\ExportHeader $header): Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:161;s:7:"endLine";i:202;s:3:"ccn";i:7;}s:15:"getModelFullNum";a:6:{s:10:"methodName";s:15:"getModelFullNum";s:9:"signature";s:38:"getModelFullNum(Model $model): ?string";s:10:"visibility";s:7:"private";s:9:"startLine";i:210;s:7:"endLine";i:255;s:3:"ccn";i:10;}s:22:"extractTablesForRecord";a:6:{s:10:"methodName";s:22:"extractTablesForRecord";s:9:"signature";s:84:"extractTablesForRecord(Nzoom\Export\Entity\ExportRecord $record, Model $model): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:263;s:7:"endLine";i:279;s:3:"ccn";i:5;}s:24:"mapFieldTypeToExportType";a:6:{s:10:"methodName";s:24:"mapFieldTypeToExportType";s:9:"signature";s:51:"mapFieldTypeToExportType(string $fieldType): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:287;s:7:"endLine";i:309;s:3:"ccn";i:1;}s:12:"setChunkSize";a:6:{s:10:"methodName";s:12:"setChunkSize";s:9:"signature";s:28:"setChunkSize(int $chunkSize)";s:10:"visibility";s:6:"public";s:9:"startLine";i:316;s:7:"endLine";i:319;s:3:"ccn";i:1;}s:15:"createStreaming";a:6:{s:10:"methodName";s:15:"createStreaming";s:9:"signature";s:118:"createStreaming(string $factoryClass, array $filters, Outlook $outlook, int $pageSize): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:330;s:7:"endLine";i:367;s:3:"ccn";i:2;}s:21:"createCursorStreaming";a:6:{s:10:"methodName";s:21:"createCursorStreaming";s:9:"signature";s:143:"createCursorStreaming(string $modelClass, array $filters, Outlook $outlook, int $pageSize, string $cursorField): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:379;s:7:"endLine";i:435;s:3:"ccn";i:4;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:437;s:18:"commentLinesOfCode";i:140;s:21:"nonCommentLinesOfCode";i:297;}s:15:"ignoredLinesFor";a:1:{i:0;i:17;}s:17:"executableLinesIn";a:147:{i:41;i:4;i:51;i:5;i:61;i:6;i:70;i:7;i:72;i:8;i:73;i:9;i:74;i:10;i:87;i:11;i:90;i:12;i:91;i:12;i:92;i:12;i:93;i:12;i:98;i:13;i:99;i:14;i:100;i:15;i:103;i:16;i:114;i:17;i:115;i:18;i:117;i:19;i:118;i:20;i:120;i:21;i:122;i:22;i:124;i:23;i:125;i:24;i:127;i:25;i:128;i:25;i:129;i:25;i:130;i:25;i:131;i:25;i:132;i:25;i:133;i:25;i:137;i:26;i:149;i:27;i:150;i:28;i:163;i:29;i:164;i:29;i:165;i:29;i:168;i:30;i:169;i:31;i:170;i:32;i:173;i:33;i:175;i:34;i:176;i:35;i:179;i:36;i:182;i:37;i:183;i:38;i:186;i:39;i:187;i:40;i:188;i:41;i:189;i:42;i:193;i:43;i:197;i:44;i:198;i:45;i:201;i:46;i:213;i:47;i:214;i:48;i:215;i:49;i:219;i:50;i:222;i:51;i:223;i:52;i:224;i:53;i:225;i:54;i:230;i:55;i:231;i:56;i:232;i:57;i:233;i:58;i:238;i:59;i:239;i:60;i:240;i:61;i:241;i:62;i:246;i:63;i:247;i:64;i:248;i:65;i:250;i:66;i:254;i:67;i:266;i:68;i:268;i:69;i:269;i:70;i:271;i:71;i:273;i:72;i:274;i:73;i:275;i:73;i:276;i:73;i:289;i:74;i:290;i:74;i:291;i:74;i:292;i:74;i:293;i:74;i:294;i:74;i:295;i:74;i:296;i:74;i:297;i:74;i:298;i:74;i:299;i:74;i:300;i:74;i:301;i:74;i:302;i:74;i:303;i:74;i:304;i:74;i:305;i:74;i:306;i:74;i:308;i:75;i:318;i:76;i:333;i:77;i:336;i:78;i:337;i:78;i:338;i:78;i:339;i:78;i:344;i:79;i:361;i:79;i:346;i:80;i:347;i:80;i:348;i:80;i:349;i:80;i:352;i:81;i:355;i:82;i:356;i:83;i:357;i:84;i:360;i:85;i:364;i:86;i:366;i:87;i:382;i:88;i:385;i:89;i:386;i:89;i:387;i:89;i:388;i:89;i:393;i:90;i:396;i:91;i:429;i:91;i:398;i:92;i:401;i:93;i:402;i:94;i:403;i:95;i:404;i:96;i:408;i:97;i:409;i:98;i:412;i:99;i:414;i:100;i:415;i:101;i:419;i:102;i:420;i:103;i:423;i:104;i:424;i:105;i:425;i:106;i:428;i:107;i:432;i:108;i:434;i:109;}}