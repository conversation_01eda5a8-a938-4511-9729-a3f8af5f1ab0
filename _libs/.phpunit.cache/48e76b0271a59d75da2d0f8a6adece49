a:6:{s:9:"classesIn";a:1:{s:40:"Nzoom\Export\Provider\ModelTableProvider";a:6:{s:4:"name";s:18:"ModelTableProvider";s:14:"namespacedName";s:40:"Nzoom\Export\Provider\ModelTableProvider";s:9:"namespace";s:21:"Nzoom\Export\Provider";s:9:"startLine";i:18;s:7:"endLine";i:356;s:7:"methods";a:13:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:46:"__construct(Registry $registry, array $config)";s:10:"visibility";s:6:"public";s:9:"startLine";i:36;s:7:"endLine";i:40;s:3:"ccn";i:1;}s:16:"getDefaultConfig";a:6:{s:10:"methodName";s:16:"getDefaultConfig";s:9:"signature";s:25:"getDefaultConfig(): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:47;s:7:"endLine";i:56;s:3:"ccn";i:1;}s:18:"getTablesForRecord";a:6:{s:10:"methodName";s:18:"getTablesForRecord";s:9:"signature";s:86:"getTablesForRecord($record, array $options): Nzoom\Export\Entity\ExportTableCollection";s:10:"visibility";s:6:"public";s:9:"startLine";i:61;s:7:"endLine";i:87;s:3:"ccn";i:8;}s:12:"extractTable";a:6:{s:10:"methodName";s:12:"extractTable";s:9:"signature";s:96:"extractTable(Model $record, string $tableType, array $options): ?Nzoom\Export\Entity\ExportTable";s:10:"visibility";s:7:"private";s:9:"startLine";i:97;s:7:"endLine";i:140;s:3:"ccn";i:7;}s:17:"createTableHeader";a:6:{s:10:"methodName";s:17:"createTableHeader";s:9:"signature";s:67:"createTableHeader(array $columns): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:148;s:7:"endLine";i:172;s:3:"ccn";i:4;}s:21:"populateTableFromData";a:6:{s:10:"methodName";s:21:"populateTableFromData";s:9:"signature";s:106:"populateTableFromData(Nzoom\Export\Entity\ExportTable $table, $data, array $columns, array $options): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:182;s:7:"endLine";i:206;s:3:"ccn";i:7;}s:20:"createRecordFromItem";a:6:{s:10:"methodName";s:20:"createRecordFromItem";s:9:"signature";s:94:"createRecordFromItem($item, array $columns, array $options): ?Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:216;s:7:"endLine";i:237;s:3:"ccn";i:3;}s:20:"extractValueFromItem";a:6:{s:10:"methodName";s:20:"extractValueFromItem";s:9:"signature";s:42:"extractValueFromItem($item, string $field)";s:10:"visibility";s:7:"private";s:9:"startLine";i:246;s:7:"endLine";i:257;s:3:"ccn";i:5;}s:11:"formatValue";a:6:{s:10:"methodName";s:11:"formatValue";s:9:"signature";s:66:"formatValue($value, string $type, ?string $format, array $options)";s:10:"visibility";s:7:"private";s:9:"startLine";i:268;s:7:"endLine";i:302;s:3:"ccn";i:13;}s:22:"getSupportedTableTypes";a:6:{s:10:"methodName";s:22:"getSupportedTableTypes";s:9:"signature";s:31:"getSupportedTableTypes(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:307;s:7:"endLine";i:310;s:3:"ccn";i:1;}s:17:"supportsTableType";a:6:{s:10:"methodName";s:17:"supportsTableType";s:9:"signature";s:42:"supportsTableType(string $tableType): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:315;s:7:"endLine";i:318;s:3:"ccn";i:1;}s:21:"getTableConfiguration";a:6:{s:10:"methodName";s:21:"getTableConfiguration";s:9:"signature";s:47:"getTableConfiguration(string $tableType): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:323;s:7:"endLine";i:326;s:3:"ccn";i:1;}s:14:"validateRecord";a:6:{s:10:"methodName";s:14:"validateRecord";s:9:"signature";s:57:"validateRecord($record, array $requestedTableTypes): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:331;s:7:"endLine";i:355;s:3:"ccn";i:7;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:357;s:18:"commentLinesOfCode";i:89;s:21:"nonCommentLinesOfCode";i:268;}s:15:"ignoredLinesFor";a:1:{i:0;i:18;}s:17:"executableLinesIn";a:133:{i:36;i:3;i:38;i:4;i:39;i:5;i:49;i:6;i:50;i:6;i:51;i:6;i:52;i:6;i:53;i:6;i:54;i:6;i:55;i:6;i:61;i:7;i:63;i:8;i:64;i:9;i:66;i:10;i:67;i:11;i:70;i:12;i:71;i:13;i:72;i:14;i:75;i:15;i:76;i:16;i:77;i:17;i:80;i:18;i:81;i:19;i:82;i:20;i:86;i:21;i:99;i:22;i:100;i:23;i:101;i:24;i:104;i:25;i:105;i:26;i:106;i:27;i:108;i:28;i:109;i:29;i:113;i:30;i:116;i:31;i:117;i:31;i:118;i:31;i:119;i:31;i:120;i:31;i:121;i:31;i:122;i:31;i:126;i:32;i:127;i:33;i:128;i:34;i:130;i:35;i:132;i:36;i:133;i:37;i:134;i:37;i:135;i:37;i:139;i:38;i:150;i:39;i:152;i:40;i:153;i:41;i:154;i:42;i:155;i:43;i:157;i:44;i:158;i:45;i:161;i:46;i:162;i:47;i:164;i:48;i:165;i:49;i:168;i:50;i:171;i:51;i:184;i:52;i:185;i:53;i:188;i:54;i:189;i:55;i:190;i:56;i:191;i:57;i:194;i:58;i:195;i:59;i:196;i:60;i:197;i:61;i:200;i:62;i:201;i:63;i:202;i:64;i:203;i:65;i:218;i:66;i:220;i:67;i:221;i:68;i:222;i:69;i:223;i:70;i:224;i:71;i:226;i:72;i:227;i:73;i:230;i:74;i:231;i:75;i:233;i:76;i:236;i:77;i:248;i:78;i:249;i:79;i:250;i:80;i:251;i:81;i:252;i:82;i:253;i:83;i:256;i:84;i:270;i:85;i:271;i:86;i:275;i:87;i:276;i:88;i:277;i:89;i:278;i:90;i:279;i:91;i:281;i:92;i:283;i:93;i:284;i:94;i:285;i:95;i:286;i:96;i:287;i:97;i:289;i:98;i:291;i:99;i:292;i:100;i:294;i:101;i:295;i:102;i:297;i:103;i:298;i:104;i:301;i:105;i:309;i:106;i:317;i:107;i:325;i:108;i:331;i:109;i:333;i:110;i:334;i:111;i:337;i:112;i:338;i:113;i:341;i:114;i:342;i:115;i:343;i:116;i:346;i:117;i:347;i:118;i:349;i:119;i:350;i:120;i:354;i:121;}}