a:6:{s:9:"classesIn";a:1:{s:41:"Nzoom\Export\Entity\ExportTableCollection";a:6:{s:4:"name";s:21:"ExportTableCollection";s:14:"namespacedName";s:41:"Nzoom\Export\Entity\ExportTableCollection";s:9:"namespace";s:19:"Nzoom\Export\Entity";s:9:"startLine";i:11;s:7:"endLine";i:311;s:7:"methods";a:21:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:28:"__construct(array $metadata)";s:10:"visibility";s:6:"public";s:9:"startLine";i:28;s:7:"endLine";i:31;s:3:"ccn";i:1;}s:8:"addTable";a:6:{s:10:"methodName";s:8:"addTable";s:9:"signature";s:54:"addTable(Nzoom\Export\Entity\ExportTable $table): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:39;s:7:"endLine";i:51;s:3:"ccn";i:2;}s:8:"getTable";a:6:{s:10:"methodName";s:8:"getTable";s:9:"signature";s:61:"getTable(string $tableType): ?Nzoom\Export\Entity\ExportTable";s:10:"visibility";s:6:"public";s:9:"startLine";i:59;s:7:"endLine";i:62;s:3:"ccn";i:1;}s:8:"hasTable";a:6:{s:10:"methodName";s:8:"hasTable";s:9:"signature";s:33:"hasTable(string $tableType): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:70;s:7:"endLine";i:73;s:3:"ccn";i:1;}s:11:"removeTable";a:6:{s:10:"methodName";s:11:"removeTable";s:9:"signature";s:36:"removeTable(string $tableType): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:81;s:7:"endLine";i:89;s:3:"ccn";i:2;}s:9:"getTables";a:6:{s:10:"methodName";s:9:"getTables";s:9:"signature";s:18:"getTables(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:96;s:7:"endLine";i:99;s:3:"ccn";i:1;}s:13:"getTableTypes";a:6:{s:10:"methodName";s:13:"getTableTypes";s:9:"signature";s:22:"getTableTypes(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:106;s:7:"endLine";i:109;s:3:"ccn";i:1;}s:11:"clearTables";a:6:{s:10:"methodName";s:11:"clearTables";s:9:"signature";s:19:"clearTables(): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:114;s:7:"endLine";i:117;s:3:"ccn";i:1;}s:9:"hasTables";a:6:{s:10:"methodName";s:9:"hasTables";s:9:"signature";s:17:"hasTables(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:124;s:7:"endLine";i:127;s:3:"ccn";i:1;}s:20:"getTablesWithRecords";a:6:{s:10:"methodName";s:20:"getTablesWithRecords";s:9:"signature";s:29:"getTablesWithRecords(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:134;s:7:"endLine";i:139;s:3:"ccn";i:1;}s:24:"getTableTypesWithRecords";a:6:{s:10:"methodName";s:24:"getTableTypesWithRecords";s:9:"signature";s:33:"getTableTypesWithRecords(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:146;s:7:"endLine";i:150;s:3:"ccn";i:1;}s:11:"getMetadata";a:6:{s:10:"methodName";s:11:"getMetadata";s:9:"signature";s:20:"getMetadata(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:157;s:7:"endLine";i:160;s:3:"ccn";i:1;}s:11:"setMetadata";a:6:{s:10:"methodName";s:11:"setMetadata";s:9:"signature";s:34:"setMetadata(array $metadata): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:167;s:7:"endLine";i:170;s:3:"ccn";i:1;}s:16:"getMetadataValue";a:6:{s:10:"methodName";s:16:"getMetadataValue";s:9:"signature";s:39:"getMetadataValue(string $key, $default)";s:10:"visibility";s:6:"public";s:9:"startLine";i:179;s:7:"endLine";i:182;s:3:"ccn";i:1;}s:16:"setMetadataValue";a:6:{s:10:"methodName";s:16:"setMetadataValue";s:9:"signature";s:43:"setMetadataValue(string $key, $value): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:190;s:7:"endLine";i:193;s:3:"ccn";i:1;}s:8:"validate";a:6:{s:10:"methodName";s:8:"validate";s:9:"signature";s:16:"validate(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:200;s:7:"endLine";i:209;s:3:"ccn";i:3;}s:19:"getTotalRecordCount";a:6:{s:10:"methodName";s:19:"getTotalRecordCount";s:9:"signature";s:26:"getTotalRecordCount(): int";s:10:"visibility";s:6:"public";s:9:"startLine";i:216;s:7:"endLine";i:223;s:3:"ccn";i:2;}s:5:"count";a:6:{s:10:"methodName";s:5:"count";s:9:"signature";s:12:"count(): int";s:10:"visibility";s:6:"public";s:9:"startLine";i:230;s:7:"endLine";i:233;s:3:"ccn";i:1;}s:11:"getIterator";a:6:{s:10:"methodName";s:11:"getIterator";s:9:"signature";s:26:"getIterator(): Traversable";s:10:"visibility";s:6:"public";s:9:"startLine";i:240;s:7:"endLine";i:243;s:3:"ccn";i:1;}s:7:"toArray";a:6:{s:10:"methodName";s:7:"toArray";s:9:"signature";s:31:"toArray(bool $formatted): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:252;s:7:"endLine";i:266;s:3:"ccn";i:2;}s:9:"fromArray";a:6:{s:10:"methodName";s:9:"fromArray";s:9:"signature";s:28:"fromArray(array $data): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:274;s:7:"endLine";i:310;s:3:"ccn";i:6;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:312;s:18:"commentLinesOfCode";i:129;s:21:"nonCommentLinesOfCode";i:183;}s:15:"ignoredLinesFor";a:1:{i:0;i:11;}s:17:"executableLinesIn";a:67:{i:28;i:3;i:30;i:4;i:41;i:5;i:43;i:6;i:47;i:8;i:44;i:8;i:45;i:8;i:46;i:8;i:50;i:9;i:61;i:10;i:72;i:11;i:83;i:12;i:84;i:13;i:87;i:14;i:88;i:15;i:98;i:16;i:108;i:17;i:116;i:18;i:126;i:19;i:136;i:20;i:138;i:20;i:137;i:21;i:148;i:22;i:149;i:23;i:159;i:24;i:169;i:25;i:181;i:26;i:192;i:27;i:202;i:28;i:203;i:29;i:204;i:30;i:208;i:31;i:218;i:32;i:219;i:33;i:220;i:34;i:222;i:35;i:232;i:36;i:242;i:37;i:254;i:38;i:256;i:39;i:257;i:40;i:258;i:40;i:259;i:40;i:260;i:40;i:261;i:40;i:262;i:40;i:265;i:41;i:276;i:42;i:278;i:43;i:279;i:44;i:280;i:45;i:281;i:46;i:282;i:47;i:285;i:48;i:286;i:49;i:287;i:50;i:288;i:51;i:289;i:52;i:290;i:53;i:295;i:54;i:298;i:55;i:299;i:56;i:300;i:57;i:301;i:58;i:303;i:59;i:306;i:60;i:309;i:61;}}