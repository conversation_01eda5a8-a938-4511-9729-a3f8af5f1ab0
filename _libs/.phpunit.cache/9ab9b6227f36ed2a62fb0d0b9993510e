a:6:{s:9:"classesIn";a:1:{s:40:"Nzoom\Export\Provider\ModelTableProvider";a:6:{s:4:"name";s:18:"ModelTableProvider";s:14:"namespacedName";s:40:"Nzoom\Export\Provider\ModelTableProvider";s:9:"namespace";s:21:"Nzoom\Export\Provider";s:9:"startLine";i:18;s:7:"endLine";i:415;s:7:"methods";a:15:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:47:"__construct(Registry $registry, array $options)";s:10:"visibility";s:6:"public";s:9:"startLine";i:41;s:7:"endLine";i:45;s:3:"ccn";i:1;}s:17:"getDefaultOptions";a:6:{s:10:"methodName";s:17:"getDefaultOptions";s:9:"signature";s:26:"getDefaultOptions(): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:52;s:7:"endLine";i:59;s:3:"ccn";i:1;}s:18:"getTablesForRecord";a:6:{s:10:"methodName";s:18:"getTablesForRecord";s:9:"signature";s:86:"getTablesForRecord($record, array $options): Nzoom\Export\Entity\ExportTableCollection";s:10:"visibility";s:6:"public";s:9:"startLine";i:64;s:7:"endLine";i:94;s:3:"ccn";i:8;}s:25:"discoverGroupingVariables";a:6:{s:10:"methodName";s:25:"discoverGroupingVariables";s:9:"signature";s:46:"discoverGroupingVariables(Model $model): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:102;s:7:"endLine";i:143;s:3:"ccn";i:10;}s:27:"createTableFromGroupingData";a:6:{s:10:"methodName";s:27:"createTableFromGroupingData";s:9:"signature";s:129:"createTableFromGroupingData(Model $model, string $varName, array $groupingData, array $options): ?Nzoom\Export\Entity\ExportTable";s:10:"visibility";s:7:"private";s:9:"startLine";i:154;s:7:"endLine";i:185;s:3:"ccn";i:3;}s:22:"getOrCreateTableHeader";a:6:{s:10:"methodName";s:22:"getOrCreateTableHeader";s:9:"signature";s:119:"getOrCreateTableHeader(string $tableType, array $names, array $labels, array $hidden): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:196;s:7:"endLine";i:223;s:3:"ccn";i:5;}s:15:"formatTableName";a:6:{s:10:"methodName";s:15:"formatTableName";s:9:"signature";s:40:"formatTableName(string $varName): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:231;s:7:"endLine";i:238;s:3:"ccn";i:1;}s:15:"guessColumnType";a:6:{s:10:"methodName";s:15:"guessColumnType";s:9:"signature";s:40:"guessColumnType(string $varName): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:246;s:7:"endLine";i:273;s:3:"ccn";i:6;}s:29:"populateTableFromGroupingData";a:6:{s:10:"methodName";s:29:"populateTableFromGroupingData";s:9:"signature";s:135:"populateTableFromGroupingData(Nzoom\Export\Entity\ExportTable $table, array $values, array $names, array $hidden, array $options): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:284;s:7:"endLine";i:292;s:3:"ccn";i:3;}s:23:"createRecordFromRowData";a:6:{s:10:"methodName";s:23:"createRecordFromRowData";s:9:"signature";s:119:"createRecordFromRowData(array $rowData, array $names, array $hidden, array $options): ?Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:303;s:7:"endLine";i:322;s:3:"ccn";i:4;}s:11:"formatValue";a:6:{s:10:"methodName";s:11:"formatValue";s:9:"signature";s:66:"formatValue($value, string $type, ?string $format, array $options)";s:10:"visibility";s:7:"private";s:9:"startLine";i:335;s:7:"endLine";i:369;s:3:"ccn";i:13;}s:22:"getSupportedTableTypes";a:6:{s:10:"methodName";s:22:"getSupportedTableTypes";s:9:"signature";s:31:"getSupportedTableTypes(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:374;s:7:"endLine";i:379;s:3:"ccn";i:1;}s:17:"supportsTableType";a:6:{s:10:"methodName";s:17:"supportsTableType";s:9:"signature";s:42:"supportsTableType(string $tableType): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:384;s:7:"endLine";i:388;s:3:"ccn";i:1;}s:21:"getTableConfiguration";a:6:{s:10:"methodName";s:21:"getTableConfiguration";s:9:"signature";s:47:"getTableConfiguration(string $tableType): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:393;s:7:"endLine";i:400;s:3:"ccn";i:1;}s:14:"validateRecord";a:6:{s:10:"methodName";s:14:"validateRecord";s:9:"signature";s:57:"validateRecord($record, array $requestedTableTypes): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:405;s:7:"endLine";i:414;s:3:"ccn";i:2;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:416;s:18:"commentLinesOfCode";i:134;s:21:"nonCommentLinesOfCode";i:282;}s:15:"ignoredLinesFor";a:1:{i:0;i:18;}s:17:"executableLinesIn";a:130:{i:41;i:4;i:43;i:5;i:44;i:6;i:54;i:7;i:55;i:7;i:56;i:7;i:57;i:7;i:58;i:7;i:64;i:8;i:66;i:9;i:67;i:10;i:69;i:11;i:70;i:12;i:75;i:13;i:77;i:14;i:78;i:15;i:80;i:16;i:81;i:17;i:84;i:18;i:86;i:19;i:87;i:20;i:88;i:20;i:89;i:20;i:93;i:21;i:104;i:22;i:110;i:23;i:111;i:24;i:115;i:25;i:116;i:26;i:117;i:27;i:120;i:28;i:123;i:29;i:124;i:30;i:128;i:31;i:129;i:32;i:130;i:33;i:133;i:34;i:135;i:35;i:136;i:36;i:137;i:36;i:138;i:36;i:142;i:37;i:157;i:38;i:158;i:39;i:159;i:40;i:160;i:41;i:162;i:42;i:163;i:43;i:167;i:44;i:170;i:45;i:173;i:46;i:174;i:46;i:175;i:46;i:176;i:46;i:177;i:46;i:178;i:46;i:179;i:46;i:182;i:47;i:184;i:48;i:199;i:49;i:200;i:50;i:204;i:51;i:206;i:52;i:208;i:53;i:209;i:54;i:212;i:55;i:213;i:56;i:215;i:57;i:216;i:58;i:220;i:59;i:222;i:60;i:234;i:61;i:235;i:62;i:237;i:63;i:248;i:64;i:251;i:65;i:252;i:66;i:253;i:67;i:255;i:68;i:259;i:69;i:260;i:70;i:261;i:71;i:263;i:72;i:267;i:73;i:268;i:74;i:272;i:75;i:286;i:76;i:287;i:77;i:288;i:78;i:289;i:79;i:305;i:80;i:307;i:81;i:309;i:82;i:310;i:83;i:314;i:84;i:315;i:85;i:316;i:86;i:318;i:87;i:321;i:88;i:337;i:89;i:338;i:90;i:342;i:91;i:343;i:92;i:344;i:93;i:345;i:94;i:346;i:95;i:348;i:96;i:350;i:97;i:351;i:98;i:352;i:99;i:353;i:100;i:354;i:101;i:356;i:102;i:358;i:103;i:359;i:104;i:361;i:105;i:362;i:106;i:364;i:107;i:365;i:108;i:368;i:109;i:378;i:110;i:387;i:111;i:396;i:112;i:397;i:112;i:398;i:112;i:399;i:112;i:405;i:113;i:407;i:114;i:408;i:115;i:413;i:116;}}