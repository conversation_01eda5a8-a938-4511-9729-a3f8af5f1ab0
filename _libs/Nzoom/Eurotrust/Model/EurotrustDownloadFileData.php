<?php

namespace Nzoom\Eurotrust\Model;

use JsonSerializable;

class EurotrustDownloadFileData implements JsonSerializable
{
    private string $transactionID;
    private string $vendorNumber;

    public function __construct(string $transactionID, string $vendorNumber)
    {
        $this->vendorNumber = $vendorNumber;
        $this->transactionID = $transactionID;
    }

    /**
     * @return string
     */
    public function getTransactionID(): string
    {
        return $this->transactionID;
    }

    /**
     * @return string
     */
    public function getVendorNumber(): string
    {
        return $this->vendorNumber;
    }

    public function jsonSerialize()
    {
        $data = [
            'transactionID' => $this->transactionID,
            'vendorNumber' => $this->vendorNumber,
        ];

        return $data;
    }


}
