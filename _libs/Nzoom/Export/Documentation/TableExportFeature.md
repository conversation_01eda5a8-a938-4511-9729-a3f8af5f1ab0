# Export Table Support Feature

## Overview

The Export Table Support feature automatically discovers and includes related table data alongside main export records. It looks for variables with `'type' => 'grouping'` in Model objects and creates separate Excel worksheets for each table type found.

## Key Features

- **Auto-Discovery**: Automatically finds grouping variables in models - no configuration required
- **Non-breaking**: Existing export functionality remains unchanged
- **Streaming Compatible**: Works with both eager and lazy loading
- **Excel Multi-sheet**: Creates separate worksheets for each table type in Excel exports
- **Type Safety**: Tables have their own headers and strongly-typed columns
- **Zero Configuration**: No need to pre-define table structures or relationships

## Core Components

### 1. ExportTable
Represents a single table with its own header and records.

```php
$table = new ExportTable('purchases', 'Purchase History', $header, 'customer_123');
$table->addRecord($record);
```

### 2. ExportTableCollection
Manages multiple tables for a single record.

```php
$collection = new ExportTableCollection();
$collection->addTable($purchaseTable);
$collection->addTable($contactTable);
```

### 3. ExportTableProviderInterface
Defines how to extract table data from source records.

```php
interface ExportTableProviderInterface {
    public function getTablesForRecord($record, array $options = []): ExportTableCollection;
    public function getSupportedTableTypes(): array;
    public function supportsTableType(string $tableType): bool;
}
```

### 4. ModelTableProvider
Automatically discovers tables from Model objects.

```php
// Looks for variables like this in models:
$model->get('purchases') = [
    'type' => 'grouping',
    'names' => ['item', 'price', 'quantity'],
    'labels' => ['Item', 'Price', 'Quantity'],
    'hidden' => [], // Optional: columns to hide
    'values' => [
        ['Laptop', 999.99, 1],
        ['Mouse', 29.99, 2]
    ]
];
```

### 5. Enhanced ExportRecord
Can contain an optional table collection.

```php
$record->setTableCollection($tableCollection);
$record->hasTables(); // Check if record has tables
$record->getTable('purchases'); // Get specific table
```

## Usage Patterns

### Basic Auto-Discovery Setup

```php
$dataFactory = new DataFactory($registry);

// Enable auto-discovery of tables
$dataFactory->withModelTableProvider([
    'include_empty_tables' => false,
    'date_format' => 'd.m.Y'
]);

// Tables are automatically discovered and included
$exportData = $dataFactory($models, $outlook);

// Export with tables
$adapter = new ExcelExportFormatAdapter($registry, 'customers', 'export');
$adapter->export('export_with_tables.xlsx', 'xlsx', $exportData);
```

### Model Structure Requirements

Your Model classes should have grouping variables like this:

```php
class Customer extends Model
{
    public function get($varName)
    {
        if ($varName === 'purchases') {
            return [
                'type' => 'grouping',
                'names' => ['item_name', 'price', 'quantity', 'date'],
                'labels' => ['Item Name', 'Price', 'Quantity', 'Purchase Date'],
                'hidden' => [], // Optional: hide specific columns
                'values' => [
                    ['Laptop', 999.99, 1, '2024-01-15'],
                    ['Mouse', 29.99, 2, '2024-01-20'],
                    ['Keyboard', 79.99, 1, '2024-01-25']
                ]
            ];
        }

        return parent::get($varName);
    }
}
```

### Manual Table Creation

```php
// Create table header
$header = new ExportHeader();
$header->addColumn(new ExportColumn('Item', 'item', 'Item'));
$header->addColumn(new ExportColumn('Price', 'price', 'Price'));

// Create table
$table = new ExportTable('purchases', 'Purchase History', $header);

// Add records
$record = new ExportRecord();
$record->addValue('item', 'Laptop', ExportValue::TYPE_STRING);
$record->addValue('price', 1299.99, ExportValue::TYPE_FLOAT, '2');
$table->addRecord($record);

// Add to main record
$mainRecord->addTable($table);
```

## Configuration Options

### ModelTableProvider Options

- `include_empty_tables`: Whether to include tables with no records (default: false)
- `date_format`: Format for date values (default: 'd.m.Y')
- `datetime_format`: Format for datetime values (default: 'd.m.Y H:i')

### Grouping Variable Structure

Each grouping variable in your model should have:

- `type`: Must be 'grouping' to be discovered
- `names`: Array of column variable names
- `labels`: Array of column display labels
- `hidden`: Array of column names/indices to hide (optional)
- `values`: Two-dimensional array of data rows

### Automatic Type Detection

Column types are automatically detected based on variable names:

- **Dates**: Variables containing 'date', 'time', 'created', 'updated'
- **Numbers**: Variables containing 'id', 'count', 'quantity', 'amount', 'price'
- **Booleans**: Variables starting with 'is_', 'has_', 'can_' or containing 'active', 'enabled'
- **Strings**: Everything else (default)

## Excel Output

When tables are enabled, the Excel export creates:

1. **Main Sheet**: Contains the primary export data (as usual)
2. **Table Sheets**: One worksheet per table type containing:
   - Combined data from all records of that table type
   - Proper headers and formatting
   - Same styling as main sheet

### Worksheet Naming

- Table worksheets are named using the table's `name` property
- Names are sanitized to comply with Excel requirements (max 31 chars, no special chars)

## Performance Considerations

- **Memory Usage**: Tables use the data already in memory - no additional queries
- **Processing Time**: More data means longer export times
- **Streaming**: Works with lazy loading for main records, table data comes from model
- **Shared Headers**: Single header instance per table type reduces memory usage
- **Garbage Collection**: Automatic cleanup during processing

## Error Handling

- **Invalid Tables**: Logged but don't stop export
- **Missing Relations**: Gracefully handled with empty tables
- **Validation Errors**: Can validate records before processing
- **Excel Limits**: Worksheet creation errors are logged

## Backward Compatibility

- **Default Behavior**: Tables are disabled by default
- **Existing Code**: No changes required for existing exports
- **API Stability**: All existing methods work unchanged

## Best Practices

1. **Structure Data Properly**: Ensure grouping variables follow the required format
2. **Use Descriptive Names**: Column names help with automatic type detection
3. **Handle Errors**: Check logs for table extraction issues
4. **Test with Real Data**: Verify table discovery works with your model structure
5. **Conditional Use**: Only enable tables when needed (e.g., user option)
6. **Monitor Memory**: Large datasets with many tables can use significant memory

## Example Use Cases

- **Customer Export**: Include purchase history, contact information, notes
- **Order Export**: Include line items, payments, shipping details
- **Invoice Export**: Include invoice items, payments, adjustments
- **Product Export**: Include variants, pricing history, inventory levels
- **Project Export**: Include tasks, team members, time entries

## Future Enhancements

- Support for other export formats (CSV with multiple files, JSON with nested structure)
- Table relationships and foreign keys
- Aggregated table data (sums, counts, averages)
- Custom table processors for complex data transformations
- Table-level formatting and styling options
