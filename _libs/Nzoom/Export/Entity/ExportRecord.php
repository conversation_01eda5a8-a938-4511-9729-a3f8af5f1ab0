<?php

namespace Nzoom\Export\Entity;

/**
 * Class ExportRecord
 *
 * Represents a record in an export with values
 * Implements Iterator to allow iteration over values
 */
class ExportRecord implements \Iterator, \Countable
{
    /**
     * @var ExportValue[] Array of export values
     */
    private $values = [];

    /**
     * @var int Current position for iterator
     */
    private $position = 0;

    /**
     * @var array Associative array mapping column names to value indices
     */
    private $columnMap = [];

    /**
     * @var array Additional metadata for the record
     */
    private $metadata = [];

    /**
     * @var ExportTableCollection|null Collection of related tables for this record
     */
    private $tableCollection;

    /**
     * ExportRecord constructor
     *
     * @param array $metadata Additional metadata for the record
     */
    public function __construct(array $metadata = [])
    {
        $this->position = 0;
        $this->metadata = $metadata;
    }

    /**
     * Add a value to the record
     *
     * @param string $columnName The column name for this value
     * @param mixed $value The value to add
     * @param string|null $type The expected type of the value
     * @param string|null $format The format specification for the value
     */
    public function addValue(string $columnName, $value, ?string $type = null, ?string $format = null)
    {
        $exportValue = ($value instanceof ExportValue)
            ? $value
            : new ExportValue($value, $type, $format);

        $this->values[] = $exportValue;
        $this->columnMap[$columnName] = count($this->values) - 1;
    }

    /**
     * Set a value at a specific index
     *
     * @param int $index The index to set the value at
     * @param mixed $value The value to set
     * @param string|null $type The expected type of the value
     * @param string|null $format The format specification for the value
     * @throws \OutOfRangeException If the index is out of range
     */
    public function setValueAt(int $index, $value, ?string $type = null, ?string $format = null)
    {
        if ($index < 0 || $index >= count($this->values)) {
            throw new \OutOfRangeException(sprintf(
                'Index %d is out of range (0-%d)',
                $index,
                count($this->values) - 1
            ));
        }

        $exportValue = ($value instanceof ExportValue)
            ? $value
            : new ExportValue($value, $type, $format);

        $this->values[$index] = $exportValue;
    }

    /**
     * Set a value by column name
     *
     * @param string $columnName The column name
     * @param mixed $value The value to set
     * @param string|null $type The expected type of the value
     * @param string|null $format The format specification for the value
     * @return $this
     * @throws \InvalidArgumentException If the column name does not exist
     */
    public function setValueByColumnName(string $columnName, $value, ?string $type = null, ?string $format = null)
    {
        if (!isset($this->columnMap[$columnName])) {
            throw new \InvalidArgumentException(sprintf(
                'Column name "%s" does not exist',
                $columnName
            ));
        }

        $index = $this->columnMap[$columnName];
        return $this->setValueAt($index, $value, $type, $format);
    }

    /**
     * Get all values
     *
     * @return ExportValue[] Array of values
     */
    public function getValues(): array
    {
        return $this->values;
    }

    /**
     * Get value at specific index
     *
     * @param int $index The index of the value
     * @return ExportValue|null The value or null if not found
     */
    public function getValueAt(int $index)
    {
        return $this->values[$index] ?? null;
    }

    /**
     * Get value by column name
     *
     * @param string $columnName The column name to search for
     * @return ExportValue|null The value or null if not found
     */
    public function getValueByColumnName(string $columnName)
    {
        if (!isset($this->columnMap[$columnName])) {
            return null;
        }

        return $this->values[$this->columnMap[$columnName]];
    }

    /**
     * Check if a value exists for the given column name
     *
     * @param string $columnName The column name to check
     * @return bool True if the value exists
     */
    public function hasValue(string $columnName): bool
    {
        return isset($this->columnMap[$columnName]);
    }

    /**
     * Get raw values as array
     *
     * @return array Array of raw values
     */
    public function getRawValues(): array
    {
        return array_map(function (ExportValue $value) {
            return $value->getValue();
        }, $this->values);
    }

    /**
     * Get formatted values as array
     *
     * @return array Array of formatted values
     */
    public function getFormattedValues(): array
    {
        return array_map(function (ExportValue $value) {
            return $value->getFormattedValue();
        }, $this->values);
    }

    /**
     * Get the metadata
     *
     * @return array The metadata
     */
    public function getMetadata(): array
    {
        return $this->metadata;
    }

    /**
     * Set the metadata
     *
     * @param array $metadata The metadata
     */
    public function setMetadata(array $metadata)
    {
        $this->metadata = $metadata;
    }

    /**
     * Get a metadata value
     *
     * @param string $key The metadata key
     * @param mixed $default The default value to return if the key does not exist
     * @return mixed The metadata value or the default value
     */
    public function getMetadataValue(string $key, $default = null)
    {
        return $this->metadata[$key] ?? $default;
    }

    /**
     * Set a metadata value
     *
     * @param string $key The metadata key
     * @param mixed $value The metadata value
     */
    public function setMetadataValue(string $key, $value)
    {
        $this->metadata[$key] = $value;
    }

    /**
     * Get table collection
     *
     * @return ExportTableCollection|null
     */
    public function getTableCollection(): ?ExportTableCollection
    {
        return $this->tableCollection;
    }

    /**
     * Set table collection
     *
     * @param ExportTableCollection|null $tableCollection
     */
    public function setTableCollection(?ExportTableCollection $tableCollection): void
    {
        $this->tableCollection = $tableCollection;
    }

    /**
     * Check if record has tables
     *
     * @return bool
     */
    public function hasTables(): bool
    {
        return $this->tableCollection !== null && $this->tableCollection->hasTables();
    }

    /**
     * Get table by type
     *
     * @param string $tableType
     * @return ExportTable|null
     */
    public function getTable(string $tableType): ?ExportTable
    {
        return $this->tableCollection ? $this->tableCollection->getTable($tableType) : null;
    }

    /**
     * Add table to record
     *
     * @param ExportTable $table
     */
    public function addTable(ExportTable $table): void
    {
        if ($this->tableCollection === null) {
            $this->tableCollection = new ExportTableCollection();
        }

        $this->tableCollection->addTable($table);
    }

    /**
     * Validate the record against a header
     *
     * @param ExportHeader $header The header to validate against
     * @return bool True if valid
     */
    public function validate(ExportHeader $header): bool
    {
        return $header->validateRecord($this);
    }

    /**
     * Get value count
     *
     * @return int The number of values
     */
    public function count(): int
    {
        return count($this->values);
    }

    // Iterator implementation methods

    /**
     * Rewind the Iterator to the first element
     */
    public function rewind(): void
    {
        $this->position = 0;
    }

    /**
     * Return the current element
     *
     * @return ExportValue
     */
    public function current(): ExportValue
    {
        return $this->values[$this->position];
    }

    /**
     * Return the key of the current element
     *
     * @return int
     */
    public function key(): int
    {
        return $this->position;
    }

    /**
     * Move forward to next element
     */
    public function next(): void
    {
        ++$this->position;
    }

    /**
     * Checks if current position is valid
     *
     * @return bool
     */
    public function valid(): bool
    {
        return isset($this->values[$this->position]);
    }
}
