<?php

namespace Nzoom\Export\Entity;

/**
 * Class ExportTableCollection
 *
 * Manages a collection of ExportTable objects for a single record
 * Provides methods to add, retrieve, and manage tables by type
 */
class ExportTableCollection implements \IteratorAggregate, \Countable
{
    /**
     * @var ExportTable[] Array of tables indexed by table type
     */
    private $tables = [];

    /**
     * @var array Additional metadata for the collection
     */
    private $metadata = [];

    /**
     * ExportTableCollection constructor
     *
     * @param array $metadata Additional metadata
     */
    public function __construct(array $metadata = [])
    {
        $this->metadata = $metadata;
    }

    /**
     * Add a table to the collection
     *
     * @param ExportTable $table
     * @throws \InvalidArgumentException If a table with the same type already exists
     */
    public function addTable(ExportTable $table): void
    {
        $tableType = $table->getTableType();

        if ($this->hasTable($tableType)) {
            throw new \InvalidArgumentException(sprintf(
                'A table with type "%s" already exists in this collection',
                $tableType
            ));
        }

        $this->tables[$tableType] = $table;
    }

    /**
     * Get a table by type
     *
     * @param string $tableType
     * @return ExportTable|null
     */
    public function getTable(string $tableType): ?ExportTable
    {
        return $this->tables[$tableType] ?? null;
    }

    /**
     * Check if a table exists by type
     *
     * @param string $tableType
     * @return bool
     */
    public function hasTable(string $tableType): bool
    {
        return isset($this->tables[$tableType]);
    }

    /**
     * Remove a table by type
     *
     * @param string $tableType
     * @return bool True if table was removed, false if it didn't exist
     */
    public function removeTable(string $tableType): bool
    {
        if (!$this->hasTable($tableType)) {
            return false;
        }

        unset($this->tables[$tableType]);
        return true;
    }

    /**
     * Get all tables
     *
     * @return ExportTable[]
     */
    public function getTables(): array
    {
        return $this->tables;
    }

    /**
     * Get all table types
     *
     * @return string[]
     */
    public function getTableTypes(): array
    {
        return array_keys($this->tables);
    }

    /**
     * Clear all tables
     */
    public function clearTables(): void
    {
        $this->tables = [];
    }

    /**
     * Check if collection has any tables
     *
     * @return bool
     */
    public function hasTables(): bool
    {
        return !empty($this->tables);
    }

    /**
     * Get tables that have records
     *
     * @return ExportTable[]
     */
    public function getTablesWithRecords(): array
    {
        return array_filter($this->tables, function (ExportTable $table) {
            return $table->hasRecords();
        });
    }

    /**
     * Get table types that have records
     *
     * @return string[]
     */
    public function getTableTypesWithRecords(): array
    {
        $tablesWithRecords = $this->getTablesWithRecords();
        return array_keys($tablesWithRecords);
    }

    /**
     * Get metadata
     *
     * @return array
     */
    public function getMetadata(): array
    {
        return $this->metadata;
    }

    /**
     * Set metadata
     *
     * @param array $metadata
     */
    public function setMetadata(array $metadata): void
    {
        $this->metadata = $metadata;
    }

    /**
     * Get metadata value by key
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function getMetadataValue(string $key, $default = null)
    {
        return $this->metadata[$key] ?? $default;
    }

    /**
     * Set metadata value
     *
     * @param string $key
     * @param mixed $value
     */
    public function setMetadataValue(string $key, $value): void
    {
        $this->metadata[$key] = $value;
    }

    /**
     * Validate all tables in the collection
     *
     * @return bool
     */
    public function validate(): bool
    {
        foreach ($this->tables as $table) {
            if (!$table->validate()) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get total record count across all tables
     *
     * @return int
     */
    public function getTotalRecordCount(): int
    {
        $total = 0;
        foreach ($this->tables as $table) {
            $total += $table->count();
        }
        return $total;
    }

    /**
     * Get table count
     *
     * @return int
     */
    public function count(): int
    {
        return count($this->tables);
    }

    /**
     * Get iterator for tables
     *
     * @return \Traversable
     */
    public function getIterator(): \Traversable
    {
        return new \ArrayIterator($this->tables);
    }

    /**
     * Convert collection to array format
     * Returns array indexed by table type, each containing table data
     *
     * @param bool $formatted Whether to use formatted values
     * @return array
     */
    public function toArray(bool $formatted = false): array
    {
        $result = [];

        foreach ($this->tables as $tableType => $table) {
            $result[$tableType] = [
                'table_name' => $table->getTableName(),
                'parent_reference' => $table->getParentReference(),
                'metadata' => $table->getMetadata(),
                'data' => $table->toArray($formatted)
            ];
        }

        return $result;
    }

    /**
     * Create a collection from array data
     *
     * @param array $data Array data in the format returned by toArray()
     * @return self
     */
    public static function fromArray(array $data): self
    {
        $collection = new self();

        foreach ($data as $tableType => $tableData) {
            $tableName = $tableData['table_name'] ?? $tableType;
            $parentReference = $tableData['parent_reference'] ?? null;
            $metadata = $tableData['metadata'] ?? [];
            $tableArrayData = $tableData['data'] ?? [];

            // Create header from first row if data exists
            $header = new ExportHeader();
            if (!empty($tableArrayData)) {
                $headerRow = array_shift($tableArrayData);
                foreach ($headerRow as $index => $label) {
                    $column = new ExportColumn($label, "col_{$index}", $label);
                    $header->addColumn($column);
                }
            }

            // Create table
            $table = new ExportTable($tableType, $tableName, $header, $parentReference, $metadata);

            // Add records
            foreach ($tableArrayData as $rowData) {
                $record = new ExportRecord();
                foreach ($rowData as $index => $value) {
                    $record->addValue("col_{$index}", $value);
                }
                $table->addRecord($record, false); // Skip validation for simplicity
            }

            $collection->addTable($table);
        }

        return $collection;
    }
}
