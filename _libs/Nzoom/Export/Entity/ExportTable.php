<?php

namespace Nzoom\Export\Entity;

/**
 * Class ExportTable
 *
 * Represents a table of related data that can be exported alongside main records
 * Each table has its own header and collection of records
 */
class ExportTable implements \IteratorAggregate, \Countable
{
    /**
     * @var string Unique identifier for this table type
     */
    private $tableType;

    /**
     * @var string Human-readable name for this table
     */
    private $tableName;

    /**
     * @var ExportHeader The header for this table
     */
    private $header;

    /**
     * @var ExportRecord[] Array of records in this table
     */
    private $records = [];

    /**
     * @var array Additional metadata for the table
     */
    private $metadata = [];

    /**
     * @var string|null Reference to the parent record (e.g., record ID)
     */
    private $parentReference;

    /**
     * ExportTable constructor
     *
     * @param string $tableType Unique identifier for this table type
     * @param string $tableName Human-readable name for this table
     * @param ExportHeader|null $header The header for this table
     * @param string|null $parentReference Reference to the parent record
     * @param array $metadata Additional metadata
     */
    public function __construct(
        string $tableType,
        string $tableName,
        ExportHeader $header = null,
        ?string $parentReference = null,
        array $metadata = []
    ) {
        $this->tableType = $tableType;
        $this->tableName = $tableName;
        $this->header = $header ?? new ExportHeader();
        $this->parentReference = $parentReference;
        $this->metadata = $metadata;
    }

    /**
     * Get table type identifier
     *
     * @return string
     */
    public function getTableType(): string
    {
        return $this->tableType;
    }

    /**
     * Get table name
     *
     * @return string
     */
    public function getTableName(): string
    {
        return $this->tableName;
    }

    /**
     * Set table name
     *
     * @param string $tableName
     */
    public function setTableName(string $tableName): void
    {
        $this->tableName = $tableName;
    }

    /**
     * Get table header
     *
     * @return ExportHeader
     */
    public function getHeader(): ExportHeader
    {
        return $this->header;
    }

    /**
     * Set table header
     *
     * @param ExportHeader $header
     */
    public function setHeader(ExportHeader $header): void
    {
        $this->header = $header;
    }

    /**
     * Get parent reference
     *
     * @return string|null
     */
    public function getParentReference(): ?string
    {
        return $this->parentReference;
    }

    /**
     * Set parent reference
     *
     * @param string|null $parentReference
     */
    public function setParentReference(?string $parentReference): void
    {
        $this->parentReference = $parentReference;
    }

    /**
     * Add a record to this table
     *
     * @param ExportRecord $record
     * @param bool $validate Whether to validate the record against the header
     * @throws \InvalidArgumentException If validation fails
     */
    public function addRecord(ExportRecord $record, bool $validate = true): void
    {
        if ($validate && !$record->validate($this->header)) {
            throw new \InvalidArgumentException('Record does not match the table header structure');
        }

        $this->records[] = $record;
    }

    /**
     * Get all records
     *
     * @return ExportRecord[]
     */
    public function getRecords(): array
    {
        return $this->records;
    }

    /**
     * Set all records
     *
     * @param ExportRecord[] $records
     * @param bool $validate Whether to validate records against the header
     * @throws \InvalidArgumentException If validation fails
     */
    public function setRecords(array $records, bool $validate = true): void
    {
        if ($validate) {
            foreach ($records as $record) {
                if (!$record->validate($this->header)) {
                    throw new \InvalidArgumentException('One or more records do not match the table header structure');
                }
            }
        }

        $this->records = $records;
    }

    /**
     * Clear all records
     */
    public function clearRecords(): void
    {
        $this->records = [];
    }

    /**
     * Check if table has any records
     *
     * @return bool
     */
    public function hasRecords(): bool
    {
        return !empty($this->records);
    }

    /**
     * Get metadata
     *
     * @return array
     */
    public function getMetadata(): array
    {
        return $this->metadata;
    }

    /**
     * Set metadata
     *
     * @param array $metadata
     */
    public function setMetadata(array $metadata): void
    {
        $this->metadata = $metadata;
    }

    /**
     * Get metadata value by key
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function getMetadataValue(string $key, $default = null)
    {
        return $this->metadata[$key] ?? $default;
    }

    /**
     * Set metadata value
     *
     * @param string $key
     * @param mixed $value
     */
    public function setMetadataValue(string $key, $value): void
    {
        $this->metadata[$key] = $value;
    }

    /**
     * Validate all records against the header
     *
     * @return bool
     */
    public function validate(): bool
    {
        foreach ($this->records as $record) {
            if (!$record->validate($this->header)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get the table data as a two-dimensional array
     * First row contains headers, subsequent rows contain values
     *
     * @param bool $formatted Whether to use formatted values
     * @return array
     */
    public function toArray(bool $formatted = false): array
    {
        $result = [];

        // Add header row
        $result[] = $this->header->getLabels();

        // Add data rows
        foreach ($this->records as $record) {
            $result[] = $formatted ? $record->getFormattedValues() : $record->getRawValues();
        }

        return $result;
    }

    /**
     * Get record count
     *
     * @return int
     */
    public function count(): int
    {
        return count($this->records);
    }

    /**
     * Get iterator for records
     *
     * @return \Traversable
     */
    public function getIterator(): \Traversable
    {
        return new \ArrayIterator($this->records);
    }
}
