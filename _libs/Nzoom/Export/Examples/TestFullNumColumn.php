<?php

namespace Nzoom\Export\Examples;

use Nzoom\Export\Adapter\ExcelExportFormatAdapter;
use Nzoom\Export\Entity\ExportColumn;
use Nzoom\Export\Entity\ExportData;
use Nzoom\Export\Entity\ExportHeader;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportTable;
use Nzoom\Export\Entity\ExportTableCollection;
use Nzoom\Export\Entity\ExportValue;

/**
 * Test class for verifying the full_num column functionality in table exports
 */
class TestFullNumColumn
{
    /**
     * Test the full_num column functionality
     */
    public static function testFullNumColumn(\Registry $registry)
    {
        // Create main export data
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('name', 'Customer Name', ExportValue::TYPE_STRING));
        $header->addColumn(new ExportColumn('email', 'Email', ExportValue::TYPE_STRING));
        $header->addColumn(new ExportColumn('full_num', 'Full Number', ExportValue::TYPE_STRING));

        $exportData = new ExportData($header);

        // Create first main record with full_num in metadata
        $record1 = new ExportRecord(['full_num' => 'CUST-2024-001']);
        $record1->addValue('name', 'John Doe', ExportValue::TYPE_STRING);
        $record1->addValue('email', '<EMAIL>', ExportValue::TYPE_STRING);
        $record1->addValue('full_num', 'CUST-2024-001', ExportValue::TYPE_STRING);

        // Create purchase table for first record
        $purchaseHeader = new ExportHeader();
        $purchaseHeader->addColumn(new ExportColumn('item', 'Item', ExportValue::TYPE_STRING));
        $purchaseHeader->addColumn(new ExportColumn('amount', 'Amount', ExportValue::TYPE_FLOAT));
        $purchaseHeader->addColumn(new ExportColumn('date', 'Date', ExportValue::TYPE_DATE));

        $purchaseTable1 = new ExportTable('purchases', 'Purchase History', $purchaseHeader, 'customer_1');

        // Add purchase records for first customer
        $purchase1 = new ExportRecord();
        $purchase1->addValue('item', 'Laptop', ExportValue::TYPE_STRING);
        $purchase1->addValue('amount', 1299.99, ExportValue::TYPE_FLOAT, '2');
        $purchase1->addValue('date', new \DateTime('2024-01-15'), ExportValue::TYPE_DATE);
        $purchaseTable1->addRecord($purchase1);

        $purchase2 = new ExportRecord();
        $purchase2->addValue('item', 'Mouse', ExportValue::TYPE_STRING);
        $purchase2->addValue('amount', 29.99, ExportValue::TYPE_FLOAT, '2');
        $purchase2->addValue('date', new \DateTime('2024-01-20'), ExportValue::TYPE_DATE);
        $purchaseTable1->addRecord($purchase2);

        // Create table collection and add to first record
        $tableCollection1 = new ExportTableCollection();
        $tableCollection1->addTable($purchaseTable1);
        $record1->setTableCollection($tableCollection1);

        // Add first record to export data
        $exportData->addRecord($record1);

        // Create second main record with different full_num
        $record2 = new ExportRecord(['full_num' => 'CUST-2024-002']);
        $record2->addValue('name', 'Jane Smith', ExportValue::TYPE_STRING);
        $record2->addValue('email', '<EMAIL>', ExportValue::TYPE_STRING);
        $record2->addValue('full_num', 'CUST-2024-002', ExportValue::TYPE_STRING);

        // Create purchase table for second record
        $purchaseTable2 = new ExportTable('purchases', 'Purchase History', $purchaseHeader, 'customer_2');

        // Add purchase records for second customer
        $purchase3 = new ExportRecord();
        $purchase3->addValue('item', 'Keyboard', ExportValue::TYPE_STRING);
        $purchase3->addValue('amount', 89.99, ExportValue::TYPE_FLOAT, '2');
        $purchase3->addValue('date', new \DateTime('2024-02-01'), ExportValue::TYPE_DATE);
        $purchaseTable2->addRecord($purchase3);

        $purchase4 = new ExportRecord();
        $purchase4->addValue('item', 'Monitor', ExportValue::TYPE_STRING);
        $purchase4->addValue('amount', 299.99, ExportValue::TYPE_FLOAT, '2');
        $purchase4->addValue('date', new \DateTime('2024-02-05'), ExportValue::TYPE_DATE);
        $purchaseTable2->addRecord($purchase4);

        // Create table collection and add to second record
        $tableCollection2 = new ExportTableCollection();
        $tableCollection2->addTable($purchaseTable2);
        $record2->setTableCollection($tableCollection2);

        // Add second record to export data
        $exportData->addRecord($record2);

        $adapter = new ExcelExportFormatAdapter($registry, 'customers', 'export');
        $adapter->export('test_full_num_column.xlsx', 'xlsx', $exportData);

        echo "Test completed! Check the file 'test_full_num_column.xlsx' to verify:\n";
        echo "1. Main sheet has customer data\n";
        echo "2. Purchase History sheet has:\n";
        echo "   - Column A: full_num values (CUST-2024-001, CUST-2024-002)\n";
        echo "   - Column B: enumeration (#)\n";
        echo "   - Column C onwards: purchase data (Item, Amount, Date)\n";
        echo "3. Each table row shows the correct full_num from its parent record\n";
    }

    /**
     * Test with missing full_num in metadata
     */
    public static function testMissingFullNum(\Registry $registry)
    {
        // Create main export data
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('name', 'Customer Name', ExportValue::TYPE_STRING));

        $exportData = new ExportData($header);

        // Create record without full_num in metadata
        $record = new ExportRecord(); // No metadata
        $record->addValue('name', 'Test Customer', ExportValue::TYPE_STRING);

        // Create simple table
        $tableHeader = new ExportHeader();
        $tableHeader->addColumn(new ExportColumn('item', 'Item', ExportValue::TYPE_STRING));

        $table = new ExportTable('items', 'Items', $tableHeader, 'customer_1');
        
        $tableRecord = new ExportRecord();
        $tableRecord->addValue('item', 'Test Item', ExportValue::TYPE_STRING);
        $table->addRecord($tableRecord);

        $tableCollection = new ExportTableCollection();
        $tableCollection->addTable($table);
        $record->setTableCollection($tableCollection);

        $exportData->addRecord($record);

        $adapter = new ExcelExportFormatAdapter($registry, 'test', 'export');
        $adapter->export('test_missing_full_num.xlsx', 'xlsx', $exportData);

        echo "Test completed! Check 'test_missing_full_num.xlsx' - full_num column should be empty.\n";
    }
}
