/* $Id: stop_n_go.css 4322 2006-09-04 08:49:33Z shacka $ */
@import url("layout/basic.css");
/* Trees */

.zpMenuStop_n_go .zpMenuContainer {
   background: RGB(200,200,200);
	padding-left: 1px;
	padding-top: 2px;
	padding-right: 0px;
	padding-bottom: 2px;
}

/* All hrefs in the tree color black */
.zpMenuStop_n_go .zpMenu-top .zpMenu-label, 
.zpMenuStop_n_go .zpMenu-top a{
	color: RGB (255,255,255);
}

.zpMenuStop_n_go .zpMenuContainer .zpMenuContainer .zpMenu-item {
   background: RGB(200,200,200);
}

.zpMenuStop_n_go .zpMenuContainer .zpMenuContainer {
	position: absolute;
	margin-top:6px;
	margin-left: 1px;
}

/* General items that's not a top menu */
.zpMenuStop_n_go .zpMenuContainer .zpMenuContainer .zpMenu-item {
	padding-top:0px;
	padding-bottom:0px; 
}

/* Current selected items in top horizontal menu*/
.zpMenuStop_n_go .zpMenuContainer .zpMenuContainer .zpMenu-item-selected,
.zpMenuStop_n_go .zpMenuContainer .zpMenu-item-selected {
	background: RGB(154,154,154);
}

.zpMenuStop_n_go .zpMenuContainer .zpMenu-item-selected .zpMenu-label,
.zpMenuStop_n_go .zpMenuContainer .zpMenu-item-selected a{
	color: RGB(0,255,0);
}

/* The arrow that shows up when there's a sub-menu */
.zpMenuStop_n_go .zpMenuContainer .zpMenuContainer .zpMenu-item-collapsed{
	background-image: url("stop_n_go/red_right.gif");
	background-repeat: no-repeat;
	background-position: 95% 50%;
}

/* The arrow that shows up when there's a sub-menu and the item is hovered*/
.zpMenuStop_n_go .zpMenuContainer .zpMenuContainer .zpMenu-item-expanded {
	background: url("stop_n_go/green_right.gif") RGB(154,154,154) no-repeat 95% 50%; 
}

.zpMenuStop_n_go .zpMenuContainer .zpMenu-item-collapsed {
	background-image: url("stop_n_go/red_down.gif");
	background-repeat: no-repeat;
	background-position: 95% 50%;
}

/* The arrow that shows up when there's a sub-menu and the item is hovered*/
.zpMenuStop_n_go .zpMenuContainer .zpMenu-item-expanded {
	background: url("stop_n_go/green_down.gif") RGB(154,154,154) no-repeat 95% 50%; 
}

.zpMenuStop_n_go .zpMenuContainer .zpMenu-item-hr,
.zpMenuStop_n_go .zpMenuContainer .zpMenuContainer .zpMenu-item-hr {
	border:none;
	background: red;
	border-top:1px solid red;
}
