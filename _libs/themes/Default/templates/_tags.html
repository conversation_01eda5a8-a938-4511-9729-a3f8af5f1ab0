{if $mode == 'view'}
  <table cellspacing="3" cellpadding="0" border="0" width="100%">
    <tr class="vtop">
      <td class="nopadding">
        {assign var='tags' value=$model->get('available_tags')}
        {capture assign="c0"}{if $model->get('tags')|@count gt 24}4{elseif $model->get('tags')|@count gt 12}3{elseif $model->get('tags')|@count gt 6}2{else}1{/if}{/capture}
        {math assign='tag_label_length' equation='round(80/x)' x=$c0}
        {counter name='t' start=0 print=false}
        {foreach name='i' from=$tags item='tag' name='key'}
          {if is_array($model->get('tags')) && in_array($tag->get('id'),$model->get('tags'))}
           {counter name='t' assign='c' print=false}
            <span class="{$tag->get('color')}_pushpin" title="{$tag->get('description')|escape}">{mb_truncate_overlib length=$tag_label_length break_words=true text=$tag->get('name')|escape}</span>
            {if !($c % $c0)}
              {math equation="x+y" x=$c y=$c0 assign="c"}
            </td></tr>
            <tr class="vtop"><td class="nopadding">
            {else}
          </td><td class="nopadding">
            {/if}
          {/if}
        {/foreach}
      </td>
    </tr>
  </table>
{else}
  <table cellspacing="0" cellpadding="0" border="0" width="100%">
    {assign var='tags_count' value=$model->get('available_tags_count')}
    {assign var='tags_grouped' value=$model->get('available_tags_grouped')}
    {capture assign="c0"}{if $tags_count gt 24}4{elseif $tags_count gt 12}3{elseif $tags_count gt 6}2{else}1{/if}{/capture}
    {math assign='tag_label_length' equation='round(80/x)' x=$c0}
    {foreach from=$tags_grouped item='tags' key='tgk' name='tgi'}
    {if $tgk}
    <tr class="vtop">
      <td colspan="{$c0}" class="labelbox" style="width: 100%!important; padding: {if $smarty.foreach.tgi.first}0{else}13px{/if} 5px 2px;">
        {$tgk|escape}
      </td>
    </tr>
    {/if}
    <tr class="vtop">
      <td class="nopadding">
        {capture assign="c"}{$c0}{/capture}
        {foreach from=$tags item='tag' key='tk' name='ti'}
          <input type="checkbox" name="tags[]" id="tag_{$tk}" value="{$tk}" {if $tag->get('section') && $tag->get('tag_limit') gt 0}class="section_{$tag->get('section')}" onclick="return checkTagLimit(this, '{$tag->get('tag_limit')}');" {/if}{if is_array($model->get('tags')) && in_array($tk, $model->get('tags'))}checked="checked" {/if}/>
          <label for="tag_{$tk}"><span class="{$tag->get('color')}_pushpin" title="{$tag->get('description')|escape}">{mb_truncate_overlib length=$tag_label_length break_words=true text=$tag->get('name')}</span></label>
          {if $smarty.foreach.ti.iteration eq $c && $tags|@count - $smarty.foreach.ti.iteration > 0}
             {math equation="x+y" x=$c y=$c0 assign="c"}
           </td></tr>
           <tr class="vtop"><td class="nopadding">
          {elseif $smarty.foreach.ti.last ne 1}
          </td><td class="nopadding">
          {/if}
          {if $smarty.foreach.ti.last && $c0 gt 1}
            {math equation="(x-(y%x))%x" x=$c0 y=$tags|@count assign='c1'}
            {if $c1}
          </td><td class="nopadding" colspan="{$c1}">&nbsp;
            {/if}
          {/if}
        {/foreach}
      </td>
    </tr>
  {/foreach}
  </table>
{/if}