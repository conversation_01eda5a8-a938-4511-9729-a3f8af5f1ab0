{capture assign='real_module'}{$current_model->modelName|mb_lower}s{/capture}
{assign var='real_controller' value=''}
{assign var='real_action' value='communications'}
{capture assign='can_add'}{if !$current_model->get('archived_by') && !($current_model->get('requires_completed_minitasks') && in_array($current_model->get('status'), array('finished', 'closed')) && ($current_model->modelName != 'Project' || $current_model->modelName == 'Project' && ($current_model->get('finished') === '0' || $current_model->get('finished') === '1'))) && $currentUser->checkRights('minitasks', 'add')}1{else}0{/if}{/capture}

<table>
  <tr>
    <td valign="top" style="padding-left: 0; margin-left: 0; border-collapse: collapse;">
      <div id="communication_messages_container">
        {if $messages->getErrors()}{include file='message.html' display="error" items=$messages->getErrors()}{/if}
        {if $messages->getMessages()}{include file='message.html' display="message" items=$messages->getMessages()}{/if}
        {if $messages->getWarnings()}{include file='message.html' display="warning" items=$messages->getWarnings()}{/if}
      </div>
      <table border="0" cellpadding="0" cellspacing="0">
        {capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=communications&amp;communications=ajax_list_communications&amp;model_id={$current_model->get('id')}&amp;communication_type={$communication_type}&amp;module={$current_model->modelName|mb_lower}s&amp;page={/capture}
        {if $pagination.pages gt 1}
          <tr>
            <td class="pagemenu">
              {assign var=sort value=$communications_sort}
              {include file="`$theme->templatesDir`pagination.html"
                found=$pagination.found
                total=$pagination.total
                rpp=$pagination.rpp
                page=$pagination.page
                pages=$pagination.pages
                pagination=$pagination
                sort=$communications_sort
                session_param=$communications_session_param
                use_ajax=$communications_use_ajax
                link=$link
                hide_stats=1
              }
            </td>
          </tr>
        {/if}
        <tr>
          <td id="communications_container">
            <form name="minitasks" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=minitasks" method="post">
            <table border="0" cellpadding="0" cellspacing="0" class="t_table t_table_border t_list">
              <tr>
                <td class="t_caption t_border" nowrap="nowrap" style="display: none;"><div class="t_caption_title" onclick="">{#minitasks_for_record#|escape}</div></td>
                <td class="t_caption t_border {$communications_sort.customer.class}" nowrap="nowrap" style="display: none;"><div class="t_caption_title" onclick="{$communications_sort.customer.link}">{#minitasks_customer#|escape}</div></td>
                <td class="t_caption t_border {$communications_sort.description.class}" nowrap="nowrap" style="width: 406px;"><div class="t_caption_title" onclick="{$communications_sort.description.link}">{#minitasks_description#|escape}</div></td>
                <td class="t_caption t_border {$communications_sort.deadline.class}" nowrap="nowrap" style="width: 70px;"><div class="t_caption_title" onclick="{$communications_sort.deadline.link}">{#minitasks_deadline#|escape}</div></td>
                <td class="t_caption t_border {$communications_sort.assigned_to.class}" nowrap="nowrap" style="width: 154px;"><div class="t_caption_title" onclick="{$communications_sort.assigned_to.link}">{#minitasks_assigned_to#|escape}</div></td>
                <td class="t_caption hright" style="width: 104px; vertical-align: middle;">
                  {if $can_add}
                  <img src="{$theme->imagesUrl}small/plus5.png" border="0" onclick="insertNewMinitaskRow(this);" alt="" title="{#minitasks_add_new#|escape}" class="pointer" style="padding-right: 4px" />
                  {else}
                  <img src="{$theme->imagesUrl}small/plus5.png" border="0" onclick="alert('{#error_add_notallowed#|escape:'quotes'|escape}')" alt="" title="{#minitasks_add_new#|escape}" class="pointer dimmed" style="padding-right: 4px" />
                  {/if}
                </td>
              </tr>
              {* row index of hidden add row should be next after that of last mini task from list *}
              {assign var='row_index' value=$communications|@count}
              <tr class="t_selected_row_for_edit" id="row_data_{$row_index}" style="display: none">
                {include file="`$theme->templatesDir`_minitasks_edit.html" minitask=$empty_minitask row_index=$row_index}
              </tr>
              {* empty row in add mode which should be loaded when coming from Create tab of model *}
              {if $can_add && $show_add}
              {math assign='row_index_next' equation='x+1' x=$row_index}
              <tr class="t_selected_row_for_edit t_even" id="row_data_{$row_index_next}">
                {include file="`$theme->templatesDir`_minitasks_edit.html" minitask=$empty_minitask row_index=$row_index_next}
              </tr>
              {/if}
            {foreach name='i' key='row_index' from=$communications item='minitask'}
            {strip}
            {capture assign='info'}
              <strong>{#added#|escape}:</strong> {$minitask->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$minitask->get('added_by_name')|escape}<br />
              <strong>{#modified#|escape}:</strong> {$minitask->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$minitask->get('modified_by_name')|escape}<br />
              <strong>{#status_modified#|escape}:</strong> {$minitask->get('status_modified')|date_format:#date_mid#|escape} {#by#|escape} {$minitask->get('status_modified_by_name')|escape}<br />
              {if $minitask->get('status') neq 'opened'}
              <strong>{#comment#}</strong>: {$minitask->get('comment')|escape|nl2br|default:"&nbsp;"}<br />
              {/if}
            {/capture}
            {/strip}
              <tr class="{cycle values='t_odd,t_even'} {$minitask->get('severity')}" id="row_data_{$row_index}">
                <td class="t_border" style="display: none;">&nbsp;</td>
                <td class="t_border {$communications_sort.customer.isSorted}" style="display: none;">&nbsp;</td>
                <td class="t_border {$communications_sort.description.isSorted}">{$minitask->get('description')|escape|nl2br|url2href}</td>
                <td class="t_border {$communications_sort.deadline.isSorted}">{$minitask->get('deadline')|date_format:#date_short#|default:"&nbsp;"}</td>
                <td class="t_border {$communications_sort.assigned_to.isSorted}">{$minitask->get('assigned_to_name')|escape|default:"&nbsp;"}</td>
                <td class="hcenter" nowrap="nowrap">
                  {if $minitask->checkPermissions('edit')}
                  <input type="image" id="img_edit_{$row_index}" name="img_edit[{$row_index}]" src="{$theme->imagesUrl}edit.png" class="pointer" border="0" alt="" title="{#edit#}" onclick="manageMinitask(this.form, '{$real_module}', '{$real_action}', 'edit', this, {$minitask->get('id')}); return false;" />
                  {else}
                  <img id="img_edit_{$row_index}" name="img_edit[{$row_index}]" src="{$theme->imagesUrl}edit.png" class="pointer dimmed" border="0" alt="" title="{#edit#}" onclick="alert('{#error_edit_notallowed#|escape:'quotes'|escape}');" />
                  {/if}
                  {if $minitask->checkPermissions('setstatus')}
                  <input type="image" id="img_finished_{$row_index}" name="img_finished[{$row_index}]" src="{$theme->imagesUrl}minitasks_finished.png" class="img_finished_{$row_index}" border="0" alt="" title="{#minitasks_finish#}" onclick="changeMinitaskStatus(this.className, '{$real_module}', '{$real_action}', {$minitask->get('id')}, 'finished'); return false;" />
                  <input type="image" id="img_failed_{$row_index}" name="img_failed[{$row_index}]" src="{$theme->imagesUrl}minitasks_failed.png" class="img_failed_{$row_index}" border="0" alt="" title="{#minitasks_cancel#}" onclick="changeMinitaskStatus(this.className, '{$real_module}', '{$real_action}', {$minitask->get('id')}, 'failed'); return false;" />
                  {else}
                  {if $minitask->get('status') != 'failed'}
                  <img id="img_finished_{$row_index}" name="img_finished[{$row_index}]" src="{$theme->imagesUrl}minitasks_finished.png" class="pointer dimmed" border="0" alt="" title="{if $minitask->get('status') eq 'finished'}{#minitasks_status_finished#|escape}{else}{#minitasks_finish#}{/if}" onclick="alert('{#error_changestatus_notallowed#|escape:'quotes'|escape}');" />
                  {/if}
                  {if $minitask->get('status') != 'finished'}
                  <img id="img_failed_{$row_index}" name="img_failed[{$row_index}]" src="{$theme->imagesUrl}minitasks_failed.png" class="pointer dimmed" border="0" alt="" title="{if $minitask->get('status') eq 'failed'}{#minitasks_status_failed#|escape}{else}{#minitasks_cancel#}{/if}" onclick="alert('{#error_changestatus_notallowed#|escape:'quotes'|escape}');" />
                  {/if}
                  {/if}
                  <img src="{$theme->imagesUrl}info.png" width="16" height="16" border="0" alt="" class="help" {popup text=$info|escape caption=#system_info#|escape} />
                  {if $minitask->checkPermissions('edit') && $minitask->get('status') eq 'opened'}
                  <input type="image" id="img_severity_{$row_index}" name="img_severity[{$row_index}]" src="{$theme->imagesUrl}slider_vertical.png" class="img_severity_{$row_index} {$minitask->get('severity')}" alt="" title="{#minitasks_severity#|escape}" onclick="showSeveritySlider(this, '{$real_action}', {$minitask->get('id')}); return false;" />
                  {/if}
                </td>
              </tr>
            {foreachelse}
              <tr class="{cycle values='t_odd,t_even'}">
                <td class="error" colspan="4">{#no_items_found#|escape}</td>
              </tr>
            {/foreach}
              <tr>
                <td class="t_footer" colspan="4"></td>
              </tr>
            </table>
            {include file="`$theme->templatesDir`_severity_legend.html" prefix='minitasks'}
            </form>
          </td>
        </tr>
        <tr>
          <td class="pagemenu">
            {include file="`$theme->templatesDir`pagination.html"
              found=$pagination.found
              total=$pagination.total
              rpp=$pagination.rpp
              page=$pagination.page
              pages=$pagination.pages
              pagination=$pagination
              sort=$communications_sort
              session_param=$communications_session_param
              use_ajax=$communications_use_ajax
              hide_selection_stats=true
              link=$link
            }
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>