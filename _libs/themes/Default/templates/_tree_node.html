<div onclick="toggleInfo(this);" style="float: left; margin: 0px; width: 10px; height: 14px; color: red; cursor: pointer; text-align: center;">{strip}
  <img name="info_arrow_right" src="{$theme->imagesUrl}red-arrow-right.png" width="8" height="8" alt="" style="margin: 3px 0px 0px 0px; border: none;{if $branch.infobox_open} display: none;{/if}" />
  <img name="info_arrow_down"  src="{$theme->imagesUrl}red-arrow-down.png"  width="8" height="8" alt="" style="margin: 3px 0px 0px 0px; border: none;{if !$branch.infobox_open} display: none;{/if}" />
{/strip}</div>
{if $branch.url}<a {if $branch.is_current_model}name="branch_current_model" {/if}href="{$branch.url}" target="_blank"{if $branch.annulled} style="text-decoration: line-through;"{/if}>{/if}<span {if $branch.is_current_model}class="branch_is_current_model" {/if}style="{if $branch.annulled}text-decoration: line-through; {/if}{if !$branch.is_current_model && isset($branch.active) && !$branch.active}color: #999999; {/if}">{$branch.label|escape|default:"&nbsp;"}</span>{if $branch.url}</a>{/if}
<div name="info_box" style="{if !$branch.infobox_open}display: none; {/if}min-width: 300px; background-color: #E3E3E3; border: 1px solid #999999; margin: 2px 0px 2px 13px; padding: 2px; cursor: default; border-radius: 3px;">
  <strong>{#type#}:</strong> {$branch.type|escape}<br />
  {if $branch.model_name eq 'Document'}
    <strong>{#added#}:</strong> {$branch.added|escape}<br />
    <strong>{#status#}:</strong> {$branch.status|escape}<br />
  {elseif $branch.model_name eq 'Finance_Incomes_Reason' || $branch.model_name eq 'Finance_Expenses_Reason' || $branch.model_name eq 'Finance_Payment'}
    <strong>{#issue_date#|mb_ucfirst}:</strong> {$branch.issue_date|escape}<br />
    {if $branch.model_name neq 'Finance_Payment'}<strong>{#date_of_payment#}:</strong> {$branch.date_of_payment|escape}<br />{/if}
    <strong>{#status#}:</strong> {$branch.status|escape}<br />
    {if !$branch.total_with_vat_layout_hidden}
      <strong>{$branch.total_with_vat_label|escape}:</strong> {$branch.total_with_vat|escape}<br />
    {/if}
  {elseif $branch.model_name eq 'Finance_Warehouses_Document'}
    <strong>{#issue_date#}:</strong> {$branch.issue_date|escape}<br />
    <strong>{#status#}:</strong> {$branch.status|escape}<br />
  {elseif $branch.model_name eq 'Contract'}
    {if !$branch.num_layout_hidden}
      <strong>{$branch.num_label|escape}:</strong> {$branch.num|escape}<br />
    {/if}
    {if !$branch.custom_num_layout_hidden}
      <strong>{$branch.custom_num_label|escape}:</strong> {$branch.custom_num|escape}<br />
    {/if}
    <strong>{#date_sign#}:</strong> {$branch.date_sign|escape}<br />
    <strong>{#date_start#}:</strong> {$branch.date_start|escape}<br />
    <strong>{#date_validity#}:</strong> {$branch.date_validity|escape}<br />
    <strong>{#status#}:</strong> {$branch.status|escape}{if $branch.substatus}&raquo;{$branch.substatus}{/if}<br />
    <strong>{$branch.total_with_vat_label|escape}:</strong> {$branch.total_with_vat|escape}
  {elseif $branch.model_name eq 'Customer'}
    <strong>{#kind#}:</strong> {$branch.kind|escape}<br />
    <strong>{#code#}:</strong> {$branch.code|escape}<br />
  {elseif $branch.model_name eq 'Project'}
    <strong>{#code#}:</strong> {$branch.code|escape}<br />
    <strong>{#status#}:</strong> {$branch.status|escape}<br />
  {elseif $branch.model_name eq 'Event'}
    <strong>{#status#}:</strong> {$branch.status|escape}<br />
    <strong>{#lr_model_customer#}:</strong> {$branch.customer|escape}<br />
    <strong>{#lr_model_project#}:</strong> {$branch.project|escape}<br />
  {elseif $branch.model_name eq 'Task'}
    <strong>{#status#}:</strong> {$branch.status|escape}<br />
    <strong>{#lr_model_customer#}:</strong> {$branch.customer|escape}<br />
    <strong>{#lr_model_project#}:</strong> {$branch.project|escape}<br />
  {/if}
</div>