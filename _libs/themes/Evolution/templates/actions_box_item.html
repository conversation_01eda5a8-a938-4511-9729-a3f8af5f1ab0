<li class="nz-actions-list-item{if !empty($action.options)} nz-actions-list-item-has_options{/if}{if !empty($action.drop_menu) && $action.drop_menu && !empty($action.options) && !isset($action.template)} nz-actions-list-item-has_dropdown nz-openable{/if}{if $action.selected} nz--selected{/if}{if !$currentUser->getPersonalSettings('interface', 'action_labels')} tab_no_label{/if}{if !empty($action.annulled)} nz--strike{/if}" title="{$action.label}">
    <a  draggable="false" {if $action.options}href="javascript:void(0)"{else}href="{$action.url}"{if $action.target} target="{$action.target}"{/if}{/if}
    class="nz-action__{$action.name}{if isset($action.template) || ($action.options && empty($action.drop_menu))} nz-popout-trigger nz-popout-autoinit{/if}
    {if isset($tabs) && empty($action.onclick)} nz-tab-button{/if}"
      {if !empty($action.onclick) && !(($action.name eq 'referent_records' || $action.name eq 'finance') && preg_match('/^#related_subpanel/', $action.url))}
        onclick="document.querySelector('#{$action.name}_action')?.click(); event.preventDefault();"{/if}
    {if $action.options && empty($action.drop_menu)}
       data-popout-on-load="prep_compatibility_{$action.name}"
    {/if}
    {if !empty($action.options) && empty($action.drop_menu) && $action.name != 'search' && $action.name != 'filter'}
        data-popout-element="#temp_{$action.controller}_{$action.action}"
        {if in_array($action.name, array('attachments','tag','status','remind'))}
          data-popout-position="panel: top right at: bottom center"
        {else}
          data-popout-position="panel: top left at: bottom left"
        {/if}
        data-container-attr="{ldelim}&quot;class&quot;: &quot;nz-actions-box-popout nz-popout-panel {if in_array($action.name, array('attachments','tag','status','remind'))}nz-pointer-top-right{else}nz-pointer-top-left{/if} nz-modal&quot;{rdelim}"
    {elseif isset($action.template) || ($action.options && empty($action.drop_menu))}data-popout-template="#xtemp_{$action.controller}_{$action.action}"
        data-popout-position="panel: top left at: bottom left"
    {/if}
        title="{$action.label}"
    {if isset($tabs) && empty($action.onclick)}data-tabref="{$action.name}"{/if}>
    {if !empty($action.icon)}
        <i class="material-icons nz-glyph">{$action.icon}</i>
    {elseif !empty($action.img)}
        <img src="{$theme->imagesUrl}{$action.img}.png" width="16" height="16" alt="" title="{$action.label}" border="0" />
    {elseif empty($action.icon) && empty($action.img)}
        <img src="{$theme->imagesUrl}{$action.name}.png" width="16" height="16" alt="" title="{$action.label}" border="0" />
    {/if}
    {if $currentUser->getPersonalSettings('interface', 'action_labels')}<span class="nz-actions-list-label">{$action.label|mb_truncate:20:'...':true}</span>{/if}</a>
    {*dummy span for onclick calls*}
    {if $action.url_preview|default:false}
        <span style="vertical-align: middle;"><img onclick="window.open('{$action.url_preview}', '{$action.target_preview}');" class="menu_additional_option_button_img" src="{$theme->imagesUrl}{$action.img_preview}.png" alt="" width="14" height="14" title="{$action.label_preview}" border="0" /></span>
    {/if}
    <span style="display: none;"
          {if isset($tabs)}
              {if ($action.name eq 'referent_records' || $action.name eq 'finance') && preg_match('/^#related_subpanel/', $action.url)}
                  onclick="toggleRelatedTabs(this); $('rel_type').value='{$action.name}';" id="tab_{$action.name}"
              {elseif $action.onclick}onclick="{$action.onclick}"{/if}
              id="tab_{$action.name}"
          {else}
              {if $action.options} onclick="if (null === $('{$action.name}Go')) if ('{$action.ajax_no}' != '1') {ldelim}getActionOptions('td_{$action.name}_options', '{$module}', '{$controller}', '{$action.name}', {if $model && $model->get('id')}'{$model->get('id')}'{else}0{/if}, {ldelim}{rdelim});{rdelim}toggleActionOptions(this); return false;"
              {elseif $action.confirm} onclick="return confirmAction('{$action.name}', function(el) {ldelim} window.open('{$action.url}', '{$action.target|default:'_self'}'); {rdelim}, this, i18n['messages']['confirm_{$action.confirm_label|default:$action.name}']);"
              {elseif $action.onclick} onclick="{$action.onclick}"{/if}
              id="{$action.name}_action"
          {/if}
    ></span>
    {if $action.drop_menu|default:false}
        {if $action.options}
        <ul class="nz-actions-list-dropdown">
            {foreach from=$action.options item='option'}
            {if isset($option.hr)}
                <li><hr /></li>
            {else}
                {include file="`$theme->templatesDir`actions_box_item.html" action=$option}
            {/if}
            {/foreach}
        </ul>
        {/if}
    {/if}
    {if isset($action.template) && !empty($action.ajax_nol)}
        {capture assign="template_box"}<div id="td_{$action.name}_options">{include file="`$action.template`" }</div>{/capture}
    {elseif $action.options && empty($action.drop_menu)}
      {if $action.name eq 'filter' || $action.name eq 'search'}
        {capture assign="template_box"}
          <table border="0" cellpadding="0" cellspacing="0" class="t_table" width="100%" style="min-width: 50px; min-height: 40px;">
            <tr>
              <td id="td_{$action.name}_options">
                {if $action.show_notice}
                  <span style="color: #0000FF">{$action.show_notice}</span>
                {/if}
                {if $action.ajax_no == '1'}
                  {if $action.template}
                    {include file=$action.template}
                  {else}
                    {include file='_action_common_options.html'}
                  {/if}
                {/if}
              </td>
            </tr>
          </table>
        {/capture}
      {else}
        {capture assign="scriptTemp"}{include file="`$theme->templatesDir`/_action_common.html" action=$action}{*<div id="td_{$action.name}_options"></div>*}{/capture}
      {/if}
    {/if}
    {if isset($template_box) || isset($scriptTemp)}
        {if $action.name eq 'filter'}
          <script type="text/javascript">
              window.addEventListener('load', getSearchOptionsExclusively);
              function getSearchOptionsExclusively(e) {ldelim}
                getActionOptions('td_{$action.name}_options', '{$module}', '{$controller}', '{$action.name}', {if $model && $model->get('id')}'{$model->get('id')}'{else}0{/if}, {ldelim}{if $smarty.get.autocomplete_filter}autocomplete_filter: '{$smarty.get.autocomplete_filter}'{/if}{rdelim});
                scalePopup();
              {rdelim}
          </script>
        {/if}
        {if isset($template_box)}
          {capture assign="scriptTemp"}<aside id="actionBox_{$action.controller}_{$action.action}" class="nz-actions-box-popout nz-popout-panel nz-pointer-top-left nz-modal"
            ><div class="nz-actions-box-popout__body nz-popout-surface nz-surface nz-elevation--z6">
              <form method="{$action.options.form_method|default:'get'}" action="{$smarty.server.PHP_SELF}" id="{$action.action|default:''}_form" enctype="multipart/form-data">
              <input type="hidden" name="{$action.module_param}" value="{$action.module}" />
              {if $action.controller}
                  <input type="hidden" name="{$action.controller_param}" value="{$action.controller}" />
              {/if}
              <input type="hidden" name="{$action.action_param}" value="{$action.action}" />
              {if $action.model_id}
                  <input type="hidden" name="{$action.action}" value="{$action.model_id}" />
              {/if}
              {if $action.model_lang}
                  <input type="hidden" name="model_lang" value="{$aaction.model_lang}" />
              {/if}
              {if $action.name eq 'search' || $action.name eq 'filter'}
                  <input type="hidden" name="{$action.session_param}" value="1" />
                  <input type="hidden" name="{$action.name}_module" value="{$action.module}" />
                  <input type="hidden" name="{$action.name}_controller" value="{$action.controller}" />
                  {if $event && !is_object($event)}
                      <input type="hidden" name="event" value="{$event}" />
                  {/if}
                  {if $relation}
                      <input type="hidden" name="relation" value="{$relation}" />
                  {/if}
                  {if $group_table}
                      <input type="hidden" name="group_table" value="{$group_table}" />
                  {/if}
                  {if $mynzoom_settings_table}
                      <input type="hidden" name="mynzoom_settings_table" value="{$mynzoom_settings_table}" />
                  {/if}
                  {if $form_name}
                      <input type="hidden" name="form_name" value="{$form_name}" />
                  {/if}
                  {if $smarty.request.autocomplete_filter}
                      <input type="hidden" name="autocomplete_filter" id="autocomplete_filter" value="session" />
                  {/if}
                  {if $smarty.request.uniqid}
                      <input type="hidden" name="uniqid" id="uniqid" value="{$smarty.request.uniqid}" />
                  {/if}
                  {if $session_param}
                      <input type="hidden" name="session_param" value="{$session_param}" />
                  {/if}
              {/if}
              {if $hidden_fields}{$hidden_fields}{/if}
              {if $template_box}
                {$template_box}
              {else}
              {/if}
              </form>
          </div></aside>{/capture}
          <script type="text/x-template" id="xtemp_{$action.controller}_{$action.action}">{$scriptTemp}
          </script>
        {else}
          <div id="temp_{$action.controller}_{$action.action}" class="nz-actions-box-popout__body">{$scriptTemp}</div>
        {/if}
    <script>
      {if $action.name eq 'search' || $action.name eq 'filter'}
      window.prep_compatibility_{$action.name} = function() {ldelim}
        prep_compatibility('{$action.name}', 'td_{$action.name}_options', '{$action.ajax_no}' !== '1');
        {rdelim}
      {/if}
        // {$action.name };
      {if $action.name eq 'attachments' || $action.name eq 'setstatus' || $action.name eq 'remind' || $action.name eq 'add' || $action.name eq 'multiadd' || $action.name eq 'adds' || $action.name eq 'create' || $action.name eq 'tag' || $action.name eq 'export' || $action.name eq 'clone' || $action.name eq 'generate' || $action.name eq 'receive_date' || $action.name eq 'annul' || $action.name eq 'timesheets' || $action.name eq 'transfer' || $action.name eq 'annulmentsubtype' || 'import'}
      window.prep_compatibility_{$action.name} = function() {ldelim}
        prep_compatibility('{$action.name}', 'td_{$action.name}_options', '{$action.ajax_no}' !== '1', {$action.model_id});
        const actionName = '{$action.name}';
        {literal}
        if (['annulmentsubtype'].includes(actionName)) {
          this.clickOutsideClose = false;
        }
        if (actionName === 'adds') {
          var options_row = document.querySelector(`#${actionName}_suboptions_row`);
          if (!options_row) {
            return;
          }
          var inside_inputs = options_row.getElementsByTagName('input');
          for (let j = 0; j < inside_inputs.length; j++) {
            if (inside_inputs[j].type === 'radio') {
              const options_box_id = actionName + '_' + inside_inputs[j].id + '_box';
              const options_box = document.querySelector('#'+options_box_id);
              if (inside_inputs[j].checked === true) {
                options_box.style.display = 'block';
                toggleFields(options_box, true);
              } else {
                options_box.style.display = 'none';
                toggleFields(options_box, false);
              }
            }
          }
        }
        if ((actionName === 'export')) {

        }
        {/literal}
      {rdelim}
      {/if}

      {if $action.name eq 'transformations'}
      window.prep_compatibility_{$action.name} = function() {ldelim}{rdelim}
      {/if}
    </script>
    {/if}
</li>
