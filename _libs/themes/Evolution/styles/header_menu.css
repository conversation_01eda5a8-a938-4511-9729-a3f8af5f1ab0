/*Header Menu styles for old menus where we have*/
.m_header_menu {
    color: #666666;
    height: 35px;
    background:#FFFFFF;
    padding-top: 5px;
    padding-bottom: 1px;
    vertical-align: bottom;
    background:url('../images/header_menu_bg.png') repeat-x left bottom;
}
div.m_header_menu {
    height: 0!important;
    padding: 0;
}
.m_header_menu ul {
    margin: 0;
    padding: 5px;
    z-index: 40;
}
.m_header_menu li {
    display:inline;
    margin:0;
    padding:0;
}
.m_header_menu li span {
    float:left;
    background:url('../images/menu_tab_left.gif') no-repeat left top;
    background-position:0% 0px;
    margin:0;
    padding:0 0 0 4px;
    text-decoration:none;
}
.m_header_menu li span a {
    float: left;
    display: block;
    background: url('../images/menu_tab_right.gif') no-repeat right top;
    padding: 5px 7px 4px 2px;
    color: #000000;
    cursor: pointer;
}
/* Commented Backslash Hack hides rule from IE5-Mac \*/
.m_header_menu li span a {
    /*float:none;*/
}
/* End IE5-Mac hack */
.m_header_menu li span:hover, .m_header_menu li span.selected {
    background-position:0% -42px;
}

.m_header_menu li span a:hover, .m_header_menu li span.selected a {
    color:#000;
    background-position:100% -42px;
}
.m_header_menu li span.selected a {
    font-weight: bold;
    color:#000;
    background-position:100% -42px;
}
.m_header_menu li a:hover img, .m_header_menu li span.selected img {
    /*filter: alpha(opacity=100);*/
}
.m_header_menu li a img {
    float: left;
    margin: 0 2px 0 0;
}
.m_header_menu li.has_sub span a {
    padding-right: 20px;
    background-image: url('../images/menu_tab_right_has_sub.gif');
    background-position:100% 0px;
}

/*************************************************************************************/

/* styles from themes/layout/basic.css we will need for our theme */
.zpMenuContainer * {
    -moz-box-sizing: content-box;
}

/* Top menu, horizontal */
.zpMenu-horizontal-mode .zpMenu-level-1 {
    display: inline-block;
    border-radius: 0.25rem;
}

.zpMenuContainer>.zpMenuContainer>.zpMenu {
    transform: translateY(5px);
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0px 3px 3px -2px rgba(0, 0, 0, 0.2),
                0px 3px 4px 0px rgba(0, 0, 0, 0.14),
                0px 1px 8px 0px rgba(0,0,0,.12);
}

/* What to do if the LI has NO icon
by default show NO icons
-you css can override this
*/
.zpMenu-level-1 {
    background: none;
}

.zpMenu-item {
    cursor: pointer;
}

/* Hide expansion indicators */
/* These are indicators for items that have sub-menus, and are hidden cause they generate a
   blank space in front of the content of those items */
.zpMenu .zpMenu-item .minus, .zpMenuContainer .zpMenuContainer .zpMenu-item .minus,
.zpMenu .zpMenu-item .plus, .zpMenuContainer .zpMenuContainer .zpMenu-item .plus {
    display: none;
}

/* Helper classes to hide the menu onload */
/* To hide the menu before it is loaded */

ul.zpHideOnLoad {
    display: none;
}

/* Sub-menu */
.zpMenuContainer .zpMenuContainer {
    position: absolute;
}

/* Must include this CSS for Animation and Special Effects.  Fixes IE problems */
.zpMenuContainer .zpMenuContainer .zpMenu {
    /*filter: alpha(opacity = 100);*/
}

/*  THEME STYLES TO MAKE THE NZOOM's MENU LOOKS LIKE THE OLDER MENU */
.m_header_menu .zpMenuNzoom {
    padding-left: 10px;
    background:url('../images/menu_bg_border.jpg') repeat-x left bottom;
}

.zpMenu-table {
    height: 25px;
    padding-right: 20px;
}

.zpMenu-label {
    white-space: nowrap!important;
    text-align: left;
    padding-left: 5px;
    color: inherit;
}

.zpMenu-table .icon {
    padding: 0px 0.25rem 0 0;
}

.zpMenuNoIcon .zpMenuContainer .zpMenuContainer .zpMenu-table .icon {
    display: none;
}

.zpMenu-label a {
    color: inherit!important;
}

.zpMenu-item-selected .zpMenu-label a,
.zpMenu-item-expanded .zpMenu-label a {

}
.zpMenu-item.strike .zpMenu-label a,
.zpMenu-item.strike .zpMenu-label a {
    text-decoration: line-through;
}
.zpMenuNzoom {
    position: relative;
}
/* Fix for no tabbed buttons (the upper right print button) */
.zpMenuNzoom .zpMenu-level-1.no_tab {
    background-image: none!important;
}
.zpMenuNzoom .zpMenu-level-1.no_tab div {
    background-image: none!important;
}
.zpMenuNzoom .zpMenu-level-1.no_tab table {
    background-image: none!important;
    padding: 0;
}

/* FIRST LEVEL MENU ITEMS */
.zpMenu-level-1 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    margin: 0 0.5rem;
}


.zpMenu-level-1.zpMenu-item-expanded,
.zpMenu-level-1.zpMenu-item-selected,
.zpMenu-level-1.menu-path {

}
.zpMenu-level-1.zpMenu-item-selected, .zpMenu-level-1.menu-path {
    background: var(--primary-color);
    color: var(--text-onprimary-color);
}

.zpMenu-level-1 .zpMenu-table {
}

.zpMenu-level-1.zpMenu-item-selected .zpMenu-table, .zpMenu-level-1.menu-path .zpMenu-table {
}

.zpMenu-level-1.zpMenu-item-collapsed .zpMenu-table {
    background: var(--background-color);
}

.zpMenu-level-1.zpMenu-item-expanded .zpMenu-table,
.zpMenu-level-1.zpMenu-item-collapsed.menu-path .zpMenu-table {
    background: var(--background-color);
}

/* OTHER LEVELS MENU ITEMS */
.zpMenuContainer .zpMenuContainer .zpMenu {
    border-radius: var(--border-radius);
}

.zpMenuContainer .zpMenuContainer .zpMenu-item {
    min-width: 170px;
}

.zpMenuContainer .zpMenuContainer .zpMenu-item-expanded,
.zpMenuContainer .zpMenuContainer .zpMenu-item-selected,
.zpMenuContainer .zpMenuContainer .menu-path {
    background-color: var(--primary-color)!important;
    color: var(--text-onprimary-color);
}
.zpMenuContainer .zpMenuContainer .menu-path {
    font-weight: bold;
}
.zpMenuContainer .zpMenuContainer .zpMenu-table {
    background: none;
}

.zpMenuContainer .zpMenuContainer .zpMenu-item-single,
.zpMenuContainer .zpMenuContainer .zpMenu-item-last {
    border-bottom: 0px;
}
.zpMenuContainer .zpMenuContainer .zpMenu-item-collapsed,
.zpMenuContainer .zpMenuContainer .zpMenu-item-expanded {
    background: url("../images/arrow_right1.png") no-repeat right center;
}

.zpMenuContainer .zpMenuContainer .zpMenu-table .icon {
    padding-left: 0.25rem!important;
    width: 1.25rem!important;
}
.zpMenuContainer .zpMenuContainer .zpMenu-item-hr {
    background-color: var(--muted-text-color) !important;
    height: 1px !important;
}

/* styles to make a menu option look like a button */
.zpMenu-item-single {
    background-color: var(--primary-color)!important;
    color: var(--text-onprimary-color)!important;
}

.no_tab.button {
    margin: 3px 0 0 0.5rem;
    padding: 0.1em 1.25rem 0.1em;
    height: initial;
    color: var(--text-onprimary-color)!important;
}
.no_tab.button:hover {
    color: var(--text-onprimary-color)!important;
    box-shadow: 0px 3px 1px -2px rgb(0 0 0 / 20%),
                0px 2px 2px 0px rgb(0 0 0 / 14%),
                0px 1px 5px 0px rgb(0 0 0 / 12%);
}
.no_tab.button .zpMenu-table {
    height: initial!important;
    padding: 0px!important;
}

.zpMenu-level-1.zpMenu-item-expanded .zpMenu-table,
.zpMenu-level-1.zpMenu-item-collapsed.menu-path .zpMenu-table,
.zpMenu-level-1.zpMenu-item-collapsed .zpMenu-table {
    background-color: transparent !important;
}


.no_tab.button .zpMenu-table .zpMenu-label {
    display: inline-block;
    color: inherit;
    padding: 0 0 0.25rem 0 !important;
    line-height: 1.75rem;
    vertical-align: middle;
    -webkit-touch-callout: none; /* iOS Safari */
    -webkit-user-select: none; /* Safari */
    user-select: none;
}
.no_tab.button .zpMenu-table .zpMenu-label * {
    display: inline;
    vertical-align: middle;
    line-height: 1.75rem;
}
.no_tab.button .zpMenu-table .zpMenu-label:after {
    content: '\e5c5';
    font-family: "Material Icons";
    border: none;
    font-size: 1.5rem;
    line-height: 1.75rem;
    vertical-align: middle;

    transition: color 200ms ease-out;
}
.no_tab.button .zpMenu-table td {
    padding: 0px!important;
}
