.nz-dialog {
    position: fixed;
    z-index: 10200;
    padding: 0.5rem 1rem 1rem 0.5rem;
    left: calc(50% - 50vw);
    top: calc(50% - 50vh);
    transition: opacity var(--transitions-time) ease-out,
                width var(--transitions-time) ease-out,
                height var(--transitions-time) ease-out;
}

.nz-dialog-scrim {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 10199;
    background-color: #00000030;
    /*pointer-events: none;*/
    transition: opacity var(--transitions-time) ease-out;
}

.nz-dialog,
.nz-dialog-scrim {
    opacity: 1;
}

.nz-dialog:not(.nz--opened),
.nz-dialog-scrim:not(.nz--opened) {
    display: none;
}

.nz-dialog.nz--opening,
.nz-dialog-scrim.nz--opening {
    display: initial;
    opacity: 0;
}

.nz-dialog.nz--closing,
.nz-dialog-scrim.nz--closing {
    opacity: 0;
}

.nz-dialog .nz-dialog-surface {
    width: 100%;
    height: 100%;
    overflow: hidden;;
    padding: 0;
    border-radius: var(--border-radius, 0.25rem);
}


.nz-dialog-head {
    position: relative;
    display: flex;
    flex-direction: row;
    justify-items: stretch;
    justify-content: space-between;
    padding: 0 0 0 1rem;
    height: 3rem;
    line-height: 3rem;
    font-size: 1rem;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.87);
    border-bottom: 1px solid var(--border-color, rgba(0, 0, 0, 0.12));

    -webkit-user-select: none; /* Safari */
    -ms-user-select: none; /* IE 10 and IE 11 */
    user-select: none; /* Standard syntax */
}

.nz-dialog-head .nz-dialog-close {
    right: 0.5rem;
    vertical-align: middle;
    width: 2rem;
    margin: 0;
    padding: 0;
    font-size: 1rem;
    line-height: inherit;
    cursor: pointer;
    color: var(--primary-color);
}
.nz-dialog-head .nz-dialog-closehover {
    color: var(--important-color);
}
.nz-dialog-head .nz-dialog-title {
    display: inline-block;
    vertical-align: middle;
    margin: 0;
    padding: 0;
    font-size: inherit;
    font-weight: inherit;
    line-height: inherit;
    color: var(--primary-color);
    overflow: hidden;
    text-overflow: ellipsis;
}

.nz-dialog-body {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.nz-dialog-content {
    overflow: auto;
    padding: 1rem;
    max-height: calc(100vh - 2rem);
    transition: opacity var(--transitions-time) ease-out,
                width var(--transitions-time) ease-out,
                height var(--transitions-time) ease-out;
}

.nz-dialog-buttons {
    display: flex;
    flex-direction: row;
    justify-items: stretch;
    justify-content: center;
    padding: 1rem;
}

.nz-dialog-buttons:empty {
    display: none;
}

.nz-dialog-buttons .nz-button {
    margin: 0 0.5rem;
}

