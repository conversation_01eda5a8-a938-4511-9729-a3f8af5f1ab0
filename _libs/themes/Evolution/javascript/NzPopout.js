'use strict';

class NzPopout extends NzAbstractOpenable {
    static openedPopouts = [];
    contentEl;
    shouldRecycleView = true;
    template;
    triggerEl;
    popoutEl;
    onLoad;
    onOpen;
    onClose;
    clickOutsideClose;
    outsideClickHandlerBound;
    triggerBound;
    position;
    targetObserver;

    constructor(options) {
        super('popout');
        this.template = options.template ? options.template : null;
        this.popoutElParent = options.popoutElParent
            ? (typeof options.popoutElParent === 'string' ? document.querySelector(options.popoutElParent) : options.popoutElParent)
            : document.body;

        this.onLoad = options.onLoad ? options.onLoad : null;
        this.onOpen = options.onOpen ? options.onOpen : null;
        this.onClose = options.onClose ? options.onClose : null;
        this.isModal = options.modal ? !!options.modal : false;

        this.clickOutsideClose = typeof options.clickOutsideClose === 'undefined' ? true : options.clickOutsideClose;

        this.position = options.position ? options.position : {
            panel: 'center middle',
            at: 'center middle',
        };
        this.position.distance = options.position && options.position.distance ? options.position.distance : [0, 0];

        this.containerAttributes = options.containerAttributes ? options.containerAttributes : null;

        if (this.template) {
            this.popoutEl = Nz.template2Element(this.template)[0];
        } else if (options.contentElement) {
            if (typeof options.contentElement === 'string') {
                options.contentElement = document.querySelector(options.contentElement);
            }
            this.popoutEl = this.createPopoutEl(options.contentElement);
        } else {
            this.popoutEl = this.createPopoutEl();
        }

        if (options.endpoint) {
            this.setContentLoader(new NzContentLoader(options.endpoint, this.contentEl));
        } else if (!this.template && options.content) {
            this.contentEl.innerHTML = options.content;
        }

        this.outsideClickHandlerBound = this.outsideClickHandler.bind(this);

        if (this.isModal) {
            this.popoutEl.classList.add('nz-popout--modal');
        }
    }

    createPopoutEl(contentElement) {
        const popoutEl = document.createElement('div');
        popoutEl.classList.add('nz-popout-panel');

        if (this.containerAttributes) {
            for (const [key, value] of Object.entries(this.containerAttributes)) {
                // class attribute should be added as a list not replaced
                if (key === 'class') {
                    if (typeof value === 'string') {
                        popoutEl.classList.add(...(value.split(' ').filter(v => v)));
                    } else if (Array.isArray(value)) {
                        popoutEl.classList.add(...value);
                    }
                    continue;
                }
                // other attributes
                popoutEl.setAttribute(key, value);
            }
        }

        const popSurface = document.createElement('div');
        popSurface.classList.add('nz-popout-surface', 'nz-surface', 'nz-elevation--z8');
        popoutEl.appendChild(popSurface);
        if (contentElement) {
            this.contentEl = contentElement;
        } else {
            this.contentEl = document.createElement('div');
        }
        this.contentEl.classList.add('nz-popout-content');
        popSurface.appendChild(this.contentEl);
        return popoutEl;
    }

    trigger(e) {
        if (e) {
            e.preventDefault();
            e.stopPropagation();
        }
        this.toggle();
    }

    attach(element) {
        if (typeof element === 'string') {
            this.triggerEl = document.querySelector(element);
        } else {
            this.triggerEl = element;
        }
        this.triggerBound = this.trigger.bind(this);
        this.triggerEl.addEventListener('click', this.triggerBound);
        this.triggerEl.classList.add('nz-popout--upgrade');

        this.popoutElParent.appendChild(this.popoutEl);
        this.popoutEl.addEventListener('click', (e) => {
            if (e.target.closest('.nz-popout--close')) {
                e.preventDefault();
                this.close(e.target);
            }
        });

        this.targetObserver = new MutationObserver((records, observer) => {
            records.forEach((record) => {
                record.removedNodes.forEach((node) => {
                    if (node.contains(this.triggerEl)) {
                        this.targetObserver.disconnect();
                        // Whait to see if the element is removed, or it is just moved from one place to another
                        setTimeout(() => {
                            if (this.triggerEl.closest('body')) {
                                // Element was removed in a procedure, and is restored in the dom.
                                // Continue observing
                                this.targetObserver.observe(document.body, {
                                    childList: true,
                                    subtree: true,
                                });
                            } else {
                                // Element is removed for good - detach
                                this.detach();
                            }
                        }, 2);
                    }
                });
            });
        });
        this.targetObserver.observe(document.body, {
            childList: true,
            subtree: true,
        });
    }

    detach() {
        this.targetObserver.disconnect();
        this.targetObserver = null;

        this.triggerEl.removeEventListener('click', this.triggerBound);
        this.triggerEl.classList.remove('nz-popout--upgrade');
        this.triggerEl = null;
        this.popoutElParent.removeChild(this.popoutEl);
    }

    onLoadFn() {
        setTimeout(() => {
            this.fixPosition();
        }, 100);

        let callback = null;

        if (typeof this.onLoad === 'function') {
            callback = this.onLoad;
        } else if (typeof this.onLoad === 'string') {
            callback = this.onLoad = window[this.onLoad];
        }

        if (typeof callback === 'function') {
            callback.apply(this, [this.triggerEl, this.popoutEl]);;
        }
    }

    outsideClickHandler(e) {
        // Ignore clicks on the date selection popup, autocompleter option, and real option element.
        if (e.target.nodeName === 'OPTION'
            || e.target.closest('.calendar')
            || e.target.closest('.autocompletebox')
            || e.target.matches('li.selected')) {
            return;
        }

        // Check for hierarchy of the target elemnt. If the target is inside the popout, ignore the click.
        if (e.target.closest('.nz-popout-panel') === this.popoutEl) {
            return;
        }

        // Use geometric criteria for determining if the click was outside the popout
        const rect = this.popoutEl.getBoundingClientRect();
        if (e.clientX  < rect.left || e.clientX  > rect.right || e.clientY < rect.top || e.clientY > rect.bottom) {
            this.close();
        }
    }

    fixPosition() {
        for (let i = 0; i < 220; i += 50) {
            setTimeout(() => {
                Nz.positionAsync(this.triggerEl, this.popoutEl, this.position, this.position.distance);
            }, i);
        }
    }

    beforeOpenFn() {
        this.closeAll();
        this.triggerEl.classList.add('nz--loading');
    }

    onOpenFn() {
        this.triggerEl.classList.add('nz--active');
        this.popoutEl.classList.add('nz--opened');
        this.triggerEl.classList.remove('nz--loading');

        if (this.clickOutsideClose) {
            document.body.addEventListener('click', this.outsideClickHandlerBound);
        }
        setTimeout(()=>{
            this.fixPosition();
            if (typeof this.onOpen === 'function') {
                this.onOpen.apply(this, [this.triggerEl, this.popoutEl]);
            }
        }, 1);
    }

    onCloseFn(target) {
        this.triggerEl.classList.remove('nz--active');
        this.popoutEl.classList.remove('nz--opened');
        if (this.clickOutsideClose) {
            document.body.removeEventListener('click', this.outsideClickHandlerBound);
        }

        if (typeof this.onClose === 'function') {
            this.onClose.apply(this, [this.triggerEl, this.popoutEl, target]);
        }
        if (!this.shouldRecycleView) {
            this.getContentLoader().setLoaded(false);
        }
    }

    static initElement(element) {
        const options = {};

        if (typeof element.dataset.popoutTemplate !== 'undefined') {
            options.template = element.dataset.popoutTemplate;
        }

        if (typeof element.dataset.popoutContent !== 'undefined') {
            options.content = element.dataset.popoutContent;
        }

        if (typeof element.dataset.popoutElement !== 'undefined') {
            options.contentElement = element.dataset.popoutElement;
        }

        if (typeof element.dataset.popoutElParent !== 'undefined') {
            options.popoutElParent = element.dataset.popoutElParent;
        }

        if (typeof element.dataset.popoutEndpoint !== 'undefined') {
            options.endpoint = element.dataset.popoutEndpoint;
        }

        if (typeof element.dataset.popoutOnLoad !== 'undefined') {
            options.onLoad = element.dataset.popoutOnLoad;
        }

        if (typeof element.dataset.popoutOnOpen !== 'undefined') {
            options.onOpen = element.dataset.popoutOnOpen;
        }

        if (typeof element.dataset.popoutOnClose !== 'undefined') {
            options.onClose = element.dataset.popoutOnClose;
        }

        if (typeof element.dataset.popoutClickOutsideClose !== 'undefined') {
            if (typeof element.dataset.popoutClickOutsideClose === 'string') {
                options.clickOutsideClose = ['false', '0'].includes(element.dataset.popoutClickOutsideClose) ? false : true;
            } else {
                options.clickOutsideClose = !!element.dataset.popoutClickOutsideClose;
            }
        }

        if (typeof element.dataset.containerAttr !== 'undefined') {
            options.containerAttributes = JSON.parse(element.dataset.containerAttr);
        }

        if (typeof element.dataset.popoutPosition !== 'undefined') {
            const positionStr = element.dataset.popoutPosition;
            const positionParts = positionStr.split(/\s+/);
            let panel, at, distance;
            if (positionParts[0] === 'panel:' && positionParts[3] === 'at:') {
                panel = `${positionParts[1]} ${positionParts[2]}`;
                at = `${positionParts[4]} ${positionParts[5]}`;
            } else if (positionParts[3] === 'panel:' && positionParts[0] === 'at:') {
                panel = `${positionParts[4]} ${positionParts[5]}`;
                at = `${positionParts[1]} ${positionParts[2]}`;
            }
            options.position = {panel: panel, at: at};
            if (typeof positionParts[6] !== 'undefined') {
                distance = [positionParts[6]];
                if (typeof positionParts[7] !== 'undefined') {
                    distance.push(positionParts[7]);
                }
                options.position.distance = distance;
            }
        }
        const instance = new NzPopout(options);
        instance.attach(element);
        if (typeof element.__ez_instance === 'undefined') {
            element.__ez_instance = [];
        }
        element.__ez_instance.push(instance);
        element.classList.add('nz-popout--upgraded');
    }

    static autoInit() {
        document.querySelectorAll('.nz-popout-trigger.nz-popout-autoinit:not(.nz-popout--upgraded)').forEach((el, i) => {
            this.initElement(el);
        });
    }

    static closeAll() {
        super.closeAll('popout');
    }
}
