{if $translations}
<div id="translate_container">
  <div class="t_footer" id="translate_switch"><div class="{if $smarty.cookies.translate_box eq 'off'}switch_up{else}switch_down{/if}"></div></div>
  <div id="translate_flags"{if $smarty.cookies.translate_box eq 'off'} style="display: none"{/if}>
    <table>
      <tr>
        <td>
          <div>{if $action_param eq 'users' && $action eq 'profile'}{#posible_translations#|escape}{else}{#available_translations#|escape}{/if}:</div>
        </td>
          {foreach from=$translations item='trans'}
            <td>
              {if $trans.lang}
                {if $trans.selected}
                  <img src="{$theme->imagesUrl}flags/{$trans.lang}.png" alt="{$trans.lang_name}" title="{$trans.lang_name}" border="0" class="selected" />
                {else}
                  <a href="{$trans.url}"><img src="{$theme->imagesUrl}flags/{$trans.lang}.png" alt="{$trans.lang_name}" title="{$trans.lang_name}" border="0" /></a>
                {/if}
              {else}
                <img src="{$theme->imagesUrl}flags/{$lang}.png" alt="{$lang}" border="0" class="selected" />
              {/if}
            </td>
          {foreachelse}
            <td>
              <img src="{$theme->imagesUrl}flags/{$lang}.png" alt="{$lang}" border="0" class="selected" />
            </td>
          {/foreach}
        {if $make_translations && $model->checkPermissions('translate')}
          <td>
            &nbsp;&nbsp;
          </td>
          <td style="border-left: 1px solid #CCCCCC;">
            <div class="make_translation_div">{#make_translation#|escape}:</div>
          </td>
          {foreach from=$make_translations item='mk_trans'}
            <td>
              {if $mk_trans.lang}
                {if $mk_trans.lang eq $model->get('model_lang')}
                  <img src="{$theme->imagesUrl}flags/{$mk_trans.lang}.png" alt="{$mk_trans.lang_name}" title="{$mk_trans.lang_name}" border="0" class="selected" />
                {else}
                  <a href="{$mk_trans.url}"><img src="{$theme->imagesUrl}flags/{$mk_trans.lang}.png" alt="{$mk_trans.lang_name}" title="{$mk_trans.lang_name}" border="0" /></a>
                {/if}
              {else}
                <img src="{$theme->imagesUrl}flags/{$lang}.png" alt="{$lang}" border="0" class="selected" />
              {/if}
            </td>
          {/foreach}
        {/if}
      </tr>
    </table>
  </div>
</div>
{/if}
