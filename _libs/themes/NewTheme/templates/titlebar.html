{if $validLogin}
          <div class="m_logout">
          <!-- Begin Logout Bar -->
{include file='logout.html'}
          <!-- End Welcome Bar -->
          </div>
{/if}

          <div class="m_welcome">
          <!-- Begin Welcome Bar -->
{include file='welcome.html'}
          <!-- End Welcome Bar -->
          </div>

          <div class="m_navbar">
          <!-- Begin Navigation Bar -->
{include file='navbar.html}
          <!-- End Navigation Bar -->
          </div>

          <!-- Begin Lang Menu -->
          {if is_array($lang_menu) && count($lang_menu) > 1}
            <div class="m_lang_menu{if $include_keyboard_inputs_toggler} floatr{/if}">
              {foreach name='i' from=$lang_menu item='supported_lang'}
                <a href="{$supported_lang.url}"{if $supported_lang.selected} class="selected"{/if}><img src="{$theme->imagesUrl}flags/{$supported_lang.lang}.png" width="16" height="11" border="0" alt="{$supported_lang.i18n}" title="{$supported_lang.i18n}"{if !$supported_lang.selected} class="dimmed"{/if} /></a>
              {/foreach}
            </div>
          {else}
            &nbsp;
          {/if}
          <!-- End Lang Menu -->

          <!-- Begin Alternative Keyboard Change Menu -->
          {if $include_keyboard_inputs_toggler && $prefered_keyboard_inputs}
            <div class="langLink{if $smarty.cookies.molang} {$smarty.cookies.molang}{/if}">{$smarty.cookies.molang|default:'OFF'}</div>
            <input type="hidden" id="prefered_keyboard_inputs" name="prefered_keyboard_inputs" value="{$prefered_keyboard_inputs}" />
          {/if}
          <!-- End Alternative Keyboard Change Menu -->

          <!-- Icon to show if e-mails are turned off -->
          {if $emails_off}
            <div class="m_emailing floatr" {popup text=#emailing_is_off# caption=#system_info#|escape}></div>
          {/if}

          <div class="m_stopwatchbar" id="m_stopwatchbar">
            <!-- Stopwatch Info Bar -->
            <span id="m_stopwatchbar_box">
{include file='stopwatchbar.html}
            </span>
            <!-- End Stopwatch Bar -->
          </div>

          <div class="m_lockbar" id="m_lockbar">
            <!-- Begin Lock Info Bar -->
            <!--  Loaded with AJAX -->
            <!-- End Lock Bar -->
          </div>