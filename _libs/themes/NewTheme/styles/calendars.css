.cal_hour {
    font-size: 10px;
    text-align: right;
    width: 20px;
    padding: 0 1px;
    color: #222433;
}
.cal_title_bar.t_caption2 {
    height: 25px;
}
.cal_title_bar .t_caption2_title {
    font-size: 14px !important;
    color: #222433;
}
.cal_weekday_title {
    background: 0 0 repeat-x #EDEDED;
    padding: 2px;
    border-bottom: 1px solid #B2B2B2;
}
/*Week View*/
/* Month View*/
.cal_month_day {
    height: 100px;
    vertical-align: top;
    border-right: 1px solid #B2B2B2;
    border-bottom: 1px solid #B2B2B2;
}
.cal_not_available {
    background: url('../images/t_caption7.png') #C6C6C6 0 0;
}
.cal_weekend, .cal_weekend.cal_day_with_events.cal_past, .cal_weekend.cal_day_with_events.cal_future, .cal_weekend.cal_day_with_events.cal_present {
    background: silver;
}
.cal_weekend_title {
    background: url('../images/t_caption3.jpg') #C6C6C6 0 0;
    padding: 2px;
    border-bottom: 1px solid #BBBBBB;
    color: #FFFFFF;
}
.cal_weekend.cal_day_with_events a:link, .cal_weekend.cal_day_with_events a:active, .cal_weekend.cal_day_with_events a:visited {
    color: #FF6600;
}
.cal_today {
    /*background: #AFF2FF;*/
    color: #FFFFFF;
    border: 2px solid red;
}
.cal_today a:link, .cal_today a:active, .cal_today a:visited {
    /*color: #FF0000;*/
}
.cal_no_border {
    border-right: 0;
}
.cal_week_num {
    vertical-align: top;
    border-right: 1px solid #CCCCCC;
    border-bottom: 1px solid #CCCCCC;
    background: #E6E6E6 url('../images/t_caption5.png');
}
.cal_week_num_title {
    background: url('../images/t_caption6.png') #CBCBCB 0 0;
    padding: 2px;
    border-bottom: 1px solid #CCCCCC;
    color: #1A4392;
}
.cal_day_with_events, .cal_day_with_events.cal_past {
    background: #FFFFD1;
}
.cal_day_with_events.cal_present {
    background: #FFEFF0;
}
.cal_day_with_events.cal_future {
    background: #D5EFD1;
}
.cal_day_with_events.cal_not_available {
    background: #FFFFEC;
}
.cal_day_with_events .cal_month_daynum a:link, .cal_day_with_events .cal_month_daynum a:active, .cal_day_with_events .cal_month_daynum a:visited {
    color: #222433;
    font-weight: bold;
}
.cal_day_with_events .cal_month_daynum a:hover {
    color: #007F00;
}
.cal_month_daynum {
    height: 12px;
    text-align: right;
    vertical-align: top;
}
.cal_month_daynum a:link, .cal_month_daynum a:visited, .cal_month_daynum a:active {
    display: block;
    float: right;
}
.cal_month_daynum_addlink:link, .cal_month_daynum_addlink:visited, .cal_month_daynum_addlink:active {
    width: 11px;
    height: 11px;
    background: url('../images/small/plus4.png') 0 0 no-repeat;
    opacity: 0.30;
    filter: alpha(opacity=30);
}
.cal_month_daynum_addlink:hover {
    background: url('../images/small/plus3.png') 0 0 no-repeat;
    opacity: 1;
    filter: alpha(opacity=100);
}
/*Year View*/
.cal_year_month {
    text-align: center;
    vertical-align: top;
    border: 2px solid #BBBBBB;
}
.cal_month .cal_month_day {
    text-align: center;
    height: 15px;
}
.cal_month .cal_weekday_title {
    font-size: 10px;
}
/*Events in Calendar*/
/*Day Events*/
.cal_day_day {
}
.cal_day_day .cal_event {
    position: absolute;
    border: 1px dotted #AAAAAA;
    background: #FFFBCF;
    padding: 1px 0 1px 1px;
    font-size: 10px;
    overflow: hidden;
}
.cal_day_day .cal_event_allday {
    padding: 0 0 0 1px;
    position: relative;
    background: #FFE2BF;
    border: 1px dotted #FF8800 !important;
}
.cal_day_day .cal_event a, .cal_day_day .cal_event span {
    font-size: 10px;
}
.cal_day_day .cal_event:hover {
    background: #FFF8AF;
}
.cal_day_day .cal_event_allday:hover {
    background: #FFD39F;
}
.cal_day_day .cal_event.cal_event_private {
    background: #DFFFE0;
    color: #999999;
    cursor: help;
    font-style: italic;
}
.cal_day_day .cal_event_allday.cal_event_private {
    background: #FFDFDF;
}
.cal_day_day .cal_event.cal_event_private:hover {
    color: #666666;
    background: #BFFFC2;
}
.cal_day_day .cal_event_allday.cal_event_private:hover {
    background: #FFBFBF;
}
/*Week Events*/
.cal_week_day {
}
.cal_week_day .cal_event {
    position: absolute;
    border: 1px dotted #AAAAAA;
    background: #FFFBCF;
    padding: 1px 0 1px 1px;
    font-size: 10px;
    overflow: hidden;
}
.cal_week_day .cal_event_allday {
    padding: 0 0 0 1px;
    position: relative;
    background: #FFE2BF;
    border: 1px dotted #FF8800 !important;
}
.cal_week_day .cal_event a, .cal_week_day .cal_event span {
    font-size: 10px;
}
.cal_week_day .cal_event:hover {
    background: #FFF8AF;
}
.cal_week_day .cal_event_allday:hover {
    background: #FFD39F;
}
.cal_week_day .cal_event.cal_event_private {
    background: #DFFFE0;
    color: #999999;
    cursor: help;
    font-style: italic;
}
.cal_week_day .cal_event_allday.cal_event_private {
    background: #FFDFDF;
}
.cal_week_day .cal_event.cal_event_private:hover {
    color: #666666;
    background: #BFFFC2;
}
.cal_week_day .cal_event_allday.cal_event_private:hover {
    background: #FFBFBF;
}
/*Month Events*/
.cal_month_day .cal_event {
    width: 95%;
    clear: both;
    padding: 1px 0 1px 1px;
    height: 15px;
    border-bottom: 1px dotted #CCCCCC;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -o-box-sizing: border-box;
    background: #FFFBCF;
    overflow: hidden;
}
.cal_month_day .cal_event:hover {
    background: #FFF8AF;
}
.cal_month_day .cal_remaining_events {
    height: 12px;
    text-align: center;
    border: 0;
}
.cal_month_day .cal_remaining_events a:link, .cal_remaining_events a:visited, .cal_remaining_events a:visited {
    font-style: italic;
    color: #666666;
}
.cal_month_day .cal_remaining_events a:hover {
    color: #FF0000;
}
.cal_month_day .cal_event.cal_event_private {
    background: #DFFFE0;
    color: #999999;
    cursor: help;
    font-style: italic;
}
.cal_month_day .cal_event.cal_event_private:hover {
    color: #666666;
    background: #BFFFC2;
}
.cal_month_day .cal_allday_event {
    outline: 1px dotted #FF8800 !important;
    outline-offset: -1px;
}
/*Month Events List*/
.cal_month_list_container {
    margin-top: 15px;
}
.cal_month_list {
    padding: 0px !important;
}
.cal_month_list .cal_event {
    border: 1px dotted #AAAAAA;
    background: #FFFBCF;
    padding: 2px;
    margin-bottom: 1px;
    overflow: hidden;
}
.cal_month_list .cal_event:hover {
    background: #FFF8AF;
}
.cal_month_list .cal_event.cal_event_private {
    background: #DFFFE0;
    color: #999999;
    cursor: help;
    font-style: italic;
}
.cal_month_list .cal_event.cal_event_private:hover {
    color: #666666;
    background: #BFFFC2;
}
/*Side Panel*/
.cal_side_panel {
    margin: 24px 0 0 20px;
    float: left;
}
.cal_side_panel_item {
    margin-bottom: 15px;
}
/*Side Panel Events List*/
.cal_events_panel_list {
    padding: 0px !important;
}
.cal_events_panel_list .cal_event {
    border: 1px dotted #AAAAAA;
    background: #FFFBCF;
    padding: 2px;
    margin-bottom: 1px;
    overflow: hidden;
}
.cal_events_panel_list .cal_event:hover {
    background: #FFF8AF;
}
.cal_events_panel_list .cal_event.cal_event_private {
    background: #DFFFE0;
    color: #999999;
    cursor: help;
    font-style: italic;
}
.cal_events_panel_list .cal_event.cal_event_private:hover {
    color: #666666;
    background: #BFFFC2;
}
.cal_events_panel_list .cal_allday_event {
    border: 1px dotted #FF8800 !important;
}
.cal_remaining_events {
    height: 14px !important;
    text-align: center;
    border: 1px dotted #CCCCCC;
    font-style: italic;
    color: #666666;
    background: #E3E3E3;
}
.cal_side_panel_item .cal_remaining_events:hover {
    background: #EFEFEF;
}
.cal_event.cal_event_denied {
    border: 1px dashed #AAAAAA;
    filter: alpha(opacity=50);
    opacity: 0.50;
}
.cal_month_day .cal_event.cal_event_denied {
    border: 0px none;
    border-bottom: 1px dashed #AAAAAA;
}
/*Item Alternating Rows*/
.cal_odd td {
    background-color: #F8F8F8;
}
.cal_odd:hover td {
    color: #000000;
    background-color: #FFFFF4;
}
.cal_even td {
    background-color: #ECECEC;
}
.cal_even:hover td {
    color: #000000;
    background-color: #FFFFF4;
}
/* Calendar Navigation Styles */
#cal_nav_container #cal_nav_details div {
    padding-top: 2px;
    color: #999999;
}
#cal_nav_container #cal_nav_switch {
    height: 7px;
    cursor: pointer;
    font-size: 1px;
    line-height: 0;
}
#cal_nav_container #cal_nav_switch div {
    height: 7px;
    font-size: 1px;
    line-height: 0;
}
.cal_nav_footer {
    border-right: 1px solid #CCCCCC;
    border-bottom: 1px solid #CCCCCC;
    border-left: 1px solid #CCCCCC;
}
/* Calendar Grid Time Add Box */
.cal_add_box {
    width: 12px;
    height: 12px;
    vertical-align: top;
    float: right;
}
.cal_add_box_link:link, .cal_add_box_link:visited, .cal_add_box_link:active {
    display: block;
    width: 11px;
    height: 11px;
    background: url('../images/small/plus4.png') 0 0 no-repeat;
    opacity: 0.30;
    filter: alpha(opacity=30);
}
.cal_add_box_link:hover {
    background: url('../images/small/plus3.png') 0 0 no-repeat;
    opacity: 1;
    filter: alpha(opacity=100);
}
