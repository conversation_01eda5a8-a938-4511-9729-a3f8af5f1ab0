    <table border="1" cellpadding="3" cellspacing="0">
        <tr>
          <th nowrap="nowrap">{#num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.full_num|default:#documents_full_num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.customer|default:#documents_customer#|escape}</th>
          <th nowrap="nowrap">{if 'documents' == 'projects'}{#documents_status_phase#|escape}{else}{$basic_vars_labels.status|default:#documents_status#|escape}{/if}</th>
          <th nowrap="nowrap">{$basic_vars_labels.tags|default:#documents_tags#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.added|default:#added#|escape}</th>
          <th nowrap="nowrap">{$add_vars_labels.118.plr_leave_days}</th>
          <th nowrap="nowrap">{$basic_vars_labels.decision|default:#documents_decision#|escape}</th>
          <th nowrap="nowrap">{#documents_group#|escape}</th>
          <th nowrap="nowrap">{$add_vars_labels.118.plr_leave_finish_date}</th>
          <th nowrap="nowrap">{$basic_vars_labels.added_by|default:#documents_added_by#|escape}</th>

        </tr>
      {foreach name='i' from=$documents item='single'}
        {if !$single->checkPermissions('list')}
          <tr>
            <td nowrap="nowrap">{counter name='item_counter' print=true}</td>
            <td colspan="13-1">{#error_right_notallowed#|escape}</td>
          </tr>
        {else}
          <tr valign="top">
            <td>{counter name='item_counter' print=true}</td>
          <td style="mso-number-format: \@;">{$single->get('full_num')|numerate:$single->get('direction')}</td>
          <td style="mso-number-format: \@;">{if $single->get('customer')}{$single->get('customer_name')|escape|default:"&nbsp;"}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
          {capture assign='popup_and_onclick'}
            {popup text=$document_status|escape|default:'&nbsp;' caption=#help_documents_status#|escape width=250}{if $single->checkPermissions('setstatus')} onclick="changeStatus({$single->get('id')}, 'documents')" style="cursor:pointer;"{/if}
          {/capture}
          {capture assign='document_expired'}
            {if $single->get('status') != 'closed' && $single->get('deadline') && $single->get('deadline')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#}
              {#documents_expired_legend#}: {$single->get('deadline')|date_format:#date_mid#}!
            {/if}
            {if $single->get('status') != 'closed' && $single->get('validity_term') && $single->get('validity_term')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#}
              {$documents_expired} {#documents_expired_validity_legend#}: {$single->get('validity_term')|date_format:#date_mid#}!
            {/if}
          {/capture}
          {if $single->get('status') != 'closed' && (($single->get('deadline') && $single->get('deadline')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#) || ($single->get('validity_term') && $single->get('validity_term')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#))}
            
          {/if}
          {if $single->get('substatus_name')}
            {if $single->get('icon_name')}
              
            {else}
              
            {/if}
            {$single->get('substatus_name')}
          {else}
            
            {capture assign='status_param'}documents_status_{$single->get('status')}{/capture}
            {$smarty.config.$status_param}
          {/if}
          </td>
          <td style="mso-number-format: \@;">
            {if $single->get('model_tags') && is_array($single->get('model_tags')) && $single->get('model_tags')|@count gt 0 && $single->checkPermissions('tags_view')}
              {foreach from=$single->get('model_tags') item='tag' name='ti'}
                {$tag->get('name')|escape}{if !$smarty.foreach.ti.last}, {/if}
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td nowrap="nowrap">{$single->get('added')|date_format:#date_short#|escape}</td>
          {capture assign='var_value'}{$single->getVarValue('plr_leave_days')}{/capture}
          {if is_numeric($var_value)}{assign var='var_value' value=$var_value|replace:".":","}{/if}
          {capture assign='var_back_label'}{$add_vars_back_labels.118.plr_leave_days}{/capture}
          {capture assign='content'}{if $var_value || $var_value === '0'}{$var_value|nl2br|url2href}{include file="_back_label.html" back_label=$var_back_label}{else}&nbsp;{/if}{/capture}
          <td{if is_numeric($var_value)} align="right"{/if}>{$content}</td>
        {if preg_match('#^Finance_.*$#i', $single->modelName)}
          {assign var='_module' value='finance'}
          {capture assign='_controller'}{$single->modelName|regex_replace:'#^Finance_(.*)$#i':'$1'|mb_lower}s{/capture}
        {else}
          {capture assign='_module'}{$single->modelName|mb_lower}s{/capture}
        {/if}
        {capture assign='title_label'}{$_module}_assign_change{/capture}
        <td style="mso-number-format: \@;">
        {assign var='long_text' value=''}
        {assign var='short_text' value=''}
        {if $single->get('assignments_decision')}
              {foreach from=$single->get('assignments_decision') item='assignment' name='assignees'}
                {capture assign='long_text'}
                  {$long_text}{$assignment.assigned_to_name|escape|default:'&nbsp;'}{if !$smarty.foreach.assignees.last}, {/if}
                {/capture}
                {if $smarty.foreach.assignees.iteration le 3}
                  {capture assign='short_text'}
                    {$short_text}{$assignment.assigned_to_name|escape|default:'&nbsp;'}{if !$smarty.foreach.assignees.last}, {/if}
                  {/capture}
                {/if}
              {foreachelse}
                &nbsp;
              {/foreach}
              {$long_text}
            {else}
              &nbsp;
            {/if}
        </td>
          <td style="mso-number-format: \@;">{$single->get('group_name')|escape|default:"&nbsp;"}</td>
          {capture assign='var_value'}{$single->getVarValue('plr_leave_finish_date')}{/capture}
          {if is_numeric($var_value)}{assign var='var_value' value=$var_value|replace:".":","}{/if}
          {capture assign='var_back_label'}{$add_vars_back_labels.118.plr_leave_finish_date}{/capture}
          {capture assign='content'}{if $var_value || $var_value === '0'}{$var_value|nl2br|url2href}{include file="_back_label.html" back_label=$var_back_label}{else}&nbsp;{/if}{/capture}
          <td{if is_numeric($var_value)} align="right"{/if}>{$content}</td>
          <td style="mso-number-format: \@;">{$single->get('added_by_name')|escape|default:"&nbsp;"}</td>

          </tr>
        {/if}
      {foreachelse}
        <tr>
          <td colspan="13">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </table>
